-- Drop existing policies
DROP POLICY IF EXISTS "Teachers can read any room location" ON public.room_locations;
DROP POLICY IF EXISTS "Teachers can update their own room locations" ON public.room_locations;
DROP POLICY IF EXISTS "Students can read room locations" ON public.room_locations;

-- Re-enable RLS
ALTER TABLE public.room_locations ENABLE ROW LEVEL SECURITY;

-- Allow teachers to read any room location
CREATE POLICY "Teachers can read any room location"
ON public.room_locations FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.user_id = auth.uid()
    AND profiles.role = 'teacher'
  )
);

-- Allow teachers to update their own room locations
CREATE POLICY "Teachers can update their own room locations"
ON public.room_locations FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.rooms
    WHERE rooms.id = room_locations.room_id
    AND rooms.teacher_id = (
      SELECT id FROM public.profiles
      WHERE profiles.user_id = auth.uid()
      AND profiles.role = 'teacher'
    )
  )
);

-- Allow students to read room locations for rooms they can access
CREATE POLICY "Students can read room locations"
ON public.room_locations FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.user_id = auth.uid()
    AND profiles.role = 'student'
    AND EXISTS (
      SELECT 1 FROM public.rooms
      WHERE rooms.id = room_locations.room_id
    )
  )
);

-- Grant permissions
GRANT ALL ON public.room_locations TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated; 