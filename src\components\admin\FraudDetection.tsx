import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase';
import { format } from 'date-fns';
import { MapPin, AlertTriangle, Check, Trash2 } from 'lucide-react';

interface FraudAlert {
  id: string;
  title: string;
  message: string;
  created_at: string;
  type: string;
  category: string;
  severity: string;
  status: string;
  distance_meters: number;
  student_id: string;
  teacher_id: string;
  student_location: {
    x: number;
    y: number;
  };
  metadata: {
    student_name: string;
    room_number: string;
    exceeded_by_meters: number;
  };
  admin_read_at: string | null;
}

export default function FraudDetection() {
  const [alerts, setAlerts] = useState<FraudAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { profile } = useAuth();

  useEffect(() => {
    fetchAlerts();
    const unsubscribe = subscribeToAlerts();
    return () => {
      unsubscribe();
    };
  }, []);

  const fetchAlerts = async () => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('type', 'distance_alert')
        .eq('category', 'fraud_detection')
        .eq('severity', 'high')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAlerts(data || []);
    } catch (error) {
      console.error('Error fetching fraud alerts:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch fraud detection alerts',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const subscribeToAlerts = () => {
    const subscription = supabase
      .channel('fraud_alerts')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: 'type=eq.distance_alert AND category=eq.fraud_detection AND severity=eq.high'
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setAlerts(prev => [payload.new as FraudAlert, ...prev]);
            toast({
              title: 'New Fraud Alert',
              description: 'A new potential fraud attempt has been detected',
              variant: 'destructive',
            });
          } else if (payload.eventType === 'DELETE') {
            setAlerts(prev => prev.filter(alert => alert.id !== payload.old.id));
          } else if (payload.eventType === 'UPDATE') {
            setAlerts(prev => prev.map(alert => 
              alert.id === payload.new.id ? payload.new as FraudAlert : alert
            ));
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  };

  const markAsRead = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({
          admin_read_at: new Date().toISOString(),
          status: 'investigating'
        })
        .eq('id', alertId);

      if (error) throw error;

      setAlerts(prev =>
        prev.map(alert =>
          alert.id === alertId
            ? { ...alert, admin_read_at: new Date().toISOString(), status: 'investigating' }
            : alert
        )
      );
    } catch (error) {
      console.error('Error marking alert as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to update alert status',
        variant: 'destructive',
      });
    }
  };

  const deleteAlert = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', alertId);

      if (error) throw error;

      setAlerts(prev => prev.filter(alert => alert.id !== alertId));
      toast({
        title: 'Success',
        description: 'Alert deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting alert:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete alert',
        variant: 'destructive',
      });
    }
  };

  const openInMaps = (lat: number, lng: number) => {
    window.open(`https://www.google.com/maps?q=${lat},${lng}`, '_blank');
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading fraud detection alerts...</CardTitle>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Fraud Detection Alerts
            </CardTitle>
            <CardDescription>
              Monitor potential attendance fraud attempts
            </CardDescription>
          </div>
          <Badge variant="destructive">
            {alerts.filter(a => !a.admin_read_at).length} Unread
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <p className="text-center text-muted-foreground py-8">
              No fraud alerts to display
            </p>
          ) : (
            alerts.map(alert => (
              <Card key={alert.id} className={alert.admin_read_at ? 'opacity-70' : ''}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold">{alert.metadata.student_name}</h3>
                        <Badge variant="destructive">
                          {alert.severity === 'high' ? 'Critical' : 'High Risk'}
                        </Badge>
                      </div>
                      <p className="text-sm">{alert.message}</p>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="outline" className="text-xs">
                          Room: {alert.metadata.room_number}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          Distance: {Math.round(alert.distance_meters)}m
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          Exceeded by: {Math.round(alert.metadata.exceeded_by_meters)}m
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          Status: {alert.status}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {format(new Date(alert.created_at), 'PPp')}
                      </p>
                      <div className="flex gap-2 mt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openInMaps(alert.student_location.y, alert.student_location.x)}
                        >
                          <MapPin className="h-4 w-4 mr-1" />
                          View Location
                        </Button>
                        {!alert.admin_read_at && (
                          <Button
                            size="sm"
                            variant="default"
                            onClick={() => markAsRead(alert.id)}
                          >
                            <Check className="h-4 w-4 mr-1" />
                            Mark as Investigating
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => deleteAlert(alert.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
} 