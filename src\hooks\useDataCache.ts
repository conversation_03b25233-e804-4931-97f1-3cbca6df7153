import { useState, useRef, useEffect } from 'react';

// Type for the cache entry
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  loading: boolean;
  error: string | null;
}

// Type for the cache
interface Cache<T> {
  [key: string]: CacheEntry<T>;
}

// Options for the hook
interface UseDataCacheOptions {
  cacheKey: string;
  cacheDuration?: number; // Duration in milliseconds
  initialFetch?: boolean;
}

/**
 * A hook for caching data across component renders and tab switches
 * @param fetchFn The function to fetch the data
 * @param options Options for the cache
 * @returns [data, loading, error, refetch]
 */
export function useDataCache<T>(
  fetchFn: () => Promise<T>,
  options: UseDataCacheOptions
) {
  // Static cache shared across all instances of the hook
  const staticCache = useRef<Cache<T>>({});

  // State for the current instance
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Default cache duration is 5 minutes
  const cacheDuration = options.cacheDuration || 5 * 60 * 1000;
  const { cacheKey } = options;

  // Function to fetch data and update cache
  const fetchData = async (force = false) => {
    // Check if we have cached data and it's not expired
    const cachedEntry = staticCache.current[cacheKey];
    const now = Date.now();
    
    if (
      !force &&
      cachedEntry &&
      now - cachedEntry.timestamp < cacheDuration &&
      !cachedEntry.loading
    ) {
      // Use cached data
      setData(cachedEntry.data);
      setLoading(false);
      setError(cachedEntry.error);
      return cachedEntry.data;
    }

    // Set loading state
    setLoading(true);
    
    // Update cache to indicate loading
    staticCache.current[cacheKey] = {
      ...(cachedEntry || { data: null as unknown as T }),
      loading: true,
      timestamp: now,
      error: null
    };

    try {
      // Fetch new data
      const newData = await fetchFn();
      
      // Update cache with new data
      staticCache.current[cacheKey] = {
        data: newData,
        timestamp: Date.now(),
        loading: false,
        error: null
      };
      
      // Update state
      setData(newData);
      setError(null);
      return newData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      
      // Update cache with error
      staticCache.current[cacheKey] = {
        ...(cachedEntry || { data: null as unknown as T }),
        loading: false,
        timestamp: Date.now(),
        error: errorMessage
      };
      
      // Update state
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on mount if initialFetch is true
  useEffect(() => {
    if (options.initialFetch !== false) {
      fetchData();
    }
  }, [cacheKey]);

  return { data, loading, error, refetch: () => fetchData(true) };
}
