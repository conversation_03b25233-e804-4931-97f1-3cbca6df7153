-- Drop existing policies to start fresh
DROP POLICY IF EXISTS "Students can insert their own attendance records" ON public.attendance_records;
DROP POLICY IF EXISTS "Students can update their own attendance records" ON public.attendance_records;
DROP POLICY IF EXISTS "Students can view their own attendance records" ON public.attendance_records;
DROP POLICY IF EXISTS "Teachers can view attendance records for their rooms" ON public.attendance_records;
DROP POLICY IF EXISTS "Teachers can update attendance records for their rooms" ON public.attendance_records;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all attendance records" ON public.attendance_records;

DROP POLICY IF EXISTS "Students can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Students can update their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Teachers can create notifications" ON public.notifications;
DROP POLICY IF EXISTS "Teachers can view notifications for their students" ON public.notifications;
DROP POLICY IF EXISTS "System can create notifications" ON public.notifications;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all notifications" ON public.notifications;

-- Re-enable RLS
ALTER TABLE public.attendance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- <PERSON> necessary permissions
GRANT ALL ON public.attendance_records TO authenticated;
GRANT ALL ON public.notifications TO authenticated;

-- Attendance Records Policies

-- Students can manage their own attendance records
CREATE POLICY "manage_own_attendance"
ON public.attendance_records
FOR ALL
TO authenticated
USING (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE profiles.id::text = attendance_records.student_id::text
  )
)
WITH CHECK (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE profiles.id::text = attendance_records.student_id::text
  )
);

-- Teachers can view and update attendance for their rooms
CREATE POLICY "teachers_manage_room_attendance"
ON public.attendance_records
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role = 'teacher'
    AND EXISTS (
      SELECT 1 
      FROM rooms 
      WHERE rooms.id::text = attendance_records.room_id::text
      AND rooms.teacher_id::text = profiles.id::text
    )
  )
);

-- Admins can manage all attendance records
CREATE POLICY "admins_manage_attendance"
ON public.attendance_records
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role = 'admin'
  )
);

-- Notification Policies

-- Students can view and update their own notifications
CREATE POLICY "manage_own_notifications"
ON public.notifications
FOR ALL
TO authenticated
USING (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE profiles.id::text = notifications.student_id::text
  )
)
WITH CHECK (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE profiles.id::text = notifications.student_id::text
  )
);

-- Teachers can manage notifications for their students
CREATE POLICY "teachers_manage_notifications"
ON public.notifications
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles teacher_profile
    WHERE teacher_profile.user_id::text = auth.uid()::text 
    AND teacher_profile.role = 'teacher'
    AND EXISTS (
      SELECT 1 
      FROM rooms 
      WHERE rooms.teacher_id::text = teacher_profile.id::text
      AND rooms.id::text = (notifications.metadata->>'room_id')::text
    )
  )
);

-- Admins can manage all notifications
CREATE POLICY "admins_manage_notifications"
ON public.notifications
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role = 'admin'
  )
); 