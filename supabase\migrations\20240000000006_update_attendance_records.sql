-- First, drop the existing table (this will also drop dependent objects like indexes and policies)
DROP TABLE IF EXISTS public.attendance_records;

-- Recreate attendance_records table with UUID room_id
CREATE TABLE IF NOT EXISTS public.attendance_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL,
  room_id UUID NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT now(),
  device_info TEXT,
  verification_method VARCHAR NOT NULL,
  status VARCHAR NOT NULL,
  location JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  FOREIGN KEY (room_id) REFERENCES rooms(id)
);

-- Recreate indexes
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON public.attendance_records(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_room_id ON public.attendance_records(room_id);
CREATE INDEX IF NOT EXISTS idx_attendance_timestamp ON public.attendance_records(timestamp);

-- Enable RLS
ALTER TABLE public.attendance_records ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow authenticated users full access" ON public.attendance_records
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Grant permissions
GRANT ALL ON public.attendance_records TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated; 