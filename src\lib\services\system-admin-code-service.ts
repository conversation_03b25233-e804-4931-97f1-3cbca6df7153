import { supabase } from "@/lib/supabase";

/**
 * Get the current system admin code
 * @returns The current system admin code or null if not found
 */
export const getSystemAdminCode = async (): Promise<string | null> => {
  try {
    // Try to get the system admin code from the system_settings table
    const { data, error } = await supabase
      .from("system_settings")
      .select("setting_value")
      .eq("setting_name", "system_admin_code")
      .single();

    if (error) {
      console.error("Error fetching system admin code:", error);

      // If the record doesn't exist, return null (not the default code)
      if (error.code === "PGRST116") {
        console.log("System admin code record not found in database");
        return null;
      }

      // For other errors, also return null
      return null;
    }

    if (data && data.setting_value && data.setting_value.code) {
      console.log("Successfully retrieved system admin code from database");
      return data.setting_value.code;
    }

    // If we got data but no code property, return null
    console.log("System admin code record exists but has no code property");
    return null;
  } catch (error) {
    console.error("Unexpected error in getSystemAdminCode:", error);
    return null;
  }
};

/**
 * Update the system admin code
 * @param newCode The new system admin code
 * @returns True if the update was successful, false otherwise
 */
export const updateSystemAdminCode = async (
  newCode: string
): Promise<boolean> => {
  try {
    console.log("Attempting to update system admin code to:", newCode);

    // First, try to update the existing record
    const { data: updateData, error: updateError } = await supabase
      .from("system_settings")
      .update({
        setting_value: { code: newCode },
        updated_at: new Date().toISOString(),
      })
      .eq("setting_name", "system_admin_code")
      .select();

    if (updateError) {
      console.error("Error updating system admin code:", updateError);
      return false;
    }

    // If update was successful and affected rows
    if (updateData && updateData.length > 0) {
      console.log("Successfully updated system admin code");
      return true;
    }

    // If no rows were affected, the record doesn't exist, so insert it
    console.log("No existing record found, inserting new system admin code");
    const { error: insertError } = await supabase
      .from("system_settings")
      .insert({
        setting_name: "system_admin_code",
        setting_value: { code: newCode },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (insertError) {
      console.error("Error inserting system admin code:", insertError);
      return false;
    }

    console.log("Successfully inserted new system admin code");
    return true;
  } catch (error) {
    console.error("Unexpected error in updateSystemAdminCode:", error);
    return false;
  }
};

/**
 * Get whether system admin code is required
 * @returns True if code is required, false otherwise
 */
export const getSystemAdminCodeRequired = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from("system_settings")
      .select("setting_value")
      .eq("setting_name", "require_system_admin_code")
      .single();

    if (error) {
      // If setting doesn't exist, default to requiring code for security
      if (error.code === "PGRST116") {
        return true;
      }
      console.error("Error fetching system admin code requirement:", error);
      return true;
    }

    return data?.setting_value?.required ?? true;
  } catch (error) {
    console.error("Error in getSystemAdminCodeRequired:", error);
    return true;
  }
};

/**
 * Update whether system admin code is required
 * @param required Whether the code should be required
 * @returns True if the update was successful, false otherwise
 */
export const updateSystemAdminCodeRequired = async (
  required: boolean
): Promise<boolean> => {
  try {
    const { error } = await supabase.from("system_settings").upsert(
      {
        setting_name: "require_system_admin_code",
        setting_value: { required },
        updated_at: new Date().toISOString(),
      },
      { onConflict: "setting_name" }
    );

    if (error) {
      console.error("Error updating system admin code requirement:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in updateSystemAdminCodeRequired:", error);
    return false;
  }
};

/**
 * Validate a system admin code
 * @param code The code to validate
 * @returns True if the code is valid, false otherwise
 */
export const validateSystemAdminCode = async (
  code: string
): Promise<boolean> => {
  try {
    // First check if system admin code is required
    const isCodeRequired = await getSystemAdminCodeRequired();

    // If code is not required, always return true
    if (!isCodeRequired) {
      console.log("System admin code not required, allowing signup");
      return true;
    }

    // Get the current system admin code first to check if a custom code has been set
    const currentCode = await getSystemAdminCode();

    // Only accept the default code for initial setup if no custom code has been set
    if (code === "INITIAL_SYSTEM_SETUP_CODE") {
      // If a custom code has been set in the database, reject the default code
      if (currentCode && currentCode !== "INITIAL_SYSTEM_SETUP_CODE") {
        console.log("Custom system admin code exists, rejecting default code");
        return false;
      }

      // Check if any system admins exist
      const { data: existingAdmins, error: adminError } = await supabase
        .from("profiles")
        .select("count")
        .eq("role", "admin")
        .eq("access_level", 3);

      if (adminError) {
        console.error("Error checking existing admins:", adminError);
      } else {
        // If no system admins exist, accept the initial code
        const count = existingAdmins?.[0]?.count || 0;
        if (count === 0) {
          console.log("No system admins exist, accepting initial code");
          return true;
        } else {
          console.log("System admins exist, rejecting default code");
          return false;
        }
      }
    }

    // If we couldn't get the current code, only accept the default code for initial setup
    if (!currentCode) {
      console.log(
        "No system admin code found in database, checking if this is initial setup"
      );
      return code === "INITIAL_SYSTEM_SETUP_CODE";
    }

    // Compare the codes
    console.log("Comparing provided code with stored code");
    return code === currentCode;
  } catch (error) {
    console.error("Unexpected error in validateSystemAdminCode:", error);
    return code === "INITIAL_SYSTEM_SETUP_CODE";
  }
};

/**
 * Ensure the system_settings table exists and has a system admin code
 * @returns True if the table exists and has a system admin code, false otherwise
 */
export const ensureSystemAdminCode = async (): Promise<boolean> => {
  try {
    // Check if the system_settings table exists by trying to select from it
    const { data: settingsData, error: settingsError } = await supabase
      .from("system_settings")
      .select("id")
      .limit(1);

    if (settingsError) {
      console.error("Error checking system_settings table:", settingsError);

      // Try to create the table
      try {
        await supabase.rpc("execute_sql", {
          sql: `
            CREATE TABLE IF NOT EXISTS public.system_settings (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              setting_name TEXT NOT NULL UNIQUE,
              setting_value JSONB NOT NULL,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            );

            -- Enable RLS
            ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;

            -- Create policies
            DROP POLICY IF EXISTS "Allow authenticated users to select system_settings" ON public.system_settings;
            CREATE POLICY "Allow authenticated users to select system_settings"
              ON public.system_settings FOR SELECT
              USING (auth.role() = 'authenticated');

            DROP POLICY IF EXISTS "Allow authenticated users to insert system_settings" ON public.system_settings;
            CREATE POLICY "Allow authenticated users to insert system_settings"
              ON public.system_settings FOR INSERT
              WITH CHECK (auth.role() = 'authenticated');

            DROP POLICY IF EXISTS "Allow authenticated users to update system_settings" ON public.system_settings;
            CREATE POLICY "Allow authenticated users to update system_settings"
              ON public.system_settings FOR UPDATE
              USING (auth.role() = 'authenticated');
          `,
        });
      } catch (createTableError) {
        console.error(
          "Error creating system_settings table:",
          createTableError
        );
        return false;
      }
    }

    // Check if the system admin code exists
    const { data, error } = await supabase
      .from("system_settings")
      .select("setting_value")
      .eq("setting_name", "system_admin_code")
      .single();

    if (error) {
      console.error("Error checking system admin code:", error);

      // If the record doesn't exist, insert it
      if (error.code === "PGRST116") {
        const { error: insertError } = await supabase
          .from("system_settings")
          .insert({
            setting_name: "system_admin_code",
            setting_value: { code: "INITIAL_SYSTEM_SETUP_CODE" },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });

        if (insertError) {
          console.error("Error inserting system admin code:", insertError);
          return false;
        }
      } else {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("Unexpected error in ensureSystemAdminCode:", error);
    return false;
  }
};
