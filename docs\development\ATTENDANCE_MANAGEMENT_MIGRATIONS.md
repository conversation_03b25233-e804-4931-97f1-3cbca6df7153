# 📋 Attendance Management Database Migrations

## 🚀 Required Database Changes

To make the new **Attendance Management** feature work properly, you need to run the following SQL commands in your Supabase dashboard.

### 📍 **Step 1: Create Indexes for Performance**

Run this SQL in your Supabase SQL Editor:

```sql
-- Attendance Cleanup Indexes Migration
-- This migration adds indexes to optimize attendance record cleanup operations

-- Index for efficient cleanup by timestamp
CREATE INDEX IF NOT EXISTS idx_attendance_records_timestamp 
ON attendance_records (timestamp);

-- Index for efficient cleanup by school and timestamp
CREATE INDEX IF NOT EXISTS idx_attendance_records_school_timestamp 
ON attendance_records (school_id, timestamp);

-- Index for efficient date range queries
CREATE INDEX IF NOT EXISTS idx_attendance_records_date_range 
ON attendance_records (timestamp, school_id, student_id);

-- Index for room-based queries
CREATE INDEX IF NOT EXISTS idx_attendance_records_room_timestamp
ON attendance_records (room_id, timestamp);

-- Index for student-based queries
CREATE INDEX IF NOT EXISTS idx_attendance_records_student_timestamp
ON attendance_records (student_id, timestamp);

-- Comment explaining the indexes
COMMENT ON INDEX idx_attendance_records_timestamp IS 'Optimizes cleanup operations by timestamp';
COMMENT ON INDEX idx_attendance_records_school_timestamp IS 'Optimizes school-specific cleanup operations';
COMMENT ON INDEX idx_attendance_records_date_range IS 'Optimizes date range queries for reports';
COMMENT ON INDEX idx_attendance_records_room_timestamp IS 'Optimizes room-specific attendance queries';
COMMENT ON INDEX idx_attendance_records_student_timestamp IS 'Optimizes student-specific attendance queries';
```

### 🏗️ **Step 2: Ensure Blocks and Rooms Tables Exist**

Check if these tables exist, if not, create them:

```sql
-- Create blocks table if it doesn't exist
CREATE TABLE IF NOT EXISTS blocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create rooms table if it doesn't exist
CREATE TABLE IF NOT EXISTS rooms (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    building VARCHAR(255),
    floor VARCHAR(50),
    capacity INTEGER,
    block_id UUID NOT NULL REFERENCES blocks(id) ON DELETE CASCADE,
    school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for blocks
CREATE INDEX IF NOT EXISTS idx_blocks_school_id ON blocks(school_id);
CREATE INDEX IF NOT EXISTS idx_blocks_name ON blocks(name);

-- Add indexes for rooms
CREATE INDEX IF NOT EXISTS idx_rooms_school_id ON rooms(school_id);
CREATE INDEX IF NOT EXISTS idx_rooms_block_id ON rooms(block_id);
CREATE INDEX IF NOT EXISTS idx_rooms_name ON rooms(name);
```

### 🔐 **Step 3: Set Up Row Level Security (RLS)**

Enable RLS and create policies:

```sql
-- Enable RLS on blocks table
ALTER TABLE blocks ENABLE ROW LEVEL SECURITY;

-- Enable RLS on rooms table
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;

-- Blocks policies
CREATE POLICY "School admins can manage their school blocks" ON blocks
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = blocks.school_id 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Teachers can view their school blocks" ON blocks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = blocks.school_id 
            AND profiles.role IN ('teacher', 'admin')
        )
    );

CREATE POLICY "Students can view their school blocks" ON blocks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = blocks.school_id
        )
    );

-- Rooms policies
CREATE POLICY "School admins can manage their school rooms" ON rooms
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = rooms.school_id 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Teachers can view their school rooms" ON rooms
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = rooms.school_id 
            AND profiles.role IN ('teacher', 'admin')
        )
    );

CREATE POLICY "Students can view their school rooms" ON rooms
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = rooms.school_id
        )
    );
```

### 📊 **Step 4: Update Attendance Records Table (if needed)**

Ensure the attendance_records table has the correct structure:

```sql
-- Add school_id column if it doesn't exist
ALTER TABLE attendance_records 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- Update existing records to have school_id (if needed)
-- This query sets school_id based on the student's school
UPDATE attendance_records 
SET school_id = profiles.school_id
FROM profiles 
WHERE attendance_records.student_id = profiles.id 
AND attendance_records.school_id IS NULL;

-- Make school_id NOT NULL after updating existing records
ALTER TABLE attendance_records 
ALTER COLUMN school_id SET NOT NULL;

-- Add index for school_id if not exists
CREATE INDEX IF NOT EXISTS idx_attendance_records_school_id 
ON attendance_records(school_id);
```

### 🔄 **Step 5: Create Updated At Triggers**

Add automatic timestamp updates:

```sql
-- Create or replace function for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for blocks table
DROP TRIGGER IF EXISTS update_blocks_updated_at ON blocks;
CREATE TRIGGER update_blocks_updated_at
    BEFORE UPDATE ON blocks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add triggers for rooms table
DROP TRIGGER IF EXISTS update_rooms_updated_at ON rooms;
CREATE TRIGGER update_rooms_updated_at
    BEFORE UPDATE ON rooms
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

## ✅ **Verification**

After running all migrations, verify everything works:

```sql
-- Check if indexes were created
SELECT indexname, tablename 
FROM pg_indexes 
WHERE tablename IN ('attendance_records', 'blocks', 'rooms')
ORDER BY tablename, indexname;

-- Check if tables exist with correct structure
\d blocks
\d rooms

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('blocks', 'rooms');

-- Test a simple query
SELECT COUNT(*) FROM blocks;
SELECT COUNT(*) FROM rooms;
```

## 🎯 **What These Migrations Enable**

1. **📊 Efficient Attendance Reports** - Optimized queries for 7-day exports
2. **🏗️ Block & Room Management** - Complete CRUD operations for school structure
3. **🧹 Automatic Cleanup** - Fast deletion of old records
4. **🔐 Security** - Proper access control for school-scoped data
5. **⚡ Performance** - Indexed queries for large datasets

## 🚨 **Important Notes**

- **Backup First**: Always backup your database before running migrations
- **Test Environment**: Run these in a test environment first
- **Existing Data**: The migrations are designed to work with existing data
- **Performance**: The indexes will improve query performance significantly
- **Security**: RLS policies ensure data isolation between schools

## 🔧 **Troubleshooting**

If you encounter issues:

1. **Check Permissions**: Ensure you have admin access to run DDL commands
2. **Existing Constraints**: Some constraints might already exist
3. **Data Integrity**: Ensure all foreign key references are valid
4. **Index Names**: If indexes already exist with different names, that's okay

**The Attendance Management feature will work without these migrations, but performance and security will be significantly improved with them.**
