-- Drop the notifications table first to remove all dependencies
DROP TABLE IF EXISTS notifications CASCAD<PERSON>;
DROP TYPE IF EXISTS notification_type CASCADE;

-- Now we can safely modify the profiles constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_user_id_key CASCADE;
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_user_id_unique CASCADE;
ALTER TABLE profiles ADD CONSTRAINT profiles_user_id_unique UNIQUE (user_id);

-- Create notification type enum
CREATE TYPE notification_type AS ENUM (
  'distance_alert',
  'system_alert',
  'attendance_alert',
  'attendance',
  'absence',
  'late',
  'excused',
  'system'
);

-- Create notifications table with all required columns
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type notification_type NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  student_id UUID,
  teacher_id UUID,
  resolved_by UUID,
  student_location POINT,
  distance_meters DOUBLE PRECISION,
  room_number TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  teacher_read_at TIMESTAMPTZ,
  admin_read_at TIMESTAMPTZ,
  read BOOLEAN DEFAULT false,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Now add the foreign key constraints back
ALTER TABLE notifications 
ADD CONSTRAINT notifications_student_id_fkey 
FOREIGN KEY (student_id) REFERENCES profiles(user_id);

ALTER TABLE notifications 
ADD CONSTRAINT notifications_teacher_id_fkey 
FOREIGN KEY (teacher_id) REFERENCES profiles(user_id);

ALTER TABLE notifications 
ADD CONSTRAINT notifications_resolved_by_fkey 
FOREIGN KEY (resolved_by) REFERENCES profiles(user_id);

-- Create indexes for better query performance
CREATE INDEX idx_notifications_student_id ON notifications(student_id);
CREATE INDEX idx_notifications_teacher_id ON notifications(teacher_id);
CREATE INDEX idx_notifications_resolved_by ON notifications(resolved_by);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_notifications_timestamp ON notifications(timestamp);

-- Enable RLS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies
DROP POLICY IF EXISTS "Enable notifications access for users" ON notifications;
DROP POLICY IF EXISTS "Enable notifications read access" ON notifications;
DROP POLICY IF EXISTS "Enable notifications update access" ON notifications;
DROP POLICY IF EXISTS "Enable notifications delete access" ON notifications;

-- Create separate policies for different operations

-- Read access policy
CREATE POLICY "Enable notifications read access"
ON notifications FOR SELECT
TO authenticated
USING (
  -- Students can view their own notifications
  student_id = auth.uid()
  OR
  -- Teachers can view notifications for their students
  teacher_id = auth.uid()
  OR
  -- Admins can view all notifications
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Create separate update policies for different roles

-- Admin update policy
CREATE POLICY "Enable admin notifications update"
ON notifications FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Teacher update policy (only for read status)
CREATE POLICY "Enable teacher notifications update"
ON notifications FOR UPDATE
TO authenticated
USING (
  teacher_id = auth.uid()
)
WITH CHECK (
  teacher_id = auth.uid() AND
  (
    -- Only allow updating teacher_read_at
    (teacher_read_at IS NOT NULL AND read = true) OR
    -- Or marking as read
    (read = true)
  )
);

-- Delete access policy
CREATE POLICY "Enable notifications delete access"
ON notifications FOR DELETE
TO authenticated
USING (
  -- Teachers can delete their own notifications
  teacher_id = auth.uid()
  OR
  -- Admins can delete any notification
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Grant permissions
GRANT ALL ON notifications TO authenticated; 