-- Create tables for system settings if they don't exist
CREATE TABLE IF NOT EXISTS public.system_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  setting_name TEXT NOT NULL UNIQUE,
  setting_value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create table for school-specific setting overrides
CREATE TABLE IF NOT EXISTS public.system_school_settings_overrides (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  school_id UUID NOT NULL REFERENCES public.schools(id) ON DELETE CASCADE,
  setting_name TEXT NOT NULL,
  setting_value JSONB NOT NULL,
  override_enabled BOOLEAN DEFAULT TRUE,
  applies_to_all BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(school_id, setting_name)
);

-- Function to update or insert a system setting
CREATE OR REPLACE FUNCTION public.update_system_setting(
  p_setting_name TEXT,
  p_setting_value JSONB
) RETURNS VOID AS $$
BEGIN
  INSERT INTO public.system_settings (setting_name, setting_value)
  VALUES (p_setting_name, p_setting_value)
  ON CONFLICT (setting_name) 
  DO UPDATE SET 
    setting_value = p_setting_value,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get a system setting
CREATE OR REPLACE FUNCTION public.get_system_setting(
  p_setting_name TEXT
) RETURNS JSONB AS $$
DECLARE
  v_setting_value JSONB;
BEGIN
  SELECT setting_value INTO v_setting_value
  FROM public.system_settings
  WHERE setting_name = p_setting_name;
  
  RETURN v_setting_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update or insert a school-specific setting override
CREATE OR REPLACE FUNCTION public.update_school_setting_override(
  p_school_id UUID,
  p_setting_name TEXT,
  p_setting_value JSONB,
  p_override_enabled BOOLEAN DEFAULT TRUE,
  p_applies_to_all BOOLEAN DEFAULT FALSE
) RETURNS VOID AS $$
BEGIN
  INSERT INTO public.system_school_settings_overrides (
    school_id, 
    setting_name, 
    setting_value, 
    override_enabled,
    applies_to_all
  )
  VALUES (
    p_school_id, 
    p_setting_name, 
    p_setting_value, 
    p_override_enabled,
    p_applies_to_all
  )
  ON CONFLICT (school_id, setting_name) 
  DO UPDATE SET 
    setting_value = p_setting_value,
    override_enabled = p_override_enabled,
    applies_to_all = p_applies_to_all,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get a school-specific setting
CREATE OR REPLACE FUNCTION public.get_school_setting(
  p_school_id UUID,
  p_setting_name TEXT
) RETURNS JSONB AS $$
DECLARE
  v_setting_value JSONB;
  v_global_setting JSONB;
BEGIN
  -- First try to get school-specific override
  SELECT setting_value INTO v_setting_value
  FROM public.system_school_settings_overrides
  WHERE school_id = p_school_id 
    AND setting_name = p_setting_name
    AND override_enabled = TRUE;
  
  -- If no school-specific override, try to get global setting
  IF v_setting_value IS NULL THEN
    SELECT setting_value INTO v_global_setting
    FROM public.system_settings
    WHERE setting_name = p_setting_name;
    
    RETURN v_global_setting;
  END IF;
  
  RETURN v_setting_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to apply a setting to all schools
CREATE OR REPLACE FUNCTION public.apply_setting_to_all_schools(
  p_setting_name TEXT,
  p_setting_value JSONB
) RETURNS VOID AS $$
DECLARE
  school_rec RECORD;
BEGIN
  -- First update the global setting
  PERFORM public.update_system_setting(p_setting_name, p_setting_value);
  
  -- Then apply to all schools
  FOR school_rec IN SELECT id FROM public.schools LOOP
    PERFORM public.update_school_setting_override(
      school_rec.id,
      p_setting_name,
      p_setting_value,
      TRUE,
      TRUE
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add RLS policies
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_school_settings_overrides ENABLE ROW LEVEL SECURITY;

-- System admins can do anything with system settings
CREATE POLICY system_settings_system_admin_policy
  ON public.system_settings
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'system_admin'
    )
  );

-- System admins can do anything with school setting overrides
CREATE POLICY school_settings_system_admin_policy
  ON public.system_school_settings_overrides
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'system_admin'
    )
  );

-- School admins can read their own school's setting overrides
CREATE POLICY school_settings_school_admin_select_policy
  ON public.system_school_settings_overrides
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'school_admin'
      AND profiles.school_id = system_school_settings_overrides.school_id
    )
  );

-- Insert default theme settings if they don't exist
INSERT INTO public.system_settings (setting_name, setting_value)
VALUES (
  'global_theme_settings',
  '{
    "lightTheme": {
      "primary": "#1a365d",
      "secondary": "#0d9488",
      "accent": "#f97316",
      "background": "#ffffff",
      "foreground": "#0f172a",
      "muted": "#f1f5f9",
      "card": "#ffffff",
      "border": "#e2e8f0"
    },
    "darkTheme": {
      "primary": "#F39228",
      "secondary": "#0d9488",
      "accent": "#f97316",
      "background": "#1e2124",
      "foreground": "#c9d1d9",
      "muted": "#374151",
      "card": "#1e2124",
      "border": "#374151"
    },
    "updated_at": "2024-01-01T00:00:00Z"
  }'::jsonb
)
ON CONFLICT (setting_name) DO NOTHING;
