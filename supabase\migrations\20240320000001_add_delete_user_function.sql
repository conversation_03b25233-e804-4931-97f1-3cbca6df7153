-- Create a function to delete users that checks for admin privileges
create or replace function public.delete_user(target_user_id uuid)
returns void
language plpgsql
security definer
as $$
declare
  calling_user_role text;
  profile_id uuid;
begin
  -- Get the role of the calling user
  select role into calling_user_role
  from public.profiles
  where user_id = auth.uid();
  
  -- Check if the calling user is an admin
  if calling_user_role != 'admin' then
    raise exception 'Only administrators can delete users';
  end if;

  -- Get the profile ID
  select id into profile_id
  from public.profiles
  where user_id = target_user_id;

  -- Delete from notifications first
  delete from public.notifications
  where student_id = profile_id;

  -- Delete from attendance records if they exist
  delete from public.attendance_records
  where student_id = profile_id;

  -- Delete from any other related tables that reference profiles
  -- Add more delete statements here if there are other tables

  -- Delete from profiles
  delete from public.profiles
  where id = profile_id;

  -- Finally delete from auth.users
  delete from auth.users 
  where id = target_user_id;
end;
$$;

-- Set the proper permissions
grant execute on function public.delete_user(uuid) to authenticated; 