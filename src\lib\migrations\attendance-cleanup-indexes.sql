-- Attendance Cleanup Indexes Migration
-- This migration adds indexes to optimize attendance record cleanup operations

-- Index for efficient cleanup by timestamp
CREATE INDEX IF NOT EXISTS idx_attendance_records_timestamp 
ON attendance_records (timestamp);

-- Index for efficient cleanup by school and timestamp
CREATE INDEX IF NOT EXISTS idx_attendance_records_school_timestamp 
ON attendance_records (school_id, timestamp);

-- Index for efficient date range queries
CREATE INDEX IF NOT EXISTS idx_attendance_records_date_range 
ON attendance_records (timestamp, school_id, student_id);

-- Comment explaining the indexes
COMMENT ON INDEX idx_attendance_records_timestamp IS 'Optimizes cleanup operations by timestamp';
COMMENT ON INDEX idx_attendance_records_school_timestamp IS 'Optimizes school-specific cleanup operations';
COMMENT ON INDEX idx_attendance_records_date_range IS 'Optimizes date range queries for reports';
