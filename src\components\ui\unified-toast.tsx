import { useTheme } from "next-themes";
import { Toaster as Son<PERSON> } from "sonner";
import { safeString } from "@/lib/utils/toast";

type ToasterProps = React.ComponentPropsWithoutRef<typeof Sonner>;

/**
 * A unified toast provider component that ensures consistent toast styling and behavior
 * across the application. This component should be used in the main layout.
 */
const UnifiedToaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      richColors
      closeButton
      position="top-right"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          success:
            "group-[.toaster]:bg-green-50 group-[.toaster]:text-green-700 group-[.toaster]:border-green-200 dark:group-[.toaster]:bg-green-900/20 dark:group-[.toaster]:text-green-400 dark:group-[.toaster]:border-green-900/30",
          error:
            "group-[.toaster]:bg-red-50 group-[.toaster]:text-red-700 group-[.toaster]:border-red-200 dark:group-[.toaster]:bg-red-900/20 dark:group-[.toaster]:text-red-400 dark:group-[.toaster]:border-red-900/30",
          warning:
            "group-[.toaster]:bg-yellow-50 group-[.toaster]:text-yellow-700 group-[.toaster]:border-yellow-200 dark:group-[.toaster]:bg-yellow-900/20 dark:group-[.toaster]:text-yellow-400 dark:group-[.toaster]:border-yellow-900/30",
          info:
            "group-[.toaster]:bg-blue-50 group-[.toaster]:text-blue-700 group-[.toaster]:border-blue-200 dark:group-[.toaster]:bg-blue-900/20 dark:group-[.toaster]:text-blue-400 dark:group-[.toaster]:border-blue-900/30",
        },
        // Transform all toast data to ensure it's safe
        transform: (toast) => {
          return {
            ...toast,
            title: safeString(toast.title),
            description: safeString(toast.description),
          };
        },
      }}
      {...props}
    />
  );
};

export { UnifiedToaster };
