import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast as sonnerToast } from "sonner";
import {
  Check,
  Loader2,
  KeyRound,
  Fingerprint,
  MapPin,
  Clock,
  AlertCircle,
  Shield,
} from "lucide-react";
import CustomCameraIcon from "./icons/CustomCameraIcon";
import SimpleCameraIcon from "./icons/SimpleCameraIcon";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { useAttendanceSettings } from "@/hooks/useAttendanceSettings";
import { useTranslation } from "react-i18next";
import { motion, AnimatePresence } from "framer-motion";

export default function EnhancedQRScanner() {
  // Reuse all the state variables from the original component
  const [isScanning, setIsScanning] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [isAlreadyPresent, setIsAlreadyPresent] = useState(false);
  const [roomData, setRoomData] = useState<any>(null);
  const [existingRecord, setExistingRecord] = useState<any>(null);
  const [isLoadingRoom, setIsLoadingRoom] = useState(true);
  const [showPinVerification, setShowPinVerification] = useState(false);
  const [pin, setPin] = useState("");
  const [pinError, setPinError] = useState("");
  const [biometricSupported, setBiometricSupported] = useState(false);
  const [biometricError, setBiometricError] = useState("");
  const [verificationMethod, setVerificationMethod] = useState<
    "pin" | "biometric"
  >("pin");
  const [userLocation, setUserLocation] = useState<GeolocationPosition | null>(
    null
  );
  const [locationError, setLocationError] = useState("");
  const [isCheckingLocation, setIsCheckingLocation] = useState(false);
  const { profile } = useAuth();
  const { settings, isWithinRecordingHours } = useAttendanceSettings();
  const { t } = useTranslation();

  // Create default settings if none exist
  const defaultSettings = {
    recording_start_time: "08:00:00",
    recording_end_time: "17:00:00",
  };

  // Define effectiveSettings at component level
  const effectiveSettings = settings || defaultSettings;

  // Calculate isOutsideRecordingHours at component level for use in multiple places
  const [isOutsideRecordingHours, setIsOutsideRecordingHours] = useState(true);

  // Update isOutsideRecordingHours when settings change or time changes
  useEffect(() => {
    // Function to update time check
    const updateTimeCheck = () => {
      if (settings) {
        const isOutside = !isWithinRecordingHours();
        setIsOutsideRecordingHours(isOutside);
      } else {
        setIsOutsideRecordingHours(true);
      }
    };

    // Update immediately
    updateTimeCheck();

    // Set up interval to check every minute
    const intervalId = setInterval(updateTimeCheck, 60000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [settings, isWithinRecordingHours]);

  // Format time for display (HH:MM)
  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5);
  };

  // Get current time formatted as HH:MM
  const getCurrentTimeFormatted = () => {
    return new Date().toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Check if biometrics are supported
  useEffect(() => {
    const checkBiometricSupport = async () => {
      try {
        // Check if the browser supports WebAuthn
        if (
          window.PublicKeyCredential &&
          typeof window.PublicKeyCredential === "function" &&
          typeof window.PublicKeyCredential
            .isUserVerifyingPlatformAuthenticatorAvailable === "function"
        ) {
          // Check if platform authenticator is available (TouchID, FaceID, Windows Hello, etc.)
          const available =
            await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
          setBiometricSupported(available);

          // If biometrics are supported and the student has biometrics registered,
          // set biometric as the default verification method
          if (available && profile?.biometricRegistered) {
            setVerificationMethod("biometric");
          }
        } else {
          setBiometricSupported(false);
        }
      } catch (error) {
        console.error("Error checking biometric support:", error);
        setBiometricSupported(false);
      }
    };

    checkBiometricSupport();
  }, [profile]);

  // Fetch the student's assigned room when component mounts
  useEffect(() => {
    const fetchStudentRoom = async () => {
      if (!profile) return;

      setIsLoadingRoom(true);

      try {
        // Check if the profile has a direct room_id reference
        if (profile.room_id) {
          // Fetch the room using the room_id
          const { data: room, error } = await supabase
            .from("rooms")
            .select("id, name, block_id")
            .eq("id", profile.room_id)
            .single();

          if (error) {
            console.error("Error fetching room by ID:", error);
          } else if (room) {
            setRoomData(room);
            return;
          }
        }

        // If no room_id or room not found, try using block_name and room_number
        const blockName = profile.blockName;
        const roomNumber = profile.roomNumber;

        if (blockName && roomNumber) {
          // Try to find a room with a matching name that contains the room number
          // Since there's no direct room_number column, we'll try to match by name
          const { data: rooms, error } = await supabase
            .from("rooms")
            .select("id, name, block_id")
            .ilike("name", `%${roomNumber}%`)
            .limit(5);

          if (error) {
            console.error("Error searching for room by name:", error);
          } else if (rooms && rooms.length > 0) {
            // Find the best match - exact match first, then contains
            const exactMatch = rooms.find(
              (r) => r.name === roomNumber || r.name === `Room ${roomNumber}`
            );
            const containsMatch = rooms.find((r) =>
              r.name.includes(roomNumber)
            );

            const bestMatch = exactMatch || containsMatch || rooms[0];
            setRoomData(bestMatch);
            return;
          }
        }

        // Fallback: If no matching room found, get any room from the student's school
        let query = supabase.from("rooms").select("id, name");

        // Filter by school_id if available
        if (profile.school_id) {
          query = query.eq("school_id", profile.school_id);
        }

        query = query.limit(1);

        const { data: anyRooms, error } = await query;

        if (error) {
          console.error("Error fetching fallback room:", error);
        } else if (anyRooms && anyRooms.length > 0) {
          setRoomData(anyRooms[0]);
        } else {
          // No rooms found at all - this is okay, we'll handle it gracefully
          sonnerToast(t("qrScanner.noRoomAvailable"), {
            description: t("qrScanner.noRoomsSetup"),
          });
          // Set a placeholder room data so the student can still record attendance
          setRoomData({ id: "placeholder", name: "Default Room" });
        }
      } catch (error) {
        console.error("Error in fetchStudentRoom:", error);
      } finally {
        setIsLoadingRoom(false);
      }
    };

    fetchStudentRoom();
  }, [profile, t]);

  // Handle PIN verification
  const handlePinVerify = () => {
    if (!profile) {
      setPinError(t("qrScanner.userProfileNotFound"));
      return;
    }

    // Check if PIN is empty
    if (!pin.trim()) {
      setPinError(t("qrScanner.enterPin"));
      return;
    }

    // Verify PIN against student's profile
    if (pin !== profile.pin) {
      setPinError(t("qrScanner.incorrectPin"));
      return;
    }

    // PIN is correct, proceed with attendance check
    setShowPinVerification(false);
    setVerificationMethod("pin");
    checkAttendance();
  };

  // Handle biometric verification
  const handleBiometricVerify = async () => {
    try {
      setBiometricError("");

      if (!profile) {
        setBiometricError(t("qrScanner.userProfileNotFound"));
        return;
      }

      if (!biometricSupported) {
        setBiometricError(t("qrScanner.biometricNotSupported"));
        return;
      }

      // In a real implementation, we would use WebAuthn to verify the user's biometrics
      // For this simulation, we'll just show a success message and proceed

      // Simulate biometric verification
      sonnerToast(t("qrScanner.biometricVerification"), {
        description: t("qrScanner.verifyIdentity"),
      });

      // Simulate a delay for biometric verification
      setTimeout(() => {
        setShowPinVerification(false);
        setVerificationMethod("biometric");
        checkAttendance();
      }, 1500);
    } catch (error) {
      console.error("Biometric verification error:", error);
      setBiometricError(t("qrScanner.biometricVerificationFailed"));
    }
  };

  // Get user's current location
  const getUserLocation = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error(t("qrScanner.geolocationNotSupported")));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve(position);
        },
        (error) => {
          reject(error);
        },
        { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
      );
    });
  };

  // Check location verification settings for the room
  const getLocationVerificationSettings = async (
    roomId: string
  ): Promise<{
    globalEnabled: boolean;
    blockEnabled: boolean;
    roomEnabled: boolean;
    blockRadiusMeters: number;
    roomRadiusMeters: number;
  }> => {
    try {
      // Default settings to use if there's an error
      const defaultSettings = {
        globalEnabled: true,
        blockEnabled: true,
        roomEnabled: true,
        blockRadiusMeters: 100,
        roomRadiusMeters: 50,
      };

      // Try to get settings directly from the database
      try {
        // Get room data to find the block_id
        const { data: roomData, error: roomError } = await supabase
          .from("rooms")
          .select("block_id")
          .eq("id", roomId)
          .single();

        if (roomError) {
          console.error("Error getting room data:", roomError);
          return defaultSettings;
        }

        const blockId = roomData.block_id;

        // Get the latest settings at each level
        const { data: globalSettings, error: globalError } = await supabase
          .from("location_verification_settings")
          .select("global_enabled")
          .is("block_id", null)
          .is("room_id", null)
          .order("updated_at", { ascending: false })
          .limit(1);

        const { data: blockSettings, error: blockError } = await supabase
          .from("location_verification_settings")
          .select("block_enabled")
          .eq("block_id", blockId)
          .is("room_id", null)
          .order("updated_at", { ascending: false })
          .limit(1);

        const { data: roomSettings, error: roomSettingsError } = await supabase
          .from("location_verification_settings")
          .select("room_enabled")
          .eq("room_id", roomId)
          .order("updated_at", { ascending: false })
          .limit(1);

        if (globalError || blockError || roomSettingsError) {
          console.error("Error getting verification settings:", {
            globalError,
            blockError,
            roomSettingsError,
          });
          return defaultSettings;
        }

        // Get radius settings
        const { data: blockLocationData, error: blockLocationError } =
          await supabase
            .from("block_locations")
            .select("radius_meters")
            .eq("block_id", blockId)
            .limit(1);

        const { data: roomLocationData, error: roomLocationError } =
          await supabase
            .from("room_locations")
            .select("radius_meters")
            .eq("room_id", roomId)
            .limit(1);

        // Combine all settings
        return {
          globalEnabled:
            globalSettings && globalSettings.length > 0
              ? globalSettings[0].global_enabled
              : true,
          blockEnabled:
            blockSettings && blockSettings.length > 0
              ? blockSettings[0].block_enabled
              : true,
          roomEnabled:
            roomSettings && roomSettings.length > 0
              ? roomSettings[0].room_enabled
              : true,
          blockRadiusMeters:
            blockLocationData && blockLocationData.length > 0
              ? blockLocationData[0].radius_meters
              : 100,
          roomRadiusMeters:
            roomLocationData && roomLocationData.length > 0
              ? roomLocationData[0].radius_meters
              : 50,
        };
      } catch (dbError) {
        console.error("Exception in database call:", dbError);
      }

      // If direct database query fails, fall back to RPC
      try {
        const { data, error } = await supabase.rpc(
          "get_location_verification_settings",
          {
            room_uuid: roomId,
          }
        );

        if (error) {
          console.error(
            "Error getting location verification settings from RPC:",
            error
          );
          return defaultSettings;
        }

        if (data && data.length > 0) {
          return {
            globalEnabled: data[0].global_enabled,
            blockEnabled: data[0].block_enabled,
            roomEnabled: data[0].room_enabled,
            blockRadiusMeters: data[0].block_radius_meters,
            roomRadiusMeters: data[0].room_radius_meters,
          };
        }
      } catch (rpcError) {
        console.error("Exception in RPC call:", rpcError);
      }

      // If we get here, either there was no data or an exception occurred
      return defaultSettings;
    } catch (error) {
      console.error(
        "Unexpected exception in getLocationVerificationSettings:",
        error
      );
      // Default to requiring verification if there's an error
      return {
        globalEnabled: true,
        blockEnabled: true,
        roomEnabled: true,
        blockRadiusMeters: 100,
        roomRadiusMeters: 50,
      };
    }
  };

  // Verify if user is within the allowed radius of the classroom
  const verifyLocation = async (): Promise<boolean> => {
    try {
      setIsCheckingLocation(true);
      setLocationError("");

      if (!roomData?.id) {
        throw new Error("Room data not available");
      }

      // Get location verification settings
      const settings = await getLocationVerificationSettings(roomData.id);

      // If location verification is disabled globally, skip verification
      if (!settings.globalEnabled) {
        sonnerToast.info(t("qrScanner.locationVerification"), {
          description: t("qrScanner.locationVerificationDisabled"),
        });
        return true;
      }

      // If block-level verification is disabled, skip verification
      if (!settings.blockEnabled) {
        sonnerToast.info(t("qrScanner.locationVerification"), {
          description: t("qrScanner.blockVerificationDisabled"),
        });
        return true;
      }

      // If room-level verification is disabled, skip room-specific verification
      // but still do block-level verification
      const verifyRoomLevel = settings.roomEnabled;

      // Get user's current location
      const position = await getUserLocation();
      setUserLocation(position);

      // Show verification toast
      sonnerToast.info(t("qrScanner.locationVerification"), {
        description: verifyRoomLevel
          ? t("qrScanner.verifyingRoomLevel")
          : t("qrScanner.verifyingBlockLevel"),
      });

      // For demo purposes, we'll just return true
      // In a real implementation, we would check if the user is within the allowed radius
      return true;
    } catch (error) {
      console.error("Location verification error:", error);
      setLocationError(
        error instanceof Error
          ? error.message
          : t("qrScanner.locationAccessFailed")
      );
      return false;
    } finally {
      setIsCheckingLocation(false);
    }
  };

  // Check if student already has attendance for today
  const checkAttendance = async () => {
    if (!profile || !roomData) {
      sonnerToast.error(t("common.error"), {
        description: !profile
          ? t("qrScanner.userProfileNotFound")
          : t("qrScanner.noRoomData"),
      });
      return;
    }

    // Double-check time restrictions before proceeding
    if (settings && !isWithinRecordingHours()) {
      const currentTime = getCurrentTimeFormatted();

      sonnerToast.error(t("qrScanner.attendanceRecordingNotAvailable"), {
        description: t("qrScanner.recordingTimeRestriction", {
          startTime: formatTime(settings.recording_start_time),
          endTime: formatTime(settings.recording_end_time),
          currentTime: currentTime,
        }),
      });
      return;
    }

    setIsSubmitting(true);

    // Verify location before checking attendance
    const locationVerified = await verifyLocation();
    if (!locationVerified) {
      setIsSubmitting(false);
      sonnerToast.error(t("qrScanner.locationVerificationFailed"), {
        description: locationError || t("qrScanner.mustBeInRoom"),
      });
      return;
    }

    try {
      // Get the current authenticated user ID
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error(t("qrScanner.userNotAuthenticated"));
      }

      // Check if there's already an attendance record for today
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      console.log("Checking for existing attendance record today...");

      // Skip checking for existing records if using a placeholder room
      let existingAttendance = null;
      let attendanceError = null;

      if (roomData.id !== "placeholder") {
        const response = await supabase
          .from("attendance_records")
          .select("id, timestamp, status")
          .eq("student_id", user.id)
          .eq("room_id", roomData.id)
          .gte("timestamp", today.toISOString())
          .order("timestamp", { ascending: false })
          .limit(1);

        existingAttendance = response.data;
        attendanceError = response.error;
      }

      if (attendanceError) {
        console.error("Error checking existing attendance:", attendanceError);
      }

      // If student is already present, show message and return
      if (
        existingAttendance &&
        existingAttendance.length > 0 &&
        existingAttendance[0].status === "present"
      ) {
        console.log(
          "Student already has present attendance record:",
          existingAttendance[0]
        );

        // Save the existing record for display
        setExistingRecord(existingAttendance[0]);

        // Set the already present flag
        setIsAlreadyPresent(true);
        console.log("Setting isAlreadyPresent to TRUE");

        // Show toast and success UI
        // Format room name to include "Room" if it doesn't already
        const formattedRoomName =
          roomData.name && !roomData.name.toLowerCase().startsWith("room")
            ? `${t("attendance.room")} ${roomData.name}`
            : roomData.name;

        sonnerToast.info(t("qrScanner.alreadyPresent"), {
          description: t("qrScanner.alreadyRecordedToday", {
            room: formattedRoomName,
            time: new Date(
              existingAttendance[0].timestamp
            ).toLocaleTimeString(),
          }),
        });

        setSuccess(true);
        setIsSubmitting(false);
      } else {
        // Student is not present yet, record attendance
        recordAttendance(user.id);
      }
    } catch (error) {
      console.error("Error checking attendance:", error);
      sonnerToast.error(t("common.error"), {
        description: t("qrScanner.failedToCheckAttendance"),
      });
      setIsSubmitting(false);
    }
  };

  // Record attendance for student
  const recordAttendance = async (userId: string) => {
    try {
      // Disable the attendance notification trigger
      try {
        await supabase.rpc("disable_attendance_notification_trigger");
        console.log("Trigger disabled successfully");
      } catch (triggerError) {
        console.error("Error disabling trigger:", triggerError);
      }

      // Create a GeoJSON Point for location
      let geoJsonLocation = null;

      if (userLocation) {
        // Use the actual user location if available
        geoJsonLocation = {
          type: "Point",
          coordinates: [
            userLocation.coords.longitude,
            userLocation.coords.latitude,
          ],
        };
        console.log("Using actual user location:", geoJsonLocation);
      } else {
        // Fallback to default coordinates if location not available
        geoJsonLocation = {
          type: "Point",
          coordinates: [0, 0],
        };
        console.log("Using default location coordinates");
      }

      const now = new Date().toISOString();

      // Get device info
      const deviceInfo = {
        device: "web",
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        timestamp: now,
        locationVerified: !!userLocation,
        locationAccuracy: userLocation ? userLocation.coords.accuracy : null,
        locationTimestamp: userLocation ? userLocation.timestamp : null,
      };

      // Insert attendance record
      let data, error;

      // If using a placeholder room, create a special record
      if (roomData.id === "placeholder") {
        // Get the student's school_id
        const { data: profileData } = await supabase
          .from("profiles")
          .select("school_id")
          .eq("id", userId)
          .single();

        // Insert attendance record with school_id but null room_id
        const response = await supabase
          .from("attendance_records")
          .insert({
            student_id: userId,
            room_id: null, // No room associated
            timestamp: now,
            device_info: JSON.stringify(deviceInfo),
            verification_method: verificationMethod,
            status: "present",
            location: geoJsonLocation,
            created_at: now,
            school_id: profileData?.school_id || null, // Include school_id for proper isolation
          })
          .select()
          .single();

        data = response.data;
        error = response.error;
      } else {
        // Normal case with a real room
        const response = await supabase
          .from("attendance_records")
          .insert({
            student_id: userId,
            room_id: roomData.id,
            timestamp: now,
            device_info: JSON.stringify(deviceInfo),
            verification_method: verificationMethod,
            status: "present",
            location: geoJsonLocation,
            created_at: now,
          })
          .select()
          .single();

        data = response.data;
        error = response.error;
      }

      // Re-enable the trigger
      try {
        await supabase.rpc("enable_attendance_notification_trigger");
        console.log("Trigger re-enabled successfully");
      } catch (triggerError) {
        console.error("Error re-enabling trigger:", triggerError);
      }

      if (error) {
        console.error("Error recording attendance:", error);
        throw new Error(`Failed to record attendance: ${error.message}`);
      }

      // Successfully recorded attendance

      // Show success message
      sonnerToast.success(t("common.success"), {
        description: t("qrScanner.attendanceRecordedSuccessfully"),
      });

      // Set success state but NOT already present
      setIsAlreadyPresent(false);
      setSuccess(true);
      setIsSubmitting(false);
    } catch (error) {
      console.error("Error recording attendance:", error);
      sonnerToast.error(t("common.error"), {
        description: t("qrScanner.failedToRecordAttendance"),
      });
      setIsSubmitting(false);
    }
  };

  // Simulate QR scanning with enhanced animation
  const handleStartScan = () => {
    // Check if room data is loaded
    if (isLoadingRoom) {
      sonnerToast(t("qrScanner.pleaseWait"), {
        description: t("qrScanner.loadingRoomData"),
      });
      return;
    }

    // Check if room data exists
    if (!roomData) {
      sonnerToast.error(t("qrScanner.noRoomAvailable"), {
        description: t("qrScanner.noRoomsSetup"),
      });
      return;
    }

    // Check if current time is within allowed recording hours
    if (isOutsideRecordingHours) {
      // Use Sonner toast directly
      sonnerToast.error(t("qrScanner.attendanceRecordingNotAvailable"), {
        description: t("qrScanner.recordingTimeRestriction", {
          startTime: formatTime(effectiveSettings.recording_start_time),
          endTime: formatTime(effectiveSettings.recording_end_time),
          currentTime: getCurrentTimeFormatted(),
        }),
        duration: 5000,
      });
      return;
    }

    // All checks passed, proceed with scanning
    setIsScanning(true);
    setSuccess(false);
    setIsAlreadyPresent(false);
    setPinError("");
    setPin("");

    // Simulate a delay for "scanning"
    setTimeout(() => {
      setIsScanning(false);
      // Show PIN verification instead of immediately checking attendance
      setShowPinVerification(true);
    }, 2000);
  };

  // Render the enhanced QR scanner UI
  const renderContent = () => {
    if (success) {
      return (
        <motion.div
          className="flex flex-col items-center justify-center p-8 space-y-4"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            className="w-24 h-24 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 200, damping: 15 }}
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <Check size={48} className="text-green-600 dark:text-green-400" />
            </motion.div>
          </motion.div>
          <motion.h3
            className="text-xl font-medium text-center"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            {isAlreadyPresent
              ? t("qrScanner.alreadyPresent")
              : t("qrScanner.attendanceRecorded")}
          </motion.h3>
          <motion.p
            className="text-center text-muted-foreground"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {isAlreadyPresent
              ? t("qrScanner.alreadyMarkedPresent", {
                  time: existingRecord
                    ? new Date(existingRecord.timestamp).toLocaleTimeString()
                    : t("qrScanner.earlierToday"),
                })
              : t("qrScanner.attendanceSuccessfullyRecorded")}
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Button
              onClick={() => {
                setSuccess(false);
              }}
              className="mt-4 btn-primary"
            >
              {t("qrScanner.scanAnotherCode")}
            </Button>
          </motion.div>
        </motion.div>
      );
    }

    if (isLoadingRoom) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="skeleton w-64 h-64 rounded-lg"></div>
          <div className="skeleton w-48 h-6 rounded-md"></div>
          <div className="skeleton w-64 h-10 rounded-md"></div>
        </div>
      );
    }

    if (showPinVerification) {
      return (
        <motion.div
          className="flex flex-col items-center justify-center p-8 space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            className="w-20 h-20 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-2"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <KeyRound size={36} className="text-blue-600 dark:text-blue-400" />
          </motion.div>
          <motion.h3
            className="text-xl font-medium text-center"
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1, duration: 0.3 }}
          >
            {t("qrScanner.verifyIdentity")}
          </motion.h3>
          <motion.p
            className="text-center text-muted-foreground"
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.3 }}
          >
            {t("qrScanner.verifyToRecord")}
          </motion.p>

          {/* PIN Verification Section */}
          <motion.div
            className="w-full max-w-xs"
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.3 }}
          >
            <Input
              type="password"
              placeholder={t("qrScanner.enterYourPin")}
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              className="text-center text-lg tracking-widest focus-ring"
              maxLength={6}
              autoFocus
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handlePinVerify();
                }
              }}
            />
            <AnimatePresence>
              {pinError && (
                <motion.p
                  className="text-red-500 text-sm mt-2 text-center error-shake"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  {pinError}
                </motion.p>
              )}
            </AnimatePresence>
          </motion.div>

          <motion.div
            className="flex gap-2 mt-4 w-full max-w-xs"
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.3 }}
          >
            <Button
              variant="outline"
              onClick={() => {
                setShowPinVerification(false);
                setPin("");
                setPinError("");
              }}
              className="flex-1 btn-scale"
            >
              {t("common.cancel")}
            </Button>
            <Button
              onClick={handlePinVerify}
              className="flex-1 btn-primary btn-scale"
            >
              <KeyRound className="mr-2 h-4 w-4" />
              {t("qrScanner.verifyWithPin")}
            </Button>
          </motion.div>

          {/* Biometric Option */}
          {biometricSupported && profile?.biometricRegistered && (
            <motion.div
              className="mt-4 w-full max-w-xs"
              initial={{ y: 10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.3 }}
            >
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-muted-foreground/20" />
                </div>
                <div className="relative flex justify-center text-xs">
                  <span className="bg-background px-2 text-muted-foreground">
                    OR
                  </span>
                </div>
              </div>

              <Button
                variant="outline"
                className="w-full mt-4 hover-glow btn-scale"
                onClick={handleBiometricVerify}
              >
                <Fingerprint className="mr-2 h-4 w-4" />
                {t("qrScanner.useBiometricVerification")}
              </Button>

              <AnimatePresence>
                {biometricError && (
                  <motion.p
                    className="text-red-500 text-sm mt-2 text-center error-shake"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                  >
                    {biometricError}
                  </motion.p>
                )}
              </AnimatePresence>
            </motion.div>
          )}
        </motion.div>
      );
    }

    if (isScanning) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="qr-scanner-frame">
            <div className="qr-scanner-line"></div>
            <div className="qr-scanner-corner qr-scanner-corner-top-left"></div>
            <div className="qr-scanner-corner qr-scanner-corner-top-right"></div>
            <div className="qr-scanner-corner qr-scanner-corner-bottom-left"></div>
            <div className="qr-scanner-corner qr-scanner-corner-bottom-right"></div>
          </div>
          <p className="text-center text-muted-foreground mt-4">
            {t("qrScanner.scanningQrCode")}
          </p>
        </div>
      );
    }

    if (isSubmitting || isCheckingLocation) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <motion.div
            animate={{
              rotate: 360,
              transition: { duration: 1, repeat: Infinity, ease: "linear" },
            }}
            className="w-16 h-16 rounded-full border-4 border-primary border-t-transparent"
          />
          <p className="text-center">
            {isCheckingLocation
              ? t("qrScanner.verifyingLocation")
              : t("qrScanner.processingAttendance")}
          </p>
          {isCheckingLocation && (
            <div className="flex items-center justify-center text-sm text-muted-foreground">
              <MapPin className="mr-1 h-4 w-4" />
              {t("qrScanner.allowLocationAccess")}
            </div>
          )}
          <AnimatePresence>
            {locationError && (
              <motion.p
                className="text-red-500 text-sm mt-2 text-center error-shake"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                {locationError}
              </motion.p>
            )}
          </AnimatePresence>
        </div>
      );
    }

    if (!roomData) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md text-amber-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-amber-400">
            <p>{t("qrScanner.noRoomInfo")}</p>
          </div>
          <Button
            onClick={() => {
              // Update localStorage for tab persistence
              localStorage.setItem("studentActiveTab", "profile");
              // Navigate to profile tab
              window.location.href = "/student?tab=profile";
            }}
            className="mt-4 btn-primary"
          >
            {t("profile.updateProfile")}
          </Button>
        </div>
      );
    }

    // Check if student has set up a PIN
    if (profile && !profile.pin) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md text-amber-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-amber-400">
            <p className="text-center">{t("qrScanner.setupPinRequired")}</p>
          </div>
          <Button
            onClick={() => {
              // Use the global function to switch tabs
              // @ts-ignore
              if (window.setStudentActiveTab) {
                // @ts-ignore
                window.setStudentActiveTab("profile");
                console.log("Switching to profile tab using global function");
              } else {
                console.error("setStudentActiveTab not found on window");
                // Fallback to URL parameter
                const url = new URL(window.location.href);
                url.searchParams.set("tab", "profile");
                window.location.href = url.toString();
              }
            }}
            className="mt-4 btn-primary"
          >
            {t("profile.updateProfile")}
          </Button>
        </div>
      );
    }

    // Show biometric registration suggestion if supported but not registered
    if (biometricSupported && profile && !profile.biometricRegistered) {
      return (
        <motion.div
          className="flex flex-col items-center justify-center p-8 space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            animate={{
              scale: [1, 1.05, 1],
              transition: { duration: 2, repeat: Infinity },
            }}
            className="relative"
          >
            <SimpleCameraIcon size={72} />
            <motion.div
              className="absolute inset-0 rounded-full"
              animate={{
                boxShadow: [
                  "0 0 0 0 rgba(var(--primary), 0)",
                  "0 0 0 10px rgba(var(--primary), 0.2)",
                  "0 0 0 0 rgba(var(--primary), 0)",
                ],
                transition: { duration: 2, repeat: Infinity },
              }}
            />
          </motion.div>

          <p className="text-center">{t("qrScanner.scanQrDescription")}</p>

          {/* Show room info with proper formatting */}
          {roomData && (
            <div className="flex items-center justify-center text-sm text-muted-foreground">
              <MapPin className="mr-1 h-4 w-4" />
              {roomData.name && !roomData.name.toLowerCase().startsWith("room")
                ? `${t("attendance.room")} ${roomData.name}`
                : roomData.name}
            </div>
          )}

          <Button
            onClick={handleStartScan}
            className="w-full btn-primary animate-pulse-primary"
          >
            {t("qrScanner.startScan")}
          </Button>

          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md dark:bg-blue-900/20 dark:border-blue-800">
            <p className="text-center text-blue-800 dark:text-blue-400">
              <Fingerprint className="inline-block mr-1 h-4 w-4" />
              {t("Biometric not registered.")}
              <span
                className="text-blue-600 dark:text-blue-300 underline ml-1 cursor-pointer"
                onClick={() => {
                  // Use the global function to switch tabs
                  // @ts-ignore
                  if (window.setStudentActiveTab) {
                    // @ts-ignore
                    window.setStudentActiveTab("profile");
                    // Switching to profile tab using global function
                  } else {
                    console.error("setStudentActiveTab not found on window");
                    // Fallback to URL parameter
                    const url = new URL(window.location.href);
                    url.searchParams.set("tab", "profile");
                    window.location.href = url.toString();
                  }
                }}
              >
                {t("Register your biometrics")}
              </span>{" "}
              {t("for faster attendance verification.")}
            </p>
          </div>
        </motion.div>
      );
    }

    // Default view - ready to scan
    return (
      <motion.div
        className="flex flex-col items-center justify-center p-8 space-y-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          animate={
            isOutsideRecordingHours
              ? {}
              : {
                  scale: [1, 1.05, 1],
                  transition: { duration: 2, repeat: Infinity },
                }
          }
          className="relative"
        >
          <SimpleCameraIcon
            size={72}
            isOutsideRecordingHours={isOutsideRecordingHours}
          />
          <motion.div
            className="absolute inset-0 rounded-full"
            animate={
              isOutsideRecordingHours
                ? {}
                : {
                    boxShadow: [
                      "0 0 0 0 rgba(var(--primary), 0)",
                      "0 0 0 10px rgba(var(--primary), 0.2)",
                      "0 0 0 0 rgba(var(--primary), 0)",
                    ],
                    transition: { duration: 2, repeat: Infinity },
                  }
            }
          />
        </motion.div>

        <p className="text-center">{t("qrScanner.scanQrDescription")}</p>

        {/* Show room info with proper formatting */}
        {roomData && (
          <div className="flex items-center justify-center text-sm text-muted-foreground">
            <MapPin className="mr-1 h-4 w-4" />
            {roomData.name && !roomData.name.toLowerCase().startsWith("room")
              ? `${t("attendance.room")} ${roomData.name}`
              : roomData.name}
          </div>
        )}

        <Button
          onClick={handleStartScan}
          className={`w-full btn-primary ${
            isOutsideRecordingHours ? "" : "animate-pulse-primary"
          }`}
          disabled={isOutsideRecordingHours}
        >
          {t("qrScanner.startScan")}
        </Button>

        {isOutsideRecordingHours && (
          <div className="flex items-center justify-center text-sm text-amber-500 dark:text-amber-400">
            <Clock className="mr-1 h-4 w-4" />
            {t("qrScanner.outsideRecordingHours", {
              startTime: formatTime(effectiveSettings.recording_start_time),
              endTime: formatTime(effectiveSettings.recording_end_time),
            })}
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <Card className="w-full max-w-md mx-auto enhanced-card border-2 border-muted hover:border-primary/20 transition-all duration-300">
      <CardHeader>
        <CardTitle>{t("qrScanner.scanQrCode")}</CardTitle>
        <CardDescription>{t("qrScanner.scanQrDescription")}</CardDescription>
      </CardHeader>
      <CardContent>{renderContent()}</CardContent>
    </Card>
  );
}
