# ⚡ Performance Optimization Guide

## 📊 **Current Performance Analysis**

Your app has several performance optimization opportunities that should be addressed before production.

## 🎯 **Performance Goals**

- **First Contentful Paint (FCP)**: < 1.5s
- **Largest Contentful Paint (LCP)**: < 2.5s
- **Cumulative Layout Shift (CLS)**: < 0.1
- **First Input Delay (FID)**: < 100ms
- **Bundle Size**: < 1MB initial load

## 🔧 **Frontend Optimizations**

### **1. Bundle Analysis & Code Splitting**

**Current Issue**: Large bundle size due to importing entire libraries

**Solution**: Implement code splitting and tree shaking

```bash
# Analyze current bundle
npm install --save-dev vite-bundle-analyzer
```

```typescript
// vite.config.ts - Add bundle analyzer
import { defineConfig } from 'vite'
import { analyzer } from 'vite-bundle-analyzer'

export default defineConfig({
  plugins: [
    // ... existing plugins
    analyzer({
      analyzerMode: 'server',
      openAnalyzer: false,
    })
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Split vendor libraries
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          'chart-vendor': ['recharts'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers'],
          'date-vendor': ['date-fns'],
          'qr-vendor': ['html5-qrcode', 'qrcode.react'],
        }
      }
    },
    // Enable minification
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
      },
    },
  },
})
```

### **2. Lazy Loading & Route-Based Code Splitting**

```typescript
// src/App.tsx - Implement lazy loading
import { lazy, Suspense } from 'react'
import { Routes, Route } from 'react-router-dom'

// Lazy load pages
const Admin = lazy(() => import('./pages/Admin'))
const Teacher = lazy(() => import('./pages/Teacher'))
const Student = lazy(() => import('./pages/Student'))
const SystemAdmin = lazy(() => import('./pages/SystemAdmin'))

// Loading component
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
  </div>
)

function App() {
  return (
    <Suspense fallback={<PageLoader />}>
      <Routes>
        <Route path="/admin" element={<Admin />} />
        <Route path="/teacher" element={<Teacher />} />
        <Route path="/student" element={<Student />} />
        <Route path="/system-admin" element={<SystemAdmin />} />
      </Routes>
    </Suspense>
  )
}
```

### **3. Image Optimization**

```typescript
// src/components/ui/OptimizedImage.tsx
import { useState } from 'react'

interface OptimizedImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
}

export const OptimizedImage = ({ src, alt, className, width, height }: OptimizedImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)

  return (
    <div className={`relative ${className}`}>
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        loading="lazy"
        decoding="async"
        onLoad={() => setIsLoaded(true)}
        onError={() => setHasError(true)}
        className={`transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        } ${className}`}
      />
    </div>
  )
}
```

### **4. Component Optimization**

```typescript
// src/hooks/useVirtualization.ts - For large lists
import { useMemo } from 'react'

export const useVirtualization = (items: any[], containerHeight: number, itemHeight: number) => {
  return useMemo(() => {
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const buffer = 5 // Render extra items for smooth scrolling
    
    return {
      visibleCount: visibleCount + buffer * 2,
      startIndex: 0, // This would be calculated based on scroll position
    }
  }, [items.length, containerHeight, itemHeight])
}
```

```typescript
// src/components/shared/VirtualizedList.tsx
import { memo } from 'react'

interface VirtualizedListProps {
  items: any[]
  renderItem: (item: any, index: number) => React.ReactNode
  itemHeight: number
  containerHeight: number
}

export const VirtualizedList = memo(({ 
  items, 
  renderItem, 
  itemHeight, 
  containerHeight 
}: VirtualizedListProps) => {
  const { visibleCount, startIndex } = useVirtualization(items, containerHeight, itemHeight)
  
  const visibleItems = items.slice(startIndex, startIndex + visibleCount)
  
  return (
    <div style={{ height: containerHeight, overflow: 'auto' }}>
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => (
          <div
            key={startIndex + index}
            style={{
              position: 'absolute',
              top: (startIndex + index) * itemHeight,
              height: itemHeight,
              width: '100%',
            }}
          >
            {renderItem(item, startIndex + index)}
          </div>
        ))}
      </div>
    </div>
  )
})
```

### **5. Caching Strategies**

```typescript
// src/lib/cache.ts - Enhanced caching
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  set(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })
  }
  
  get(key: string) {
    const entry = this.cache.get(key)
    if (!entry) return null
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return entry.data
  }
  
  clear() {
    this.cache.clear()
  }
  
  // Preload critical data
  async preload(keys: string[], fetchFn: (key: string) => Promise<any>) {
    const promises = keys.map(async (key) => {
      if (!this.get(key)) {
        const data = await fetchFn(key)
        this.set(key, data)
      }
    })
    
    await Promise.all(promises)
  }
}

export const cacheManager = new CacheManager()
```

### **6. Database Query Optimization**

```typescript
// src/lib/optimized-queries.ts
import { supabase } from './supabase'

// Batch queries instead of multiple individual queries
export const getBatchedData = async (schoolId: string) => {
  const [
    { data: profiles },
    { data: attendanceRecords },
    { data: blocks },
  ] = await Promise.all([
    supabase
      .from('profiles')
      .select('id, full_name, role')
      .eq('school_id', schoolId)
      .limit(100),
    
    supabase
      .from('attendance_records')
      .select('id, student_id, created_at, location_id')
      .eq('school_id', schoolId)
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(500),
    
    supabase
      .from('blocks')
      .select('id, name')
      .eq('school_id', schoolId),
  ])
  
  return { profiles, attendanceRecords, blocks }
}

// Use indexes for better performance
export const getOptimizedAttendance = async (studentId: string, startDate: string, endDate: string) => {
  return supabase
    .from('attendance_records')
    .select(`
      id,
      created_at,
      location_id,
      block_locations!inner(name, block_id),
      blocks!inner(name)
    `)
    .eq('student_id', studentId)
    .gte('created_at', startDate)
    .lte('created_at', endDate)
    .order('created_at', { ascending: false })
}
```

## 🗄️ **Database Optimizations**

### **1. Add Performance Indexes**

```sql
-- Add these indexes to your Supabase database
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_records_composite 
ON attendance_records(school_id, student_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_school_role 
ON profiles(school_id, role);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread 
ON notifications(user_id, read, created_at DESC) 
WHERE read = false;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_excuses_student_status 
ON excuses(student_id, status, created_at DESC);

-- Partial indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_attendance_recent 
ON attendance_records(student_id, created_at DESC) 
WHERE created_at > NOW() - INTERVAL '30 days';
```

### **2. Database Connection Optimization**

```typescript
// src/lib/supabase-optimized.ts
import { createClient } from '@supabase/supabase-js'

export const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY,
  {
    db: {
      schema: 'public',
    },
    auth: {
      persistSession: true,
      autoRefreshToken: true,
    },
    global: {
      headers: {
        'X-Client-Info': 'attendance-app',
      },
    },
    // Optimize for performance
    realtime: {
      timeout: 30000,
      heartbeatIntervalMs: 30000,
    },
  }
)

// Connection pooling for edge functions
export const createOptimizedClient = () => {
  return createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
      db: {
        schema: 'public',
      },
    }
  )
}
```

## 📱 **Mobile Performance**

### **1. Touch Optimization**

```css
/* src/styles/mobile-optimizations.css */
/* Improve touch responsiveness */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

/* Reduce paint operations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Optimize scrolling */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
```

### **2. Reduce JavaScript Execution**

```typescript
// src/hooks/useDebounce.ts - Reduce API calls
import { useState, useEffect } from 'react'

export const useDebounce = (value: any, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}
```

## 🔄 **Service Worker Optimization**

```javascript
// public/service-worker.js - Enhanced caching
const CACHE_NAME = 'attendance-app-v2'
const STATIC_CACHE = 'static-v2'
const DYNAMIC_CACHE = 'dynamic-v2'

const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/offline',
  // Add critical CSS and JS files
]

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then(cache => cache.addAll(STATIC_ASSETS)),
      caches.open(DYNAMIC_CACHE).then(cache => cache.add('/offline'))
    ])
  )
  self.skipWaiting()
})

// Fetch event - cache strategy
self.addEventListener('fetch', (event) => {
  const { request } = event
  
  // Skip non-GET requests
  if (request.method !== 'GET') return
  
  // API requests - network first, cache fallback
  if (request.url.includes('/rest/v1/')) {
    event.respondWith(
      fetch(request)
        .then(response => {
          if (response.ok) {
            const responseClone = response.clone()
            caches.open(DYNAMIC_CACHE).then(cache => {
              cache.put(request, responseClone)
            })
          }
          return response
        })
        .catch(() => caches.match(request))
    )
    return
  }
  
  // Static assets - cache first, network fallback
  event.respondWith(
    caches.match(request)
      .then(response => response || fetch(request))
      .catch(() => caches.match('/offline'))
  )
})
```

## 📊 **Performance Monitoring**

```typescript
// src/lib/performance-monitor.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

interface PerformanceMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  timestamp: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  
  init() {
    getCLS(this.handleMetric.bind(this))
    getFID(this.handleMetric.bind(this))
    getFCP(this.handleMetric.bind(this))
    getLCP(this.handleMetric.bind(this))
    getTTFB(this.handleMetric.bind(this))
  }
  
  private handleMetric(metric: any) {
    this.metrics.push({
      name: metric.name,
      value: metric.value,
      rating: metric.rating,
      timestamp: Date.now(),
    })
    
    // Send to analytics service
    this.sendToAnalytics(metric)
  }
  
  private sendToAnalytics(metric: any) {
    // Send to your analytics service
    if (import.meta.env.PROD) {
      // Example: send to Google Analytics, Mixpanel, etc.
      console.log('Performance metric:', metric)
    }
  }
  
  getMetrics() {
    return this.metrics
  }
}

export const performanceMonitor = new PerformanceMonitor()
```

## 🎯 **Performance Testing**

### **Lighthouse CI Setup**

```json
// .lighthouserc.json
{
  "ci": {
    "collect": {
      "url": ["http://localhost:5173"],
      "numberOfRuns": 3
    },
    "assert": {
      "assertions": {
        "categories:performance": ["error", {"minScore": 0.9}],
        "categories:accessibility": ["error", {"minScore": 0.9}],
        "categories:best-practices": ["error", {"minScore": 0.9}],
        "categories:seo": ["error", {"minScore": 0.9}]
      }
    }
  }
}
```

### **Performance Budget**

```json
// package.json - Add performance scripts
{
  "scripts": {
    "perf:analyze": "npm run build && npx lighthouse-ci autorun",
    "perf:bundle": "npm run build && npx vite-bundle-analyzer",
    "perf:audit": "npm run build && npx lighthouse http://localhost:5173 --view"
  }
}
```

## 📈 **Expected Results**

After implementing these optimizations:

- **Bundle Size**: Reduced by 40-60%
- **First Load**: < 2 seconds
- **Subsequent Loads**: < 500ms
- **Mobile Performance**: Significantly improved
- **Database Queries**: 50-70% faster
- **User Experience**: Much smoother interactions

## 🔍 **Monitoring & Maintenance**

1. **Weekly**: Check performance metrics
2. **Monthly**: Analyze bundle size growth
3. **Quarterly**: Review and optimize database queries
4. **Annually**: Major performance audit

**Remember**: Performance optimization is an ongoing process, not a one-time task!
