# 🚀 Production Deployment Guide

## 📋 **Pre-Deployment Checklist**

Before deploying to production, ensure you've completed the critical security fixes from `SECURITY_REMEDIATION_GUIDE.md`.

### ✅ **Prerequisites**
- [ ] API keys moved to Supabase Edge Functions
- [ ] Production environment variables configured
- [ ] Database RLS policies verified
- [ ] Security headers implemented
- [ ] SSL certificates ready
- [ ] Domain name purchased and configured

## 🏗️ **Recommended Hosting Platforms**

### **Option 1: Vercel (Recommended)** ⭐

**Pros**: 
- Excellent React/Vite support
- Automatic HTTPS
- Global CDN
- Easy environment variable management
- Built-in analytics

**Setup Steps**:

1. **Install Vercel CLI**:
```bash
npm install -g vercel
```

2. **Configure vercel.json**:
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Strict-Transport-Security",
          "value": "max-age=31536000; includeSubDomains"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/((?!api/).*)",
      "destination": "/index.html"
    }
  ]
}
```

3. **Deploy**:
```bash
vercel --prod
```

### **Option 2: Netlify** 🌐

**Setup Steps**:

1. **Configure netlify.toml**:
```toml
[build]
  command = "npm run build"
  publish = "dist"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

2. **Deploy via Git** or **Netlify CLI**:
```bash
npm install -g netlify-cli
netlify deploy --prod --dir=dist
```

### **Option 3: AWS Amplify** ☁️

**Setup Steps**:

1. **Configure amplify.yml**:
```yaml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: dist
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
```

## 🔧 **Environment Configuration**

### **Production Environment Variables**

Create `.env.production`:

```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key

# App Configuration
VITE_APP_NAME="Campus Guardian"
VITE_APP_SHORT_NAME="CG"
VITE_COMPANY_NAME="Your Institution"
VITE_CONTACT_EMAIL="<EMAIL>"
VITE_SUPPORT_EMAIL="<EMAIL>"

# QR Security Configuration
NEXT_PUBLIC_QR_EXPIRY_SECONDS=300
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=30
NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=1

# Remove these - now handled by edge functions
# VITE_SENDGRID_API_KEY=
# VITE_TWILIO_AUTH_TOKEN=
# VITE_TWILIO_ACCOUNT_SID=
```

### **Hosting Platform Environment Variables**

Set these in your hosting platform's dashboard:

**Vercel**: Project Settings → Environment Variables
**Netlify**: Site Settings → Environment Variables  
**AWS Amplify**: App Settings → Environment Variables

## 🗄️ **Database Setup**

### **Supabase Production Configuration**

1. **Upgrade to Paid Plan**:
   - Go to Supabase Dashboard
   - Upgrade to Pro plan for production features
   - Configure custom domain if needed

2. **Database Optimization**:
```sql
-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_attendance_records_student_id ON attendance_records(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_school_id ON attendance_records(school_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_created_at ON attendance_records(created_at);
CREATE INDEX IF NOT EXISTS idx_profiles_school_id ON profiles(school_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
```

3. **Configure Backups**:
   - Enable automatic backups in Supabase dashboard
   - Set backup retention period
   - Test backup restoration process

### **Deploy Edge Functions**

```bash
# Deploy secure notification function
supabase functions deploy send-secure-notification --project-ref your_project_ref

# Deploy QR security function
supabase functions deploy qr-security --project-ref your_project_ref

# Set production secrets
supabase secrets set SENDGRID_API_KEY=your_production_key --project-ref your_project_ref
supabase secrets set TWILIO_AUTH_TOKEN=your_production_token --project-ref your_project_ref
supabase secrets set TWILIO_ACCOUNT_SID=your_production_sid --project-ref your_project_ref
```

## 🔒 **SSL/HTTPS Configuration**

### **Custom Domain Setup**

1. **Purchase Domain**: Buy your domain from a registrar
2. **Configure DNS**: Point your domain to your hosting provider
3. **SSL Certificate**: Most hosting providers auto-generate SSL certificates

### **HTTPS Enforcement**

Ensure all HTTP traffic redirects to HTTPS:

**Vercel**: Automatic
**Netlify**: Automatic  
**AWS Amplify**: Configure in settings

## 📊 **Monitoring Setup**

### **Error Tracking with Sentry**

1. **Install Sentry**:
```bash
npm install @sentry/react @sentry/tracing
```

2. **Configure Sentry**:
```typescript
// src/lib/sentry.ts
import * as Sentry from "@sentry/react";
import { BrowserTracing } from "@sentry/tracing";

Sentry.init({
  dsn: "your_sentry_dsn",
  integrations: [
    new BrowserTracing(),
  ],
  tracesSampleRate: 1.0,
  environment: import.meta.env.MODE,
});
```

3. **Add to main.tsx**:
```typescript
import './lib/sentry'
```

### **Performance Monitoring**

1. **Web Vitals**:
```typescript
// src/lib/analytics.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric: any) {
  // Send to your analytics service
  console.log(metric);
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);
```

### **Uptime Monitoring**

Set up monitoring with services like:
- **UptimeRobot** (free)
- **Pingdom**
- **StatusCake**

## 🧪 **Testing in Production**

### **Staging Environment**

1. **Create Staging Branch**:
```bash
git checkout -b staging
```

2. **Deploy to Staging**:
   - Use same hosting provider
   - Different subdomain (staging.yourdomain.com)
   - Separate Supabase project for staging

3. **Test Critical Flows**:
   - User registration/login
   - Attendance recording
   - QR code scanning
   - Notifications
   - Admin functions

### **Production Testing Checklist**

- [ ] All pages load correctly
- [ ] Authentication works
- [ ] Database connections work
- [ ] QR codes generate and scan
- [ ] Notifications send (test with real email/SMS)
- [ ] Mobile responsiveness
- [ ] PWA installation
- [ ] Offline functionality
- [ ] Performance is acceptable

## 🚀 **Deployment Process**

### **Step-by-Step Deployment**

1. **Final Code Review**:
```bash
npm run lint
npm run build
npm run preview  # Test production build locally
```

2. **Environment Setup**:
   - Configure production environment variables
   - Test with production Supabase instance

3. **Deploy**:
```bash
# For Vercel
vercel --prod

# For Netlify
netlify deploy --prod --dir=dist

# For AWS Amplify
# Push to main branch (if auto-deploy enabled)
```

4. **Post-Deployment Verification**:
   - Check all functionality works
   - Monitor error logs
   - Test from different devices/browsers
   - Verify SSL certificate

### **Rollback Plan**

If issues occur:

1. **Immediate Rollback**:
```bash
# Vercel
vercel rollback

# Netlify
netlify rollback

# AWS Amplify
# Redeploy previous version
```

2. **Database Rollback** (if needed):
   - Restore from backup
   - Run reverse migrations

## 📈 **Post-Deployment**

### **Monitoring Dashboard**

Set up monitoring for:
- Application errors
- Performance metrics
- User activity
- Database performance
- API response times

### **Regular Maintenance**

- **Weekly**: Check error logs and performance
- **Monthly**: Review security updates
- **Quarterly**: Performance optimization review
- **Annually**: Security audit

### **Scaling Considerations**

As your app grows:
- Monitor database performance
- Consider CDN for static assets
- Implement caching strategies
- Consider database read replicas
- Monitor and optimize bundle size

## 🆘 **Emergency Procedures**

### **If Site Goes Down**

1. Check hosting provider status
2. Check domain/DNS configuration
3. Review recent deployments
4. Check error monitoring dashboard
5. Rollback if necessary

### **If Database Issues**

1. Check Supabase dashboard
2. Review database logs
3. Check connection limits
4. Consider read-only mode if needed
5. Contact Supabase support if needed

---

## 🎯 **Success Metrics**

After deployment, monitor:
- **Uptime**: >99.9%
- **Page Load Time**: <3 seconds
- **Error Rate**: <1%
- **User Satisfaction**: Collect feedback
- **Security**: No security incidents

**Congratulations! Your app is now production-ready! 🎉**
