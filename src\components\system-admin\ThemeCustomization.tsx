import { useState, useEffect } from "react";
import { useTheme } from "@/components/providers/ThemeProvider";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { ThemeColors, ThemeSettings } from "@/lib/types";

// Add TypeScript declarations for window.__originalThemeColors
declare global {
  interface Window {
    __originalThemeColors?: Record<string, string>;
  }
}
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Loader2,
  Save,
  RefreshCw,
  AlertTriangle,
  Info,
  Check,
  Globe,
  Paintbrush,
  Eye,
  EyeOff,
  Palette,
  Undo,
  RotateCcw,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { hexToHSL } from "@/lib/utils/color-utils";
import { cn } from "@/lib/utils";

interface School {
  id: string;
  name: string;
}

interface ThemeCustomizationProps {
  schools: School[];
  selectedSchool: School | null;
  onSchoolSelect: (schoolId: string | null) => void;
}

const defaultLightTheme: ThemeColors = {
  primary: "#1a365d",
  secondary: "#0d9488",
  accent: "#f97316",
  background: "#ffffff",
  foreground: "#0f172a",
  muted: "#f1f5f9",
  card: "#ffffff",
  border: "#e2e8f0",
};

const defaultDarkTheme: ThemeColors = {
  primary: "#F39228", // Vibrant orange
  secondary: "#0d9488",
  accent: "#f97316",
  background: "#1e2124",
  foreground: "#c9d1d9",
  muted: "#374151",
  card: "#1e2124",
  border: "#374151",
};

export default function ThemeCustomization({
  schools,
  selectedSchool,
  onSchoolSelect,
}: ThemeCustomizationProps) {
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const [activeTab, setActiveTab] = useState("light");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  // Theme settings
  const [themeSettings, setThemeSettings] = useState<ThemeSettings>({
    lightTheme: { ...defaultLightTheme },
    darkTheme: { ...defaultDarkTheme },
    applyToAllSchools: true,
    targetSchoolId: null,
    overrideSchoolCustomizations: true,
  });

  // Target school for theme application
  const [targetSchoolId, setTargetSchoolId] = useState<string | null>(null);

  useEffect(() => {
    if (selectedSchool) {
      loadThemeSettings(selectedSchool.id);
      setThemeSettings((prev) => ({
        ...prev,
        applyToAllSchools: false,
        targetSchoolId: selectedSchool.id,
      }));
    } else {
      loadGlobalThemeSettings();
      setThemeSettings((prev) => ({
        ...prev,
        applyToAllSchools: true,
        targetSchoolId: null,
      }));
    }
  }, [selectedSchool]);

  const loadThemeSettings = async (schoolId: string) => {
    setLoading(true);
    try {
      // First check for school-specific theme settings
      const { data: schoolTheme, error: schoolThemeError } = await supabase
        .from("system_school_settings_overrides")
        .select("*")
        .eq("school_id", schoolId)
        .eq("setting_name", "theme_settings")
        .maybeSingle();

      console.log("School theme settings:", { schoolTheme, schoolThemeError });

      if (schoolTheme && !schoolThemeError) {
        // School has custom theme settings
        setThemeSettings({
          lightTheme: schoolTheme.setting_value.lightTheme || {
            ...defaultLightTheme,
          },
          darkTheme: schoolTheme.setting_value.darkTheme || {
            ...defaultDarkTheme,
          },
          applyToAllSchools: false,
          targetSchoolId: schoolId,
          overrideSchoolCustomizations:
            schoolTheme.setting_value.overrideSchoolCustomizations !== undefined
              ? schoolTheme.setting_value.overrideSchoolCustomizations
              : true,
        });
      } else {
        // Fall back to global theme settings
        const { data: globalTheme, error: globalThemeError } = await supabase
          .from("system_settings")
          .select("*")
          .eq("setting_name", "global_theme_settings")
          .maybeSingle();

        console.log("Fallback to global theme settings:", {
          globalTheme,
          globalThemeError,
        });

        if (globalTheme && !globalThemeError) {
          setThemeSettings({
            lightTheme: globalTheme.setting_value.lightTheme || {
              ...defaultLightTheme,
            },
            darkTheme: globalTheme.setting_value.darkTheme || {
              ...defaultDarkTheme,
            },
            applyToAllSchools: false,
            targetSchoolId: schoolId,
            overrideSchoolCustomizations:
              globalTheme.setting_value.overrideSchoolCustomizations !==
              undefined
                ? globalTheme.setting_value.overrideSchoolCustomizations
                : true,
          });
        } else {
          // Use defaults if no settings found
          console.log(
            "No theme settings found for school or globally, using defaults"
          );
          setThemeSettings({
            lightTheme: { ...defaultLightTheme },
            darkTheme: { ...defaultDarkTheme },
            applyToAllSchools: false,
            targetSchoolId: schoolId,
            overrideSchoolCustomizations: true,
          });
        }
      }
    } catch (error) {
      console.error("Error loading theme settings:", error);
      toast({
        title: "Error",
        description: "Failed to load theme settings",
        variant: "destructive",
      });

      // Use defaults if error
      setThemeSettings({
        lightTheme: { ...defaultLightTheme },
        darkTheme: { ...defaultDarkTheme },
        applyToAllSchools: false,
        targetSchoolId: schoolId,
        overrideSchoolCustomizations: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const loadGlobalThemeSettings = async () => {
    setLoading(true);
    try {
      const { data: globalTheme, error: globalThemeError } = await supabase
        .from("system_settings")
        .select("*")
        .eq("setting_name", "global_theme_settings")
        .maybeSingle();

      console.log("Global theme settings:", { globalTheme, globalThemeError });

      if (globalTheme && !globalThemeError) {
        setThemeSettings({
          lightTheme: globalTheme.setting_value.lightTheme || {
            ...defaultLightTheme,
          },
          darkTheme: globalTheme.setting_value.darkTheme || {
            ...defaultDarkTheme,
          },
          applyToAllSchools: true,
          targetSchoolId: null,
          overrideSchoolCustomizations:
            globalTheme.setting_value.overrideSchoolCustomizations !== undefined
              ? globalTheme.setting_value.overrideSchoolCustomizations
              : true,
        });
      } else {
        // Use defaults if no settings found
        console.log("No global theme settings found, using defaults");

        // Create default global theme settings
        try {
          await supabase.rpc("update_system_setting", {
            p_setting_name: "global_theme_settings",
            p_setting_value: {
              lightTheme: defaultLightTheme,
              darkTheme: defaultDarkTheme,
              overrideSchoolCustomizations: true,
              updated_at: new Date().toISOString(),
            },
          });

          console.log("Created default global theme settings");
        } catch (createError) {
          console.error(
            "Error creating default global theme settings:",
            createError
          );
        }

        setThemeSettings({
          lightTheme: { ...defaultLightTheme },
          darkTheme: { ...defaultDarkTheme },
          applyToAllSchools: true,
          targetSchoolId: null,
          overrideSchoolCustomizations: true,
        });
      }
    } catch (error) {
      console.error("Error loading global theme settings:", error);
      toast({
        title: "Error",
        description: "Failed to load global theme settings",
        variant: "destructive",
      });

      // Use defaults if error
      setThemeSettings({
        lightTheme: { ...defaultLightTheme },
        darkTheme: { ...defaultDarkTheme },
        applyToAllSchools: true,
        targetSchoolId: null,
        overrideSchoolCustomizations: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const saveThemeSettings = async () => {
    setSaving(true);
    setRefreshing(true);

    try {
      console.log("Saving theme settings:", themeSettings);

      if (themeSettings.applyToAllSchools) {
        // Save to global settings
        console.log("Saving to global settings");

        const { error: rpcError } = await supabase.rpc(
          "update_system_setting",
          {
            p_setting_name: "global_theme_settings",
            p_setting_value: {
              lightTheme: themeSettings.lightTheme,
              darkTheme: themeSettings.darkTheme,
              overrideSchoolCustomizations:
                themeSettings.overrideSchoolCustomizations,
              updated_at: new Date().toISOString(),
            },
          }
        );

        if (rpcError) {
          console.error("Error saving global theme settings:", rpcError);
          throw rpcError;
        }

        // Apply to all schools
        if (schools.length > 0) {
          console.log("Applying to all schools:", schools.length);

          for (const school of schools) {
            try {
              console.log(`Applying theme to school ${school.id} using RPC`);

              if (themeSettings.overrideSchoolCustomizations) {
                // Use the stored procedure instead of direct upsert
                const { error: rpcError } = await supabase.rpc(
                  "update_school_setting_override",
                  {
                    p_school_id: school.id,
                    p_setting_name: "theme_settings",
                    p_setting_value: {
                      lightTheme: themeSettings.lightTheme,
                      darkTheme: themeSettings.darkTheme,
                      overrideSchoolCustomizations:
                        themeSettings.overrideSchoolCustomizations,
                      updated_at: new Date().toISOString(),
                    },
                    p_override_enabled: true,
                    p_applies_to_all: true,
                  }
                );

                if (rpcError) {
                  console.error(
                    `Error applying theme to school ${school.id} using RPC:`,
                    rpcError
                  );
                  // Continue with other schools even if one fails
                } else {
                  console.log(
                    `Successfully applied theme to school ${school.id}`
                  );
                }
              } else {
                // If override is disabled, we need to update the override flag but keep the school's theme
                // First, get the current school theme settings
                const { data: currentSettings, error: fetchError } =
                  await supabase
                    .from("system_school_settings_overrides")
                    .select("*")
                    .eq("school_id", school.id)
                    .eq("setting_name", "theme_settings")
                    .maybeSingle();

                if (fetchError) {
                  console.error(
                    `Error fetching current theme for school ${school.id}:`,
                    fetchError
                  );
                } else {
                  // Update only the override flag, keeping the existing theme
                  const settingValue = currentSettings?.setting_value || {
                    lightTheme: themeSettings.lightTheme,
                    darkTheme: themeSettings.darkTheme,
                    updated_at: new Date().toISOString(),
                  };

                  // Set the override flag to false
                  settingValue.overrideSchoolCustomizations = false;

                  const { error: updateError } = await supabase.rpc(
                    "update_school_setting_override",
                    {
                      p_school_id: school.id,
                      p_setting_name: "theme_settings",
                      p_setting_value: settingValue,
                      p_override_enabled: false, // Disable the override
                      p_applies_to_all: true,
                    }
                  );

                  if (updateError) {
                    console.error(
                      `Error updating override flag for school ${school.id}:`,
                      updateError
                    );
                  } else {
                    console.log(
                      `Successfully disabled theme override for school ${school.id}`
                    );
                  }
                }
              }
            } catch (schoolError) {
              console.error(
                `Exception applying theme to school ${school.id}:`,
                schoolError
              );
              // Continue with other schools even if one fails
            }
          }
        }

        // Apply the theme changes immediately
        applyThemeChanges();

        // Show success message
        toast({
          title: "Theme Settings Saved",
          description: themeSettings.overrideSchoolCustomizations
            ? "Theme settings have been applied to all schools"
            : "School admins can now customize their own themes",
          variant: "default",
        });

        // Also show a Sonner toast for more visibility
        import("sonner")
          .then(({ toast: sonnerToast }) => {
            sonnerToast.success("Theme Settings Saved", {
              description: themeSettings.overrideSchoolCustomizations
                ? "Theme settings have been applied to all schools"
                : "School admins can now customize their own themes",
              duration: 5000,
            });
          })
          .catch((err) => console.error("Error showing Sonner toast:", err));
      } else if (themeSettings.targetSchoolId) {
        // Save to specific school
        console.log("Saving to specific school:", themeSettings.targetSchoolId);

        if (themeSettings.overrideSchoolCustomizations) {
          // Use the stored procedure instead of direct upsert
          const { error: rpcError } = await supabase.rpc(
            "update_school_setting_override",
            {
              p_school_id: themeSettings.targetSchoolId,
              p_setting_name: "theme_settings",
              p_setting_value: {
                lightTheme: themeSettings.lightTheme,
                darkTheme: themeSettings.darkTheme,
                overrideSchoolCustomizations:
                  themeSettings.overrideSchoolCustomizations,
                updated_at: new Date().toISOString(),
              },
              p_override_enabled: true,
              p_applies_to_all: false,
            }
          );

          if (rpcError) {
            console.error("Error saving school theme settings:", rpcError);
            throw rpcError;
          } else {
            console.log("Successfully saved theme to specific school");
          }
        } else {
          // If override is disabled, update the override flag but keep the school's theme
          // First, get the current school theme settings
          const { data: currentSettings, error: fetchError } = await supabase
            .from("system_school_settings_overrides")
            .select("*")
            .eq("school_id", themeSettings.targetSchoolId)
            .eq("setting_name", "theme_settings")
            .maybeSingle();

          if (fetchError) {
            console.error(
              `Error fetching current theme for school ${themeSettings.targetSchoolId}:`,
              fetchError
            );
            throw fetchError;
          }

          // Update only the override flag, keeping the existing theme
          const settingValue = currentSettings?.setting_value || {
            lightTheme: themeSettings.lightTheme,
            darkTheme: themeSettings.darkTheme,
            updated_at: new Date().toISOString(),
          };

          // Set the override flag to false
          settingValue.overrideSchoolCustomizations = false;

          const { error: updateError } = await supabase.rpc(
            "update_school_setting_override",
            {
              p_school_id: themeSettings.targetSchoolId,
              p_setting_name: "theme_settings",
              p_setting_value: settingValue,
              p_override_enabled: false, // Disable the override
              p_applies_to_all: false,
            }
          );

          if (updateError) {
            console.error(
              `Error updating override flag for school ${themeSettings.targetSchoolId}:`,
              updateError
            );
            throw updateError;
          } else {
            console.log(
              `Successfully disabled theme override for school ${themeSettings.targetSchoolId}`
            );
          }
        }

        // Apply the theme changes immediately
        applyThemeChanges();

        // Show success message
        toast({
          title: "Theme Settings Saved",
          description: themeSettings.overrideSchoolCustomizations
            ? `Theme settings have been applied to ${
                selectedSchool?.name || "the selected school"
              }`
            : `School admin can now customize the theme for ${
                selectedSchool?.name || "the selected school"
              }`,
          variant: "default",
        });

        // Also show a Sonner toast for more visibility
        import("sonner")
          .then(({ toast: sonnerToast }) => {
            sonnerToast.success("Theme Settings Saved", {
              description: themeSettings.overrideSchoolCustomizations
                ? `Theme settings have been applied to ${
                    selectedSchool?.name || "the selected school"
                  }`
                : `School admin can now customize the theme for ${
                    selectedSchool?.name || "the selected school"
                  }`,
              duration: 5000,
            });
          })
          .catch((err) => console.error("Error showing Sonner toast:", err));
      }

      // If preview mode was enabled, disable it
      if (previewMode) {
        setPreviewMode(false);
        if (window.__originalThemeColors) {
          delete window.__originalThemeColors;
        }
      }
    } catch (error) {
      console.error("Error saving theme settings:", error);
      toast({
        title: "Error",
        description: "Failed to save theme settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
      setRefreshing(false);

      // Reload theme settings to ensure we have the latest data
      if (selectedSchool) {
        loadThemeSettings(selectedSchool.id);
      } else {
        loadGlobalThemeSettings();
      }
    }
  };

  const applyThemeChanges = () => {
    // Apply theme changes to CSS variables
    const root = document.documentElement;
    const currentTheme = theme === "dark" ? "darkTheme" : "lightTheme";
    const colors =
      themeSettings[
        currentTheme as keyof Pick<ThemeSettings, "lightTheme" | "darkTheme">
      ];

    console.log(`Applying ${currentTheme} changes:`, colors);

    // Convert hex colors to HSL for CSS variables
    Object.entries(colors).forEach(([key, value]) => {
      const hslValue = hexToHSL(value);
      root.style.setProperty(`--${key}`, hslValue);
    });

    // Force refresh
    root.classList.add("theme-refreshing");
    setTimeout(() => {
      root.classList.remove("theme-refreshing");
    }, 100);

    // Broadcast a custom event that other components can listen for
    const themeChangeEvent = new CustomEvent("system-theme-changed", {
      detail: {
        theme: currentTheme,
        colors: colors,
        overrideSchoolCustomizations:
          themeSettings.overrideSchoolCustomizations,
      },
    });

    window.dispatchEvent(themeChangeEvent);
    console.log("Broadcasted system-theme-changed event");
  };

  const handleColorChange = (colorKey: keyof ThemeColors, value: string) => {
    const themeType = activeTab === "light" ? "lightTheme" : "darkTheme";

    // Update the theme settings state
    setThemeSettings((prevSettings) => ({
      ...prevSettings,
      [themeType]: {
        ...prevSettings[
          themeType as keyof Pick<ThemeSettings, "lightTheme" | "darkTheme">
        ],
        [colorKey]: value,
      },
    }));

    // Apply changes in real-time if preview mode is enabled or if we're in the current theme mode
    if (
      previewMode ||
      (activeTab === "light" && theme === "light") ||
      (activeTab === "dark" && theme === "dark")
    ) {
      // Apply changes in real-time for preview
      const root = document.documentElement;
      const hslValue = hexToHSL(value);

      console.log(`Applying color change: --${colorKey} = ${hslValue}`);
      root.style.setProperty(`--${colorKey}`, hslValue);

      // Force a CSS refresh for the change to take effect
      root.classList.add("theme-refreshing");
      setTimeout(() => {
        root.classList.remove("theme-refreshing");
      }, 100);
    }
  };

  const resetToDefaults = () => {
    const defaultTheme =
      activeTab === "light" ? defaultLightTheme : defaultDarkTheme;

    // Update the theme settings state
    setThemeSettings((prevSettings) => ({
      ...prevSettings,
      [activeTab === "light" ? "lightTheme" : "darkTheme"]: { ...defaultTheme },
    }));

    // Apply default theme if in preview mode or if we're in the current theme mode
    if (
      previewMode ||
      (activeTab === "light" && theme === "light") ||
      (activeTab === "dark" && theme === "dark")
    ) {
      // Apply default theme for preview
      const root = document.documentElement;

      console.log("Resetting to default theme:", defaultTheme);

      Object.entries(defaultTheme).forEach(([key, value]) => {
        const hslValue = hexToHSL(value);
        root.style.setProperty(`--${key}`, hslValue);
      });

      // Force a CSS refresh for the changes to take effect
      root.classList.add("theme-refreshing");
      setTimeout(() => {
        root.classList.remove("theme-refreshing");
      }, 100);

      toast({
        title: "Reset to Defaults",
        description: `${
          activeTab === "light" ? "Light" : "Dark"
        } theme has been reset to default values.`,
      });
    }
  };

  const togglePreviewMode = () => {
    if (!previewMode) {
      // Save current theme state before entering preview mode
      const currentTheme = theme === "dark" ? "darkTheme" : "lightTheme";
      const currentColors =
        themeSettings[
          currentTheme as keyof Pick<ThemeSettings, "lightTheme" | "darkTheme">
        ];

      // Store original values to restore later
      window.__originalThemeColors = { ...currentColors };

      // Entering preview mode - apply current theme settings
      applyThemeChanges();
      setPreviewMode(true);

      toast({
        title: "Preview Mode Enabled",
        description:
          "Changes will be previewed in real-time but not saved until you click Save.",
      });
    } else {
      // Exiting preview mode - restore original theme
      if (window.__originalThemeColors) {
        const root = document.documentElement;
        Object.entries(window.__originalThemeColors).forEach(([key, value]) => {
          const hslValue = hexToHSL(value as string);
          root.style.setProperty(`--${key}`, hslValue);
        });

        // Force refresh
        root.classList.add("theme-refreshing");
        setTimeout(() => {
          root.classList.remove("theme-refreshing");
        }, 100);

        delete window.__originalThemeColors;
      }

      setPreviewMode(false);

      toast({
        title: "Preview Mode Disabled",
        description: "Theme has been restored to saved settings.",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h3 className="text-lg font-medium">Theme Customization</h3>
          <p className="text-sm text-muted-foreground">
            Customize the appearance of the application for light and dark modes
          </p>
        </div>
        <div className="flex flex-col xs:flex-row items-stretch xs:items-center gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={togglePreviewMode}
            className={cn(
              previewMode ? "bg-primary/10" : "",
              "w-full xs:w-auto"
            )}
          >
            {previewMode ? (
              <>
                <EyeOff className="mr-2 h-4 w-4" />
                Exit Preview
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                Preview Changes
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetToDefaults}
            className="w-full xs:w-auto"
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            Reset to Defaults
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="apply-all-schools"
              checked={themeSettings.applyToAllSchools}
              onCheckedChange={(checked) => {
                setThemeSettings({
                  ...themeSettings,
                  applyToAllSchools: checked,
                  targetSchoolId: checked ? null : selectedSchool?.id || null,
                });
              }}
            />
            <Label htmlFor="apply-all-schools" className="text-sm sm:text-base">
              Apply to all schools
            </Label>
          </div>

          <div className="flex flex-wrap items-center gap-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="override-school-customizations"
                checked={themeSettings.overrideSchoolCustomizations}
                onCheckedChange={(checked) => {
                  setThemeSettings({
                    ...themeSettings,
                    overrideSchoolCustomizations: checked,
                  });
                }}
              />
              <Label
                htmlFor="override-school-customizations"
                className="text-sm sm:text-base"
              >
                Override school admin customizations
              </Label>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  When enabled, this theme will override any customizations made
                  by school admins. When disabled, school admins can still
                  customize their school's theme.
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {!themeSettings.applyToAllSchools && !selectedSchool && (
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
            <Label htmlFor="target-school" className="text-sm sm:text-base">
              Target School:
            </Label>
            <Select
              value={themeSettings.targetSchoolId || ""}
              onValueChange={(value) => {
                setThemeSettings({
                  ...themeSettings,
                  targetSchoolId: value || null,
                });
              }}
            >
              <SelectTrigger className="w-full sm:w-[250px]">
                <SelectValue placeholder="Select a school" />
              </SelectTrigger>
              <SelectContent>
                {schools.map((school) => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full">
          <TabsTrigger value="light" className="flex items-center gap-2 w-full">
            <div className="w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-white border border-gray-300"></div>
            <span className="text-xs sm:text-sm">Light Theme</span>
          </TabsTrigger>
          <TabsTrigger value="dark" className="flex items-center gap-2 w-full">
            <div className="w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-gray-800 border border-gray-600"></div>
            <span className="text-xs sm:text-sm">Dark Theme</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="light" className="space-y-4 pt-4">
          <ThemeColorEditor
            colors={themeSettings.lightTheme}
            onChange={handleColorChange}
            loading={loading}
          />
        </TabsContent>

        <TabsContent value="dark" className="space-y-4 pt-4">
          <ThemeColorEditor
            colors={themeSettings.darkTheme}
            onChange={handleColorChange}
            loading={loading}
          />
        </TabsContent>
      </Tabs>

      <div className="flex justify-center sm:justify-end">
        <Button
          onClick={saveThemeSettings}
          disabled={saving}
          className="flex items-center gap-2 w-full sm:w-auto"
        >
          {saving ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Save className="h-4 w-4" />
          )}
          {saving ? "Saving..." : "Save Theme Settings"}
        </Button>
      </div>
    </div>
  );
}

interface ThemeColorEditorProps {
  colors: ThemeColors;
  onChange: (colorKey: keyof ThemeColors, value: string) => void;
  loading: boolean;
}

function ThemeColorEditor({
  colors,
  onChange,
  loading,
}: ThemeColorEditorProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <ColorPicker
        label="Primary Color"
        description="Main brand color used for buttons and accents"
        value={colors.primary}
        onChange={(value) => onChange("primary", value)}
      />
      <ColorPicker
        label="Secondary Color"
        description="Used for secondary buttons and elements"
        value={colors.secondary}
        onChange={(value) => onChange("secondary", value)}
      />
      <ColorPicker
        label="Accent Color"
        description="Used for highlighting and special elements"
        value={colors.accent}
        onChange={(value) => onChange("accent", value)}
      />
      <ColorPicker
        label="Background Color"
        description="Main background color of the application"
        value={colors.background}
        onChange={(value) => onChange("background", value)}
      />
      <ColorPicker
        label="Foreground Color"
        description="Main text color"
        value={colors.foreground}
        onChange={(value) => onChange("foreground", value)}
      />
      <ColorPicker
        label="Muted Color"
        description="Used for subtle backgrounds and disabled states"
        value={colors.muted}
        onChange={(value) => onChange("muted", value)}
      />
      <ColorPicker
        label="Card Color"
        description="Background color for cards and panels"
        value={colors.card}
        onChange={(value) => onChange("card", value)}
      />
      <ColorPicker
        label="Border Color"
        description="Color used for borders and dividers"
        value={colors.border}
        onChange={(value) => onChange("border", value)}
      />
    </div>
  );
}

interface ColorPickerProps {
  label: string;
  description: string;
  value: string;
  onChange: (value: string) => void;
}

function ColorPicker({
  label,
  description,
  value,
  onChange,
}: ColorPickerProps) {
  return (
    <div className="space-y-2">
      <div className="flex flex-col xs:flex-row justify-between gap-1">
        <Label
          htmlFor={label.toLowerCase().replace(/\s+/g, "-")}
          className="text-sm sm:text-base"
        >
          {label}
        </Label>
        <span className="text-xs text-muted-foreground">{value}</span>
      </div>
      <div className="flex gap-2">
        <Input
          id={label.toLowerCase().replace(/\s+/g, "-")}
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-10 sm:w-12 h-10 p-1"
        />
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="flex-1 text-xs sm:text-sm"
        />
      </div>
      <p className="text-xs text-muted-foreground">{description}</p>
    </div>
  );
}
