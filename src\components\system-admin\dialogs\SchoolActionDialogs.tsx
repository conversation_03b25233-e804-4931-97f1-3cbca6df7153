import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertCircle,
  CheckCircle,
  RotateCw,
  Wrench as WrenchIcon,
} from "lucide-react";
import { School } from "@/lib/types";

interface MaintenanceDialogProps {
  school: School | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (schoolId: string, enable: boolean) => Promise<void>;
  isProcessing: boolean;
}

export function MaintenanceDialog({
  school,
  open,
  onOpenChange,
  onConfirm,
  isProcessing,
}: MaintenanceDialogProps) {
  const [maintenanceMessage, setMaintenanceMessage] = useState(
    school?.maintenance_message || ""
  );
  const [maintenanceTime, setMaintenanceTime] = useState(
    school?.maintenance_estimated_time || "a few hours"
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {school?.maintenance_mode
              ? "Disable Maintenance Mode"
              : "Enable Maintenance Mode"}
          </DialogTitle>
          <DialogDescription>
            {school?.maintenance_mode
              ? "This will disable maintenance mode for this school."
              : "This will put the school in maintenance mode. Users will see a maintenance page when they try to access the app."}
          </DialogDescription>
        </DialogHeader>

        {!school?.maintenance_mode && (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="maintenance-message">Maintenance Message</Label>
              <Input
                id="maintenance-message"
                placeholder="We're performing scheduled maintenance to improve your experience."
                value={maintenanceMessage}
                onChange={(e) => setMaintenanceMessage(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                This message will be displayed to users during maintenance.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maintenance-time">Estimated Time</Label>
              <Input
                id="maintenance-time"
                placeholder="a few hours"
                value={maintenanceTime}
                onChange={(e) => setMaintenanceTime(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                How long the maintenance is expected to take.
              </p>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            variant={school?.maintenance_mode ? "default" : "default"}
            onClick={() =>
              onConfirm(school?.id || "", !school?.maintenance_mode)
            }
            disabled={isProcessing}
          >
            {isProcessing && (
              <RotateCw className="mr-2 h-4 w-4 animate-spin" />
            )}
            {school?.maintenance_mode
              ? "Disable Maintenance Mode"
              : "Enable Maintenance Mode"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface BlockDialogProps {
  school: School | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (schoolId: string, activate: boolean) => Promise<void>;
  isProcessing: boolean;
}

export function BlockDialog({
  school,
  open,
  onOpenChange,
  onConfirm,
  isProcessing,
}: BlockDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {school?.status === "active" ? "Block School" : "Activate School"}
          </DialogTitle>
          <DialogDescription>
            {school?.status === "active"
              ? "This will block all users from this school from accessing the app."
              : "This will allow users from this school to access the app."}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="flex items-center space-x-2">
            <p>School:</p>
            <p className="font-medium">{school?.name}</p>
          </div>

          <div className="mt-4">
            {school?.status === "active" ? (
              <div className="bg-destructive/10 p-3 rounded-md border border-destructive/20">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
                  <div>
                    <p className="font-medium text-destructive">Warning</p>
                    <p className="text-sm text-muted-foreground">
                      Blocking this school will prevent all users from accessing
                      the app. You can reactivate it at any time.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-green-500/10 p-3 rounded-md border border-green-500/20">
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <p className="font-medium text-green-600">Activation</p>
                    <p className="text-sm text-muted-foreground">
                      Activating this school will allow all users to access the
                      app again.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            variant={school?.status === "active" ? "destructive" : "default"}
            onClick={() =>
              onConfirm(school?.id || "", school?.status !== "active")
            }
            disabled={isProcessing}
          >
            {isProcessing && <RotateCw className="mr-2 h-4 w-4 animate-spin" />}
            {school?.status === "active" ? "Block School" : "Activate School"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface DeleteDialogProps {
  school: School | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (schoolId: string) => Promise<void>;
  isProcessing: boolean;
}

export function DeleteDialog({
  school,
  open,
  onOpenChange,
  onConfirm,
  isProcessing,
}: DeleteDialogProps) {
  const [confirmName, setConfirmName] = useState("");
  const isConfirmDisabled = confirmName !== school?.name;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete School</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the school
            and all associated data.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="flex items-center space-x-2 mb-4">
            <p>School:</p>
            <p className="font-medium">{school?.name}</p>
          </div>

          <div className="bg-destructive/10 p-3 rounded-md border border-destructive/20">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
              <div>
                <p className="font-medium text-destructive">Warning</p>
                <p className="text-sm text-muted-foreground">
                  This will permanently delete the school and all associated
                  data, including all user accounts, attendance records, and
                  settings. This action cannot be undone.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <p className="text-sm font-medium text-destructive">
              Type the school name to confirm deletion:
            </p>
            <Input
              className="mt-2"
              placeholder={school?.name}
              value={confirmName}
              onChange={(e) => setConfirmName(e.target.value)}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={() => onConfirm(school?.id || "")}
            disabled={isProcessing || isConfirmDisabled}
          >
            {isProcessing && <RotateCw className="mr-2 h-4 w-4 animate-spin" />}
            Delete School
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
