# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Email Service Configuration (SendGrid)
VITE_SENDGRID_API_KEY=your_sendgrid_api_key
VITE_EMAIL_FROM=your_verified_sender_email

# SMS Service Configuration (Twilio)
VITE_TWILIO_ACCOUNT_SID=your_twilio_account_sid
VITE_TWILIO_AUTH_TOKEN=your_twilio_auth_token
VITE_TWILIO_PHONE_NUMBER=your_twilio_phone_number

# AI Assistant Configuration (OpenRouter - DeepSeek R1)
VITE_OPENROUTER_API_KEY=your_openrouter_api_key

# QR Code Security Configuration

# QR Code expiry time in seconds
# Default: 300 (5 minutes)
# Range: 30 seconds to 1800 seconds (30 minutes)
# Examples:
#   60 = 1 minute
#   180 = 3 minutes
#   300 = 5 minutes (default)
#   600 = 10 minutes
#   900 = 15 minutes
NEXT_PUBLIC_QR_EXPIRY_SECONDS=40

# Challenge rotation interval in seconds
# Default: 30 seconds
# Range: 5 seconds to 300 seconds
# This controls how often the anti-screenshot challenge changes
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=20

# Challenge grace period in slots
# Default: Auto-calculated based on QR expiry time
# This allows older challenges to still be valid
# Set to 0 to disable grace period (strict mode)
# NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=10

# QR Code signing secret key
# IMPORTANT: Change this in production!
# Use a long, random string for security
NEXT_PUBLIC_QR_SECRET_KEY=your-256-bit-secret-key-for-qr-signing
