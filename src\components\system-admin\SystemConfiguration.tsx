import { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import SecondaryNavigation from "./navigation/SecondaryNavigation";
import {
  Settings,
  Mail,
  MessageSquare,
  RefreshCw,
  ToggleLeft,
  Globe,
  Database,
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function SystemConfiguration() {
  const { toast } = useToast();
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState("notifications");

  const handleRefresh = async () => {
    setRefreshing(true);
    // Implement refresh logic here
    setTimeout(() => setRefreshing(false), 1000);
  };

  // Secondary navigation items
  const navItems = [
    {
      id: "notifications",
      label: "Notifications",
      icon: <Mail className="h-4 w-4" />,
    },
    {
      id: "integrations",
      label: "Integrations",
      icon: <Globe className="h-4 w-4" />,
    },
    {
      id: "features",
      label: "Feature Toggles",
      icon: <ToggleLeft className="h-4 w-4" />,
    },
    {
      id: "database",
      label: "Database",
      icon: <Database className="h-4 w-4" />,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">System Configuration</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      <SecondaryNavigation
        items={navItems}
        activeItem={activeTab}
        onItemChange={setActiveTab}
        className="mt-4"
      >
        {{
          notifications: (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Email Notification Settings</CardTitle>
                  <CardDescription>
                    Configure system-wide email notification settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="sendgrid-api-key">SendGrid API Key</Label>
                    <Input
                      id="sendgrid-api-key"
                      type="password"
                      placeholder="Enter your SendGrid API key"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="from-email">From Email Address</Label>
                    <Input
                      id="from-email"
                      type="email"
                      placeholder="<EMAIL>"
                    />
                    <p className="text-xs text-muted-foreground">
                      Must match a verified Sender Identity in SendGrid
                    </p>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="email-enabled">Enable Email Notifications</Label>
                      <p className="text-xs text-muted-foreground">
                        Toggle email notifications system-wide
                      </p>
                    </div>
                    <Switch id="email-enabled" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button>Save Email Settings</Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>SMS Notification Settings</CardTitle>
                  <CardDescription>
                    Configure system-wide SMS notification settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="twilio-account-sid">Twilio Account SID</Label>
                    <Input
                      id="twilio-account-sid"
                      type="password"
                      placeholder="Enter your Twilio Account SID"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twilio-auth-token">Twilio Auth Token</Label>
                    <Input
                      id="twilio-auth-token"
                      type="password"
                      placeholder="Enter your Twilio Auth Token"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twilio-phone-number">Twilio Phone Number</Label>
                    <Input
                      id="twilio-phone-number"
                      type="text"
                      placeholder="+**********"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="sms-enabled">Enable SMS Notifications</Label>
                      <p className="text-xs text-muted-foreground">
                        Toggle SMS notifications system-wide
                      </p>
                    </div>
                    <Switch id="sms-enabled" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button>Save SMS Settings</Button>
                </CardFooter>
              </Card>
            </div>
          ),
          integrations: (
            <Card>
              <CardHeader>
                <CardTitle>External Integrations</CardTitle>
                <CardDescription>
                  Configure integrations with external services
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="api" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="api">API Access</TabsTrigger>
                    <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
                    <TabsTrigger value="oauth">OAuth</TabsTrigger>
                  </TabsList>
                  <TabsContent value="api" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label>API Keys</Label>
                      <div className="p-4 border rounded-md">
                        <p className="text-sm text-muted-foreground">
                          No API keys have been created yet.
                        </p>
                      </div>
                      <Button variant="outline" size="sm">
                        Generate API Key
                      </Button>
                    </div>
                  </TabsContent>
                  <TabsContent value="webhooks" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label>Webhook Endpoints</Label>
                      <div className="p-4 border rounded-md">
                        <p className="text-sm text-muted-foreground">
                          No webhook endpoints have been configured.
                        </p>
                      </div>
                      <Button variant="outline" size="sm">
                        Add Webhook
                      </Button>
                    </div>
                  </TabsContent>
                  <TabsContent value="oauth" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label>OAuth Providers</Label>
                      <div className="p-4 border rounded-md">
                        <p className="text-sm text-muted-foreground">
                          No OAuth providers have been configured.
                        </p>
                      </div>
                      <Button variant="outline" size="sm">
                        Add OAuth Provider
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ),
          features: (
            <Card>
              <CardHeader>
                <CardTitle>Feature Toggles</CardTitle>
                <CardDescription>
                  Enable or disable system features
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="feature-biometric">Biometric Authentication</Label>
                    <p className="text-xs text-muted-foreground">
                      Allow students to verify attendance using biometrics
                    </p>
                  </div>
                  <Switch id="feature-biometric" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="feature-geolocation">Geolocation Verification</Label>
                    <p className="text-xs text-muted-foreground">
                      Verify student location during attendance
                    </p>
                  </div>
                  <Switch id="feature-geolocation" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="feature-excuses">Excuse Submissions</Label>
                    <p className="text-xs text-muted-foreground">
                      Allow students to submit absence excuses
                    </p>
                  </div>
                  <Switch id="feature-excuses" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="feature-parent">Parent Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Send notifications to parents
                    </p>
                  </div>
                  <Switch id="feature-parent" defaultChecked />
                </div>
              </CardContent>
              <CardFooter>
                <Button>Save Feature Settings</Button>
              </CardFooter>
            </Card>
          ),
          database: (
            <Card>
              <CardHeader>
                <CardTitle>Database Management</CardTitle>
                <CardDescription>
                  Manage database settings and operations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 border rounded-md">
                  <h3 className="text-lg font-medium mb-2">Database Information</h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">Provider:</div>
                    <div>PostgreSQL (Supabase)</div>
                    <div className="font-medium">Status:</div>
                    <div className="text-green-600">Connected</div>
                    <div className="font-medium">Version:</div>
                    <div>15.1</div>
                  </div>
                </div>
                
                <div className="p-4 border rounded-md bg-amber-50">
                  <h3 className="text-lg font-medium mb-2 text-amber-800">Database Operations</h3>
                  <p className="text-sm text-amber-700 mb-4">
                    These operations should be used with caution as they can affect system data.
                  </p>
                  <div className="space-y-2">
                    <Button variant="outline" className="w-full justify-start">
                      Run Database Migrations
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      Backup Database
                    </Button>
                    <Button variant="outline" className="w-full justify-start text-red-600 hover:text-red-700">
                      Reset Test Data
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ),
        }}
      </SecondaryNavigation>
    </div>
  );
}
