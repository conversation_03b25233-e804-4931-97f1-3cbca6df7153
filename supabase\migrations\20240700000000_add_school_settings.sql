-- Create school_settings table
CREATE TABLE IF NOT EXISTS public.school_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  email_notifications_enabled BOOLEAN DEFAULT true,
  sms_notifications_enabled BOOLEAN DEFAULT false,
  invitation_expiry TIMESTAMP WITH TIME ZONE,
  theme_css TEXT,
  custom_login_message TEXT,
  require_location_verification BOOLEAN DEFAULT true,
  require_biometric_verification BOOLEAN DEFAULT false,
  allow_pin_verification BOOLEAN DEFAULT true,
  allow_wifi_verification BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(school_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_school_settings_school_id ON public.school_settings(school_id);

-- Enable RLS on school_settings table
ALTER TABLE public.school_settings ENABLE ROW LEVEL SECURITY;

-- School admins can manage their school settings
CREATE POLICY "School admins can manage their school settings"
ON public.school_settings
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the settings belong to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the settings belong to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all school settings
CREATE POLICY "System admins can manage all school settings"
ON public.school_settings
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Create audit_logs table for tracking important actions
CREATE TABLE IF NOT EXISTS public.audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  action_type VARCHAR NOT NULL,
  entity_type VARCHAR NOT NULL,
  entity_id UUID,
  details JSONB,
  ip_address VARCHAR,
  user_agent VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_audit_logs_school_id ON public.audit_logs(school_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action_type ON public.audit_logs(action_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);

-- Enable RLS on audit_logs table
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- School admins can view audit logs for their school
CREATE POLICY "School admins can view audit logs for their school"
ON public.audit_logs
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the logs belong to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can view all audit logs
CREATE POLICY "System admins can view all audit logs"
ON public.audit_logs
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Only the system can insert audit logs
CREATE POLICY "System can insert audit logs"
ON public.audit_logs
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Add invitation_expiry column to schools table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'schools' AND column_name = 'invitation_expiry'
  ) THEN
    ALTER TABLE schools ADD COLUMN invitation_expiry TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Grant necessary permissions
GRANT ALL ON public.school_settings TO authenticated;
GRANT ALL ON public.audit_logs TO authenticated;
