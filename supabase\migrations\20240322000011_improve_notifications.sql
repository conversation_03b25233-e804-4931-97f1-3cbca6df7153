-- Add columns for teacher notifications
ALTER TABLE notifications
ADD COLUMN teacher_id UUID REFERENCES profiles(user_id),
ADD COLUMN teacher_read_at TIMESTAMPTZ,
ADD COLUMN admin_read_at TIMESTAMPTZ;

-- Create function to get teacher for a student
CREATE OR REPLACE FUNCTION get_student_teacher(student_id UUID)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  teacher_user_id UUID;
BEGIN
  -- Get the teacher ID based on the student's room
  SELECT p.user_id INTO teacher_user_id
  FROM profiles p
  WHERE p.role = 'teacher'
  AND p.room_number = (
    SELECT room_number 
    FROM profiles 
    WHERE user_id = student_id
  )
  LIMIT 1;

  RETURN teacher_user_id;
END;
$$;

-- Modify the handle_distance_alert function to include teacher notification
CREATE OR REPLACE FUNCTION handle_distance_alert(
  student_id UUID,
  student_lat DOUBLE PRECISION,
  student_lng DOUBLE PRECISION,
  room_lat DOUBLE PRECISION,
  room_lng DOUBLE PRECISION,
  max_distance_meters DOUBLE PRECISION
)
RETURNS TABLE (
  is_within_radius BOOLEAN,
  distance DOUBLE PRECISION,
  alert_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  calculated_distance DOUBLE PRECISION;
  student_name TEXT;
  student_room TEXT;
  teacher_id UUID;
  alert_id UUID;
BEGIN
  -- Calculate distance using PostGIS
  calculated_distance := ST_DistanceSphere(
    ST_MakePoint(student_lng, student_lat),
    ST_MakePoint(room_lng, room_lat)
  );

  -- Get student details
  SELECT name, room_number INTO student_name, student_room
  FROM profiles
  WHERE user_id = student_id;

  -- Get teacher ID
  teacher_id := get_student_teacher(student_id);

  -- If outside radius, create alerts
  IF calculated_distance > max_distance_meters THEN
    -- Create notification record
    INSERT INTO notifications (
      type,
      title,
      message,
      student_id,
      teacher_id,
      student_location,
      distance_meters,
      room_number,
      metadata
    )
    VALUES (
      'distance_alert',
      'Distance Alert: Student Too Far',
      format('Student %s is attempting to verify attendance from %s meters away from room %s',
             student_name,
             ROUND(calculated_distance::numeric, 2)::text,
             student_room),
      student_id,
      teacher_id,
      ST_MakePoint(student_lng, student_lat),
      calculated_distance,
      student_room,
      jsonb_build_object(
        'student_name', student_name,
        'latitude', student_lat,
        'longitude', student_lng,
        'room_number', student_room,
        'max_allowed_distance', max_distance_meters
      )
    )
    RETURNING id INTO alert_id;

    RETURN QUERY
    SELECT 
      FALSE as is_within_radius,
      calculated_distance as distance,
      alert_id;
  ELSE
    RETURN QUERY
    SELECT 
      TRUE as is_within_radius,
      calculated_distance as distance,
      NULL::UUID as alert_id;
  END IF;
END;
$$;

-- Update the notifications policy
DROP POLICY IF EXISTS "Enable read access for notifications" ON notifications;

CREATE POLICY "Enable notifications access for users"
ON notifications FOR ALL
TO authenticated
USING (
  -- Students can view their own notifications
  student_id = auth.uid()
  OR
  -- Teachers can view notifications for their students
  teacher_id = auth.uid()
  OR
  -- Admins can view all notifications
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
)
WITH CHECK (
  -- Students can't modify notifications
  (student_id = auth.uid() AND FALSE)
  OR
  -- Teachers can only update their read status
  (teacher_id = auth.uid() AND (
    (TG_OP = 'UPDATE' AND NEW.teacher_read_at IS DISTINCT FROM OLD.teacher_read_at)
    OR
    TG_OP = 'DELETE'
  ))
  OR
  -- Admins can do anything
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
); 