import React from 'react';
import { toast } from 'sonner';
import { useTranslation } from 'react-i18next';

interface AttendanceStatusNotificationProps {
  studentName: string;
  status: 'present' | 'absent' | 'late' | 'excused';
}

/**
 * Fully internationalized attendance status notification component
 * This creates a clean, contextual notification for attendance changes
 */
export const useAttendanceStatusNotification = () => {
  const { t } = useTranslation();

  const showAttendanceStatusChanged = ({ studentName, status }: AttendanceStatusNotificationProps) => {
    // Get the translated status
    const translatedStatus = t(`teacher.dashboard.${status}`);
    
    // Create the notification title and description
    const title = t('notifications.attendanceStatusChanged');
    const description = t('notifications.attendanceStatusChangedDescription', {
      studentName,
      status: translatedStatus
    });

    // Show the notification using Sonner
    toast.success(title, {
      description,
      duration: 4000,
      className: 'attendance-status-notification',
      style: {
        background: '#f0f9ff',
        border: '1px solid #0ea5e9',
        color: '#0c4a6e'
      }
    });
  };

  return {
    showAttendanceStatusChanged
  };
};

/**
 * Direct function to show attendance status notification
 * Can be used without the hook if needed
 */
export const showAttendanceStatusNotification = (
  t: (key: string, options?: any) => string,
  { studentName, status }: AttendanceStatusNotificationProps
) => {
  const translatedStatus = t(`teacher.dashboard.${status}`);
  const title = t('notifications.attendanceStatusChanged');
  const description = t('notifications.attendanceStatusChangedDescription', {
    studentName,
    status: translatedStatus
  });

  toast.success(title, {
    description,
    duration: 4000,
    className: 'attendance-status-notification',
    style: {
      background: '#f0f9ff',
      border: '1px solid #0ea5e9',
      color: '#0c4a6e'
    }
  });
};
