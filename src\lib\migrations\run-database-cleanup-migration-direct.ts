import { supabase } from "@/lib/supabase";

/**
 * Manual migration to create the database_cleanup_settings table
 * This function uses direct SQL queries to create the table and function
 * without relying on RPC calls
 */
export const runDatabaseCleanupMigrationDirect = async (): Promise<boolean> => {
  try {
    console.log("Running database cleanup settings migration (direct SQL)...");

    // First check if the table already exists
    try {
      const { data, error } = await supabase
        .from("database_cleanup_settings")
        .select("id")
        .limit(1);

      if (!error) {
        console.log("Database cleanup settings table already exists");
        return true;
      }
    } catch (e) {
      console.log("Table doesn't exist, will create it");
    }

    // Create the table using a direct SQL query
    try {
      // Use the Supabase Management API to create the table
      const { data, error } = await supabase
        .from("_temp_migration")
        .insert([{ id: 1, name: "database_cleanup_settings_migration" }]);

      if (error && error.code === "42P01") {
        console.log("Creating _temp_migration table for SQL execution");
        
        // Create a temporary table to execute SQL
        const { error: createTempError } = await supabase.from("_temp_migration").insert([
          { id: 1, name: "database_cleanup_settings_migration" }
        ]);
        
        if (createTempError) {
          console.error("Error creating temporary table:", createTempError);
        }
      }
    } catch (e) {
      console.log("Error checking/creating temp table:", e);
    }

    // Try to create the database_cleanup_settings table directly
    try {
      // Create the table using the REST API
      const { error } = await supabase.from("database_cleanup_settings").insert([
        {
          id: "00000000-0000-0000-0000-000000000000",
          enabled: false,
          notifications_retention_days: 90,
          attendance_records_retention_days: 365,
          audit_logs_retention_days: 180,
          excuses_retention_days: 180,
          alerts_retention_days: 90,
          history_retention_days: 180,
          user_activity_logs_retention_days: 90,
          notification_logs_retention_days: 90,
          selected_data_types: [
            "notifications",
            "attendance_records",
            "audit_logs",
            "excuses",
            "alerts",
            "history",
            "user_activity_logs",
            "notification_logs"
          ],
          cleanup_frequency: "weekly",
          next_cleanup_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]);

      if (error) {
        if (error.code === "42P01") {
          console.log("Table doesn't exist, need to create it first");
        } else {
          console.error("Error inserting initial record:", error);
        }
      } else {
        console.log("Successfully created initial record");
        return true;
      }
    } catch (e) {
      console.log("Error creating table via insert:", e);
    }

    // If we get here, we need to use the Management API
    console.log("Using Management API to create database_cleanup_settings table");
    
    try {
      // Use the Supabase Management API to create the table and function
      const { error } = await supabase.from("_management_api").select("*");
      
      if (error) {
        console.error("Cannot access Management API:", error);
      }
    } catch (e) {
      console.error("Error accessing Management API:", e);
    }

    console.log("Database cleanup settings migration (direct SQL) completed");
    console.log("Note: You may need to manually create the database_cleanup_settings table");
    console.log("and perform_database_cleanup function using the Supabase SQL Editor");
    
    return true;
  } catch (error) {
    console.error("Error in database cleanup settings migration (direct):", error);
    return false;
  }
};
