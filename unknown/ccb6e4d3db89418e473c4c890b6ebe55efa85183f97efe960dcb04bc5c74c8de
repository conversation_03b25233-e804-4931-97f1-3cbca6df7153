import { UAParser } from "ua-parser-js";
import { supabase } from "@/lib/supabase";
import { createLocalizedTeacherNotification, createLocalizedAdminNotification } from "./notification-localization";

interface DeviceInfo {
  browser: string;
  os: string;
  device: string;
  userAgent: string;
}

export function getDeviceFingerprint(): DeviceInfo {
  const parser = new UAParser();
  const result = parser.getResult();

  return {
    browser: `${result.browser.name} ${result.browser.version}`,
    os: `${result.os.name} ${result.os.version}`,
    device: result.device.model || "Unknown",
    userAgent: navigator.userAgent,
  };
}

export async function isLocationSpoofed(
  latitude: number,
  longitude: number
): Promise<boolean> {
  // Check if location accuracy is suspiciously perfect
  if (Number.isInteger(latitude) && Number.isInteger(longitude)) {
    return true;
  }

  // Check if location is obviously invalid
  if (Math.abs(latitude) > 90 || Math.abs(longitude) > 180) {
    return true;
  }

  return false;
}

export async function checkConcurrentSessions(
  studentId: string,
  roomId?: string
): Promise<boolean> {
  const fiveMinutesAgo = new Date();
  fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5);

  // Build the query
  let query = supabase
    .from("attendance_records")
    .select("*")
    .eq("student_id", studentId)
    .gte("timestamp", fiveMinutesAgo.toISOString());

  // If roomId is provided, check for records in different rooms
  if (roomId) {
    query = query.neq("room_id", roomId);
  }

  // Execute the query
  const { data: recentRecords, error } = await query;

  if (error) {
    console.error("Error checking concurrent sessions:", error);
    return false;
  }

  return recentRecords && recentRecords.length > 0;
}

export async function isDeviceConsistent(
  studentId: string,
  currentDevice: DeviceInfo
): Promise<boolean> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Get previous attendance records from today
  const { data: records, error } = await supabase
    .from("attendance_records")
    .select("device_info")
    .eq("student_id", studentId)
    .gte("timestamp", today.toISOString())
    .order("timestamp", { ascending: false })
    .limit(1);

  if (error || !records || records.length === 0) {
    return true; // No previous records today, so consider it consistent
  }

  try {
    // Handle both string and object device_info
    let previousDevice: any;

    if (typeof records[0].device_info === "string") {
      try {
        // Try to parse as JSON
        previousDevice = JSON.parse(records[0].device_info);
      } catch (parseError) {
        // If it's not valid JSON, just use the string for comparison
        console.log("Device info is not JSON, using string comparison");
        return records[0].device_info === currentDevice;
      }
    } else {
      previousDevice = records[0].device_info;
    }

    // If we have a parsed object, compare critical properties
    if (typeof previousDevice === "object" && previousDevice !== null) {
      // Check if it has the expected properties
      if (
        "os" in previousDevice &&
        "browser" in previousDevice &&
        "device" in previousDevice
      ) {
        return (
          previousDevice.os === currentDevice.os &&
          previousDevice.browser === currentDevice.browser &&
          previousDevice.device === currentDevice.device
        );
      }
    }

    // If we can't do a structured comparison, do a simple string comparison
    return String(records[0].device_info) === String(currentDevice);
  } catch (error) {
    console.error("Error comparing device info:", error);
    return true; // If we can't compare the device info, assume it's consistent
  }
}

export async function hasReachedRateLimit(
  studentId: string,
  roomId?: string
): Promise<boolean> {
  const fiveMinutesAgo = new Date();
  fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5);

  // Build the query
  let query = supabase
    .from("attendance_records")
    .select("*")
    .eq("student_id", studentId)
    .gte("timestamp", fiveMinutesAgo.toISOString());

  // If roomId is provided, filter by room
  if (roomId) {
    query = query.eq("room_id", roomId);
  }

  // Execute the query
  const { data: recentAttempts, error } = await query;

  if (error) {
    console.error("Error checking rate limit:", error);
    return false;
  }

  // Limit to 5 attempts per 5 minutes
  return recentAttempts && recentAttempts.length >= 5;
}

// Calculate distance between two points using Haversine formula
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

export async function createLocationAlert(
  studentId: string,
  studentLat: number,
  studentLng: number,
  roomLat: number,
  roomLng: number,
  distance: number,
  roomName: string,
  allowedRadius?: number,
  roomId?: string
): Promise<void> {
  try {
    // Get student info to find their teacher
    const { data: studentProfile, error: studentError } = await supabase
      .from("profiles")
      .select("name, room_id, school_id")
      .eq("user_id", studentId)
      .single();

    if (studentError) {
      console.error("Error getting student profile for location alert:", studentError);
      return;
    }

    // Get ALL teachers in the same school (not just one)
    const { data: allTeachers, error: teachersError } = await supabase
      .from("profiles")
      .select("user_id")
      .eq("role", "teacher")
      .eq("school_id", studentProfile.school_id);

    if (teachersError) {
      console.error("Error getting teachers for location alert:", teachersError);
      return;
    }

    // Get all school admins for this specific school only
    const { data: schoolAdmins, error: adminsError } = await supabase
      .from("profiles")
      .select("user_id")
      .eq("role", "admin")
      .eq("school_id", studentProfile.school_id);

    if (adminsError) {
      console.error("Error getting school admins for location alert:", adminsError);
      return; // Exit early if we can't get school admins
    }

    console.log(`Found ${schoolAdmins?.length || 0} school admins for school ${studentProfile.school_id}`);

    // Calculate how far outside the radius the student is
    const excessDistance = allowedRadius ? Math.round(distance - allowedRadius) : Math.round(distance);
    const distanceDisplay = distance >= 1000
      ? `${(distance / 1000).toFixed(1)}km`
      : `${Math.round(distance)}m`;

    // Create Google Maps link for teacher to see student location
    const mapsLink = `https://www.google.com/maps?q=${studentLat},${studentLng}&z=16`;
    const directionsLink = `https://www.google.com/maps/dir/${roomLat},${roomLng}/${studentLat},${studentLng}`;

    // Create shared metadata for all notifications
    const sharedMetadata = {
      student_name: studentProfile.name,
      student_location: { latitude: studentLat, longitude: studentLng },
      room_location: { latitude: roomLat, longitude: roomLng },
      distance_meters: Math.round(distance),
      allowed_radius: allowedRadius || 0,
      excess_distance: excessDistance,
      room_name: roomName,
      alert_type: "location_violation",
      maps_link: mapsLink,
      directions_link: directionsLink,
      distance_display: distanceDisplay,
    };

    // Create notifications for ALL teachers in the school (each in their preferred language)
    if (allTeachers && allTeachers.length > 0) {
      for (const teacher of allTeachers) {
        try {
          const notificationResult = await createLocalizedTeacherNotification({
            teacherId: teacher.user_id,
            studentId: studentId,
            type: "distance_alert",
            templateKey: "studentLocationAlert",
            templateParams: [studentProfile.name, Math.round(distance), roomName],
            metadata: sharedMetadata,
            roomNumber: roomName,
          });

          if (!notificationResult.success) {
            console.error(
              `Error creating localized location alert notification for teacher ${teacher.user_id}:`,
              notificationResult.error
            );
          }
        } catch (error) {
          console.error(`Error sending notification to teacher ${teacher.user_id}:`, error);
        }
      }
      console.log(`✅ Created ${allTeachers.length} teacher notifications for school ${studentProfile.school_id}`);
    }

    // Create notifications for ALL school admins (each in their preferred language)
    if (schoolAdmins && schoolAdmins.length > 0) {
      for (const admin of schoolAdmins) {
        try {
          const adminNotificationResult = await createLocalizedAdminNotification({
            adminId: admin.user_id,
            studentId: studentId,
            type: "distance_alert",
            templateKey: "studentLocationAlert",
            templateParams: [studentProfile.name, Math.round(distance), roomName],
            metadata: sharedMetadata,
            roomNumber: roomName,
          });

          if (!adminNotificationResult.success) {
            console.error(
              `Error creating localized location alert notification for admin ${admin.user_id}:`,
              adminNotificationResult.error
            );
          }
        } catch (error) {
          console.error(`Error sending notification to admin ${admin.user_id}:`, error);
        }
      }
      console.log(`✅ Created ${schoolAdmins.length} admin notifications for school ${studentProfile.school_id}`);
    }

    // Then create a detailed alert record
    const { error } = await supabase.from("attendance_alerts").insert([
      {
        student_id: studentId,
        room_id: roomId || studentProfile.room_id, // Use provided roomId or fallback to student's room
        alert_type: "location_mismatch",
        distance_meters: Math.round(distance),
        student_location: { latitude: studentLat, longitude: studentLng },
        room_location: {
          latitude: roomLat,
          longitude: roomLng,
          name: roomName,
        },
        status: "pending",
        created_at: new Date().toISOString(),
        timestamp: new Date().toISOString(),
      },
    ]);

    if (error) {
      console.error("Error creating location alert record:", error);
    }
  } catch (error) {
    console.error("Error in createLocationAlert:", error);
  }
}
