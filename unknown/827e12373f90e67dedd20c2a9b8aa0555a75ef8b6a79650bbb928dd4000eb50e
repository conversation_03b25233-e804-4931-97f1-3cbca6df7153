import LoginForm from "@/components/auth/LoginForm";
import Navbar from "@/components/shared/Navbar";
import { useBranding } from "@/hooks/useBranding";
import { LoginLogo } from "@/components/ui/logo";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";

export default function Login() {
  const { uiText } = useBranding();
  const { t } = useTranslation();

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          {/* Back to Landing Page Button */}
          <div className="mb-6">
            <Button
              variant="ghost"
              size="sm"
              asChild
              className="text-muted-foreground hover:text-primary"
            >
              <Link to="/" className="flex items-center gap-2">
                <ArrowLeft size={16} />
                {t('common.backToHome')}
              </Link>
            </Button>
          </div>

          <div className="text-center mb-6">
            <LoginLogo className="mb-4" />
            <h1 className="text-2xl font-bold text-primary">
              {uiText.LOGIN_TITLE}
            </h1>
          </div>
          <LoginForm />
        </div>
      </div>
    </div>
  );
}
