// Supabase Edge Function to send push notifications
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PushNotificationRequest {
  user_id: string;
  title: string;
  body: string;
  type: string;
  data?: any;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const { user_id, title, body, type, data }: PushNotificationRequest = await req.json()

    // Get user's push subscription
    const { data: subscription, error: subError } = await supabaseClient
      .from('push_subscriptions')
      .select('*')
      .eq('user_id', user_id)
      .single()

    if (subError || !subscription) {
      console.log('No push subscription found for user:', user_id)
      return new Response(
        JSON.stringify({ success: false, error: 'No push subscription found' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404 
        }
      )
    }

    // Prepare push notification payload
    const notificationPayload = {
      title,
      body,
      icon: '/android-chrome-192x192.png',
      badge: '/android-chrome-192x192.png',
      tag: `notification-${Date.now()}`,
      requireInteraction: type === 'attendance_reminder',
      vibrate: type === 'attendance_reminder' ? [300, 100, 300, 100, 300] : [200, 100, 200],
      silent: false,
      renotify: type === 'attendance_reminder',
      actions: type === 'attendance_reminder' ? [
        {
          action: 'check-in',
          title: 'Check In Now'
        },
        {
          action: 'view',
          title: 'View Details'
        }
      ] : [],
      data: {
        type,
        timestamp: new Date().toISOString(),
        ...data
      }
    }

    // Send push notification using Web Push Protocol
    // Note: In a real implementation, you would use a library like 'web-push'
    // For now, we'll simulate the push notification
    
    // Log the notification attempt
    const { error: logError } = await supabaseClient
      .from('push_notification_logs')
      .insert({
        user_id,
        type,
        title,
        message: body,
        status: 'sent',
        sent_at: new Date().toISOString()
      })

    if (logError) {
      console.error('Error logging notification:', logError)
    }

    // In a real implementation, you would send the actual push notification here
    // using the subscription endpoint, p256dh_key, and auth_key
    console.log('Push notification would be sent to:', subscription.endpoint)
    console.log('Notification payload:', notificationPayload)

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Push notification sent successfully',
        subscription_endpoint: subscription.endpoint
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Error sending push notification:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Internal server error' 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
