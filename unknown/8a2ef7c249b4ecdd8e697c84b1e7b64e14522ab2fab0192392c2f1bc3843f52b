import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocation } from 'react-router-dom';

type GreetingType = 'welcomeBack' | 'firstTime' | 'newUser' | 'pageReload';
type UserRole = 'student' | 'teacher' | 'admin';

interface UseGreetingNotificationReturn {
  showGreeting: boolean;
  greetingType: GreetingType;
  userRole: UserRole;
  userName: string;
  currentPage: string;
  hideGreeting: () => void;
}

export function useGreetingNotification(): UseGreetingNotificationReturn {
  const [showGreeting, setShowGreeting] = useState(false);
  const [greetingType, setGreetingType] = useState<GreetingType>('welcomeBack');
  const [userRole, setUserRole] = useState<UserRole>('student');
  const [userName, setUserName] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<string>('');
  const { user, profile } = useAuth();
  const location = useLocation();
  const hasProcessedGreeting = useRef(false);
  const lastPathname = useRef(location.pathname);

  // Helper function to get page context
  const getPageContext = (pathname: string): string => {
    if (pathname.includes('/dashboard')) return 'dashboard';
    if (pathname.includes('/attendance')) return 'attendance';
    if (pathname.includes('/qr-scanner')) return 'qrScanner';
    if (pathname.includes('/dormitory')) return 'dormitory';
    if (pathname.includes('/social-media')) return 'socialMedia';
    if (pathname.includes('/biometric')) return 'biometric';
    if (pathname.includes('/profile')) return 'profile';
    if (pathname.includes('/settings')) return 'settings';
    if (pathname.includes('/admin')) return 'admin';
    if (pathname.includes('/teacher')) return 'teacher';
    if (pathname.includes('/student')) return 'student';
    return 'general';
  };

  useEffect(() => {
    if (!user || !profile) return;

    // Reset processing flag if the page has changed
    if (lastPathname.current !== location.pathname) {
      hasProcessedGreeting.current = false;
      lastPathname.current = location.pathname;
    }

    if (hasProcessedGreeting.current) return;

    // Get user role and name from profile
    const role = (profile.role || 'student') as UserRole;
    const name = profile.name || user.email?.split('@')[0] || 'User';
    const pageContext = getPageContext(location.pathname);

    setUserRole(role);
    setUserName(name);
    setCurrentPage(pageContext);



    // Check when the user last saw a greeting
    const lastGreetingTime = localStorage.getItem(`ai_greeting_last_shown_${user.id}`);

    const currentTime = Date.now();

    // Check session state
    const sessionExists = sessionStorage.getItem('ai_assistant_session');

    // Detect if this is an actual page reload using performance API
    // performance.navigation.type: 1 = reload, 0 = navigate, 2 = back/forward
    const isActualPageReload = performance.navigation?.type === 1 ||
                              (performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming)?.type === 'reload';

    // Determine if this is a fresh login (no session exists)
    const isFirstTimeInSession = !sessionExists;

    // For login greetings: Show if never shown before OR last shown more than 4 hours ago OR fresh session
    const shouldShowLoginGreeting = !lastGreetingTime ||
                                   (currentTime - parseInt(lastGreetingTime)) > (4 * 60 * 60 * 1000) ||
                                   isFirstTimeInSession;

    // For page reload greetings: ONLY show on actual page reloads (F5/Ctrl+R) when session exists and no login greeting needed
    const shouldShowPageReloadGreeting = isActualPageReload && sessionExists && !shouldShowLoginGreeting;



    // Determine which greeting to show
    if (shouldShowLoginGreeting) {
      // Mark that we've processed this greeting to prevent multiple executions
      hasProcessedGreeting.current = true;

      // Mark session as active and record session greeting
      sessionStorage.setItem('ai_assistant_session', 'true');
      sessionStorage.setItem(`ai_session_greeting_${user.id}`, 'login');

      // Determine greeting type based on user data and visit history
      const determineLoginGreetingType = (): GreetingType => {
        // Check if user was created recently (within last 7 days for new user greeting)
        const userCreatedAt = user.created_at ? new Date(user.created_at) : new Date();
        const now = new Date();
        const daysSinceCreation = (now.getTime() - userCreatedAt.getTime()) / (1000 * 60 * 60 * 24);

        // Check if user has ever seen the AI assistant greeting before
        const hasSeenAIGreeting = localStorage.getItem(`ai_greeting_seen_${user.id}`);

        if (!hasSeenAIGreeting) {
          // First time ever seeing the AI assistant
          localStorage.setItem(`ai_greeting_seen_${user.id}`, 'true');

          if (daysSinceCreation < 7) {
            return 'newUser'; // New user (within 7 days), first time seeing AI
          } else {
            return 'firstTime'; // Existing user, first time seeing AI
          }
        } else {
          // Check last visit time for contextual greeting
          const lastVisitTime = localStorage.getItem(`last_visit_${user.id}`);
          const currentTime = now.getTime();

          if (lastVisitTime) {
            const hoursSinceLastVisit = (currentTime - parseInt(lastVisitTime)) / (1000 * 60 * 60);

            // If it's been more than 24 hours, show a more welcoming message
            if (hoursSinceLastVisit > 24) {
              return 'firstTime'; // Long time no see
            }
          }

          // Store current visit time
          localStorage.setItem(`last_visit_${user.id}`, currentTime.toString());

          return 'welcomeBack'; // Regular returning user
        }
      };

      const type = determineLoginGreetingType();
      setGreetingType(type);

      // Show login greeting after a short delay
      const timer = setTimeout(() => {

        setShowGreeting(true);
        // Record when we showed the greeting (after it's actually shown)
        localStorage.setItem(`ai_greeting_last_shown_${user.id}`, Date.now().toString());
      }, 2000); // Show after 2 seconds

      // Auto-hide after 30 seconds as requested
      const autoHideTimer = setTimeout(() => {

        setShowGreeting(false);
      }, 32000); // 30 seconds + 2 seconds initial delay

      return () => {
        clearTimeout(timer);
        clearTimeout(autoHideTimer);
      };

    } else if (shouldShowPageReloadGreeting) {
      // Mark that we've processed this greeting to prevent multiple executions
      hasProcessedGreeting.current = true;

      // Set page reload greeting type
      setGreetingType('pageReload');

      // Show page reload greeting after a shorter delay
      const timer = setTimeout(() => {

        setShowGreeting(true);
      }, 1000); // Show after 1 second for page reloads

      // Auto-hide after 20 seconds for page reload greetings (shorter)
      const autoHideTimer = setTimeout(() => {

        setShowGreeting(false);
      }, 21000); // 20 seconds + 1 second initial delay

      return () => {
        clearTimeout(timer);
        clearTimeout(autoHideTimer);
      };
    }
  }, [user, profile, location.pathname]);

  const hideGreeting = () => {
    setShowGreeting(false);
  };

  return {
    showGreeting,
    greetingType,
    userRole,
    userName,
    currentPage,
    hideGreeting
  };
}
