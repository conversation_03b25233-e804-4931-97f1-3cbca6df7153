/**
 * 🤖 React Hook for Automatic QR Generation
 * 
 * This hook manages the automatic QR generation service and provides
 * status information and controls for the React application.
 */

import { useState, useEffect, useCallback } from "react";
import { automaticQRService } from "@/lib/services/automatic-qr-service";
import { useAuth } from "@/context/AuthContext";
import { toast } from "@/lib/utils/toast";
import { useTranslation } from "react-i18next";

interface AutoQRStatus {
  isInitialized: boolean;
  isActive: boolean;
  activeSessions: number;
  lastUpdate: Date | null;
  error: string | null;
}

interface SchoolSession {
  schoolId: string;
  isActive: boolean;
  startTime: string;
  endTime: string;
  roomCount: number;
}

export function useAutomaticQR() {
  const [status, setStatus] = useState<AutoQRStatus>({
    isInitialized: false,
    isActive: false,
    activeSessions: 0,
    lastUpdate: null,
    error: null,
  });

  const [sessions, setSessions] = useState<SchoolSession[]>([]);
  const [loading, setLoading] = useState(true);

  const { profile } = useAuth();
  const { t } = useTranslation();

  /**
   * Initialize the automatic QR service
   */
  const initializeService = useCallback(async () => {
    try {
      setLoading(true);
      // Initializing Automatic QR Service from React

      await automaticQRService.initialize();

      setStatus(prev => ({
        ...prev,
        isInitialized: true,
        error: null,
        lastUpdate: new Date(),
      }));

      // Automatic QR Service initialized successfully

      toast.translateSuccess(
        t,
        "admin.automaticQR.serviceStarted",
        "admin.automaticQR.serviceStartedDescription"
      );
    } catch (error) {
      console.error("❌ Failed to initialize Automatic QR Service:", error);
      
      setStatus(prev => ({
        ...prev,
        isInitialized: false,
        error: error instanceof Error ? error.message : "Unknown error",
        lastUpdate: new Date(),
      }));

      toast.translateError(
        t,
        "admin.automaticQR.initializationFailed",
        "admin.automaticQR.initializationFailedDescription"
      );
    } finally {
      setLoading(false);
    }
  }, [t]);

  /**
   * Update status from service
   */
  const updateStatus = useCallback(() => {
    try {
      const isInitialized = automaticQRService.isServiceInitialized();
      const activeSessions = automaticQRService.getAllActiveSessions();

      setStatus(prev => ({
        ...prev,
        isInitialized,
        isActive: activeSessions.length > 0,
        activeSessions: activeSessions.length,
        lastUpdate: new Date(),
        error: null,
      }));

      // Update sessions list
      const sessionList: SchoolSession[] = activeSessions.map(session => ({
        schoolId: session.schoolId,
        isActive: session.isActive,
        startTime: session.startTime,
        endTime: session.endTime,
        roomCount: session.rooms.length,
      }));

      setSessions(sessionList);
    } catch (error) {
      console.error("❌ Error updating status:", error);
      setStatus(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Status update failed",
        lastUpdate: new Date(),
      }));
    }
  }, []);

  /**
   * Manually trigger QR generation for current school
   */
  const manualTrigger = useCallback(async () => {
    if (!profile?.school_id) {
      toast.translateError(
        t,
        "common.error",
        "admin.automaticQR.noSchoolId"
      );
      return;
    }

    try {
      console.log(`🔄 Manually triggering QR generation for school ${profile.school_id}`);
      
      await automaticQRService.manualTrigger(profile.school_id);
      
      toast.translateSuccess(
        t,
        "admin.automaticQR.generationTriggered",
        "admin.automaticQR.generationTriggeredDescription"
      );

      updateStatus();
    } catch (error) {
      console.error("❌ Manual trigger failed:", error);
      
      toast.error(
        t("admin.automaticQR.manualTriggerFailed"),
        {
          description: error instanceof Error ? error.message : t("common.unknownError")
        }
      );
    }
  }, [profile?.school_id, t, updateStatus]);

  /**
   * Get session status for current school
   */
  const getCurrentSchoolSession = useCallback(() => {
    if (!profile?.school_id) return null;
    
    return automaticQRService.getSessionStatus(profile.school_id);
  }, [profile?.school_id]);

  /**
   * Stop the automatic QR service (WARNING: This stops the global service)
   */
  const stopService = useCallback(() => {
    try {
      console.warn("⚠️ Stopping global automatic QR service from admin interface");
      automaticQRService.stop();

      setStatus({
        isInitialized: false,
        isActive: false,
        activeSessions: 0,
        lastUpdate: new Date(),
        error: null,
      });

      setSessions([]);

      toast.translateSuccess(
        t,
        "admin.automaticQR.serviceStopped",
        "admin.automaticQR.serviceStoppedDescription"
      );
    } catch (error) {
      console.error("❌ Error stopping service:", error);

      toast.error(
        t("admin.automaticQR.errorStoppingService"),
        {
          description: error instanceof Error ? error.message : t("common.unknownError")
        }
      );
    }
  }, [t]);

  /**
   * Check if current school has active session
   */
  const isCurrentSchoolActive = useCallback(() => {
    if (!profile?.school_id) return false;
    
    return sessions.some(session => 
      session.schoolId === profile.school_id && session.isActive
    );
  }, [profile?.school_id, sessions]);

  // Check if service is already initialized globally on mount
  useEffect(() => {
    // Since the service is now initialized globally in App.tsx,
    // we just need to check its status and update our local state
    updateStatus();
  }, [updateStatus]);

  // Update status periodically (service is initialized globally)
  useEffect(() => {
    const interval = setInterval(updateStatus, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [updateStatus]);

  // Note: Service cleanup is handled globally in App.tsx, not here

  return {
    // Status
    status,
    sessions,
    loading,
    
    // Current school info
    currentSchoolSession: getCurrentSchoolSession(),
    isCurrentSchoolActive: isCurrentSchoolActive(),
    
    // Actions
    initializeService,
    manualTrigger,
    stopService,
    updateStatus,
    
    // Computed values
    isServiceRunning: status.isInitialized && status.isActive,
    hasActiveSessions: status.activeSessions > 0,
  };
}
