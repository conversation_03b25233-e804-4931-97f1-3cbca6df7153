-- Migration to internationalize biometric deletion request notifications
-- This updates the approve_biometric_deletion_request and reject_biometric_deletion_request functions
-- to send localized notifications based on the student's language preference

-- Step 1: Create helper function to get user's language preference
CREATE OR REPLACE FUNCTION public.get_user_language(user_id_param UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_language TEXT;
BEGIN
    -- Get user's preferred language from their profile
    SELECT preferred_language INTO user_language
    FROM public.profiles
    WHERE user_id = user_id_param;
    
    -- Default to English if no preference is set or user not found
    IF user_language IS NULL OR user_language = '' THEN
        RETURN 'en';
    END IF;
    
    -- Only support 'en' and 'tr' for now
    IF user_language = 'tr' THEN
        RETURN 'tr';
    ELSE
        RETURN 'en';
    END IF;
END;
$$;

-- Step 2: Update approve_biometric_deletion_request function with internationalized notifications
CREATE OR REPLACE FUNCTION public.approve_biometric_deletion_request(
    request_id UUID,
    admin_notes_param TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    request_user_id UUID;
    request_school_id UUID;
    admin_profile_record RECORD;
    user_language TEXT;
    notification_title TEXT;
    notification_message TEXT;
BEGIN
    -- Get the admin's profile to verify they're an admin and get their school
    SELECT * INTO admin_profile_record
    FROM public.profiles
    WHERE user_id = auth.uid() AND role = 'admin';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Only school admins can approve biometric deletion requests';
    END IF;

    -- Get the request details and verify it belongs to the admin's school
    SELECT user_id, school_id INTO request_user_id, request_school_id
    FROM public.biometric_deletion_requests
    WHERE id = request_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Deletion request not found or already processed';
    END IF;
    
    -- Verify the request belongs to the admin's school
    IF request_school_id != admin_profile_record.school_id THEN
        RAISE EXCEPTION 'You can only approve deletion requests for your school';
    END IF;

    -- Update the request status to approved
    UPDATE public.biometric_deletion_requests
    SET 
        status = 'approved',
        admin_notes = admin_notes_param,
        approved_by = auth.uid(),
        approved_at = NOW(),
        updated_at = NOW()
    WHERE id = request_id;

    -- Delete the user's biometric credentials
    DELETE FROM public.biometric_credentials
    WHERE user_id = request_user_id;

    -- Get user's language preference
    user_language := public.get_user_language(request_user_id);
    
    -- Set localized notification content
    IF user_language = 'tr' THEN
        notification_title := 'Biyometrik Silme Onaylandı';
        notification_message := 'Biyometrik silme talebiniz onaylandı. Biyometrik kimlik bilgileriniz kalıcı olarak silindi.';
    ELSE
        notification_title := 'Biometric Deletion Approved';
        notification_message := 'Your biometric deletion request has been approved. Your biometric credentials have been permanently deleted.';
    END IF;

    -- Create a localized notification for the student
    INSERT INTO public.notifications (
        type,
        title,
        message,
        student_id,
        metadata
    ) VALUES (
        'system',
        notification_title,
        notification_message,
        (SELECT id FROM public.profiles WHERE user_id = request_user_id),
        jsonb_build_object(
            'request_id', request_id,
            'admin_notes', admin_notes_param
        )
    );
END;
$$;

-- Step 3: Update reject_biometric_deletion_request function with internationalized notifications
CREATE OR REPLACE FUNCTION public.reject_biometric_deletion_request(
    request_id UUID,
    admin_notes_param TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    request_user_id UUID;
    request_school_id UUID;
    admin_profile_record RECORD;
    user_language TEXT;
    notification_title TEXT;
    notification_message TEXT;
BEGIN
    -- Get the admin's profile to verify they're an admin and get their school
    SELECT * INTO admin_profile_record
    FROM public.profiles
    WHERE user_id = auth.uid() AND role = 'admin';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Only school admins can reject biometric deletion requests';
    END IF;

    -- Get the request details and verify it belongs to the admin's school
    SELECT user_id, school_id INTO request_user_id, request_school_id
    FROM public.biometric_deletion_requests
    WHERE id = request_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Deletion request not found or already processed';
    END IF;
    
    -- Verify the request belongs to the admin's school
    IF request_school_id != admin_profile_record.school_id THEN
        RAISE EXCEPTION 'You can only reject deletion requests for your school';
    END IF;

    -- Update the request status to rejected
    UPDATE public.biometric_deletion_requests
    SET 
        status = 'rejected',
        admin_notes = admin_notes_param,
        rejected_by = auth.uid(),
        rejected_at = NOW(),
        updated_at = NOW()
    WHERE id = request_id;

    -- Get user's language preference
    user_language := public.get_user_language(request_user_id);
    
    -- Set localized notification content
    IF user_language = 'tr' THEN
        notification_title := 'Biyometrik Silme Reddedildi';
        IF admin_notes_param IS NOT NULL AND admin_notes_param != '' THEN
            notification_message := 'Biyometrik silme talebiniz reddedildi. Sebep: ' || admin_notes_param;
        ELSE
            notification_message := 'Biyometrik silme talebiniz reddedildi.';
        END IF;
    ELSE
        notification_title := 'Biometric Deletion Rejected';
        IF admin_notes_param IS NOT NULL AND admin_notes_param != '' THEN
            notification_message := 'Your biometric deletion request has been rejected. Reason: ' || admin_notes_param;
        ELSE
            notification_message := 'Your biometric deletion request has been rejected.';
        END IF;
    END IF;

    -- Create a localized notification for the student
    INSERT INTO public.notifications (
        type,
        title,
        message,
        student_id,
        metadata
    ) VALUES (
        'system',
        notification_title,
        notification_message,
        (SELECT id FROM public.profiles WHERE user_id = request_user_id),
        jsonb_build_object(
            'request_id', request_id,
            'admin_notes', admin_notes_param
        )
    );
END;
$$;

-- Step 4: Grant execute permissions on the helper function
GRANT EXECUTE ON FUNCTION public.get_user_language(UUID) TO authenticated;

-- Step 5: Grant execute permissions on the updated RPC functions
GRANT EXECUTE ON FUNCTION public.approve_biometric_deletion_request(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.reject_biometric_deletion_request(UUID, TEXT) TO authenticated;
