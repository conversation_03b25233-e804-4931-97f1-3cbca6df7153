/**
 * 🎨 Logo Component
 * =====================================================
 * Consistent logo component that matches favicon design
 * Supports different variants and sizes
 */

import { cn } from "@/lib/utils";
import { useBranding } from "@/hooks/useBranding";
import { useTranslation } from "react-i18next";

interface LogoProps {
  variant?: "square" | "horizontal" | "white";
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
  showText?: boolean;
}

const sizeClasses = {
  sm: "w-10 h-10 sm:w-8 sm:h-8",
  md: "w-14 h-14 sm:w-12 sm:h-12",
  lg: "w-18 h-18 sm:w-16 sm:h-16",
  xl: "w-28 h-28 sm:w-24 sm:h-24",
};

const horizontalSizeClasses = {
  sm: "h-8 w-auto sm:h-6 sm:w-auto",
  md: "h-10 w-auto sm:h-8 sm:w-auto",
  lg: "h-12 w-auto sm:h-10 sm:w-auto",
  xl: "h-14 w-auto sm:h-12 sm:w-auto",
};

export function Logo({
  variant = "square",
  size = "md",
  className,
  showText = false,
}: LogoProps) {
  const { branding } = useBranding();
  const { t } = useTranslation();

  const getLogoSrc = () => {
    switch (variant) {
      case "horizontal":
        return "/logo-horizontal.svg";
      case "white":
        return "/logo-white.svg";
      default:
        return "/logo.svg";
    }
  };

  const getSizeClass = () => {
    if (variant === "horizontal") {
      return horizontalSizeClasses[size];
    }
    return sizeClasses[size];
  };

  return (
    <div className={cn("flex items-center gap-3", className)}>
      <img
        src={getLogoSrc()}
        alt={`${branding.APP_NAME} Logo`}
        className={cn(getSizeClass(), "flex-shrink-0")}
      />
      {showText && variant !== "horizontal" && (
        <div className="flex flex-col">
          <span className="font-medium text-primary text-xs leading-tight">
            {branding.APP_NAME}
          </span>
          <span className="text-xs text-muted-foreground">
            {t("app.tagline")}
          </span>
        </div>
      )}
    </div>
  );
}

// Preset logo components for common use cases
export function NavbarLogo({ className }: { className?: string }) {
  return <Logo variant="square" size="md" className={className} />;
}

export function LoginLogo({ className }: { className?: string }) {
  return (
    <Logo
      variant="square"
      size="xl"
      className={cn("justify-center", className)}
    />
  );
}

// Note: LoadingLogo removed - use regular spinner for loading states

export function FooterLogo({ className }: { className?: string }) {
  return (
    <Logo variant="square" size="sm" showText={true} className={className} />
  );
}
