import React from 'react';
import { render, screen } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../i18n/i18n';
import TeacherDashboard from '../components/teacher/Dashboard';
import { AuthProvider } from '../context/AuthContext';
import { ThemeProvider } from '../context/ThemeContext';

// Mock the AuthContext
jest.mock('../context/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useAuth: () => ({
    profile: {
      id: 'test-id',
      name: 'Test Teacher',
      role: 'teacher',
    },
  }),
}));

// Mock the supabase client
jest.mock('../lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    then: jest.fn().mockResolvedValue({ data: [], error: null }),
    channel: jest.fn().mockReturnThis(),
    on: jest.fn().mockReturnThis(),
    subscribe: jest.fn(),
    removeChannel: jest.fn(),
    rpc: jest.fn().mockReturnThis(),
  },
}));

describe('Internationalization Tests', () => {
  beforeEach(() => {
    // Reset language to English before each test
    i18n.changeLanguage('en');
  });

  test('Teacher Dashboard renders in English by default', async () => {
    render(
      <I18nextProvider i18n={i18n}>
        <ThemeProvider>
          <TeacherDashboard />
        </ThemeProvider>
      </I18nextProvider>
    );

    // Check for English text
    expect(screen.getByText('Loading Students')).toBeInTheDocument();
  });

  test('Teacher Dashboard renders in Turkish when language is set to Turkish', async () => {
    // Change language to Turkish
    i18n.changeLanguage('tr');

    render(
      <I18nextProvider i18n={i18n}>
        <ThemeProvider>
          <TeacherDashboard />
        </ThemeProvider>
      </I18nextProvider>
    );

    // Check for Turkish text
    expect(screen.getByText('Öğrenciler Yükleniyor')).toBeInTheDocument();
  });
});
