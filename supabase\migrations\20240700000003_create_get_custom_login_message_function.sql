-- Create a function to get the custom login message
CREATE OR REPLACE FUNCTION public.get_custom_login_message()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  message TEXT;
BEGIN
  -- First check for a global override
  SELECT 
    CASE 
      WHEN jsonb_typeof(setting_value) = 'string' THEN setting_value::TEXT
      WHEN jsonb_typeof(setting_value) = 'object' AND setting_value ? 'value' THEN setting_value->>'value'
      ELSE setting_value::TEXT
    END INTO message
  FROM system_settings_overrides
  WHERE setting_name = 'custom_login_message'
    AND override_enabled = true
    AND applies_to_all = true
  LIMIT 1;
  
  -- If no global override, check for any school setting
  IF message IS NULL THEN
    SELECT 
      CASE 
        WHEN jsonb_typeof(custom_login_message::jsonb) = 'string' THEN custom_login_message::TEXT
        WHEN jsonb_typeof(custom_login_message::jsonb) = 'object' AND custom_login_message::jsonb ? 'value' THEN custom_login_message::jsonb->>'value'
        ELSE custom_login_message::TEXT
      END INTO message
    FROM school_settings
    WHERE custom_login_message IS NOT NULL
    LIMIT 1;
  END IF;
  
  RETURN message;
END;
$$;
