import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import SecondaryNavigation from "./navigation/SecondaryNavigation";
import {
  Shield,
  AlertTriangle,
  Lock,
  Key,
  Download,
  Upload,
  Database,
  RefreshCw,
  Copy,
  Check,
  Eye,
  EyeOff,
  Trash2,
} from "lucide-react";
import { format } from "date-fns";

import DatabaseCleanup from "./DatabaseCleanup";
import SystemAdminCodeManager from "@/components/admin/SystemAdminCodeManager";

export default function SecurityCenter() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("admin-code");

  // Secondary navigation items
  const navItems = [
    {
      id: "admin-code",
      label: "System Admin Code",
      icon: <Key className="h-4 w-4" />,
    },
    {
      id: "security-policy",
      label: "Security Policy",
      icon: <Shield className="h-4 w-4" />,
    },
    {
      id: "authentication",
      label: "Authentication",
      icon: <Lock className="h-4 w-4" />,
    },
    {
      id: "database-backup",
      label: "Database Backup",
      icon: <Database className="h-4 w-4" />,
    },
    {
      id: "database-cleanup",
      label: "Database Cleanup",
      icon: <Trash2 className="h-4 w-4" />,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Security Center</h2>
      </div>

      <SecondaryNavigation
        items={navItems}
        activeItem={activeTab}
        onItemChange={setActiveTab}
        className="mt-4"
      >
        {{
          "admin-code": <SystemAdminCodeManager />,
          "security-policy": (
            <Card>
              <CardHeader>
                <CardTitle>Security Policy Configuration</CardTitle>
                <CardDescription>
                  Configure system-wide security policies
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2 border p-4 rounded-md">
                    <h3 className="text-lg font-medium">Password Policy</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="min-password-length">
                            Minimum Password Length
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Minimum number of characters required
                          </p>
                        </div>
                        <Input
                          id="min-password-length"
                          type="number"
                          className="w-20"
                          defaultValue="8"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="require-special-chars">
                            Require Special Characters
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Require at least one special character
                          </p>
                        </div>
                        <Switch id="require-special-chars" defaultChecked />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="require-numbers">
                            Require Numbers
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Require at least one number
                          </p>
                        </div>
                        <Switch id="require-numbers" defaultChecked />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="require-uppercase">
                            Require Uppercase
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Require at least one uppercase letter
                          </p>
                        </div>
                        <Switch id="require-uppercase" defaultChecked />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2 border p-4 rounded-md">
                    <h3 className="text-lg font-medium">Account Security</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="max-login-attempts">
                            Maximum Login Attempts
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Before temporary lockout
                          </p>
                        </div>
                        <Input
                          id="max-login-attempts"
                          type="number"
                          className="w-20"
                          defaultValue="5"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="lockout-duration">
                            Lockout Duration (minutes)
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Time before allowing login attempts again
                          </p>
                        </div>
                        <Input
                          id="lockout-duration"
                          type="number"
                          className="w-20"
                          defaultValue="15"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="session-timeout">
                            Session Timeout (minutes)
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Inactive time before automatic logout
                          </p>
                        </div>
                        <Input
                          id="session-timeout"
                          type="number"
                          className="w-20"
                          defaultValue="30"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-2">Data Protection</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="data-retention">
                          Data Retention Period (days)
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          How long to keep audit logs and activity data
                        </p>
                      </div>
                      <Input
                        id="data-retention"
                        type="number"
                        className="w-20"
                        defaultValue="90"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="encrypt-sensitive-data">
                          Encrypt Sensitive Data
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Enable encryption for sensitive information
                        </p>
                      </div>
                      <Switch id="encrypt-sensitive-data" defaultChecked />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={() => {
                      toast({
                        title: "Security Policies Saved",
                        description:
                          "Your security policy changes have been applied successfully.",
                      });
                    }}
                  >
                    Save Security Policies
                  </Button>
                </div>
              </CardContent>
            </Card>
          ),
          authentication: (
            <Card>
              <CardHeader>
                <CardTitle>Authentication Settings</CardTitle>
                <CardDescription>
                  Configure authentication methods and policies
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">
                    Authentication Methods
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="email-auth">
                          Email/Password Authentication
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Allow users to sign in with email and password
                        </p>
                      </div>
                      <Switch id="email-auth" defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="google-auth">
                          Google Authentication
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Allow users to sign in with Google
                        </p>
                      </div>
                      <Switch id="google-auth" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="microsoft-auth">
                          Microsoft Authentication
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Allow users to sign in with Microsoft
                        </p>
                      </div>
                      <Switch id="microsoft-auth" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="apple-auth">Apple Authentication</Label>
                        <p className="text-xs text-muted-foreground">
                          Allow users to sign in with Apple
                        </p>
                      </div>
                      <Switch id="apple-auth" />
                    </div>
                  </div>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">
                    Two-Factor Authentication
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="require-2fa-admins">
                          Require for Administrators
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Force administrators to use 2FA
                        </p>
                      </div>
                      <Switch id="require-2fa-admins" defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="require-2fa-teachers">
                          Require for Teachers
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Force teachers to use 2FA
                        </p>
                      </div>
                      <Switch id="require-2fa-teachers" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="require-2fa-students">
                          Require for Students
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Force students to use 2FA
                        </p>
                      </div>
                      <Switch id="require-2fa-students" />
                    </div>
                  </div>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">Login Monitoring</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="suspicious-login-detection">
                          Suspicious Login Detection
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Detect and alert on suspicious login attempts
                        </p>
                      </div>
                      <Switch id="suspicious-login-detection" defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="ip-geolocation">
                          IP Geolocation Verification
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Verify login locations against expected regions
                        </p>
                      </div>
                      <Switch id="ip-geolocation" defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="device-fingerprinting">
                          Device Fingerprinting
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Track and verify user devices
                        </p>
                      </div>
                      <Switch id="device-fingerprinting" defaultChecked />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={() => {
                      toast({
                        title: "Authentication Settings Saved",
                        description:
                          "Your authentication settings have been applied successfully.",
                      });
                    }}
                  >
                    Save Authentication Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          ),
          "database-backup": (
            <Card>
              <CardHeader>
                <CardTitle>Database Backup & Restore</CardTitle>
                <CardDescription>
                  Manage database backups and restore operations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">Backup Schedule</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-backup">Automatic Backups</Label>
                        <p className="text-xs text-muted-foreground">
                          Enable scheduled automatic backups
                        </p>
                      </div>
                      <Switch id="auto-backup" defaultChecked />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="backup-frequency">
                          Backup Frequency
                        </Label>
                        <select
                          id="backup-frequency"
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                          <option value="daily">Daily</option>
                          <option value="weekly">Weekly</option>
                          <option value="monthly">Monthly</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="retention-period">
                          Retention Period (days)
                        </Label>
                        <Input
                          id="retention-period"
                          type="number"
                          defaultValue="30"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">Manual Backup</h3>
                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Create a manual backup of the entire database or specific
                      tables.
                    </p>

                    <div className="space-y-2">
                      <Label htmlFor="backup-type">Backup Type</Label>
                      <select
                        id="backup-type"
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      >
                        <option value="full">Full Database</option>
                        <option value="schema">Schema Only</option>
                        <option value="data">Data Only</option>
                        <option value="custom">Custom Selection</option>
                      </select>
                    </div>

                    <Button
                      className="w-full"
                      onClick={() => {
                        toast({
                          title: "Backup Started",
                          description:
                            "Database backup process has been initiated. You will be notified when it completes.",
                        });

                        // Simulate backup completion after 3 seconds
                        setTimeout(() => {
                          toast({
                            title: "Backup Completed",
                            description:
                              "Database backup has been completed successfully.",
                          });
                        }, 3000);
                      }}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Create Backup Now
                    </Button>
                  </div>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">Restore Database</h3>
                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Restore the database from a previous backup. This will
                      overwrite current data.
                    </p>

                    <div className="space-y-2">
                      <Label htmlFor="backup-file">Select Backup File</Label>
                      <div className="flex items-center gap-2">
                        <Input id="backup-file" type="file" />
                      </div>
                    </div>

                    <div className="bg-amber-50 p-4 rounded-md border border-amber-200">
                      <h4 className="text-amber-800 font-medium mb-2 flex items-center">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        Warning
                      </h4>
                      <p className="text-sm text-amber-700">
                        Restoring a database will overwrite all current data.
                        This action cannot be undone. Make sure you have a
                        recent backup before proceeding.
                      </p>
                    </div>

                    <Button
                      variant="destructive"
                      className="w-full"
                      onClick={() => {
                        // Show a confirmation dialog
                        if (
                          window.confirm(
                            "WARNING: This will overwrite all current data. Are you absolutely sure you want to proceed with database restoration?"
                          )
                        ) {
                          toast({
                            title: "Restoration Started",
                            description:
                              "Database restoration process has been initiated. This may take several minutes.",
                            variant: "destructive",
                          });

                          // Simulate restoration completion after 5 seconds
                          setTimeout(() => {
                            toast({
                              title: "Restoration Completed",
                              description:
                                "Database has been successfully restored from backup.",
                            });
                          }, 5000);
                        }
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Restore Database
                    </Button>
                  </div>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">Recent Backups</h3>
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      {/* This would be populated from actual backup history */}
                      <div className="flex justify-between items-center p-2 border-b">
                        <div>
                          <p className="font-medium">Full Database Backup</p>
                          <p className="text-xs">
                            {format(new Date(), "MMM d, yyyy HH:mm:ss")}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            toast({
                              title: "Download Started",
                              description:
                                "Your backup file download has started.",
                            });

                            // Create a dummy file for download
                            const blob = new Blob(
                              ["This is a simulated database backup file"],
                              { type: "application/octet-stream" }
                            );
                            const url = URL.createObjectURL(blob);
                            const link = document.createElement("a");
                            link.href = url;
                            link.download = `full_database_backup_${format(
                              new Date(),
                              "yyyy-MM-dd"
                            )}.sql`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                        >
                          <Download className="h-3.5 w-3.5 mr-1" />
                          Download
                        </Button>
                      </div>
                      <div className="flex justify-between items-center p-2 border-b">
                        <div>
                          <p className="font-medium">Schema Only Backup</p>
                          <p className="text-xs">
                            {format(
                              new Date(Date.now() - 86400000),
                              "MMM d, yyyy HH:mm:ss"
                            )}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            toast({
                              title: "Download Started",
                              description:
                                "Your backup file download has started.",
                            });

                            // Create a dummy file for download
                            const blob = new Blob(
                              [
                                "This is a simulated database schema backup file",
                              ],
                              { type: "application/octet-stream" }
                            );
                            const url = URL.createObjectURL(blob);
                            const link = document.createElement("a");
                            link.href = url;
                            link.download = `schema_backup_${format(
                              new Date(Date.now() - 86400000),
                              "yyyy-MM-dd"
                            )}.sql`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                        >
                          <Download className="h-3.5 w-3.5 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ),
          "database-cleanup": <DatabaseCleanup />,
        }}
      </SecondaryNavigation>
    </div>
  );
}
