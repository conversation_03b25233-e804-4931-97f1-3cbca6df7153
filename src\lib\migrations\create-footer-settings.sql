-- Create footer_settings table
CREATE TABLE IF NOT EXISTS public.footer_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  developer_name <PERSON><PERSON>HAR DEFAULT 'Attendance Tracking System Team',
  developer_website VARCHAR DEFAULT 'https://attendancetracking.edu',
  contact_email VARCHAR DEFAULT '<EMAIL>',
  app_tagline VARCHAR DEFAULT 'Streamlining attendance management for educational institutions',
  github_url VARCHAR,
  linkedin_url VARCHAR,
  twitter_url VARCHAR,
  facebook_url VARCHAR,
  instagram_url VARCHAR,
  youtube_url VARCHAR,
  whatsapp_url VARCHAR,
  show_github BOOLEAN DEFAULT true,
  show_linkedin BOOLEAN DEFAULT true,
  show_twitter BOOLEAN DEFAULT true,
  show_facebook BOOLEAN DEFAULT true,
  show_instagram BOOLEAN DEFAULT true,
  show_youtube BOOLEAN DEFAULT true,
  show_whatsapp BOOLEAN DEFAULT true,
  show_developer_info BOOLEAN DEFAULT true,
  show_copyright BOOLEAN DEFAULT true,
  show_app_info BOOLEAN DEFAULT true,
  show_contact B<PERSON><PERSON>EAN DEFAULT true,
  show_legal BOOLEAN DEFAULT true,
  custom_copyright_text VARCHAR,
  privacy_policy_url VARCHAR,
  terms_of_service_url VARCHAR,
  cookie_policy_url VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Insert default footer settings if table is empty
INSERT INTO public.footer_settings (
  developer_name,
  developer_website,
  contact_email,
  app_tagline,
  github_url,
  linkedin_url,
  twitter_url,
  facebook_url,
  instagram_url,
  youtube_url,
  whatsapp_url,
  show_github,
  show_linkedin,
  show_twitter,
  show_facebook,
  show_instagram,
  show_youtube,
  show_whatsapp,
  show_developer_info,
  show_copyright,
  show_app_info,
  show_contact,
  show_legal
)
SELECT
  'Attendance Tracking System Team',
  'https://attendancetracking.edu',
  '<EMAIL>',
  'Streamlining attendance management for educational institutions',
  'https://github.com',
  'https://linkedin.com',
  'https://twitter.com',
  'https://facebook.com',
  'https://instagram.com',
  'https://youtube.com',
  'https://wa.me/1234567890',
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true,
  true
WHERE NOT EXISTS (SELECT 1 FROM public.footer_settings);
