import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useTranslation } from "react-i18next";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { User } from "@/lib/types";

interface DeleteUserDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUserDeleted: () => void;
}

export default function SchoolAdminDeleteUserDialog({
  user,
  open,
  onOpenChange,
  onUserDeleted,
}: DeleteUserDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const handleDeleteUser = async () => {
    if (!user) return;

    setLoading(true);
    try {
      console.log(
        `Starting deletion process for user: ${user.name} (${user.id})`
      );

      // Handle teacher deletion
      if (user.role === "teacher") {
        await handleTeacherDeletion(user);
        return; // Early return if teacher deletion was successful
      }

      // For non-teachers, use the safe_delete_profile function
      await handleRegularUserDeletion(user);

      // If we get here, the deletion was successful
      console.log("User successfully deleted from database");

      // Close the dialog
      onOpenChange(false);

      // Show success message
      toast({
        title: "User deleted",
        description: `${user.name} has been permanently deleted from the system.`,
        duration: 3000,
      });

      // Refresh the user list
      onUserDeleted();
    } catch (error: any) {
      console.error("Error deleting user:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete user",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper function to handle teacher deletion
  const handleTeacherDeletion = async (teacher: User) => {
    console.log("Using delete_teacher_simple function...");

    // Try the simple function first
    try {
      const { error: simpleError } = await supabase.rpc(
        "delete_teacher_simple",
        {
          p_id: teacher.id,
        }
      );

      if (simpleError) {
        console.error("Error with delete_teacher_simple:", simpleError);
        throw simpleError;
      }

      console.log("Teacher deleted successfully with delete_teacher_simple");

      // Close the dialog
      onOpenChange(false);

      // Show success message
      toast({
        title: "User deleted",
        description: `${teacher.name} has been permanently deleted from the system.`,
        duration: 3000,
      });

      // Refresh the user list
      onUserDeleted();

      return true; // Signal success
    } catch (e) {
      // Try marking the teacher's rooms as deleted
      console.log(
        "delete_teacher_simple failed, trying mark_teacher_deleted..."
      );

      const { data: markRoomsResult, error: markRoomsError } =
        await supabase.rpc("mark_teacher_rooms_deleted", {
          p_teacher_id: teacher.id,
        });

      if (markRoomsError) {
        console.error("Error marking rooms as deleted:", markRoomsError);
      } else {
        console.log("Teacher's rooms marked as deleted successfully");
      }

      // Try marking the teacher as deleted
      const { data: markResult, error: markError } = await supabase.rpc(
        "mark_teacher_deleted",
        { p_id: teacher.id }
      );

      if (markError) {
        console.error("Error with mark_teacher_deleted:", markError);

        // Try delete_teacher_with_rooms as a last resort
        console.log(
          "mark_teacher_deleted failed, trying delete_teacher_with_rooms..."
        );
        const { data: deleteTeacherResult, error: teacherFuncError } =
          await supabase.rpc("delete_teacher_with_rooms", {
            teacher_id: teacher.id,
          });

        if (teacherFuncError || deleteTeacherResult !== "success") {
          console.error(
            "Error with delete_teacher_with_rooms function:",
            teacherFuncError || deleteTeacherResult
          );

          // Fall back to manual room deletion
          await deleteTeacherRoomsManually(teacher.id);
        } else {
          console.log("Teacher and rooms deleted successfully with function");

          // Close the dialog
          onOpenChange(false);

          // Show success message
          toast({
            title: "User deleted",
            description: `${teacher.name} has been permanently deleted from the system.`,
            duration: 3000,
          });

          // Refresh the user list
          onUserDeleted();

          return true; // Signal success
        }
      } else if (markResult === true) {
        console.log("Teacher marked as deleted successfully");

        // Close the dialog
        onOpenChange(false);

        // Show success message
        toast({
          title: "User deleted",
          description: `${teacher.name} has been marked as deleted in the system.`,
          duration: 3000,
        });

        // Refresh the user list
        onUserDeleted();

        return true; // Signal success
      } else {
        // Try delete_teacher_with_rooms as a last resort
        console.log(
          "mark_teacher_deleted didn't return true, trying delete_teacher_with_rooms..."
        );
        const { data: deleteTeacherResult, error: teacherFuncError } =
          await supabase.rpc("delete_teacher_with_rooms", {
            teacher_id: teacher.id,
          });

        if (teacherFuncError || deleteTeacherResult !== "success") {
          console.error(
            "Error with delete_teacher_with_rooms function:",
            teacherFuncError || deleteTeacherResult
          );

          // Fall back to manual room deletion
          await deleteTeacherRoomsManually(teacher.id);
        } else {
          console.log("Teacher and rooms deleted successfully with function");

          // Close the dialog
          onOpenChange(false);

          // Show success message
          toast({
            title: "User deleted",
            description: `${teacher.name} has been permanently deleted from the system.`,
            duration: 3000,
          });

          // Refresh the user list
          onUserDeleted();

          return true; // Signal success
        }
      }
    }

    return false; // Signal that we need to continue with regular user deletion
  };

  // Helper function to delete teacher rooms manually
  const deleteTeacherRoomsManually = async (teacherId: string) => {
    console.log("Falling back to manual room deletion...");

    // Get all rooms for this teacher
    const { data: rooms, error: getRoomsError } = await supabase
      .from("rooms")
      .select("id")
      .eq("teacher_id", teacherId);

    if (getRoomsError) {
      console.error("Error getting rooms:", getRoomsError);
    } else if (rooms && rooms.length > 0) {
      console.log(`Found ${rooms.length} rooms to delete`);

      // Delete each room individually
      for (const room of rooms) {
        console.log(`Deleting room ${room.id}...`);
        const { error: deleteRoomError } = await supabase
          .from("rooms")
          .delete()
          .eq("id", room.id);

        if (deleteRoomError) {
          console.error(`Error deleting room ${room.id}:`, deleteRoomError);
        } else {
          console.log(`Successfully deleted room ${room.id}`);
        }
      }
    } else {
      console.log("No rooms found for this teacher");
    }
  };

  // Helper function to handle regular user deletion
  const handleRegularUserDeletion = async (user: User) => {
    console.log("Deleting user profile using safe_delete_profile function...");
    const { data: deleteResult, error: funcError } = await supabase.rpc(
      "safe_delete_profile",
      { profile_id: user.id }
    );

    if (funcError || deleteResult !== "success") {
      console.error(
        "Error with safe_delete_profile function:",
        funcError || deleteResult
      );

      // Try direct deletion as a fallback
      console.log("Trying direct delete as fallback...");
      let { error: deleteError } = await supabase
        .from("profiles")
        .delete()
        .eq("id", user.id);

      // If there's a permission issue, try using delete_user_by_school_admin
      if (
        deleteError &&
        (deleteError.code === "PGRST301" ||
          deleteError.message.includes("recursion"))
      ) {
        console.log(
          "Permission or recursion issue, trying with delete_user_by_school_admin function"
        );

        const { error: rpcError } = await supabase.rpc(
          "delete_user_by_school_admin",
          { user_id_to_delete: user.id }
        );

        if (rpcError) {
          console.error("RPC function error:", rpcError);
          throw new Error(`Failed to delete user: ${rpcError.message}`);
        }
      } else if (deleteError) {
        console.error("Error deleting user profile:", deleteError);
        throw new Error(`Failed to delete user: ${deleteError.message}`);
      }
    } else {
      console.log(
        "User profile deleted successfully with safe_delete_profile function"
      );
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {t("admin.userManagement.deleteUser")}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {t("admin.userManagement.deleteUserConfirm", { name: user?.name })}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDeleteUser}
            className="bg-destructive text-destructive-foreground"
            disabled={loading}
          >
            {loading ? t("common.deleting") : t("admin.userManagement.deleteUser")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
