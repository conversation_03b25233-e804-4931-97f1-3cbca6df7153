-- Create a function to sync role changes to <PERSON><PERSON><PERSON> claims
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION sync_role_to_claims()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF NEW.role IS DISTINCT FROM OLD.role THEN
    -- Update the user's <PERSON><PERSON><PERSON> claims
    UPDATE auth.users
    SET raw_app_meta_data = 
      jsonb_set(
        COALESCE(raw_app_meta_data, '{}'::jsonb),
        '{role}',
        to_jsonb(NEW.role)
      )
    WHERE id = NEW.user_id;
  END IF;
  RETURN NEW;
END;
$$;

-- Create the trigger
DROP TRIGGER IF EXISTS sync_role_trigger ON profiles;
CREATE TRIGGER sync_role_trigger
  AFTER UPDATE OF role ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION sync_role_to_claims(); 