import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AccessibleTabsContent } from "@/components/ui/accessible-tabs";
import Navbar from "@/components/shared/Navbar";
import TeacherDashboard from "@/components/teacher/Dashboard";
import TeacherProfile from "@/components/teacher/Profile";
import StudentDirectory from "@/components/teacher/StudentDirectory";
import TeacherAlerts from "@/components/teacher/TeacherAlerts";
import ExcusesManagement from "@/components/teacher/ExcusesManagement";
import { RoomLocationSettings } from "@/components/teacher/RoomLocationSettings";
import { BlockLocationSettings } from "@/components/teacher/BlockLocationSettings";
import LocationVerificationSettingsSimple from "@/components/teacher/LocationVerificationSettingsSimple";
import VerificationMethodSettings from "@/components/teacher/VerificationMethodSettings";
import {
  User,
  LayoutDashboard,
  Users,
  Bell,
  Settings,
  FileText,
  Home,
  GraduationCap,
  ClipboardList,
  AlertCircle,
  UserCircle,
  Cog,
  MapPin,
  Shield,
} from "lucide-react";
import { Teacher as TeacherType, Block } from "@/lib/types";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { motion, AnimatePresence } from "framer-motion";
import DashboardMessage from "@/components/shared/DashboardMessage";
import Footer from "@/components/shared/Footer";
import FeedbackForm from "@/components/shared/FeedbackForm";
import SimpleCarousel from "@/components/shared/SimpleCarousel";
import { useTranslation } from "react-i18next";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs as InnerTabs,
  TabsContent as InnerTabsContent,
  TabsList as InnerTabsList,
  TabsTrigger as InnerTabsTrigger,
} from "@/components/ui/tabs";

export default function Teacher() {
  const { profile, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const isProfileSetup =
    new URLSearchParams(location.search).get("setup") === "true";
  // Get the active tab from URL, localStorage, or default to dashboard
  const [activeTab, setActiveTab] = useState<string>(() => {
    // First check URL
    const urlParams = new URLSearchParams(window.location.search);
    const tabFromUrl = urlParams.get("tab");

    // Then check localStorage
    const tabFromStorage = localStorage.getItem("teacherActiveTab");

    // Return the first valid value, or default to dashboard
    return tabFromUrl || tabFromStorage || "dashboard";
  });
  const [unreadCount, setUnreadCount] = useState(0);
  const [rooms, setRooms] = useState<
    Array<{
      id: string;
      name: string;
      building: string | null;
      block_id: string;
    }>
  >([]);
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [selectedRoomId, setSelectedRoomId] = useState<string>("");
  const [selectedBlockId, setSelectedBlockId] = useState<string>("");
  const [selectedBlockIdForRooms, setSelectedBlockIdForRooms] = useState<string>("");
  const [filteredRooms, setFilteredRooms] = useState<any[]>([]);
  const [settingsTab, setSettingsTab] = useState<string>("rooms");
  const [verificationSubTab, setVerificationSubTab] = useState<string>("location");

  // Helper function to check if teacher needs profile setup
  const needsProfileSetup = (teacherProfile: TeacherType) => {
    return (
      !teacherProfile.department ||
      !teacherProfile.position ||
      !teacherProfile.subject ||
      !teacherProfile.school_id
    );
  };

  // Fetch rooms and blocks when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (!profile?.id || !profile?.school_id) return;



      // Fetch all rooms from the school (for location settings, teachers should see all rooms)
      const { data: roomsData, error: roomsError } = await supabase
        .from("rooms")
        .select("id, name, building, block_id")
        .eq("school_id", profile.school_id)
        .order("name");

      if (roomsError) {
        console.error("Error fetching rooms:", roomsError);
        return;
      }


      if (roomsData) {
        setRooms(roomsData);
        if (roomsData.length > 0) {
          setSelectedRoomId(roomsData[0].id);
        }
      }

      // Fetch blocks for the current school only
      const { data: blocksData, error: blocksError } = await supabase
        .from("blocks")
        .select("*")
        .eq("school_id", profile.school_id)
        .order("name");

      if (blocksError) {
        console.error("Error fetching blocks:", blocksError);
        return;
      }


      if (blocksData) {
        setBlocks(blocksData);
        if (blocksData.length > 0) {
          setSelectedBlockId(blocksData[0].id);
        }
      }
    };

    fetchData();
  }, [profile?.id, profile?.school_id]);

  // Filter rooms when block selection changes
  useEffect(() => {


    if (selectedBlockIdForRooms && selectedBlockIdForRooms !== "all" && rooms.length > 0) {
      const filtered = rooms.filter(room => room.block_id === selectedBlockIdForRooms);

      setFilteredRooms(filtered);
      // Reset room selection when block changes
      if (filtered.length > 0) {
        setSelectedRoomId(filtered[0].id);
      } else {
        setSelectedRoomId("");
      }
    } else {
      // If no block selected or "all" selected, show all rooms

      setFilteredRooms(rooms);
      if (rooms.length > 0 && !selectedRoomId) {
        setSelectedRoomId(rooms[0].id);
      }
    }
  }, [selectedBlockIdForRooms, rooms, selectedRoomId]);

  // Fetch unread alerts count
  useEffect(() => {
    if (!profile?.id) return;

    const fetchUnreadCount = async () => {
      const { count, error } = await supabase
        .from("notifications")
        .select("*", { count: "exact", head: true })
        .eq("type", "distance_alert")
        .eq("read", false);

      if (!error && count !== null) {
        setUnreadCount(count);
      }
    };

    fetchUnreadCount();

    // Subscribe to notification changes
    const channel = supabase
      .channel("teacher-alert-count")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "notifications",
          filter: `type=eq.distance_alert AND read=eq.false`,
        },
        (payload) => {
          if (payload.eventType === "INSERT") {
            setUnreadCount((prev) => prev + 1);
          } else if (payload.eventType === "UPDATE") {
            fetchUnreadCount();
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [profile?.id]);

  useEffect(() => {
    // If we're in setup mode or profile is incomplete, force the profile tab
    if (
      isProfileSetup ||
      (profile &&
        profile.role === "teacher" &&
        needsProfileSetup(profile as TeacherType))
    ) {
      setActiveTab("profile");
      localStorage.setItem("teacherActiveTab", "profile");
    }
  }, [isProfileSetup, profile]);

  // Update URL without page reload when tab changes
  useEffect(() => {
    // Update the URL to reflect the current tab without causing a page reload
    const url = new URL(window.location.href);
    url.searchParams.set("tab", activeTab);
    window.history.pushState({}, "", url.toString());
  }, [activeTab]);

  // Reset unread count when opening alerts tab
  useEffect(() => {
    if (activeTab === "alerts") {
      setUnreadCount(0);
    }
  }, [activeTab]);

  // If loading, show loading indicator
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner message={t("loading.teacherDashboard")} size="lg" />
      </div>
    );
  }

  // If not a teacher, redirect to login
  if (!profile || profile.role !== "teacher") {
    navigate("/login");
    return null;
  }

  const teacherProfile = profile as TeacherType;

  // Check if this is a new teacher that needs to complete profile
  const requiresProfileSetup =
    isProfileSetup || needsProfileSetup(teacherProfile);

  // Force redirect to profile setup if needed (for fresh signups/logins)
  useEffect(() => {
    if (requiresProfileSetup && !isProfileSetup) {
      navigate("/teacher?setup=true");
    }
  }, [requiresProfileSetup, isProfileSetup, navigate]);

  return (
    <div className="min-h-screen flex flex-col" data-testid="teacher-dashboard">
      <Navbar />
      {requiresProfileSetup ? (
        <div className="container mx-auto py-6 px-4 flex-1">
          {/* Render the Profile component for setup - it has its own title and message */}
          <TeacherProfile isSetupMode={true} />
        </div>
      ) : (
        <div className="flex-1">
          {/* Dashboard Carousel with title overlay - directly below navbar */}
          <SimpleCarousel userType="teacher" />

          {/* Display dashboard message below carousel */}
          <div className="container mx-auto px-4 mt-6">
            <DashboardMessage userType="teacher" />

            <Tabs
              value={activeTab}
              onValueChange={(value) => {
                setActiveTab(value);
                localStorage.setItem("teacherActiveTab", value);
              }}
            >
              <TabsList
                className="w-full max-w-5xl mx-auto mb-6 p-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm grid grid-cols-3 sm:grid-cols-6 gap-1"
                style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
              >
                <TabsTrigger
                  value="dashboard"
                  disabled={requiresProfileSetup}
                  className="flex flex-col items-center justify-center gap-1 px-2 py-2.5 h-12 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 data-[state=active]:bg-blue-500 data-[state=active]:text-white"
                >
                  <Home className="h-7 w-7" />
                  <span className="font-medium text-xs text-center">
                    {t("common.dashboard")}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="students"
                  disabled={requiresProfileSetup}
                  className="flex flex-col items-center justify-center gap-1 px-2 py-2.5 h-12 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 data-[state=active]:bg-green-500 data-[state=active]:text-white"
                >
                  <GraduationCap className="h-7 w-7" />
                  <span className="font-medium text-xs text-center">
                    {t("common.students")}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="excuses"
                  disabled={requiresProfileSetup}
                  className="flex flex-col items-center justify-center gap-1 px-2 py-2.5 h-12 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 data-[state=active]:bg-orange-500 data-[state=active]:text-white"
                >
                  <ClipboardList className="h-7 w-7" />
                  <span className="font-medium text-xs text-center">
                    {t("teacher.dashboard.excuses")}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="alerts"
                  disabled={requiresProfileSetup}
                  className="relative flex flex-col items-center justify-center gap-1 px-2 py-2.5 h-12 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 data-[state=active]:bg-red-500 data-[state=active]:text-white"
                >
                  <Bell className="h-7 w-7" />
                  <AnimatePresence>
                    {unreadCount > 0 && (
                      <motion.div
                        initial={{ scale: 0.5, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.5, opacity: 0 }}
                        className="absolute top-1 right-1 bg-yellow-500 text-[8px] text-white w-4 h-4 rounded-full flex items-center justify-center font-medium"
                      >
                        {unreadCount > 9 ? "9+" : unreadCount}
                      </motion.div>
                    )}
                  </AnimatePresence>
                  <span className="font-medium text-xs text-center">
                    {t("notifications.alerts")}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="profile"
                  className="flex flex-col items-center justify-center gap-1 px-2 py-2.5 h-12 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 data-[state=active]:bg-purple-500 data-[state=active]:text-white"
                >
                  <UserCircle className="h-7 w-7" />
                  <span className="font-medium text-xs text-center">
                    {t("common.profile")}
                  </span>
                </TabsTrigger>
                <TabsTrigger
                  value="settings"
                  disabled={requiresProfileSetup}
                  className="flex flex-col items-center justify-center gap-1 px-2 py-2.5 h-12 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-slate-600 data-[state=active]:bg-gray-500 data-[state=active]:text-white"
                >
                  <Cog className="h-7 w-7" />
                  <span className="font-medium text-xs text-center">
                    {t("common.settings")}
                  </span>
                </TabsTrigger>
              </TabsList>

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <AccessibleTabsContent value="dashboard" className="mt-0">
                    <TeacherDashboard />
                  </AccessibleTabsContent>

                  <AccessibleTabsContent value="students" className="mt-0">
                    <StudentDirectory />
                  </AccessibleTabsContent>

                  <AccessibleTabsContent value="excuses" className="mt-0">
                    <ExcusesManagement />
                  </AccessibleTabsContent>

                  <AccessibleTabsContent value="alerts" className="mt-0">
                    <TeacherAlerts />
                  </AccessibleTabsContent>

                  <AccessibleTabsContent value="profile" className="mt-0">
                    <TeacherProfile isSetupMode={requiresProfileSetup} />
                  </AccessibleTabsContent>

                  <AccessibleTabsContent value="settings" className="mt-0">
                    <Card>
                      <CardHeader>
                        <CardTitle>
                          {t("teacher.settings.locationSettings")}
                        </CardTitle>
                        <CardDescription>
                          {t("teacher.settings.locationSettingsDescription")}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <InnerTabs
                          value={settingsTab}
                          onValueChange={setSettingsTab}
                        >
                          <InnerTabsList className="grid w-full max-w-2xl mx-auto grid-cols-3">
                            <InnerTabsTrigger
                              value="blocks"
                              className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2"
                            >
                              <span className="text-[10px] sm:text-sm">
                                {t("teacher.settings.blockLocations")}
                              </span>
                            </InnerTabsTrigger>
                            <InnerTabsTrigger
                              value="rooms"
                              className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2"
                            >
                              <span className="text-[10px] sm:text-sm">
                                {t("teacher.settings.roomLocations")}
                              </span>
                            </InnerTabsTrigger>
                            <InnerTabsTrigger
                              value="verification"
                              className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2"
                            >
                              <span className="text-[10px] sm:text-sm">
                                {t("teacher.settings.verificationSettings")}
                              </span>
                            </InnerTabsTrigger>
                          </InnerTabsList>

                          <InnerTabsContent value="blocks" className="mt-6">
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <label className="text-sm font-medium">
                                  {t("teacher.settings.selectBlock")}
                                </label>
                                <Select
                                  value={selectedBlockId}
                                  onValueChange={setSelectedBlockId}
                                >
                                  <SelectTrigger>
                                    <SelectValue
                                      placeholder={t(
                                        "teacher.settings.selectBlockPlaceholder"
                                      )}
                                    />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {blocks.map((block) => (
                                      <SelectItem
                                        key={block.id}
                                        value={block.id}
                                      >
                                        {t("common.block")} {block.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>

                              {selectedBlockId && blocks.length > 0 && (
                                <BlockLocationSettings
                                  blockId={selectedBlockId}
                                  blockName={
                                    blocks.find((b) => b.id === selectedBlockId)
                                      ?.name || ""
                                  }
                                />
                              )}

                              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                                <p className="text-blue-800 text-sm">
                                  {t("teacher.settings.blockLocationInfo")}
                                </p>
                              </div>
                            </div>
                          </InnerTabsContent>

                          <InnerTabsContent value="rooms" className="mt-6">
                            <div className="space-y-4">
                              {/* Block selector for filtering rooms */}
                              <div className="space-y-2">
                                <label className="text-sm font-medium">
                                  {t("teacher.settings.selectBlock")}
                                </label>
                                <Select
                                  value={selectedBlockIdForRooms}
                                  onValueChange={setSelectedBlockIdForRooms}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder={t("teacher.settings.selectBlockFirst")} />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">
                                      {t("teacher.settings.allBlocks")}
                                    </SelectItem>
                                    {blocks.map((block) => (
                                      <SelectItem key={block.id} value={block.id}>
                                        {t("common.block")} {block.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>

                              {/* Room selector - filtered by selected block */}
                              <div className="space-y-2">
                                <label className="text-sm font-medium">
                                  {t("teacher.settings.selectRoom")}
                                </label>
                                <Select
                                  value={selectedRoomId}
                                  onValueChange={setSelectedRoomId}
                                  disabled={selectedBlockIdForRooms && selectedBlockIdForRooms !== "all" && filteredRooms.length === 0}
                                >
                                  <SelectTrigger>
                                    <SelectValue
                                      placeholder={
                                        selectedBlockIdForRooms && selectedBlockIdForRooms !== "all" && filteredRooms.length === 0
                                          ? t("teacher.settings.noRoomsInBlock")
                                          : t("teacher.settings.selectRoomPlaceholder")
                                      }
                                    />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {filteredRooms.map((room) => (
                                      <SelectItem key={room.id} value={room.id}>
                                        {room.name} (
                                        {room.building ||
                                          t("teacher.settings.unknownBuilding")}
                                        )
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                {selectedBlockIdForRooms && selectedBlockIdForRooms !== "all" && (
                                  <p className="text-sm text-muted-foreground">
                                    {t("teacher.settings.showingRoomsFromBlock", {
                                      blockName: blocks.find(b => b.id === selectedBlockIdForRooms)?.name || "",
                                      count: filteredRooms.length
                                    })}
                                  </p>
                                )}
                                {selectedBlockIdForRooms === "all" && (
                                  <p className="text-sm text-muted-foreground">
                                    {t("teacher.settings.showingAllRooms", {
                                      count: filteredRooms.length
                                    })}
                                  </p>
                                )}
                              </div>

                              {selectedRoomId && (
                                <RoomLocationSettings roomId={selectedRoomId} />
                              )}

                              <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                                <p className="text-amber-800 text-sm">
                                  {t("teacher.settings.roomLocationInfo")}
                                </p>
                              </div>
                            </div>
                          </InnerTabsContent>

                          <InnerTabsContent
                            value="verification"
                            className="mt-6"
                          >
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <label className="text-sm font-medium">
                                  {t("teacher.settings.configureVerification")}
                                </label>
                                <p className="text-sm text-muted-foreground">
                                  {t("teacher.settings.verificationDescription")}
                                </p>
                              </div>

                              {/* Nested tabs for verification settings */}
                              <InnerTabs
                                value={verificationSubTab}
                                onValueChange={setVerificationSubTab}
                              >
                                <InnerTabsList className="grid w-full max-w-2xl mx-auto grid-cols-2">
                                  <InnerTabsTrigger
                                    value="location"
                                    className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2"
                                  >
                                    <MapPin className="h-3 w-3 sm:h-4 sm:w-4" />
                                    <span className="text-[10px] sm:text-sm">
                                      {t("teacher.settings.locationVerification")}
                                    </span>
                                  </InnerTabsTrigger>
                                  <InnerTabsTrigger
                                    value="methods"
                                    className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2"
                                  >
                                    <Shield className="h-3 w-3 sm:h-4 sm:w-4" />
                                    <span className="text-[10px] sm:text-sm">
                                      {t("teacher.settings.verificationMethods")}
                                    </span>
                                  </InnerTabsTrigger>
                                </InnerTabsList>

                                <InnerTabsContent value="location" className="mt-6">
                                  <div className="space-y-4">
                                    {selectedRoomId && (
                                      <LocationVerificationSettingsSimple
                                        roomId={selectedRoomId}
                                        blockId={
                                          rooms.find((r) => r.id === selectedRoomId)
                                            ?.block_id || ""
                                        }
                                      />
                                    )}

                                    <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                                      <p className="text-green-800 text-sm">
                                        {t("teacher.settings.verificationInfo")}
                                      </p>
                                    </div>
                                  </div>
                                </InnerTabsContent>

                                <InnerTabsContent value="methods" className="mt-6">
                                  <VerificationMethodSettings
                                    roomId={selectedRoomId}
                                    blockId={
                                      rooms.find((r) => r.id === selectedRoomId)
                                        ?.block_id || ""
                                    }
                                  />
                                </InnerTabsContent>
                              </InnerTabs>
                            </div>
                          </InnerTabsContent>
                        </InnerTabs>
                      </CardContent>
                      <CardFooter className="text-sm text-muted-foreground">
                        {t("teacher.settings.studentsWithinRadius")}
                      </CardFooter>
                    </Card>
                  </AccessibleTabsContent>
                </motion.div>
              </AnimatePresence>
            </Tabs>
          </div>
        </div>
      )}
      {!requiresProfileSetup && <Footer />}
    </div>
  );
}
