import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../i18n/i18n';
import { toast } from '../lib/utils/toast';
import { UnifiedToaster } from '../components/ui/unified-toast';
import { ThemeProvider } from '../context/ThemeContext';

// Mock the toast function
jest.mock('../lib/utils/toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn(),
    translate: jest.fn(),
    translateSuccess: jest.fn(),
    translateError: jest.fn(),
  },
}));

// Test component that uses toast with translations
const TestComponent = () => {
  const handleSuccessClick = () => {
    toast.translateSuccess(i18n.t, 'common.success', 'settings.languageUpdated');
  };

  const handleErrorClick = () => {
    toast.translateError(i18n.t, 'common.error', 'settings.errorSavingLanguage');
  };

  return (
    <div>
      <button onClick={handleSuccessClick}>Show Success Toast</button>
      <button onClick={handleErrorClick}>Show Error Toast</button>
    </div>
  );
};

describe('Toast Internationalization Tests', () => {
  beforeEach(() => {
    // Reset language to English before each test
    i18n.changeLanguage('en');
    jest.clearAllMocks();
  });

  test('Toast with English translations', () => {
    render(
      <I18nextProvider i18n={i18n}>
        <ThemeProvider>
          <UnifiedToaster />
          <TestComponent />
        </ThemeProvider>
      </I18nextProvider>
    );

    // Click the success button
    fireEvent.click(screen.getByText('Show Success Toast'));

    // Check that toast.translateSuccess was called with the correct parameters
    expect(toast.translateSuccess).toHaveBeenCalledWith(
      i18n.t,
      'common.success',
      'settings.languageUpdated'
    );

    // Click the error button
    fireEvent.click(screen.getByText('Show Error Toast'));

    // Check that toast.translateError was called with the correct parameters
    expect(toast.translateError).toHaveBeenCalledWith(
      i18n.t,
      'common.error',
      'settings.errorSavingLanguage'
    );
  });

  test('Toast with Turkish translations', () => {
    // Change language to Turkish
    i18n.changeLanguage('tr');

    render(
      <I18nextProvider i18n={i18n}>
        <ThemeProvider>
          <UnifiedToaster />
          <TestComponent />
        </ThemeProvider>
      </I18nextProvider>
    );

    // Click the success button
    fireEvent.click(screen.getByText('Show Success Toast'));

    // Check that toast.translateSuccess was called with the correct parameters
    expect(toast.translateSuccess).toHaveBeenCalledWith(
      i18n.t,
      'common.success',
      'settings.languageUpdated'
    );

    // Click the error button
    fireEvent.click(screen.getByText('Show Error Toast'));

    // Check that toast.translateError was called with the correct parameters
    expect(toast.translateError).toHaveBeenCalledWith(
      i18n.t,
      'common.error',
      'settings.errorSavingLanguage'
    );
  });
});
