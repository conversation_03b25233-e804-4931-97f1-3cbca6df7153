import { supabase } from "@/lib/supabase";
import { User, School } from "@/lib/types";
import { getUserSchoolId, isSystemAdmin } from "@/lib/utils/school-context";

/**
 * Utility functions for handling school-based queries
 */

/**
 * Add school context to a Supabase query
 * @param query The Supabase query to add school context to
 * @param user The current user
 * @returns The query with school context added
 */
export const withSchoolContext = async (query: any, user: User | null) => {
  if (!user) {
    return query;
  }

  // If the user is a system admin, don't filter by school
  if (user.accessLevel === 3 || (await isSystemAdmin(user.id))) {
    return query;
  }

  // Get the user's school ID
  const schoolId = user.school_id || (await getUserSchoolId(user.id));

  if (schoolId) {
    return query.eq("school_id", schoolId);
  }

  return query;
};

/**
 * Get all profiles with school context
 * @param user The current user
 * @param role Optional role filter
 * @returns Array of profiles
 */
export const getProfiles = async (user: User | null, role?: string) => {
  try {
    let query = supabase
      .from("profiles")
      .select("*")
      .order("id", { ascending: true }); // Add ordering to ensure consistent results

    // Add role filter if provided
    if (role) {
      query = query.eq("role", role);
    }

    // Filter out deleted users
    query = query.eq("is_deleted", false).is("deleted_at", null);

    // Add school context
    query = await withSchoolContext(query, user);

    const { data, error } = await query;

    if (error) {
      console.error("Error getting profiles:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Unexpected error getting profiles:", error);
    return [];
  }
};

/**
 * Get all rooms with school context
 * @param user The current user
 * @returns Array of rooms
 */
export const getRooms = async (user: User | null) => {
  try {
    let query = supabase.from("rooms").select("*");

    // Add school context
    query = await withSchoolContext(query, user);

    const { data, error } = await query;

    if (error) {
      console.error("Error getting rooms:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Unexpected error getting rooms:", error);
    return [];
  }
};

/**
 * Get all attendance records with school context
 * @param user The current user
 * @param studentId Optional student ID filter
 * @param roomId Optional room ID filter
 * @returns Array of attendance records
 */
export const getAttendanceRecords = async (
  user: User | null,
  studentId?: string,
  roomId?: string
) => {
  try {
    let query = supabase.from("attendance_records").select("*");

    // Add filters if provided
    if (studentId) {
      query = query.eq("student_id", studentId);
    }

    if (roomId) {
      query = query.eq("room_id", roomId);
    }

    // Add school context
    query = await withSchoolContext(query, user);

    const { data, error } = await query;

    if (error) {
      console.error("Error getting attendance records:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Unexpected error getting attendance records:", error);
    return [];
  }
};

/**
 * Get all excuses with school context
 * @param user The current user
 * @param studentId Optional student ID filter
 * @param status Optional status filter
 * @returns Array of excuses
 */
export const getExcuses = async (
  user: User | null,
  studentId?: string,
  status?: string
) => {
  try {
    let query = supabase.from("excuses").select("*");

    // Add filters if provided
    if (studentId) {
      query = query.eq("student_id", studentId);
    }

    if (status) {
      query = query.eq("status", status);
    }

    // Add school context
    query = await withSchoolContext(query, user);

    const { data, error } = await query;

    if (error) {
      console.error("Error getting excuses:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Unexpected error getting excuses:", error);
    return [];
  }
};

/**
 * Get all notifications with school context
 * @param user The current user
 * @param recipientId Optional recipient ID filter
 * @returns Array of notifications
 */
export const getNotifications = async (
  user: User | null,
  recipientId?: string
) => {
  try {
    let query = supabase.from("notifications").select("*");

    // Add recipient filter if provided
    if (recipientId) {
      query = query.eq("recipient_id", recipientId);
    }

    // Add school context
    query = await withSchoolContext(query, user);

    const { data, error } = await query;

    if (error) {
      console.error("Error getting notifications:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Unexpected error getting notifications:", error);
    return [];
  }
};

/**
 * Get all parent contacts with school context
 * @param user The current user
 * @param studentId Optional student ID filter
 * @returns Array of parent contacts
 */
export const getParentContacts = async (
  user: User | null,
  studentId?: string
) => {
  try {
    let query = supabase.from("parent_contacts").select("*");

    // Add student filter if provided
    if (studentId) {
      query = query.eq("student_id", studentId);
    }

    // Add school context
    query = await withSchoolContext(query, user);

    const { data, error } = await query;

    if (error) {
      console.error("Error getting parent contacts:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Unexpected error getting parent contacts:", error);
    return [];
  }
};
