-- Add block_id to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS block_id UUID REFERENCES blocks(id);

-- Update existing student profiles to use block_id based on their block_name
UPDATE profiles
SET block_id = (
    SELECT id FROM blocks WHERE name = COALESCE(
        NULLIF(REGEXP_REPLACE(block_name, '[^0-9]', '', 'g'), ''),
        '1'
    )
)
WHERE role = 'student' AND block_id IS NULL; 