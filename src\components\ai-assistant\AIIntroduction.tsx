import { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Zap, Star, Heart, Shield, Users, GraduationCap } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { useLocation } from 'react-router-dom';

interface AIIntroductionProps {
  onQuickAction: (message: string) => void;
}

export default function AIIntroduction({ onQuickAction }: AIIntroductionProps) {
  const { t } = useTranslation();
  const { profile } = useAuth();
  const location = useLocation();
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [displayedText, setDisplayedText] = useState('');
  const [isTyping, setIsTyping] = useState(true);

  // Get user role and current page context
  const userRole = profile?.role || 'student';
  const currentPage = location.pathname.split('/')[1] || 'dashboard';



  // Get role-specific icon
  const getRoleIcon = () => {
    switch (userRole) {
      case 'admin': return Shield;
      case 'teacher': return Users;
      case 'student': return GraduationCap;
      default: return Bot;
    }
  };

  const RoleIcon = getRoleIcon();

  // Get simplified introduction text
  const getIntroductionTexts = () => {
    const userName = profile?.full_name || profile?.email?.split('@')[0] || 'User';

    // Single simplified message for all roles
    const greeting = `Hi, ${userName}! 👋 I'm your intelligent ATS Assistant.`;
    const helpText = "I'm here to help you with whatever you need. Feel free to ask me anything about the system!";

    return [greeting, helpText];
  };

  // Memoize the introduction texts to prevent re-creation on every render
  const introTexts = useMemo(() => {
    return getIntroductionTexts();
  }, [userRole, currentPage, profile?.full_name, profile?.email, t]);

  // Typewriter effect - stable dependencies
  useEffect(() => {

    if (introTexts.length === 0) {
      setDisplayedText(t('aiAssistant.welcome') + ' ' + t('aiAssistant.welcomeDescription'));
      setIsTyping(false);
      return;
    }

    let currentIndex = 0;
    let charIndex = 0;
    setDisplayedText('');
    setCurrentTextIndex(0);

    const typeNextChar = () => {
      if (currentIndex >= introTexts.length) {
        // Keep cursor blinking for a bit after typing is done
        setTimeout(() => {
          setIsTyping(false);
        }, 2000);
        return;
      }

      const currentText = introTexts[currentIndex];

      if (charIndex <= currentText.length) {
        setDisplayedText(currentText.slice(0, charIndex));
        charIndex++;
        setTimeout(typeNextChar, 20); // Faster typing
      } else {
        // Move to next text after a pause
        setTimeout(() => {
          currentIndex++;
          charIndex = 0;
          setCurrentTextIndex(currentIndex);
          if (currentIndex < introTexts.length) {
            setDisplayedText(displayedText + '\n\n');
            setTimeout(typeNextChar, 50);
          } else {
            // Keep cursor blinking for a bit after typing is done
            setTimeout(() => {
              setIsTyping(false);
            }, 1000);
          }
        }, 800);
      }
    };

    // Start typing after a short delay
    const startTimeout = setTimeout(typeNextChar, 500);

    return () => {
      clearTimeout(startTimeout);
    };
  }, [introTexts]); // Re-run when introTexts change (e.g., language change)

  // Get contextual quick actions
  const getQuickActions = () => {
    const baseActions = [
      {
        id: 'help',
        label: t('aiAssistant.quickActions.help'),
        icon: Heart,
        message: t('aiAssistant.quickActions.helpMessage'),
        gradient: 'from-pink-500 to-rose-500'
      }
    ];

    const pageActions = {
      dashboard: [
        {
          id: 'overview',
          label: t('aiAssistant.quickActions.overview'),
          icon: Star,
          message: t('aiAssistant.quickActions.overviewMessage'),
          gradient: 'from-blue-500 to-cyan-500'
        }
      ],
      attendance: [
        {
          id: 'attendance',
          label: t('aiAssistant.quickActions.attendance'),
          icon: Zap,
          message: t('aiAssistant.quickActions.attendanceMessage'),
          gradient: 'from-green-500 to-emerald-500'
        }
      ],
      qr: [
        {
          id: 'qr',
          label: t('aiAssistant.quickActions.qr'),
          icon: Brain,
          message: t('aiAssistant.quickActions.qrMessage'),
          gradient: 'from-purple-500 to-violet-500'
        }
      ]
    };

    return [...(pageActions[currentPage as keyof typeof pageActions] || []), ...baseActions];
  };

  const quickActions = getQuickActions();

  return (
    <div className="flex flex-col h-full">
      {/* AI Avatar and Introduction */}
      <div className="flex-1 flex flex-col items-center justify-center p-4 text-center">
        {/* Animated AI Avatar */}
        <motion.div
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ type: "spring", damping: 15, stiffness: 300, delay: 0.2 }}
          className="relative mb-6"
        >
          {/* Outer glow ring */}
          <motion.div
            animate={{ 
              rotate: 360,
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              rotate: { duration: 20, repeat: Infinity, ease: "linear" },
              scale: { duration: 3, repeat: Infinity, ease: "easeInOut" }
            }}
            className="absolute inset-0 w-24 h-24 rounded-full bg-gradient-to-r from-[#EE0D09]/30 via-purple-500/30 to-blue-500/30 blur-lg"
          />
          
          {/* Main avatar */}
          <motion.div
            animate={{ 
              y: [0, -8, 0],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ 
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="relative w-20 h-20 rounded-full bg-gradient-to-br from-[#EE0D09] via-purple-600 to-blue-600 flex items-center justify-center shadow-2xl"
          >
            <Bot className="w-10 h-10 text-white" />
            
            {/* Sparkles around avatar */}
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ 
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                  rotate: 360
                }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.3,
                  ease: "easeInOut"
                }}
                className="absolute"
                style={{
                  top: `${20 + Math.sin(i * 60 * Math.PI / 180) * 35}px`,
                  left: `${20 + Math.cos(i * 60 * Math.PI / 180) * 35}px`,
                }}
              >
                <Sparkles className="w-3 h-3 text-yellow-400" />
              </motion.div>
            ))}
          </motion.div>

          {/* Role indicator */}
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8 }}
            className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full bg-gradient-to-br from-white to-gray-100 flex items-center justify-center shadow-lg border-2 border-white"
          >
            <RoleIcon className="w-4 h-4 text-[#EE0D09]" />
          </motion.div>
        </motion.div>

        {/* Introduction Text */}
        <div className="min-h-[100px] flex flex-col justify-center">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentTextIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="text-white text-center"
            >
              <p className="text-sm font-medium leading-relaxed">
                {displayedText}
                {isTyping && currentTextIndex < introTexts.length && (
                  <motion.span
                    animate={{ opacity: [1, 0] }}
                    transition={{ duration: 0.5, repeat: Infinity }}
                    className="text-[#EE0D09] ml-1 text-lg font-bold"
                  >
                    |
                  </motion.span>
                )}
              </p>
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Quick Actions */}
        {!isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="w-full space-y-3"
          >
            <p className="text-sm text-gray-300 mb-4">{t('aiAssistant.quickStart')}</p>
            
            <div className="grid grid-cols-1 gap-3">
              {quickActions.map((action, index) => (
                <motion.button
                  key={action.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  whileHover={{ scale: 1.02, x: 5 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => onQuickAction(action.message)}
                  className={`group relative p-3 rounded-lg bg-gradient-to-r ${action.gradient} bg-opacity-20 border border-white/10 hover:border-white/20 transition-all duration-300 text-left overflow-hidden`}
                >
                  {/* Background glow effect */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${action.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-300`} />
                  
                  <div className="relative flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${action.gradient} flex items-center justify-center shadow-lg`}>
                      <action.icon className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white font-medium text-sm">{action.label}</p>
                    </div>
                    <motion.div
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                      className="text-white/60"
                    >
                      →
                    </motion.div>
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
