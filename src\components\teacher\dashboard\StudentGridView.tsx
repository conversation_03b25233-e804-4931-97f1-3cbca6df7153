import { useState } from "react";
import { Student } from "@/lib/types";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ChevronDown,
  Mail,
  Book,
  Home,
  DoorClosed,
  UserCircle,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";

interface StudentGridViewProps {
  students: Student[];
  getAttendanceStatus: (
    studentId: string
  ) => "present" | "absent" | "late" | "excused";
  getStatusIcon: (
    status: "present" | "absent" | "late" | "excused"
  ) => JSX.Element;
  selectedRoom: string;
  onStatusUpdate?: (
    studentId: string,
    newStatus: "present" | "absent" | "late" | "excused"
  ) => void;
}

export function StudentGridView({
  students,
  getAttendanceStatus,
  getStatusIcon,
  selectedRoom,
  onStatusUpdate,
}: StudentGridViewProps) {
  const { t } = useTranslation();
  const [localStatuses, setLocalStatuses] = useState<
    Record<string, "present" | "absent" | "late" | "excused">
  >({});
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  const getEffectiveStatus = (studentId: string) => {
    return localStatuses[studentId] || getAttendanceStatus(studentId);
  };

  const handleStatusUpdate = (
    studentId: string,
    newStatus: "present" | "absent" | "late" | "excused"
  ) => {
    // Update local state
    setLocalStatuses((prev) => ({
      ...prev,
      [studentId]: newStatus,
    }));

    // Call the parent component's update function if provided
    if (onStatusUpdate) {
      onStatusUpdate(studentId, newStatus);
    }
  };

  return (
    <>
      <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 md:gap-4 w-full">
        {students.map((student) => {
          const status = getEffectiveStatus(student.id);
          return (
            <div
              key={student.id}
              className="border rounded-md p-2 md:p-4 flex flex-col items-center hover:shadow-md transition-shadow aspect-square"
            >
              <div className="relative mb-1">
                {student.photoUrl ? (
                  <img
                    src={student.photoUrl}
                    alt={student.name}
                    className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 rounded-full object-cover ring-2 ring-primary/10"
                  />
                ) : (
                  <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 rounded-full bg-muted flex items-center justify-center ring-2 ring-primary/10">
                    <UserCircle className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-muted-foreground" />
                  </div>
                )}
                <div className="absolute -bottom-1 -right-1">
                  {getStatusIcon(status)}
                </div>
              </div>

              <div className="font-medium text-center truncate w-full mb-1 text-xs">
                {student.name}
              </div>

              <div className="flex flex-col gap-1 mt-auto w-full">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full h-6 bg-gray-50 hover:bg-gray-100 text-gray-700 border-gray-200 text-[9px] px-1"
                  onClick={() => setSelectedStudent(student)}
                >
                  {t("students.directory.viewDetails")}
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className={`w-full h-7 gap-1 text-[9px] px-1 ${
                        status === "present"
                          ? "bg-green-100 hover:bg-green-200 text-green-700 border-green-300"
                          : status === "absent"
                          ? "bg-red-100 hover:bg-red-200 text-red-700 border-red-300"
                          : status === "late"
                          ? "bg-amber-100 hover:bg-amber-200 text-amber-700 border-amber-300"
                          : status === "excused"
                          ? "bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-300"
                          : "bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300"
                      }`}
                    >
                      {getStatusIcon(status)}
                      <span className="capitalize truncate max-w-[60px]">
                        {t(`teacher.dashboard.${status}`)}
                      </span>
                      <ChevronDown className="h-2 w-2 flex-shrink-0 ml-auto opacity-70" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[110px]">
                    <DropdownMenuItem
                      onClick={() => handleStatusUpdate(student.id, "present")}
                      className="gap-1 text-[9px] py-1"
                    >
                      {getStatusIcon("present")}{" "}
                      {t("teacher.dashboard.present")}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleStatusUpdate(student.id, "absent")}
                      className="gap-1 text-[9px] py-1"
                    >
                      {getStatusIcon("absent")} {t("teacher.dashboard.absent")}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleStatusUpdate(student.id, "late")}
                      className="gap-1 text-[9px] py-1"
                    >
                      {getStatusIcon("late")} {t("teacher.dashboard.late")}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleStatusUpdate(student.id, "excused")}
                      className="gap-1 text-[9px] py-1"
                    >
                      {getStatusIcon("excused")}{" "}
                      {t("teacher.dashboard.excused")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          );
        })}
      </div>

      {/* Student Details Dialog */}
      <Dialog
        open={!!selectedStudent}
        onOpenChange={() => setSelectedStudent(null)}
      >
        <DialogContent className="sm:max-w-[425px] max-w-[90vw] p-3 sm:p-6">
          <DialogHeader>
            <DialogTitle>{t("students.profile.title")}</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm">
              {t("students.profile.detailedInfo", {
                name: selectedStudent?.name,
              })}
            </DialogDescription>
          </DialogHeader>
          {selectedStudent && (
            <div className="grid gap-4 py-4">
              <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4">
                {selectedStudent.photoUrl ? (
                  <img
                    src={selectedStudent.photoUrl}
                    alt={selectedStudent.name}
                    className="w-20 h-20 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-20 h-20 rounded-full bg-muted flex items-center justify-center">
                    <UserCircle className="w-12 h-12 text-muted-foreground" />
                  </div>
                )}
                <div className="text-center sm:text-left">
                  <h3 className="font-medium text-lg">
                    {selectedStudent.name}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {t("students.profile.studentId")}:{" "}
                    {selectedStudent.studentId}
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">{selectedStudent.email}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Book className="w-4 h-4 flex-shrink-0" />
                  <span>
                    {t("students.profile.course")}:{" "}
                    {selectedStudent.course || t("students.directory.notSet")}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Home className="w-4 h-4 flex-shrink-0" />
                  <span>
                    {t("students.profile.block")}:{" "}
                    {selectedStudent.blockName ||
                      t("students.directory.notAssigned")}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <DoorClosed className="w-4 h-4 flex-shrink-0" />
                  <span>
                    {t("students.profile.room")}:{" "}
                    {selectedStudent.roomNumber ||
                      t("students.directory.notAssigned")}
                  </span>
                </div>
              </div>

              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">
                  {t("students.profile.todaysAttendance", "Today's Attendance")}
                </h4>
                <div className="flex items-center gap-2">
                  {getStatusIcon(getEffectiveStatus(selectedStudent.id))}
                  <span className="capitalize">
                    {t(`teacher.dashboard.${getEffectiveStatus(selectedStudent.id)}`)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
