import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Camera, Fingerprint, Key, Check, Loader2 } from "lucide-react";
import { toast as sonnerToast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { startAuthentication, isWebAuthnAvailable } from "@/lib/webauthn";
import { Input } from "@/components/ui/input";
import {
  getDeviceFingerprint,
  isLocationSpoofed,
  checkConcurrentSessions,
  isDeviceConsistent,
  hasReachedRateLimit,
  calculateDistance,
  createLocationAlert,
} from "@/lib/utils/security";

// Define strict types for our GeoJSON Point
type GeoJSONPoint = {
  type: "Point";
  coordinates: [number, number]; // [longitude, latitude]
};

type LocationState = GeoJSONPoint | null;

interface RoomLocation {
  latitude: number;
  longitude: number;
  radius_meters: number;
}

export default function QRScannerNew() {
  const [isScanning, setIsScanning] = useState(false);
  const [needsVerification, setNeedsVerification] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [verificationType, setVerificationType] = useState<
    "biometric" | "pin" | null
  >(null);
  const [pin, setPin] = useState("");
  const [success, setSuccess] = useState(false);
  const [location, setLocation] = useState<LocationState>(null);
  const { toast } = useToast();
  const { profile } = useAuth();

  // In a real app, this would be decoded from the QR code
  // For testing, we'll fetch a real room from the database
  const [roomData, setRoomData] = useState<{ id: string; name: string } | null>(
    null
  );

  // Fetch an available room for testing
  useEffect(() => {
    const fetchRoom = async () => {
      // If a specific room ID was provided, use it
      const roomId = "01d69ac9-d2ba-49c5-83f7-1eff69bf98d6"; // Use the provided UUID

      const { data: room, error } = await supabase
        .from("rooms")
        .select("id, name")
        .eq("id", roomId)
        .single();

      if (error) {
        console.error("Error fetching room:", error);
        // Fallback to any room if specific room not found
        const { data: rooms, error: fallbackError } = await supabase
          .from("rooms")
          .select("id, name")
          .limit(1);

        if (fallbackError) {
          console.error("Error fetching fallback room:", fallbackError);
          return;
        }

        if (rooms && rooms.length > 0) {
          setRoomData(rooms[0]);
        }
      } else if (room) {
        setRoomData(room);
      }
    };

    fetchRoom();
  }, []);

  // Simulate QR scanning
  const handleStartScan = () => {
    setIsScanning(true);
    setSuccess(false);

    // Simulate a delay for "scanning"
    setTimeout(() => {
      setIsScanning(false);

      if (!roomData) {
        toast({
          title: "Error",
          description:
            "No valid room found. Please contact your administrator.",
          variant: "destructive",
        });
        return;
      }

      setNeedsVerification(true);
    }, 1000);
  };

  const showLocationInstructions = () => {
    toast({
      title: "Location Access Required",
      description: "Please enable location access in your browser settings to record attendance.",
      variant: "destructive",
      duration: 5000,
    });
  };

  const handleVerificationMethod = async (method: "biometric" | "pin") => {
    setVerificationType(method);

    if (method === "biometric") {
      try {
        if (!profile) {
          throw new Error("User profile not found");
        }

        // Check if WebAuthn is available
        if (!isWebAuthnAvailable()) {
          throw new Error(
            "Biometric/security key authentication is not supported on this device or browser"
          );
        }

        // Check if user has registered biometrics
        const { data: credentials, error: credentialsError } = await supabase
          .from("biometric_credentials")
          .select("credential_id")
          .eq("user_id", profile.id)
          .single();

        if (credentialsError) {
          if (credentialsError.code === "PGRST116") {
            throw new Error(
              "Please register your biometrics in your profile settings first"
            );
          }
          console.error("Error checking biometric credentials:", credentialsError);
          throw new Error(
            "Failed to check biometric credentials. Please try again."
          );
        }

        // Start biometric authentication
        await startAuthentication(profile.id);

        // If authentication is successful, proceed with attendance
        recordAttendance();
      } catch (error) {
        console.error("Authentication verification failed:", error);
        let errorMessage = "";

        if (error instanceof Error) {
          if (error.message.includes("NotAllowedError")) {
            errorMessage =
              "Authentication was cancelled or timed out. Please try again.";
          } else if (error.message.includes("register your biometrics")) {
            errorMessage = error.message;
          } else {
            errorMessage =
              "Authentication failed. Please try using PIN instead.";
          }
        } else {
          errorMessage =
            "Authentication failed. Please ensure your device supports biometric or security key authentication, or use PIN instead.";
        }

        toast({
          title: "Verification Failed",
          description: errorMessage,
          variant: "destructive",
        });
        // Reset verification type to allow user to try again
        setVerificationType(null);
        setIsSubmitting(false);
      }
    }
  };

  const handlePinSubmit = async () => {
    console.log("PIN submit initiated");
    setIsSubmitting(true);

    try {
      if (!profile || !roomData) {
        console.error("Missing required data:", {
          profileExists: !!profile,
          roomDataExists: !!roomData
        });
        toast({
          title: "Error",
          description: !profile
            ? "User profile not found. Please log in again."
            : "Room data not found. Please try again or contact support.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (!profile?.pin) {
        console.log("PIN not set in profile");
        toast({
          title: "PIN not set",
          description: "Please set your PIN in your profile settings",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      console.log("Validating PIN...");
      // Validate PIN against the stored PIN
      if (profile.pin !== pin) {
        console.log("Invalid PIN entered");
        toast({
          title: "Invalid PIN",
          description: "The PIN you entered is incorrect",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      console.log("PIN validation successful");

      // Check if student has already taken attendance today for this room
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      console.log("Checking for existing attendance record today...");
      const { data: existingAttendance, error: attendanceError } = await supabase
        .from("attendance_records")
        .select("id, timestamp")
        .eq("student_id", profile.id)
        .eq("room_id", roomData.id)
        .gte("timestamp", today.toISOString())
        .order("timestamp", { ascending: false })
        .limit(1);

      if (attendanceError) {
        console.error("Error checking existing attendance:", attendanceError);
      }

      if (existingAttendance && existingAttendance.length > 0) {
        console.log("Student already has attendance record for today:", existingAttendance[0]);
        toast({
          title: "Already Recorded",
          description: `You've already recorded attendance for ${roomData.name} today at ${new Date(existingAttendance[0].timestamp).toLocaleTimeString()}`,
          variant: "default",
        });
        setIsSubmitting(false);
        return;
      }

      // PIN is valid and no existing attendance, proceed to record attendance
      recordAttendance();

    } catch (error) {
      console.error("Error in PIN submission:", error);
      toast({
        title: "Error",
        description:
          "An error occurred while processing your PIN. Please try again.",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  const recordAttendance = async () => {
    if (!profile || !roomData) {
      console.error("Missing required data for attendance recording");
      toast({
        title: "Error",
        description: "Missing required data. Please try again.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Show processing toast
    toast({
      title: "Processing",
      description: "Recording your attendance...",
      variant: "default",
    });

    try {
      // Get device information
      console.log("Getting device fingerprint...");
      const deviceInfo = await getDeviceFingerprint();

      // Start location check in parallel with attendance recording
      console.log("Starting location check...");

      // Prepare attendance data without location first
      const now = new Date().toISOString();
      let attendanceData = {
        student_id: profile.id,
        room_id: roomData.id,
        timestamp: now,
        device_info: deviceInfo,
        verification_method: verificationType || "pin",
        status: "present",
        created_at: now,
      };

      // Start getting location
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          console.log("Location obtained:", {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });

          // Create GeoJSON Point
          const geoJsonLocation = {
            type: "Point",
            coordinates: [
              Number(position.coords.longitude),
              Number(position.coords.latitude),
            ],
          };

          // Check location against room/block location
          let isWithinRadius = true;
          let distance = 0;

          try {
            // First try to get room location from room_locations table
            const { data: roomLocationData } = await supabase
              .from("room_locations")
              .select("latitude, longitude, radius_meters")
              .eq("room_id", roomData.id)
              .maybeSingle();

            let roomOrBlockLocation = roomLocationData;

            // If no room location is set, try to get block location
            if (!roomOrBlockLocation) {
              console.log("No room location found, checking block location...");

              // Get the room's block_id
              const { data: roomWithBlock } = await supabase
                .from("rooms")
                .select("block_id")
                .eq("id", roomData.id)
                .single();

              if (roomWithBlock?.block_id) {
                // Get the block location
                const { data: blockLocationData } = await supabase
                  .from("block_locations")
                  .select("latitude, longitude, radius_meters")
                  .eq("block_id", roomWithBlock.block_id)
                  .maybeSingle();

                roomOrBlockLocation = blockLocationData;
              }
            }

            // If we found a location, check distance
            if (roomOrBlockLocation) {
              distance = calculateDistance(
                position.coords.latitude,
                position.coords.longitude,
                roomOrBlockLocation.latitude,
                roomOrBlockLocation.longitude
              );

              console.log(`Distance from room: ${Math.round(distance)} meters (allowed radius: ${roomOrBlockLocation.radius_meters} meters)`);

              // Check if student is within radius
              isWithinRadius = distance <= roomOrBlockLocation.radius_meters;

              // If outside radius, create alert but still record attendance
              if (!isWithinRadius) {
                try {
                  await createLocationAlert(
                    profile.id,
                    position.coords.latitude,
                    position.coords.longitude,
                    roomOrBlockLocation.latitude,
                    roomOrBlockLocation.longitude,
                    distance,
                    roomData.name,
                    roomOrBlockLocation.radius_meters
                  );

                  // Calculate how far outside the radius the student is
                  const excessDistance = Math.round(distance - roomOrBlockLocation.radius_meters);
                  const distanceDisplay = distance >= 1000
                    ? `${(distance / 1000).toFixed(1)}km`
                    : `${Math.round(distance)}m`;

                  // Show warning to student
                  toast({
                    title: "Location Warning",
                    description: `You are ${distanceDisplay} away from your room, which is ${excessDistance}m outside the ${roomOrBlockLocation.radius_meters}m allowed radius. Your teacher will be notified.`,
                    variant: "warning",
                  });
                } catch (alertError) {
                  console.error("Error creating location alert:", alertError);
                }
              }
            }
          } catch (locationError) {
            console.error("Error checking location:", locationError);
          }

          // Update attendance data with location
          attendanceData = {
            ...attendanceData,
            location: geoJsonLocation,
          };

          // Update the attendance record with location data
          await finalizeAttendanceRecord(attendanceData, distance, isWithinRadius);
        },
        (error) => {
          console.error("Location error:", error);
          // Still record attendance even if location fails
          finalizeAttendanceRecord(attendanceData, 0, true);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0,
        }
      );
    } catch (error) {
      console.error("Error in attendance recording:", error);
      toast({
        title: "Error",
        description: "Failed to record attendance. Please try again.",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  const finalizeAttendanceRecord = async (
    attendanceData: any,
    distance: number,
    isWithinRadius: boolean
  ) => {
    try {
      console.log("Finalizing attendance record:", attendanceData);

      // Insert the attendance record
      const { data: insertData, error: recordError } = await supabase
        .from("attendance_records")
        .insert(attendanceData)
        .select()
        .single();

      if (recordError) {
        console.error("Attendance recording error:", recordError);
        toast({
          title: "Error",
          description: "Failed to record attendance. Please try again.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      console.log("Successfully inserted attendance record:", insertData);

      // Create a notification for the student
      try {
        await supabase.from("notifications").insert({
          student_id: profile?.id,
          title: "✅ Attendance Recorded",
          message: `You have successfully checked in to ${
            roomData?.name || "class"
          } at ${new Date().toLocaleTimeString()}.`,
          type: "attendance",
          read: false,
          timestamp: new Date().toISOString(),
          metadata: JSON.stringify({
            room_name: roomData?.name || "Unknown",
            verification_method: verificationType || "pin",
            distance_meters: Math.round(distance),
            within_radius: isWithinRadius,
          }),
        });
        console.log("Student notification created successfully");
      } catch (notificationError) {
        console.error("Error creating notification:", notificationError);
      }

      // Show success message
      toast({
        title: "Success",
        description: "Your attendance has been recorded successfully",
        variant: "default",
      });

      // Show additional success toast
      sonnerToast.success("Attendance Recorded", {
        description: `Your attendance has been successfully recorded for ${roomData?.name || "class"}.`,
      });

      // Update UI state
      setSuccess(true);
      setNeedsVerification(false);
      setIsSubmitting(false);

    } catch (error) {
      console.error("Error finalizing attendance record:", error);
      toast({
        title: "Error",
        description: "Failed to record attendance. Please try again.",
        variant: "destructive",
      });
      setIsSubmitting(false);
    }
  };

  const renderContent = () => {
    if (success) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
            <Check size={40} className="text-green-600" />
          </div>
          <h3 className="text-xl font-medium text-center">
            Attendance Recorded!
          </h3>
          <p className="text-center text-muted-foreground">
            Your attendance has been successfully recorded and sent to your
            teacher
          </p>
          <Button
            onClick={() => {
              setSuccess(false);
              setNeedsVerification(false);
            }}
            className="mt-4"
          >
            Scan Another Code
          </Button>
        </div>
      );
    }

    if (isScanning) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="w-64 h-64 border-4 border-secondary rounded-lg relative overflow-hidden">
            <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>
            <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-secondary transform -translate-y-1/2 animate-pulse"></div>
          </div>
          <p className="text-center text-muted-foreground">
            Scanning QR code...
          </p>
        </div>
      );
    }

    if (needsVerification) {
      if (verificationType === "biometric") {
        return (
          <div className="flex flex-col items-center justify-center p-8 space-y-6">
            <Fingerprint size={80} className="text-secondary animate-pulse" />
            <p className="text-center text-lg font-medium">
              Use your biometric or security key to verify
            </p>
          </div>
        );
      }

      if (verificationType === "pin") {
        return (
          <div className="flex flex-col items-center justify-center p-6 space-y-4">
            <h3 className="text-lg font-medium">Enter your 6-digit PIN</h3>
            <div className="flex flex-col items-center space-y-4 w-full">
              <input
                type="password"
                className="w-full px-4 py-2 text-center text-2xl tracking-widest border rounded-md"
                maxLength={6}
                value={pin}
                onChange={(e) => setPin(e.target.value.replace(/[^0-9]/g, ""))}
                autoFocus
              />
              <Button
                onClick={handlePinSubmit}
                className="w-full bg-secondary"
                disabled={pin.length !== 6 || isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Verifying
                  </>
                ) : (
                  "Verify"
                )}
              </Button>
            </div>
          </div>
        );
      }

      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-6">
          <h3 className="text-xl font-medium text-center">
            Verify Your Identity
          </h3>
          <div className="grid grid-cols-2 gap-4 w-full">
            <Button
              onClick={() => handleVerificationMethod("biometric")}
              variant="outline"
              className="h-32 flex flex-col items-center justify-center gap-2 hover:border-secondary hover:text-secondary transition-colors"
            >
              <Fingerprint size={32} />
              <span>Biometric</span>
            </Button>
            <Button
              onClick={() => handleVerificationMethod("pin")}
              variant="outline"
              className="h-32 flex flex-col items-center justify-center gap-2 hover:border-primary hover:text-primary transition-colors"
            >
              <Key size={32} />
              <span>PIN Code</span>
            </Button>
          </div>
        </div>
      );
    }

    if (isSubmitting) {
      return (
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <Loader2 size={64} className="animate-spin loader-icon" data-testid="LoaderIcon" />
          <p className="text-center">{t("loading.recordingAttendance")}</p>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <Camera size={64} className="animate-pulse camera-icon" data-testid="CameraIcon" />
        <p className="text-center">
          {t("qrScanner.scanQrDescriptionFull")}
        </p>
        <Button
          onClick={handleStartScan}
          className="w-full bg-primary hover:bg-primary/90 transition-colors"
        >
          {t("qrScanner.startScan")}
        </Button>
      </div>
    );
  };

  return (
    <Card className="w-full max-w-md mx-auto border-2 border-muted hover:border-muted/80 transition-colors">
      <CardHeader>
        <CardTitle>QR Code Attendance</CardTitle>
        <CardDescription>
          Scan the QR code to record your attendance
        </CardDescription>
      </CardHeader>
      <CardContent>{renderContent()}</CardContent>
    </Card>
  );