-- Migration to add functionality for clearing processed biometric deletion requests
-- This allows school admins to clean up old processed requests from their view

-- Step 1: Create RPC function to clear processed biometric deletion requests
CREATE OR REPLACE FUNCTION public.clear_processed_biometric_deletion_requests()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    admin_profile_record RECORD;
    deleted_count INTEGER;
BEGIN
    -- Get the admin's profile to verify they're an admin and get their school
    SELECT * INTO admin_profile_record
    FROM public.profiles
    WHERE user_id = auth.uid() AND role = 'admin';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Only school admins can clear processed biometric deletion requests';
    END IF;

    -- Delete processed requests (approved or rejected) for the admin's school
    -- Keep pending requests as they still need to be processed
    DELETE FROM public.biometric_deletion_requests
    WHERE school_id = admin_profile_record.school_id 
    AND status IN ('approved', 'rejected');
    
    -- Get the number of deleted records
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;

-- Step 2: Create RPC function to clear specific processed biometric deletion request
CREATE OR REPLACE FUNCTION public.clear_biometric_deletion_request(
    request_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    admin_profile_record RECORD;
    request_school_id UUID;
    request_status TEXT;
BEGIN
    -- Get the admin's profile to verify they're an admin and get their school
    SELECT * INTO admin_profile_record
    FROM public.profiles
    WHERE user_id = auth.uid() AND role = 'admin';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Only school admins can clear biometric deletion requests';
    END IF;

    -- Get the request details and verify it belongs to the admin's school
    SELECT school_id, status INTO request_school_id, request_status
    FROM public.biometric_deletion_requests
    WHERE id = request_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Deletion request not found';
    END IF;
    
    -- Verify the request belongs to the admin's school
    IF request_school_id != admin_profile_record.school_id THEN
        RAISE EXCEPTION 'You can only clear deletion requests for your school';
    END IF;
    
    -- Only allow clearing of processed requests (not pending ones)
    IF request_status = 'pending' THEN
        RAISE EXCEPTION 'Cannot clear pending requests. Please approve or reject the request first.';
    END IF;

    -- Delete the specific request
    DELETE FROM public.biometric_deletion_requests
    WHERE id = request_id;
END;
$$;

-- Step 3: Grant execute permissions on the RPC functions
GRANT EXECUTE ON FUNCTION public.clear_processed_biometric_deletion_requests() TO authenticated;
GRANT EXECUTE ON FUNCTION public.clear_biometric_deletion_request(UUID) TO authenticated;
