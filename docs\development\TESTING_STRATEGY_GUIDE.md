# 🧪 Testing Strategy Guide

## 📋 **Current Testing Status**

Your app currently has minimal testing (only i18n tests). For production readiness, comprehensive testing is essential.

## 🎯 **Testing Goals**

- **Unit Test Coverage**: >80%
- **Integration Test Coverage**: >70%
- **E2E Test Coverage**: Critical user flows
- **Performance Tests**: Load and stress testing
- **Security Tests**: Vulnerability and penetration testing

## 🔧 **Testing Infrastructure Setup**

### **1. Install Testing Dependencies**

```bash
# Core testing framework
npm install --save-dev vitest @vitest/ui jsdom

# React testing utilities
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event

# E2E testing
npm install --save-dev playwright @playwright/test

# API testing
npm install --save-dev supertest

# Performance testing
npm install --save-dev lighthouse-ci

# Security testing
npm install --save-dev audit-ci
```

### **2. Configure Vitest**

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react-swc'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/tests/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})
```

### **3. Test Setup File**

```typescript
// src/tests/setup.ts
import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_SUPABASE_URL: 'http://localhost:54321',
    VITE_SUPABASE_ANON_KEY: 'test-key',
    MODE: 'test',
  },
}))

// Mock Supabase client
vi.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      signInWithPassword: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
    })),
  },
}))

// Mock router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({ pathname: '/' }),
  }
})
```

## 🔬 **Unit Testing**

### **1. Component Testing**

```typescript
// src/tests/components/Login.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { Login } from '@/pages/Login'
import { AuthProvider } from '@/context/AuthContext'
import { BrowserRouter } from 'react-router-dom'

const LoginWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <AuthProvider>
      {children}
    </AuthProvider>
  </BrowserRouter>
)

describe('Login Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders login form', () => {
    render(<Login />, { wrapper: LoginWrapper })
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    render(<Login />, { wrapper: LoginWrapper })
    
    const submitButton = screen.getByRole('button', { name: /sign in/i })
    fireEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const mockSignIn = vi.fn().mockResolvedValue({ data: { user: {} }, error: null })
    vi.mocked(supabase.auth.signInWithPassword).mockImplementation(mockSignIn)
    
    render(<Login />, { wrapper: LoginWrapper })
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }))
    
    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })
  })
})
```

### **2. Hook Testing**

```typescript
// src/tests/hooks/useAttendance.test.ts
import { renderHook, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { useAttendance } from '@/hooks/useAttendance'

describe('useAttendance', () => {
  it('fetches attendance data', async () => {
    const mockData = [
      { id: '1', student_id: 'student1', created_at: '2024-01-01' }
    ]
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: mockData, error: null }),
    } as any)
    
    const { result } = renderHook(() => useAttendance('student1'))
    
    await waitFor(() => {
      expect(result.current.data).toEqual(mockData)
      expect(result.current.loading).toBe(false)
    })
  })

  it('handles errors gracefully', async () => {
    const mockError = new Error('Database error')
    
    vi.mocked(supabase.from).mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockResolvedValue({ data: null, error: mockError }),
    } as any)
    
    const { result } = renderHook(() => useAttendance('student1'))
    
    await waitFor(() => {
      expect(result.current.error).toBe(mockError.message)
      expect(result.current.loading).toBe(false)
    })
  })
})
```

### **3. Utility Function Testing**

```typescript
// src/tests/utils/date-utils.test.ts
import { describe, it, expect } from 'vitest'
import { formatDate, isWithinTimeRange, calculateAttendanceRate } from '@/lib/date-utils'

describe('Date Utils', () => {
  describe('formatDate', () => {
    it('formats date correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z')
      expect(formatDate(date, 'yyyy-MM-dd')).toBe('2024-01-15')
    })

    it('handles invalid dates', () => {
      expect(formatDate(null, 'yyyy-MM-dd')).toBe('')
    })
  })

  describe('isWithinTimeRange', () => {
    it('returns true for time within range', () => {
      const now = new Date('2024-01-15T10:30:00Z')
      const start = new Date('2024-01-15T09:00:00Z')
      const end = new Date('2024-01-15T11:00:00Z')
      
      expect(isWithinTimeRange(now, start, end)).toBe(true)
    })

    it('returns false for time outside range', () => {
      const now = new Date('2024-01-15T12:30:00Z')
      const start = new Date('2024-01-15T09:00:00Z')
      const end = new Date('2024-01-15T11:00:00Z')
      
      expect(isWithinTimeRange(now, start, end)).toBe(false)
    })
  })

  describe('calculateAttendanceRate', () => {
    it('calculates attendance rate correctly', () => {
      const attendanceRecords = [
        { present: true },
        { present: true },
        { present: false },
        { present: true },
      ]
      
      expect(calculateAttendanceRate(attendanceRecords)).toBe(75)
    })

    it('handles empty records', () => {
      expect(calculateAttendanceRate([])).toBe(0)
    })
  })
})
```

## 🔗 **Integration Testing**

### **1. API Integration Tests**

```typescript
// src/tests/integration/attendance-api.test.ts
import { describe, it, expect, beforeEach } from 'vitest'
import { createClient } from '@supabase/supabase-js'

// Use test database
const supabaseTest = createClient(
  'http://localhost:54321',
  'test-anon-key'
)

describe('Attendance API Integration', () => {
  beforeEach(async () => {
    // Clean up test data
    await supabaseTest.from('attendance_records').delete().neq('id', '')
  })

  it('creates attendance record', async () => {
    const attendanceData = {
      student_id: 'test-student-id',
      location_id: 'test-location-id',
      school_id: 'test-school-id',
    }

    const { data, error } = await supabaseTest
      .from('attendance_records')
      .insert(attendanceData)
      .select()
      .single()

    expect(error).toBeNull()
    expect(data).toMatchObject(attendanceData)
  })

  it('retrieves attendance records', async () => {
    // Insert test data
    await supabaseTest.from('attendance_records').insert([
      { student_id: 'student1', location_id: 'loc1', school_id: 'school1' },
      { student_id: 'student1', location_id: 'loc2', school_id: 'school1' },
    ])

    const { data, error } = await supabaseTest
      .from('attendance_records')
      .select('*')
      .eq('student_id', 'student1')

    expect(error).toBeNull()
    expect(data).toHaveLength(2)
  })
})
```

### **2. Authentication Flow Tests**

```typescript
// src/tests/integration/auth-flow.test.ts
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { App } from '@/App'

describe('Authentication Flow', () => {
  it('redirects unauthenticated users to login', async () => {
    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText(/sign in/i)).toBeInTheDocument()
    })
  })

  it('allows authenticated users to access dashboard', async () => {
    // Mock authenticated state
    vi.mocked(supabase.auth.getSession).mockResolvedValue({
      data: { session: { user: { id: 'user1' } } },
      error: null,
    })

    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText(/dashboard/i)).toBeInTheDocument()
    })
  })
})
```

## 🎭 **End-to-End Testing**

### **1. Playwright Configuration**

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './src/tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
  },
})
```

### **2. E2E Test Examples**

```typescript
// src/tests/e2e/attendance-flow.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Attendance Recording Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login as student
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="login-button"]')
    await page.waitForURL('/student')
  })

  test('student can record attendance via QR code', async ({ page }) => {
    // Navigate to QR scanner
    await page.click('[data-testid="scan-qr-button"]')
    
    // Mock QR code scan (in real test, you'd simulate camera)
    await page.evaluate(() => {
      window.dispatchEvent(new CustomEvent('qr-scanned', {
        detail: { qrCode: 'test-qr-code-data' }
      }))
    })
    
    // Verify attendance recorded
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="attendance-status"]')).toContainText('Present')
  })

  test('student can submit excuse', async ({ page }) => {
    await page.click('[data-testid="submit-excuse-button"]')
    
    await page.fill('[data-testid="excuse-reason"]', 'Medical appointment')
    await page.fill('[data-testid="excuse-start-date"]', '2024-01-15')
    await page.fill('[data-testid="excuse-end-date"]', '2024-01-15')
    
    await page.click('[data-testid="submit-excuse"]')
    
    await expect(page.locator('[data-testid="excuse-submitted"]')).toBeVisible()
  })
})
```

### **3. Admin Flow Tests**

```typescript
// src/tests/e2e/admin-flow.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Admin Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'admin123')
    await page.click('[data-testid="login-button"]')
    await page.waitForURL('/admin')
  })

  test('admin can manage students', async ({ page }) => {
    await page.click('[data-testid="students-tab"]')
    
    // Add new student
    await page.click('[data-testid="add-student-button"]')
    await page.fill('[data-testid="student-name"]', 'John Doe')
    await page.fill('[data-testid="student-email"]', '<EMAIL>')
    await page.click('[data-testid="save-student"]')
    
    // Verify student added
    await expect(page.locator('[data-testid="student-list"]')).toContainText('John Doe')
  })

  test('admin can view attendance reports', async ({ page }) => {
    await page.click('[data-testid="reports-tab"]')
    
    // Generate report
    await page.selectOption('[data-testid="report-type"]', 'attendance')
    await page.fill('[data-testid="date-from"]', '2024-01-01')
    await page.fill('[data-testid="date-to"]', '2024-01-31')
    await page.click('[data-testid="generate-report"]')
    
    // Verify report generated
    await expect(page.locator('[data-testid="report-data"]')).toBeVisible()
  })
})
```

## ⚡ **Performance Testing**

### **1. Load Testing**

```typescript
// src/tests/performance/load-test.ts
import { test, expect } from '@playwright/test'

test.describe('Performance Tests', () => {
  test('page load performance', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(3000) // 3 seconds max
  })

  test('QR code generation performance', async ({ page }) => {
    await page.goto('/teacher')
    
    const startTime = Date.now()
    await page.click('[data-testid="generate-qr"]')
    await page.waitForSelector('[data-testid="qr-code"]')
    
    const generationTime = Date.now() - startTime
    expect(generationTime).toBeLessThan(1000) // 1 second max
  })
})
```

### **2. Lighthouse Performance Tests**

```javascript
// src/tests/performance/lighthouse.test.js
const lighthouse = require('lighthouse')
const chromeLauncher = require('chrome-launcher')

describe('Lighthouse Performance', () => {
  let chrome
  
  beforeAll(async () => {
    chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] })
  })
  
  afterAll(async () => {
    await chrome.kill()
  })
  
  test('homepage performance score', async () => {
    const options = { logLevel: 'info', output: 'json', port: chrome.port }
    const runnerResult = await lighthouse('http://localhost:5173', options)
    
    const performanceScore = runnerResult.lhr.categories.performance.score * 100
    expect(performanceScore).toBeGreaterThan(90)
  })
})
```

## 🔒 **Security Testing**

### **1. Input Validation Tests**

```typescript
// src/tests/security/input-validation.test.ts
import { test, expect } from '@playwright/test'

test.describe('Security Tests', () => {
  test('prevents XSS attacks', async ({ page }) => {
    await page.goto('/login')
    
    // Try XSS payload
    const xssPayload = '<script>alert("XSS")</script>'
    await page.fill('[data-testid="email"]', xssPayload)
    
    // Verify script doesn't execute
    page.on('dialog', () => {
      throw new Error('XSS vulnerability detected!')
    })
    
    await page.click('[data-testid="login-button"]')
    // Should show validation error, not execute script
  })

  test('prevents SQL injection', async ({ page }) => {
    await page.goto('/admin/students')
    
    // Try SQL injection payload
    const sqlPayload = "'; DROP TABLE students; --"
    await page.fill('[data-testid="search-students"]', sqlPayload)
    await page.press('[data-testid="search-students"]', 'Enter')
    
    // Verify search works normally (no SQL injection)
    await expect(page.locator('[data-testid="student-list"]')).toBeVisible()
  })
})
```

## 📊 **Test Automation & CI/CD**

### **1. GitHub Actions Workflow**

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npx playwright install
      - run: npm run test:e2e

  security-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm audit --audit-level high
      - run: npx audit-ci --high
```

### **2. Package.json Scripts**

```json
{
  "scripts": {
    "test": "vitest",
    "test:unit": "vitest run --coverage",
    "test:watch": "vitest --watch",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:performance": "lighthouse-ci autorun",
    "test:security": "npm audit && audit-ci --high",
    "test:all": "npm run test:unit && npm run test:e2e && npm run test:performance"
  }
}
```

## 🎯 **Testing Checklist**

### **Before Production**
- [ ] Unit tests pass (>80% coverage)
- [ ] Integration tests pass
- [ ] E2E tests cover critical flows
- [ ] Performance tests meet benchmarks
- [ ] Security tests pass
- [ ] Cross-browser testing complete
- [ ] Mobile testing complete
- [ ] Accessibility testing complete

### **Ongoing**
- [ ] Tests run on every commit
- [ ] Performance monitoring active
- [ ] Security scans automated
- [ ] Test coverage maintained
- [ ] Flaky tests identified and fixed

**Remember**: Good tests are your safety net for confident deployments!
