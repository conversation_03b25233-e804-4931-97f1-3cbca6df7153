import { supabase } from "@/lib/supabase";
import { getDeviceFingerprint } from "@/lib/utils/security";

/**
 * Action types for audit logging
 */
export enum AuditActionType {
  // User actions
  USER_LOGIN = "user_login",
  USER_LOGOUT = "user_logout",
  USER_CREATED = "user_created",
  USER_UPDATED = "user_updated",
  USER_DELETED = "user_deleted",
  
  // School actions
  SCHOOL_CREATED = "school_created",
  SCHOOL_UPDATED = "school_updated",
  SCHOOL_DELETED = "school_deleted",
  SCHOOL_SETTINGS_UPDATED = "school_settings_updated",
  
  // Invitation code actions
  INVITATION_CODE_GENERATED = "invitation_code_generated",
  INVITATION_CODE_USED = "invitation_code_used",
  
  // Attendance actions
  ATTENDANCE_RECORDED = "attendance_recorded",
  ATTENDANCE_UPDATED = "attendance_updated",
  ATTENDANCE_DELETED = "attendance_deleted",
  
  // Security actions
  SECURITY_ALERT = "security_alert",
  PERMISSION_CHANGED = "permission_changed",
  
  // Data actions
  DATA_EXPORTED = "data_exported",
  DATA_IMPORTED = "data_imported",
}

/**
 * Entity types for audit logging
 */
export enum AuditEntityType {
  USER = "user",
  SCHOOL = "school",
  STUDENT = "student",
  TEACHER = "teacher",
  ADMIN = "admin",
  ATTENDANCE = "attendance",
  ROOM = "room",
  BLOCK = "block",
  EXCUSE = "excuse",
  NOTIFICATION = "notification",
  SETTINGS = "settings",
  SYSTEM_SCHOOL_SETTINGS_OVERRIDE = "system_school_settings_override",
  INVITATION_CODE = "invitation_code",
  ATTENDANCE_RECORD = "attendance_record",
}

/**
 * Interface for audit log entry
 */
interface AuditLogEntry {
  school_id?: string;
  user_id?: string;
  action_type: AuditActionType;
  entity_type: AuditEntityType;
  entity_id?: string;
  details?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
}

/**
 * Log an action to the audit logs
 * @param entry The audit log entry to record
 * @returns Promise that resolves when the log is recorded
 */
export const logAuditEvent = async (entry: AuditLogEntry): Promise<void> => {
  try {
    // Get device info
    const deviceInfo = getDeviceFingerprint();
    
    // Insert audit log
    const { error } = await supabase.from("audit_logs").insert({
      ...entry,
      user_agent: deviceInfo.userAgent,
      created_at: new Date().toISOString(),
    });
    
    if (error) {
      console.error("Error logging audit event:", error);
    }
  } catch (error) {
    console.error("Error in audit logging:", error);
  }
};

/**
 * Get audit logs for a school
 * @param schoolId The school ID to get logs for
 * @param limit The maximum number of logs to return
 * @param offset The offset for pagination
 * @returns Promise that resolves to the audit logs
 */
export const getSchoolAuditLogs = async (
  schoolId: string,
  limit = 50,
  offset = 0
): Promise<any[]> => {
  try {
    // First get the audit logs
    const { data: auditLogs, error: auditError } = await supabase
      .from("audit_logs")
      .select("*")
      .eq("school_id", schoolId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (auditError) {
      console.error("Error fetching audit logs:", auditError);
      return [];
    }

    if (!auditLogs || auditLogs.length === 0) {
      return [];
    }

    // Get unique user IDs from audit logs
    const userIds = [...new Set(auditLogs.map(log => log.user_id).filter(Boolean))];

    // Fetch user profiles for these user IDs
    const { data: profiles, error: profilesError } = await supabase
      .from("profiles")
      .select("user_id, email")
      .in("user_id", userIds);

    if (profilesError) {
      console.error("Error fetching profiles:", profilesError);
      // Return audit logs without user info if profiles fetch fails
      return auditLogs;
    }

    // Create a map of user_id to email for quick lookup
    const userEmailMap = new Map(profiles?.map(p => [p.user_id, p.email]) || []);

    // Combine audit logs with user information
    const enrichedLogs = auditLogs.map(log => ({
      ...log,
      users: log.user_id ? { email: userEmailMap.get(log.user_id) || "Unknown" } : null
    }));

    return enrichedLogs;
  } catch (error) {
    console.error("Error in getSchoolAuditLogs:", error);
    return [];
  }
};

/**
 * Get system-wide audit logs (for system admins)
 * @param limit The maximum number of logs to return
 * @param offset The offset for pagination
 * @returns Promise that resolves to the audit logs
 */
export const getSystemAuditLogs = async (
  limit = 50,
  offset = 0
): Promise<any[]> => {
  try {
    // First get the audit logs
    const { data: auditLogs, error: auditError } = await supabase
      .from("audit_logs")
      .select("*")
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (auditError) {
      console.error("Error fetching system audit logs:", auditError);
      return [];
    }

    if (!auditLogs || auditLogs.length === 0) {
      return [];
    }

    // Get unique user IDs and school IDs from audit logs
    const userIds = [...new Set(auditLogs.map(log => log.user_id).filter(Boolean))];
    const schoolIds = [...new Set(auditLogs.map(log => log.school_id).filter(Boolean))];

    // Fetch user profiles for these user IDs
    const profilesPromise = userIds.length > 0 ? supabase
      .from("profiles")
      .select("user_id, email")
      .in("user_id", userIds) : Promise.resolve({ data: [], error: null });

    // Fetch schools for these school IDs
    const schoolsPromise = schoolIds.length > 0 ? supabase
      .from("schools")
      .select("id, name")
      .in("id", schoolIds) : Promise.resolve({ data: [], error: null });

    const [profilesResult, schoolsResult] = await Promise.all([profilesPromise, schoolsPromise]);

    if (profilesResult.error) {
      console.error("Error fetching profiles:", profilesResult.error);
    }

    if (schoolsResult.error) {
      console.error("Error fetching schools:", schoolsResult.error);
    }

    // Create maps for quick lookup
    const userEmailMap = new Map(profilesResult.data?.map(p => [p.user_id, p.email]) || []);
    const schoolNameMap = new Map(schoolsResult.data?.map(s => [s.id, s.name]) || []);

    // Combine audit logs with user and school information
    const enrichedLogs = auditLogs.map(log => ({
      ...log,
      users: log.user_id ? { email: userEmailMap.get(log.user_id) || "Unknown" } : null,
      schools: log.school_id ? { name: schoolNameMap.get(log.school_id) || "Unknown" } : null
    }));

    return enrichedLogs;
  } catch (error) {
    console.error("Error in getSystemAuditLogs:", error);
    return [];
  }
};
