-- Create rooms table
CREATE TABLE IF NOT EXISTS public.rooms (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR NOT NULL,
  building VARCHAR,
  floor INTEGER DEFAULT 1,
  capacity INTEGER DEFAULT 30,
  teacher_id UUID REFERENCES profiles(id),
  current_qr_code TEXT,
  qr_expiry TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_rooms_teacher_id ON public.rooms(teacher_id);

-- Enable RLS
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Teachers can view their own rooms" ON public.rooms;
DROP POLICY IF EXISTS "Anyone can view rooms" ON public.rooms;

-- Create policies
CREATE POLICY "Anyone can view rooms"
  ON public.rooms
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Teachers can manage their rooms"
  ON public.rooms
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE user_id = (auth.uid())::uuid
      AND (id = rooms.teacher_id OR role = 'admin')
    )
  );

-- Grant permissions
GRANT ALL ON public.rooms TO authenticated;

-- Add some default rooms for testing
INSERT INTO public.rooms (name, building, floor, capacity)
SELECT 'Test Room ' || n, 'Main Building', 1, 30
FROM generate_series(1, 3) n
WHERE NOT EXISTS (SELECT 1 FROM public.rooms LIMIT 1); 