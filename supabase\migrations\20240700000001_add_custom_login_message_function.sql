-- Create a function to get the custom login message
CREATE OR <PERSON>EPLACE FUNCTION get_custom_login_message()
R<PERSON><PERSON>NS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  message TEXT;
BEGIN
  -- First check for a global override
  SELECT setting_value::TEXT INTO message
  FROM system_settings_overrides
  WHERE setting_name = 'custom_login_message'
    AND override_enabled = true
    AND applies_to_all = true
  LIMIT 1;
  
  -- If no global override, check for any school setting
  IF message IS NULL THEN
    SELECT custom_login_message INTO message
    FROM school_settings
    WHERE custom_login_message IS NOT NULL
    LIMIT 1;
  END IF;
  
  RETURN message;
END;
$$;
