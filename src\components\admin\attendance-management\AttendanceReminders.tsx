import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { 
  Bell, 
  Clock, 
  Send, 
  Settings, 
  Users, 
  AlertCircle,
  CheckCircle,
  Timer,
  Zap
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { useAttendanceSettings } from "@/hooks/useAttendanceSettings";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { toast as sonnerToast } from "sonner";
import { 
  automatedReminderService, 
  type ReminderSettings 
} from "@/lib/services/automated-reminder-service";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function AttendanceReminders() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { profile } = useAuth();
  const { settings: attendanceSettings, loading: attendanceLoading } = useAttendanceSettings();
  
  const [reminderSettings, setReminderSettings] = useState<ReminderSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [sendingManual, setSendingManual] = useState(false);
  const [serviceStatus, setServiceStatus] = useState(automatedReminderService.getStatus());

  // Form state
  const [enabled, setEnabled] = useState(false);
  const [minutesBefore, setMinutesBefore] = useState(30);

  // Load reminder settings
  const loadReminderSettings = async () => {
    if (!profile?.school_id) return;

    try {
      setLoading(true);
      const settings = await automatedReminderService.getReminderSettings(profile.school_id);
      
      if (settings) {
        setReminderSettings(settings);
        setEnabled(settings.enabled);
        setMinutesBefore(settings.minutes_before_end);
      } else {
        // Default settings
        setEnabled(false);
        setMinutesBefore(30);
      }
    } catch (error) {
      console.error("Error loading reminder settings:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.reminders.loadError"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Save reminder settings
  const saveReminderSettings = async () => {
    if (!profile?.school_id) return;

    try {
      setSaving(true);
      
      const newSettings: ReminderSettings = {
        school_id: profile.school_id,
        enabled,
        minutes_before_end: minutesBefore,
      };

      const savedSettings = await automatedReminderService.updateReminderSettings(newSettings);
      setReminderSettings(savedSettings);

      sonnerToast.success(t("admin.attendanceManagement.reminders.settingsSaved"), {
        description: enabled 
          ? t("admin.attendanceManagement.reminders.automationEnabled", { minutes: minutesBefore })
          : t("admin.attendanceManagement.reminders.automationDisabled"),
      });
    } catch (error) {
      console.error("Error saving reminder settings:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.reminders.saveError"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Send manual reminder
  const sendManualReminder = async () => {
    if (!profile?.school_id) return;

    try {
      setSendingManual(true);
      
      const result = await automatedReminderService.sendManualReminder(
        profile.school_id,
        profile.id
      );

      if (result.success) {
        if (result.sentCount > 0) {
          sonnerToast.success(t("admin.attendanceManagement.reminders.manualSent"), {
            description: t("admin.attendanceManagement.reminders.sentToStudents", {
              count: result.sentCount,
            }),
          });
        } else {
          sonnerToast.info(t("admin.attendanceManagement.reminders.noAbsentStudents"), {
            description: t("admin.attendanceManagement.reminders.allStudentsPresent"),
          });
        }
      } else {
        throw new Error(result.error || "Failed to send reminders");
      }
    } catch (error) {
      console.error("Error sending manual reminder:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.reminders.manualSendError"),
        variant: "destructive",
      });
    } finally {
      setSendingManual(false);
    }
  };

  // Calculate reminder time
  const calculateReminderTime = () => {
    if (!attendanceSettings?.recording_end_time) return null;
    
    const [hours, minutes] = attendanceSettings.recording_end_time.split(':').map(Number);
    const endDate = new Date();
    endDate.setHours(hours, minutes, 0, 0);
    
    const reminderDate = new Date(endDate.getTime() - (minutesBefore * 60 * 1000));
    
    return reminderDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  // Update service status periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setServiceStatus(automatedReminderService.getStatus());
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    loadReminderSettings();
  }, [profile?.school_id]);

  if (loading || attendanceLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner message={t("admin.attendanceManagement.reminders.loading")} />
      </div>
    );
  }

  const reminderTime = calculateReminderTime();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold">
          {t("admin.attendanceManagement.reminders.title")}
        </h3>
        <p className="text-sm text-muted-foreground">
          {t("admin.attendanceManagement.reminders.description")}
        </p>
      </div>

      {/* Service Status */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            {t("admin.attendanceManagement.reminders.serviceStatus")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">{t("admin.attendanceManagement.reminders.serviceRunning")}</span>
            <Badge variant={serviceStatus.isRunning ? "default" : "secondary"}>
              {serviceStatus.isRunning 
                ? t("admin.attendanceManagement.reminders.active")
                : t("admin.attendanceManagement.reminders.inactive")
              }
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">{t("admin.attendanceManagement.reminders.sentToday")}</span>
            <Badge variant="outline">
              {serviceStatus.sentRemindersToday}
            </Badge>
          </div>

          {serviceStatus.nextCheck && (
            <div className="flex items-center justify-between">
              <span className="text-sm">{t("admin.attendanceManagement.reminders.nextCheck")}</span>
              <span className="text-sm text-muted-foreground">
                {serviceStatus.nextCheck.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Automated Reminders Settings */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Settings className="h-5 w-5 text-purple-500" />
            {t("admin.attendanceManagement.reminders.automatedSettings")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable/Disable Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="enable-reminders" className="text-sm font-medium">
                {t("admin.attendanceManagement.reminders.enableAutomation")}
              </Label>
              <p className="text-xs text-muted-foreground">
                {t("admin.attendanceManagement.reminders.enableDescription")}
              </p>
            </div>
            <Switch
              id="enable-reminders"
              checked={enabled}
              onCheckedChange={setEnabled}
            />
          </div>

          {/* Minutes Before Setting */}
          <div className="space-y-2">
            <Label htmlFor="minutes-before" className="text-sm font-medium">
              {t("admin.attendanceManagement.reminders.minutesBefore")}
            </Label>
            <div className="flex items-center gap-2">
              <Input
                id="minutes-before"
                type="number"
                min="1"
                max="120"
                value={minutesBefore}
                onChange={(e) => setMinutesBefore(Number(e.target.value))}
                className="w-24"
                disabled={!enabled}
              />
              <span className="text-sm text-muted-foreground">
                {t("admin.attendanceManagement.reminders.minutesBeforeEnd")}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              {t("admin.attendanceManagement.reminders.minutesDescription")}
            </p>
          </div>

          {/* Reminder Time Preview */}
          {enabled && attendanceSettings && reminderTime && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                <strong>{t("admin.attendanceManagement.reminders.reminderWillSend")}</strong>
                <br />
                {t("admin.attendanceManagement.reminders.timePreview", {
                  time: reminderTime,
                  endTime: attendanceSettings.recording_end_time.substring(0, 5),
                  minutes: minutesBefore,
                })}
              </AlertDescription>
            </Alert>
          )}

          {/* Late Marking Info */}
          {enabled && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>{t("admin.attendanceManagement.reminders.lateMarkingTitle")}</strong>
                <br />
                {t("admin.attendanceManagement.reminders.lateMarkingDescription")}
              </AlertDescription>
            </Alert>
          )}

          {/* Save Button */}
          <Button
            onClick={saveReminderSettings}
            disabled={saving}
            className="w-full"
          >
            {saving ? (
              <>
                <LoadingSpinner className="mr-2 h-4 w-4" />
                {t("common.saving")}
              </>
            ) : (
              <>
                <Settings className="mr-2 h-4 w-4" />
                {t("admin.attendanceManagement.reminders.saveSettings")}
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Manual Reminder */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Send className="h-5 w-5 text-green-500" />
            {t("admin.attendanceManagement.reminders.manualReminder")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {t("admin.attendanceManagement.reminders.manualDescription")}
          </p>
          
          <Button
            onClick={sendManualReminder}
            disabled={sendingManual}
            variant="outline"
            className="w-full"
          >
            {sendingManual ? (
              <>
                <LoadingSpinner className="mr-2 h-4 w-4" />
                {t("admin.attendanceManagement.reminders.sending")}
              </>
            ) : (
              <>
                <Bell className="mr-2 h-4 w-4" />
                {t("admin.attendanceManagement.reminders.sendNow")}
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
