// Development server with HTTPS support
const { createServer } = require('https');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Check if mkcert is installed
try {
  // Create certs directory if it doesn't exist
  if (!fs.existsSync('certs')) {
    fs.mkdirSync('certs');
  }

  // Generate certificates if they don't exist
  if (!fs.existsSync('certs/localhost.pem') || !fs.existsSync('certs/localhost-key.pem')) {
    console.log('Generating SSL certificates with mkcert...');
    
    // Use vite-plugin-mkcert to generate certificates
    const vite = spawn('npx', ['vite', 'dev', '--https', '--port', '8081', '--host'], {
      stdio: 'inherit',
      shell: true
    });
    
    vite.on('error', (err) => {
      console.error('Failed to start Vite server:', err);
      process.exit(1);
    });
  } else {
    console.log('Using existing SSL certificates');
    
    // Start Vite with existing certificates
    const vite = spawn('npx', ['vite', 'dev', '--https', '--port', '8081', '--host'], {
      stdio: 'inherit',
      shell: true,
      env: {
        ...process.env,
        VITE_PORT: '8081'
      }
    });
    
    vite.on('error', (err) => {
      console.error('Failed to start Vite server:', err);
      process.exit(1);
    });
  }
} catch (err) {
  console.error('Error setting up development server:', err);
  process.exit(1);
}
