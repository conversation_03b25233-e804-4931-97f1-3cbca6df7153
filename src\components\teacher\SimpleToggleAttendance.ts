import { SupabaseClient } from "@supabase/supabase-client";
import { Student } from "@/types/student";
import { Profile } from "@/types/profile";
import { toast } from "@/components/ui/use-toast";
import { toast as sonnerToast } from "sonner";
import { createLocalizedNotification } from "@/lib/utils/notification-localization";

// Simple function to toggle attendance status without dealing with rooms
export const toggleAttendanceStatus = async (
  student: Student,
  currentStatus: "present" | "absent" | "late" | "excused",
  supabase: SupabaseClient,
  profile: Profile | null,
  updateLocalState: (
    studentId: string,
    newStatus: "present" | "absent" | "late" | "excused"
  ) => void,
  t: (key: string, options?: any) => string
) => {
  try {
    const now = new Date();

    // Determine the new status based on current status
    // If current status is present, change to absent
    // If current status is anything else (absent, late, excused), change to present
    const newStatus = currentStatus === "present" ? "absent" : "present";

    // Update local state immediately for better UX
    updateLocalState(student.id, newStatus);

    // Show success message immediately
    toast({
      title: t("teacher.attendance.statusUpdated"),
      description: t("teacher.attendance.studentMarkedAs", {
        name: student.name,
        status: t(`teacher.dashboard.${newStatus}`),
      }),
    });

    // Show additional notification
    sonnerToast.success(t("teacher.attendance.studentMarkedAs", {
      name: student.name,
      status: t(`teacher.dashboard.${newStatus}`),
    }), {
      description: t("teacher.attendance.attendanceUpdated"),
    });

    // Create a localized notification for the student
    try {
      const teacherName = profile?.name || "Teacher";

      // Get room information if available
      let roomName = "Unknown";
      let roomId = null;

      if (student.room_id) {
        roomId = student.room_id;
        const { data: roomData } = await supabase
          .from("rooms")
          .select("name")
          .eq("id", roomId)
          .single();

        if (roomData) {
          roomName = roomData.name;
        }
      }

      // Use the localized notification utility
      let templateKey: string;
      let notificationType: string;

      switch (newStatus) {
        case "present":
          templateKey = "markedPresent";
          notificationType = "attendance";
          break;
        case "absent":
          templateKey = "markedAbsent";
          notificationType = "absence";
          break;
        case "late":
          templateKey = "markedLate";
          notificationType = "late";
          break;
        case "excused":
          templateKey = "markedExcused";
          notificationType = "excused";
          break;
        default:
          templateKey = "markedAbsent";
          notificationType = "absence";
          break;
      }

      console.log('Creating status change notification:', {
        studentId: student.id,
        newStatus,
        templateKey,
        notificationType,
        teacherName,
        roomName
      });

      const notificationResult = await createLocalizedNotification({
        studentId: student.id,
        teacherId: profile?.id,
        type: notificationType,
        templateKey,
        templateParams: [roomName, teacherName],
        metadata: {
          status: newStatus,
          previous_status: currentStatus,
          updated_by: "teacher",
          room_id: roomId,
          room_name: roomName,
        },
        roomNumber: roomName,
      });

      console.log("Status change notification result:", notificationResult);
    } catch (notificationError) {
      console.error("Error creating localized notification:", notificationError);
      // Continue execution even if notification creation fails
    }
  } catch (error) {
    console.error("Error updating attendance:", error);
    toast({
      title: t("common.error"),
      description: t("teacher.attendance.failedToUpdate", {
        name: student.name,
        status: currentStatus === "present" ? "absent" : "present",
      }),
      variant: "destructive",
    });
  }
};
