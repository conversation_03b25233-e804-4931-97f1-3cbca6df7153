/**
 * Audio Notification Service
 * Handles playing audio alerts for different types of notifications
 */

export type NotificationSoundType =
  | 'attendance_reminder'
  | 'urgent_reminder'
  | 'success'
  | 'warning'
  | 'info'
  | 'default'
  | 'excuse_approved'
  | 'excuse_rejected'
  | 'marked_present'
  | 'marked_absent'
  | 'marked_late'
  | 'marked_excused'
  | 'system_update'
  | 'gentle_reminder';

interface AudioNotificationConfig {
  volume: number;
  repeat: number;
  duration: number;
}

class AudioNotificationService {
  private audioContext: AudioContext | null = null;
  private isEnabled: boolean = true;
  private volume: number = 0.8;

  constructor() {
    this.initializeAudioContext();
    this.loadUserPreferences();
  }

  private initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Audio context not supported:', error);
    }
  }

  private loadUserPreferences() {
    const savedVolume = localStorage.getItem('notification_volume');
    const savedEnabled = localStorage.getItem('notification_sounds_enabled');
    
    if (savedVolume) {
      this.volume = parseFloat(savedVolume);
    }
    
    if (savedEnabled !== null) {
      this.isEnabled = savedEnabled === 'true';
    }
  }

  /**
   * Generate a tone using Web Audio API
   */
  private async generateTone(
    frequency: number, 
    duration: number, 
    volume: number = this.volume,
    type: OscillatorType = 'sine'
  ): Promise<void> {
    if (!this.audioContext || !this.isEnabled) return;

    // Resume audio context if suspended (required for user interaction)
    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);

    oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
    oscillator.type = type;

    // Create envelope for smooth sound
    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.1);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + duration);
  }

  /**
   * Play a sequence of tones
   */
  private async playToneSequence(tones: Array<{frequency: number, duration: number, delay?: number}>): Promise<void> {
    let currentTime = 0;
    
    for (const tone of tones) {
      setTimeout(() => {
        this.generateTone(tone.frequency, tone.duration);
      }, currentTime * 1000);
      
      currentTime += tone.duration + (tone.delay || 0);
    }
  }

  /**
   * Play notification sound based on type
   */
  async playNotificationSound(type: NotificationSoundType, config?: Partial<AudioNotificationConfig>): Promise<void> {
    if (!this.isEnabled) {
      return;
    }

    const defaultConfig: AudioNotificationConfig = {
      volume: this.volume,
      repeat: 1,
      duration: 1.0
    };

    const finalConfig = { ...defaultConfig, ...config };

    try {
      switch (type) {
        case 'attendance_reminder':
          // Urgent attention-grabbing sound
          await this.playAttendanceReminderSound(finalConfig);
          break;
        
        case 'urgent_reminder':
          // Very urgent sound with multiple repetitions
          await this.playUrgentReminderSound(finalConfig);
          break;
        
        case 'success':
          // Pleasant success sound
          await this.playSuccessSound(finalConfig);
          break;
        
        case 'warning':
          // Warning sound
          await this.playWarningSound(finalConfig);
          break;
        
        case 'info':
          // Gentle info sound
          await this.playInfoSound(finalConfig);
          break;

        case 'excuse_approved':
          // Happy, uplifting sound for approved excuses
          await this.playExcuseApprovedSound(finalConfig);
          break;

        case 'excuse_rejected':
          // Gentle, understanding sound for rejected excuses
          await this.playExcuseRejectedSound(finalConfig);
          break;

        case 'marked_present':
          // Positive confirmation sound
          await this.playMarkedPresentSound(finalConfig);
          break;

        case 'marked_absent':
          // Concerned but gentle sound
          await this.playMarkedAbsentSound(finalConfig);
          break;

        case 'marked_late':
          // Mild warning sound
          await this.playMarkedLateSound(finalConfig);
          break;

        case 'marked_excused':
          // Understanding, supportive sound
          await this.playMarkedExcusedSound(finalConfig);
          break;

        case 'system_update':
          // Professional system sound
          await this.playSystemUpdateSound(finalConfig);
          break;

        case 'gentle_reminder':
          // Soft, friendly reminder sound
          await this.playGentleReminderSound(finalConfig);
          break;

        default:
          // Default notification sound
          await this.playDefaultSound(finalConfig);
          break;
      }
    } catch (error) {
      console.warn('Error playing notification sound:', error);
    }
  }

  private async playAttendanceReminderSound(config: AudioNotificationConfig): Promise<void> {
    // Beautiful, musical "Ding Dong" chime melody - like a pleasant doorbell
    // This creates an attention-grabbing but beautiful sound that students will love
    for (let i = 0; i < config.repeat; i++) {
      // First phrase: E5-C5-G4-C5 (beautiful doorbell melody)
      await this.generateTone(659, 0.4, config.volume); // E5 - bright and attention-grabbing
      await this.delay(100);
      await this.generateTone(523, 0.4, config.volume); // C5 - harmonious middle note
      await this.delay(150);
      await this.generateTone(392, 0.3, config.volume); // G4 - warm and inviting
      await this.delay(100);
      await this.generateTone(523, 0.6, config.volume); // C5 - pleasant resolution

      // Short pause before potential repeat
      if (i < config.repeat - 1) await this.delay(1200);
    }
  }

  private async playUrgentReminderSound(config: AudioNotificationConfig): Promise<void> {
    // Urgent but musical ascending scale - like a beautiful alarm
    for (let i = 0; i < config.repeat; i++) {
      // Ascending musical scale: C-D-E-F-G (urgent but pleasant)
      await this.generateTone(523, 0.15, config.volume); // C5
      await this.delay(50);
      await this.generateTone(587, 0.15, config.volume); // D5
      await this.delay(50);
      await this.generateTone(659, 0.15, config.volume); // E5
      await this.delay(50);
      await this.generateTone(698, 0.15, config.volume); // F5
      await this.delay(50);
      await this.generateTone(784, 0.3, config.volume);  // G5 - longer final note

      if (i < config.repeat - 1) await this.delay(400);
    }
  }

  private async playSuccessSound(config: AudioNotificationConfig): Promise<void> {
    // Pleasant ascending tone
    const sequence = [
      { frequency: 523, duration: 0.2 }, // C
      { frequency: 659, duration: 0.2, delay: 0.05 }, // E
      { frequency: 784, duration: 0.4, delay: 0.05 }  // G
    ];

    await this.playToneSequence(sequence);
  }

  private async playWarningSound(config: AudioNotificationConfig): Promise<void> {
    // Warning: Two-tone pattern
    const sequence = [
      { frequency: 600, duration: 0.4 },
      { frequency: 400, duration: 0.4, delay: 0.1 }
    ];

    await this.playToneSequence(sequence);
  }

  private async playInfoSound(config: AudioNotificationConfig): Promise<void> {
    // Gentle single tone
    await this.generateTone(650, 0.3, config.volume * 0.7);
  }

  private async playDefaultSound(config: AudioNotificationConfig): Promise<void> {
    // Simple notification tone
    await this.generateTone(750, 0.5, config.volume);
  }

  private async playExcuseApprovedSound(config: AudioNotificationConfig): Promise<void> {
    // Happy ascending melody: C-E-G (major chord)
    for (let i = 0; i < config.repeat; i++) {
      await this.generateTone(523, 0.2, config.volume); // C5
      await this.delay(50);
      await this.generateTone(659, 0.2, config.volume); // E5
      await this.delay(50);
      await this.generateTone(784, 0.3, config.volume); // G5
      if (i < config.repeat - 1) await this.delay(300);
    }
  }

  private async playExcuseRejectedSound(config: AudioNotificationConfig): Promise<void> {
    // Gentle, understanding descending tone
    for (let i = 0; i < config.repeat; i++) {
      await this.generateTone(440, 0.3, config.volume * 0.7); // A4
      await this.delay(100);
      await this.generateTone(392, 0.4, config.volume * 0.6); // G4
      if (i < config.repeat - 1) await this.delay(500);
    }
  }

  private async playMarkedPresentSound(config: AudioNotificationConfig): Promise<void> {
    // Positive confirmation: Two ascending notes
    for (let i = 0; i < config.repeat; i++) {
      await this.generateTone(523, 0.15, config.volume); // C5
      await this.delay(80);
      await this.generateTone(659, 0.25, config.volume); // E5
      if (i < config.repeat - 1) await this.delay(400);
    }
  }

  private async playMarkedAbsentSound(config: AudioNotificationConfig): Promise<void> {
    // Concerned but gentle: Low tone with slight vibrato
    for (let i = 0; i < config.repeat; i++) {
      await this.generateTone(330, 0.4, config.volume * 0.8); // E4
      await this.delay(200);
      await this.generateTone(294, 0.3, config.volume * 0.7); // D4
      if (i < config.repeat - 1) await this.delay(600);
    }
  }

  private async playMarkedLateSound(config: AudioNotificationConfig): Promise<void> {
    // Mild warning: Two quick notes
    for (let i = 0; i < config.repeat; i++) {
      await this.generateTone(440, 0.15, config.volume); // A4
      await this.delay(100);
      await this.generateTone(440, 0.15, config.volume); // A4
      if (i < config.repeat - 1) await this.delay(500);
    }
  }

  private async playMarkedExcusedSound(config: AudioNotificationConfig): Promise<void> {
    // Understanding, supportive: Warm, gentle tone
    for (let i = 0; i < config.repeat; i++) {
      await this.generateTone(392, 0.3, config.volume * 0.8); // G4
      await this.delay(150);
      await this.generateTone(440, 0.4, config.volume * 0.7); // A4
      if (i < config.repeat - 1) await this.delay(500);
    }
  }

  private async playSystemUpdateSound(config: AudioNotificationConfig): Promise<void> {
    // Professional system sound: Clean, neutral
    for (let i = 0; i < config.repeat; i++) {
      await this.generateTone(880, 0.2, config.volume * 0.6); // A5
      if (i < config.repeat - 1) await this.delay(300);
    }
  }

  private async playGentleReminderSound(config: AudioNotificationConfig): Promise<void> {
    // Soft, friendly reminder: Gentle ascending notes
    for (let i = 0; i < config.repeat; i++) {
      await this.generateTone(349, 0.2, config.volume * 0.7); // F4
      await this.delay(100);
      await this.generateTone(392, 0.2, config.volume * 0.7); // G4
      await this.delay(100);
      await this.generateTone(440, 0.3, config.volume * 0.8); // A4
      if (i < config.repeat - 1) await this.delay(800);
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Enable/disable notification sounds
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    localStorage.setItem('notification_sounds_enabled', enabled.toString());
  }

  /**
   * Set notification volume (0.0 to 1.0)
   */
  setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));
    localStorage.setItem('notification_volume', this.volume.toString());
  }

  /**
   * Get current settings
   */
  getSettings(): { enabled: boolean; volume: number } {
    return {
      enabled: this.isEnabled,
      volume: this.volume
    };
  }

  /**
   * Test notification sound
   */
  async testSound(type: NotificationSoundType = 'default'): Promise<void> {
    await this.playNotificationSound(type);
  }

  /**
   * Request audio permission (for mobile browsers)
   */
  async requestAudioPermission(): Promise<boolean> {
    try {
      if (this.audioContext && this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }
      return true;
    } catch (error) {
      console.warn('Audio permission denied:', error);
      return false;
    }
  }
}

// Export singleton instance
export const audioNotificationService = new AudioNotificationService();
export default audioNotificationService;
