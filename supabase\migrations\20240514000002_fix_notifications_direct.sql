-- Direct <PERSON><PERSON> to fix the notifications table
-- This will be executed directly via the Supabase API

-- First, check if the notifications table exists
DO $$
BEGIN
    -- If the table doesn't exist, create it
    IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'notifications') THEN
        CREATE TABLE public.notifications (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
            teacher_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            type TEXT NOT NULL,
            read BOOLEAN NOT NULL DEFAULT FALSE,
            timestamp TIMESTAMPTZ DEFAULT NOW(),
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        -- <PERSON>reate indexes
        CREATE INDEX IF NOT EXISTS notifications_student_id_idx ON notifications(student_id);
        CREATE INDEX IF NOT EXISTS notifications_teacher_id_idx ON notifications(teacher_id);
        CREATE INDEX IF NOT EXISTS notifications_read_idx ON notifications(read);
        
        -- Set up RLS policies
        ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
        
        -- Students can view their own notifications
        CREATE POLICY "Students can view their own notifications"
        ON notifications FOR SELECT
        USING (auth.uid()::text = student_id::text);
        
        -- Students can update their own notifications (to mark as read)
        CREATE POLICY "Students can update their own notifications"
        ON notifications FOR UPDATE
        USING (auth.uid()::text = student_id::text)
        WITH CHECK (auth.uid()::text = student_id::text);
        
        -- Teachers can view notifications they created
        CREATE POLICY "Teachers can view notifications they created"
        ON notifications FOR SELECT
        USING (auth.uid()::text = teacher_id::text);
        
        -- Teachers can create notifications
        CREATE POLICY "Teachers can create notifications"
        ON notifications FOR INSERT
        WITH CHECK (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role IN ('teacher', 'admin')
            )
        );
        
        -- Admins can view all notifications
        CREATE POLICY "Admins can view all notifications"
        ON notifications FOR SELECT
        USING (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role = 'admin'
            )
        );
    ELSE
        -- Table exists, check if school_id column exists
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'notifications' 
            AND column_name = 'school_id'
        ) THEN
            -- Add school_id column
            ALTER TABLE public.notifications ADD COLUMN school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
            CREATE INDEX IF NOT EXISTS notifications_school_id_idx ON notifications(school_id);
        END IF;
        
        -- Check if metadata column exists
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'notifications' 
            AND column_name = 'metadata'
        ) THEN
            -- Add metadata column
            ALTER TABLE public.notifications ADD COLUMN metadata JSONB;
        END IF;
        
        -- Make sure type column is TEXT
        IF EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'notifications' 
            AND column_name = 'type'
            AND data_type != 'text'
        ) THEN
            -- Convert type column to TEXT
            ALTER TABLE public.notifications ALTER COLUMN type TYPE TEXT;
        END IF;
    END IF;
    
    -- Ensure RLS policies exist
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE tablename = 'notifications' 
        AND policyname = 'Students can view their own notifications'
    ) THEN
        -- Students can view their own notifications
        CREATE POLICY "Students can view their own notifications"
        ON notifications FOR SELECT
        USING (auth.uid()::text = student_id::text);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE tablename = 'notifications' 
        AND policyname = 'Students can update their own notifications'
    ) THEN
        -- Students can update their own notifications
        CREATE POLICY "Students can update their own notifications"
        ON notifications FOR UPDATE
        USING (auth.uid()::text = student_id::text)
        WITH CHECK (auth.uid()::text = student_id::text);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE tablename = 'notifications' 
        AND policyname = 'Teachers can create notifications'
    ) THEN
        -- Teachers can create notifications
        CREATE POLICY "Teachers can create notifications"
        ON notifications FOR INSERT
        WITH CHECK (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role IN ('teacher', 'admin')
            )
        );
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE tablename = 'notifications' 
        AND policyname = 'Admins can view all notifications'
    ) THEN
        -- Admins can view all notifications
        CREATE POLICY "Admins can view all notifications"
        ON notifications FOR SELECT
        USING (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role = 'admin'
            )
        );
    END IF;
END$$;
