-- Add school_id to attendance_records table if it doesn't exist
ALTER TABLE attendance_records ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id);

-- Update existing attendance records to use the school_id from the student's profile
UPDATE attendance_records ar
SET school_id = (
  SELECT school_id
  FROM profiles p
  WHERE p.id = ar.student_id
)
WHERE ar.school_id IS NULL;

-- Enable RLS on attendance_records table
ALTER TABLE attendance_records ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view their own attendance records" ON attendance_records;
DROP POLICY IF EXISTS "Students can insert their own attendance records" ON attendance_records;
DROP POLICY IF EXISTS "Teachers can view attendance records in their school" ON attendance_records;
DROP POLICY IF EXISTS "Teachers can manage attendance records in their school" ON attendance_records;
DROP POLICY IF EXISTS "School admins can manage attendance records in their school" ON attendance_records;
DROP POLICY IF EXISTS "System admins can manage all attendance records" ON attendance_records;

-- Students can view their own attendance records
CREATE POLICY "Students can view their own attendance records"
ON attendance_records
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
    AND student.id = student_id
  )
);

-- Students can insert their own attendance records
CREATE POLICY "Students can insert their own attendance records"
ON attendance_records
FOR INSERT
TO authenticated
WITH CHECK (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
    AND student.id = student_id
  )
  AND
  -- Ensure school_id matches the student's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Teachers can view attendance records in their school
CREATE POLICY "Teachers can view attendance records in their school"
ON attendance_records
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the attendance record belongs to a student in the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Teachers can manage attendance records in their school
CREATE POLICY "Teachers can manage attendance records in their school"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the attendance record belongs to a student in the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Ensure school_id matches the teacher's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage attendance records in their school
CREATE POLICY "School admins can manage attendance records in their school"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the attendance record belongs to a student in the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Ensure school_id matches the admin's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all attendance records
CREATE POLICY "System admins can manage all attendance records"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);