# Production Environment Configuration
# Copy these variables to your Vercel deployment settings

# Application
NODE_ENV=production
VITE_APP_NAME="Attendance Tracking System"
VITE_APP_SHORT_NAME="ATS"
VITE_APP_DESCRIPTION="Modern attendance tracking for educational institutions"
VITE_APP_VERSION="2.0.0"

# Supabase Configuration (REQUIRED - Replace with your production values)
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key_here

# Optional: Custom Branding
VITE_CUSTOM_LOGO_URL=
VITE_CUSTOM_PRIMARY_COLOR=#2563eb
VITE_CUSTOM_SECONDARY_COLOR=#64748b

# Production Settings
VITE_DEV_MODE=false
VITE_SHOW_DEBUG_INFO=false

# Performance Settings
VITE_CACHE_TTL=300000
VITE_MAX_CONCURRENT_REQUESTS=10
VITE_REQUEST_TIMEOUT=30000

# Security Settings (Optional - for advanced features)
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# External Services (Optional - Configure if using)
VITE_SENDGRID_API_KEY=
VITE_TWILIO_ACCOUNT_SID=
VITE_TWILIO_AUTH_TOKEN=
