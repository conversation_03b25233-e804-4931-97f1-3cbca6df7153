import { useState, useEffect } from "react";
import { runMigrations } from "@/lib/migrations/run-migrations";
import { runDatabaseCleanupMigration } from "@/lib/migrations/run-database-cleanup-migration";
import { runDirectSQL } from "@/lib/migrations/run-direct-sql";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Loader2,
  CheckCircle2,
  XCircle,
  Database,
  Trash2,
  Code,
} from "lucide-react";

export default function RunMigrations() {
  const [running, setRunning] = useState(false);
  const [cleanupRunning, setCleanupRunning] = useState(false);
  const [directSQLRunning, setDirectSQLRunning] = useState(false);
  const [success, setSuccess] = useState<boolean | null>(null);
  const [cleanupSuccess, setCleanupSuccess] = useState<boolean | null>(null);
  const [directSQLSuccess, setDirectSQLSuccess] = useState<boolean | null>(
    null
  );
  const [message, setMessage] = useState<string>("");
  const [cleanupMessage, setCleanupMessage] = useState<string>("");
  const [directSQLMessage, setDirectSQLMessage] = useState<string>("");

  const handleRunMigrations = async () => {
    setRunning(true);
    setSuccess(null);
    setMessage("Running migrations...");

    try {
      const result = await runMigrations();
      setSuccess(result);
      setMessage(
        result
          ? "Migrations completed successfully!"
          : "Migrations failed. Check console for details."
      );
    } catch (error) {
      console.error("Error running migrations:", error);
      setSuccess(false);
      setMessage(
        `Migrations failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setRunning(false);
    }
  };

  const handleRunDatabaseCleanupMigration = async () => {
    setCleanupRunning(true);
    setCleanupSuccess(null);
    setCleanupMessage("Setting up database cleanup...");

    try {
      const result = await runDatabaseCleanupMigration();
      setCleanupSuccess(result);
      setCleanupMessage(
        result
          ? "Database cleanup setup completed successfully!"
          : "Database cleanup setup failed. Check console for details."
      );
    } catch (error) {
      console.error("Error setting up database cleanup:", error);
      setCleanupSuccess(false);
      setCleanupMessage(
        `Database cleanup setup failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setCleanupRunning(false);
    }
  };

  const handleRunDirectSQL = async () => {
    setDirectSQLRunning(true);
    setDirectSQLSuccess(null);
    setDirectSQLMessage("Running direct SQL scripts...");

    try {
      const result = await runDirectSQL();
      setDirectSQLSuccess(result);
      setDirectSQLMessage(
        result
          ? "Direct SQL scripts executed successfully!"
          : "Direct SQL scripts execution failed. Check console for details."
      );
    } catch (error) {
      console.error("Error running direct SQL scripts:", error);
      setDirectSQLSuccess(false);
      setDirectSQLMessage(
        `Direct SQL scripts execution failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setDirectSQLRunning(false);
    }
  };

  return (
    <div className="container mx-auto py-10 space-y-6">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Database className="h-5 w-5 text-primary" />
            <CardTitle>Database Migrations</CardTitle>
          </div>
          <CardDescription>
            Run database migrations to update your schema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              This will run all pending database migrations to ensure your
              schema is up to date.
            </p>
            {success !== null && (
              <div
                className={`p-4 rounded-md ${
                  success ? "bg-green-50" : "bg-red-50"
                }`}
              >
                <div className="flex items-center">
                  {success ? (
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <p className={success ? "text-green-700" : "text-red-700"}>
                    {message}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleRunMigrations}
            disabled={running}
            className="w-full"
          >
            {running ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running Migrations...
              </>
            ) : (
              "Run Migrations"
            )}
          </Button>
        </CardFooter>
      </Card>

      <Card className="max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-primary" />
            <CardTitle>Database Cleanup Setup</CardTitle>
          </div>
          <CardDescription>
            Set up the database cleanup automation feature
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              This will create the necessary database tables and functions for
              the automatic database cleanup feature.
            </p>
            {cleanupSuccess !== null && (
              <div
                className={`p-4 rounded-md ${
                  cleanupSuccess ? "bg-green-50" : "bg-red-50"
                }`}
              >
                <div className="flex items-center">
                  {cleanupSuccess ? (
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <p
                    className={
                      cleanupSuccess ? "text-green-700" : "text-red-700"
                    }
                  >
                    {cleanupMessage}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleRunDatabaseCleanupMigration}
            disabled={cleanupRunning}
            className="w-full"
          >
            {cleanupRunning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Setting Up Database Cleanup...
              </>
            ) : (
              "Set Up Database Cleanup"
            )}
          </Button>
        </CardFooter>
      </Card>

      <Card className="max-w-md mx-auto">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Code className="h-5 w-5 text-primary" />
            <CardTitle>Direct SQL Execution</CardTitle>
          </div>
          <CardDescription>
            Run direct SQL scripts to fix database issues
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              This will run direct SQL scripts to create the footer_settings
              table and check_table_exists function. Use this if you're having
              issues with the footer settings.
            </p>
            {directSQLSuccess !== null && (
              <div
                className={`p-4 rounded-md ${
                  directSQLSuccess ? "bg-green-50" : "bg-red-50"
                }`}
              >
                <div className="flex items-center">
                  {directSQLSuccess ? (
                    <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  )}
                  <p
                    className={
                      directSQLSuccess ? "text-green-700" : "text-red-700"
                    }
                  >
                    {directSQLMessage}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleRunDirectSQL}
            disabled={directSQLRunning}
            className="w-full"
          >
            {directSQLRunning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running SQL Scripts...
              </>
            ) : (
              "Run Direct SQL Scripts"
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
