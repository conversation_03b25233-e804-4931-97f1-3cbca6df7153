-- Create the system_settings_overrides table
CREATE TABLE IF NOT EXISTS public.system_settings_overrides (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    school_id UUID REFERENCES public.schools(id) ON DELETE CASCADE,
    setting_name TEXT NOT NULL,
    setting_value JSONB NOT NULL,
    override_enabled BOOLEAN NOT NULL DEFAULT true,
    applies_to_all BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies
ALTER TABLE public.system_settings_overrides ENABLE ROW LEVEL SECURITY;

-- Allow system admins to manage all settings
CREATE POLICY "System admins can manage all settings"
    ON public.system_settings_overrides
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'system_admin'
        )
    );

-- Allow school admins to view settings for their school
CREATE POLICY "School admins can view settings for their school"
    ON public.system_settings_overrides
    FOR SELECT
    TO authenticated
    USING (
        (school_id IS NULL AND applies_to_all = true) OR
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'school_admin'
            AND profiles.school_id = system_settings_overrides.school_id
        )
    );

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_system_settings_overrides_school_id ON public.system_settings_overrides(school_id);
CREATE INDEX IF NOT EXISTS idx_system_settings_overrides_setting_name ON public.system_settings_overrides(setting_name);
CREATE INDEX IF NOT EXISTS idx_system_settings_overrides_applies_to_all ON public.system_settings_overrides(applies_to_all);
