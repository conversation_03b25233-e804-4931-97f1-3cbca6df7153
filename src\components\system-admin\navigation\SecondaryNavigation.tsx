import { ReactNode } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";

interface NavigationItem {
  id: string;
  label: string;
  icon?: ReactNode;
}

interface SecondaryNavigationProps {
  items: NavigationItem[];
  activeItem: string;
  onItemChange: (value: string) => void;
  children: Record<string, ReactNode>;
  className?: string;
}

export default function SecondaryNavigation({
  items,
  activeItem,
  onItemChange,
  children,
  className = "",
}: SecondaryNavigationProps) {
  return (
    <Tabs value={activeItem} onValueChange={onItemChange} className={className}>
      <Card className="mb-4 p-2 overflow-x-auto">
        <TabsList className="flex flex-nowrap min-w-max">
          {items.map((item) => (
            <TabsTrigger
              key={item.id}
              value={item.id}
              className="flex items-center gap-2"
            >
              {item.icon}
              {item.label}
            </TabsTrigger>
          ))}
        </TabsList>
      </Card>

      {items.map((item) => (
        <TabsContent key={item.id} value={item.id} className="mt-0">
          {children[item.id]}
        </TabsContent>
      ))}
    </Tabs>
  );
}
