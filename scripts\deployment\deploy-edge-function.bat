@echo off
REM 🚀 Deploy QR Security Edge Function to Supabase (Windows)
REM This script automates the deployment process

setlocal enabledelayedexpansion

set PROJECT_REF=wclwxrilybnzkhvqzbmy
set FUNCTION_NAME=qr-security

echo 🚀 Starting QR Security Edge Function Deployment...
echo Project: %PROJECT_REF%
echo Function: %FUNCTION_NAME%
echo.

REM Check if Supabase CLI is installed
supabase --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Supabase CLI is not installed!
    echo 📦 Installing Supabase CLI...
    npm install -g supabase
    if errorlevel 1 (
        echo ❌ Failed to install Supabase CLI
        pause
        exit /b 1
    )
    echo ✅ Supabase CLI installed successfully
) else (
    echo ✅ Supabase CLI is already installed
)

REM Check if user is logged in
echo 🔐 Checking authentication...
supabase projects list >nul 2>&1
if errorlevel 1 (
    echo ❌ Not logged in to Supabase
    echo 🔑 Please login to Supabase...
    supabase login
    if errorlevel 1 (
        echo ❌ Login failed
        pause
        exit /b 1
    )
    echo ✅ Successfully logged in
) else (
    echo ✅ Already authenticated
)

REM Link project if not already linked
echo 🔗 Linking project...
supabase status >nul 2>&1
if errorlevel 1 (
    echo 🔗 Linking to project %PROJECT_REF%...
    supabase link --project-ref %PROJECT_REF%
    if errorlevel 1 (
        echo ❌ Project linking failed
        pause
        exit /b 1
    )
    echo ✅ Project linked successfully
) else (
    echo ✅ Project already linked
)

REM Deploy the edge function
echo 📦 Deploying edge function...
echo Function: %FUNCTION_NAME%
supabase functions deploy %FUNCTION_NAME% --project-ref %PROJECT_REF%

if errorlevel 1 (
    echo ❌ Edge function deployment failed!
    pause
    exit /b 1
) else (
    echo ✅ Edge function deployed successfully!
)

REM Run database migration
echo 🗄️ Running database migration...
supabase db push --project-ref %PROJECT_REF%

if errorlevel 1 (
    echo ❌ Database migration failed!
    pause
    exit /b 1
) else (
    echo ✅ Database migration completed successfully!
)

REM Instructions for service role key
echo 🔑 Checking environment variables...
echo.
echo ⚠️  IMPORTANT: You need to set the SUPABASE_SERVICE_ROLE_KEY
echo 📋 Steps to set the service role key:
echo 1. Go to: https://supabase.com/dashboard/project/%PROJECT_REF%/settings/api
echo 2. Copy the 'service_role' key (NOT the anon key)
echo 3. Run this command:
echo    supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here --project-ref %PROJECT_REF%
echo.

REM Test the function
echo 🧪 Testing the deployed function...
set FUNCTION_URL=https://%PROJECT_REF%.supabase.co/functions/v1/%FUNCTION_NAME%
echo Function URL: %FUNCTION_URL%

echo.
echo 🎉 Deployment completed!
echo.
echo 📋 Next steps:
echo 1. Set the service role key (see instructions above)
echo 2. Test QR code generation in your app
echo 3. Test QR code scanning in your app
echo 4. Monitor function logs: supabase functions logs %FUNCTION_NAME% --project-ref %PROJECT_REF%
echo.
echo 🔍 Function URL: %FUNCTION_URL%
echo 📊 Dashboard: https://supabase.com/dashboard/project/%PROJECT_REF%/functions/%FUNCTION_NAME%
echo.
echo ✨ Your QR code system is now enterprise-grade secure!
echo.
pause
