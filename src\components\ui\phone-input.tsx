import React, { useState, useEffect } from 'react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { getCountryCallingCode } from 'react-phone-number-input';
import { useGeoLocation } from '@/hooks/useGeoLocation';

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  id?: string;
  name?: string;
  className?: string;
  error?: string;
  required?: boolean;
}

export function CustomPhoneInput({
  value,
  onChange,
  label,
  placeholder = "Enter phone number",
  id,
  name,
  className,
  error,
  required = false,
}: PhoneInputProps) {
  const { country } = useGeoLocation();
  const [isValid, setIsValid] = useState(true);
  const [countryCode, setCountryCode] = useState<string | undefined>(undefined);
  const [expectedLength, setExpectedLength] = useState<number | null>(null);
  
  // Set default country based on geolocation
  useEffect(() => {
    if (country) {
      setCountryCode(country);
    }
  }, [country]);
  
  // Get expected length for the current country
  useEffect(() => {
    if (countryCode) {
      // This is a simplified mapping - in a real app, you'd want a more complete database
      const lengthMap: Record<string, number> = {
        'US': 10, // +1 XXXXXXXXXX (10 digits after country code)
        'GB': 10, // +44 XXXXXXXXXX
        'IN': 10, // +91 XXXXXXXXXX
        'CA': 10, // +1 XXXXXXXXXX
        'AU': 9,  // +61 XXXXXXXXX
        'DE': 11, // +49 XXXXXXXXXXX
        'FR': 9,  // +33 XXXXXXXXX
        'IT': 10, // +39 XXXXXXXXXX
        'ES': 9,  // +34 XXXXXXXXX
        'BR': 11, // +55 XXXXXXXXXXX
        'JP': 10, // +81 XXXXXXXXXX
        'KR': 10, // +82 XXXXXXXXXX
        'CN': 11, // +86 XXXXXXXXXXX
        'RU': 10, // +7 XXXXXXXXXX
        'MX': 10, // +52 XXXXXXXXXX
        'SA': 9,  // +966 XXXXXXXXX
        'AE': 9,  // +971 XXXXXXXXX
        'ZA': 9,  // +27 XXXXXXXXX
        'NG': 10, // +234 XXXXXXXXXX
        'EG': 10, // +20 XXXXXXXXXX
      };
      
      setExpectedLength(lengthMap[countryCode] || null);
    }
  }, [countryCode]);
  
  // Validate phone number format
  const validatePhoneNumber = (phoneNumber: string) => {
    if (!phoneNumber) return true; // Empty is allowed unless required is true
    
    // Basic validation - should start with + and have at least 7 digits
    const basicValid = /^\+[0-9]{7,}$/.test(phoneNumber);
    
    // If we know the expected length for this country, check that too
    if (basicValid && expectedLength && countryCode) {
      try {
        // Extract the national number (without country code)
        const countryCallingCode = getCountryCallingCode(countryCode);
        const nationalNumber = phoneNumber.replace(`+${countryCallingCode}`, '').replace(/\s+/g, '');
        return nationalNumber.length === expectedLength;
      } catch (e) {
        return basicValid;
      }
    }
    
    return basicValid;
  };
  
  const handleChange = (newValue: string | undefined) => {
    const phoneValue = newValue || '';
    onChange(phoneValue);
    setIsValid(validatePhoneNumber(phoneValue));
  };
  
  // Get hint text based on country
  const getHintText = () => {
    if (!countryCode || !expectedLength) return '';
    
    try {
      const code = getCountryCallingCode(countryCode);
      return `Format: +${code} followed by ${expectedLength} digits`;
    } catch (e) {
      return '';
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label htmlFor={id} className="flex items-center">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <PhoneInput
          international
          defaultCountry={countryCode as any}
          value={value}
          onChange={handleChange as any}
          placeholder={placeholder}
          className={cn(
            "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2",
            "text-sm ring-offset-background file:border-0 file:bg-transparent",
            "file:text-sm file:font-medium placeholder:text-muted-foreground",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
            "focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            !isValid && "border-red-500 focus-visible:ring-red-500",
            className
          )}
        />
      </div>
      
      {getHintText() && (
        <p className="text-xs text-muted-foreground mt-1">{getHintText()}</p>
      )}
      
      {!isValid && (
        <p className="text-xs text-red-500 mt-1">
          Please enter a valid phone number with country code
        </p>
      )}
      
      {error && (
        <p className="text-xs text-red-500 mt-1">{error}</p>
      )}
    </div>
  );
}
