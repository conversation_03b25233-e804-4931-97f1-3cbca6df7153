/* Dark Mode Styles with Orange Primary Color */

/* Dark Gray/Black: #1e2124 */
/* Light Gray/Silver: #c9d1d9 */
/* Vibrant Orange: #F39228 */

/* Force CSS variables to use our orange color in dark mode */
.dark {
  --primary: 32 89% 56% !important;
  --primary-foreground: 0 0% 100% !important;
}

/* Override primary color in dark mode to use the orange accent */
.dark .bg-primary,
html.dark .bg-primary,
:root.dark .bg-primary,
[data-theme="dark"] .bg-primary {
  background-color: #f39228 !important;
}

/* Ensure hero section uses the same color */
.dark section.bg-primary {
  background-color: #f39228 !important;
}

.dark .text-primary {
  color: #f39228 !important;
}

.dark .border-primary {
  border-color: #f39228 !important;
}

/* Fix white buttons with primary text in dark mode */
.dark .bg-white.text-primary,
.dark button.bg-white.text-primary,
.dark .bg-white .text-primary,
.dark .bg-white a,
.dark button.bg-white a,
.dark .bg-white span {
  color: #f39228 !important;
}

/* Ensure button links inherit the correct color */
.dark button.bg-white a.text-primary,
.dark .bg-white a.text-primary {
  color: #f39228 !important;
}

.dark .hover\:bg-primary:hover {
  background-color: #f5a54d !important; /* Lighter orange for hover */
}

.dark .hover\:text-primary:hover {
  color: #f5a54d !important;
}

.dark .hover\:border-primary:hover {
  border-color: #f5a54d !important;
}

/* Primary button hover states */
.dark .bg-primary:hover {
  background-color: #d67b1f !important; /* Darker orange for hover */
}

/* Primary light/dark variants */
.dark .bg-primary-light {
  background-color: #f5a54d !important; /* Lighter orange */
}

.dark .bg-primary-dark {
  background-color: #d67b1f !important; /* Darker orange */
}

/* Fix for Navbar hover states */
.dark .hover\:bg-primary-light:hover {
  background-color: #f5a54d !important;
}

.dark {
  /* Custom scrollbar for dark mode */
  scrollbar-color: #f39228 #1e2124;
}

.dark ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.dark ::-webkit-scrollbar-track {
  background: #1e2124;
}

.dark ::-webkit-scrollbar-thumb {
  background: #f39228;
  border-radius: 5px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #f5a54d;
}

/* Enhance focus states with accent color */
.dark *:focus-visible {
  outline-color: #f39228 !important;
}

/* Add subtle glow to accent elements */
.dark .accent-glow {
  box-shadow: 0 0 8px rgba(243, 146, 40, 0.5);
}

/* Add subtle gradient to cards in dark mode */
.dark .card {
  background: linear-gradient(145deg, #1e2124, #262a2e);
}

/* Add subtle border to inputs in dark mode */
.dark input,
.dark textarea,
.dark select {
  border-color: #30363d;
}

/* Add hover effect to interactive elements */
.dark .interactive:hover {
  background-color: rgba(243, 146, 40, 0.1);
  transition: background-color 0.2s ease;
}

/* Add subtle animation to accent buttons */
@keyframes pulse-accent {
  0% {
    box-shadow: 0 0 0 0 rgba(243, 146, 40, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(243, 146, 40, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(243, 146, 40, 0);
  }
}

.dark .btn-accent-pulse:hover {
  animation: pulse-accent 1.5s infinite;
}

/* Force navbar to use the orange color ONLY in dark mode */
.dark nav,
.dark nav.bg-primary,
.dark .bg-primary[class*="nav"],
.dark header.bg-primary,
.dark div.bg-primary[role="banner"],
html.dark nav,
html.dark nav.bg-primary {
  background-color: #f39228 !important; /* Vibrant orange */
  border-bottom: 1px solid #30363d;
  box-shadow: 0 2px 8px rgba(243, 146, 40, 0.2); /* Add subtle glow */
}

/* Navbar button hover states */
.dark nav .hover\:bg-primary-light:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Navbar button styles */
.dark nav .border-white {
  border-color: rgba(255, 255, 255, 0.8) !important;
}

/* Add subtle gradient to active navigation items */
.dark .nav-item.active {
  background: linear-gradient(90deg, rgba(243, 146, 40, 0.1), transparent);
  border-left: 3px solid #f39228;
}
