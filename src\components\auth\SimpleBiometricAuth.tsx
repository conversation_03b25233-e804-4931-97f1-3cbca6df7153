import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Fingerprint, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Loader2,
  AlertTriangle
} from "lucide-react";
import { startRegistration, startAuthentication, isWebAuthnAvailable, isBiometricOnlySupported, canPerformBiometricAuth } from "@/lib/webauthn";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";

interface SimpleBiometricAuthProps {
  userId: string;
  username: string;
  onSuccess: () => void;
  onError: (error: string) => void;
  mode: 'register' | 'authenticate';
  className?: string;
}

export default function SimpleBiometricAuth({ 
  userId, 
  username,
  onSuccess, 
  onError, 
  mode, 
  className 
}: SimpleBiometricAuthProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState('');

  const { toast } = useToast();
  const { t } = useTranslation();

  // Check if WebAuthn is available
  const isSupported = isWebAuthnAvailable();

  // Handle biometric action (register or authenticate)
  const handleBiometricAction = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    setStatus('processing');
    setProgress(0);
    setStatusMessage(mode === 'register' ? 'Preparing biometric registration...' : 'Preparing authentication...');

    try {
      setProgress(25);
      setStatusMessage('Checking device capabilities...');

      if (mode === 'register') {
        // For registration, use strict biometric-only check
        const supportsBiometricOnly = await isBiometricOnlySupported();
        if (!supportsBiometricOnly) {
          throw new Error("This device does not support biometric-only authentication. Please use a device with fingerprint, face recognition, or other biometric sensors.");
        }
      } else {
        // For authentication, use more lenient check
        const canAuth = await canPerformBiometricAuth(userId);
        if (!canAuth) {
          throw new Error("Biometric authentication is not available. Please ensure you have registered biometrics and your device supports biometric authentication.");
        }
      }

      setProgress(50);

      if (mode === 'register') {
        setStatusMessage(t('biometrics.strictBiometricOnlyRegister'));
        await startRegistration(userId, username);
        setStatusMessage(t('biometrics.registrationSuccessMessage'));
      } else {
        setStatusMessage(t('biometrics.strictBiometricOnlyAuth'));
        await startAuthentication(userId);
        setStatusMessage(t('biometrics.authenticationSuccessMessage'));
      }

      setProgress(100);
      setStatus('success');

      toast({
        title: t("common.success"),
        description: statusMessage,
      });

      // Immediate success callback
      onSuccess();

    } catch (error) {
      setStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Biometric operation failed';
      setStatusMessage(errorMessage);
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      onError(errorMessage);
    } finally {
      setIsProcessing(false);

      // Quick reset for failed attempts only
      if (status === 'error') {
        setTimeout(() => {
          setStatus('idle');
          setProgress(0);
          setStatusMessage('');
        }, 2000);
      }
    }
  };

  // If WebAuthn is not supported
  if (!isSupported) {
    return (
      <Card className={cn("w-full max-w-md mx-auto", className)}>
        <CardContent className="flex items-center justify-center p-8">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 mx-auto text-orange-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Biometric Authentication Not Available
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Your device or browser doesn't support biometric authentication, or you need to use HTTPS.
            </p>
            <div className="bg-blue-50 p-3 rounded-lg text-left">
              <p className="text-xs text-blue-800 font-medium mb-1">Requirements:</p>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>• Modern browser (Chrome, Firefox, Safari, Edge)</li>
                <li>• HTTPS connection (or localhost for development)</li>
                <li>• Device with fingerprint sensor or face recognition</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full max-w-md mx-auto overflow-hidden", className)}>
      <CardHeader className="text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <CardTitle className="flex items-center justify-center gap-2">
          <Shield className="w-6 h-6" />
          {mode === 'register' ? t('biometrics.registerBiometric') : t('biometrics.biometricAuthentication')}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6 space-y-6">
        {/* Status Display */}
        {status !== 'idle' && (
          <div className="text-center space-y-4">
            <div className={cn(
              "w-20 h-20 mx-auto rounded-full flex items-center justify-center",
              status === 'processing' && "bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse",
              status === 'success' && "bg-gradient-to-r from-green-500 to-emerald-500",
              status === 'error' && "bg-gradient-to-r from-red-500 to-pink-500"
            )}>
              {status === 'processing' && (
                <Fingerprint className="w-8 h-8 text-white animate-bounce" />
              )}
              {status === 'success' && <CheckCircle className="w-8 h-8 text-white" />}
              {status === 'error' && <XCircle className="w-8 h-8 text-white" />}
            </div>
            
            {isProcessing && (
              <div className="space-y-2">
                <Progress value={progress} className="w-full" />
                <p className="text-sm text-gray-600">{statusMessage}</p>
              </div>
            )}
            
            {!isProcessing && (
              <p className={cn(
                "text-sm font-medium",
                status === 'success' && "text-green-600",
                status === 'error' && "text-red-600"
              )}>
                {statusMessage}
              </p>
            )}
          </div>
        )}

        {/* Biometric Action Button */}
        {status === 'idle' && (
          <div className="space-y-4">
            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <Fingerprint className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {mode === 'register' ? t('biometrics.registerYourBiometric') : t('biometrics.authenticateWithBiometric')}
              </h3>
              <p className="text-gray-600 text-sm mb-4">
                {mode === 'register'
                  ? t('biometrics.setupFingerprintOrFace')
                  : t('biometrics.useRegisteredBiometric')
                }
              </p>
            </div>

            <Button
              onClick={handleBiometricAction}
              disabled={isProcessing}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  {t('common.processing')}...
                </>
              ) : (
                <>
                  <Fingerprint className="w-5 h-5 mr-2" />
                  {mode === 'register' ? t('biometrics.registerBiometric') : t('common.authenticate')}
                </>
              )}
            </Button>

            {/* Instructions */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">{t('biometrics.instructions')}</p>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>{t('biometrics.clickButtonToStart')}</li>
                    <li>{t('biometrics.followBrowserPrompt')}</li>
                    <li>{t('biometrics.useFingerprintOrFace')}</li>
                    <li>{t('biometrics.completeWhenPrompted')}</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Biometric-Only Security Warning */}
            <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
                <div className="text-sm text-amber-800">
                  <p className="font-medium mb-1">Biometric-Only Authentication</p>
                  <p className="text-xs">
                    For security, only fingerprint, face recognition, or other biometric methods are allowed.
                    PIN, pattern, and password authentication are disabled to prevent unauthorized access.
                  </p>
                </div>
              </div>
            </div>

            {/* Security Notice */}
            <div className="flex items-center gap-2 text-xs text-gray-500 justify-center">
              <Shield className="w-4 h-4" />
              <span>{t('biometrics.dataStaysSecure')}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
