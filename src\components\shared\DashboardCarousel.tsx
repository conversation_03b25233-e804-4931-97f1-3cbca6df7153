import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { supabase } from "@/lib/supabase";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { getInitials } from "@/lib/utils";
import { useTranslation } from "react-i18next";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { motion } from "framer-motion";
import { RealtimeChannel } from "@supabase/supabase-js";

interface CarouselContent {
  id: string;
  title: string;
  description: string | null;
  image_url: string;
}

interface DashboardCarouselProps {
  userType: "student" | "teacher";
}

export default function DashboardCarousel({
  userType,
}: DashboardCarouselProps) {
  const { profile } = useAuth();
  const { currentSchool } = useSchool();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [carouselItems, setCarouselItems] = useState<CarouselContent[]>([]);
  const autoplayRef = useRef(
    Autoplay({ delay: 5000, stopOnInteraction: false })
  );
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  useEffect(() => {
    if (currentSchool?.id) {
      loadCarouselContent();

      // Set up real-time subscription
      setupRealtimeSubscription();
    }

    // Cleanup subscription when component unmounts
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
      }
    };
  }, [currentSchool, userType]);

  const setupRealtimeSubscription = async () => {
    // Remove any existing subscription
    if (subscriptionRef.current) {
      await supabase.removeChannel(subscriptionRef.current);
    }

    // Create a new subscription to the carousel_content table with unique channel name
    const channelName = `dashboard-carousel-${currentSchool.id}-${Date.now()}`;
    const channel = supabase
      .channel(channelName)
      .on(
        "postgres_changes",
        {
          event: "*", // Listen for all events (INSERT, UPDATE, DELETE)
          schema: "public",
          table: "carousel_content",
          filter: `school_id=eq.${currentSchool.id}`,
        },
        (payload) => {
          console.log("Dashboard carousel content changed:", payload);
          // Reload carousel images when any change is detected
          loadCarouselContent();
        }
      )
      .subscribe();

    subscriptionRef.current = channel;
  };

  const loadCarouselContent = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("carousel_content")
        .select("id, title, description, image_url")
        .eq("school_id", currentSchool.id)
        .eq("active", true)
        .contains("target_audience", `{${userType}}`)
        .order("display_order", { ascending: true });

      if (error) {
        console.error("Error loading carousel content:", error);
        return;
      }

      setCarouselItems(data || []);
    } catch (error) {
      console.error("Error in carousel content fetch:", error);
    } finally {
      setLoading(false);
    }
  };

  // If no carousel items and not loading, don't render anything
  if (!loading && carouselItems.length === 0) {
    return null;
  }

  return (
    <div className="w-full mb-6 relative">
      {loading ? (
        <div className="w-full h-[200px] md:h-[300px] rounded-xl overflow-hidden">
          <Skeleton className="w-full h-full" />
        </div>
      ) : (
        <div className="relative">
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            plugins={[autoplayRef.current]}
            className="w-full"
          >
            <CarouselContent>
              {carouselItems.map((item) => (
                <CarouselItem key={item.id}>
                  <div className="relative w-full h-[200px] md:h-[300px] rounded-xl overflow-hidden">
                    <img
                      src={item.image_url}
                      alt={item.title}
                      className="w-full h-full object-cover"
                    />

                    {/* Content overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-4 md:p-6">
                      <motion.h3
                        className="text-white text-lg md:text-xl font-bold mb-1"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        {item.title}
                      </motion.h3>
                      {item.description && (
                        <motion.p
                          className="text-white/90 text-sm md:text-base line-clamp-2"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: 0.1 }}
                        >
                          {item.description}
                        </motion.p>
                      )}
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>

            <CarouselPrevious
              variant="ghost"
              className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 border-none text-white hover:bg-transparent"
            />
            <CarouselNext
              variant="ghost"
              className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 border-none text-white hover:bg-transparent"
            />
          </Carousel>

          {/* User avatar at the bottom */}
          <div className="absolute bottom-4 right-4 z-10">
            <Avatar className="h-10 w-10 border-2 border-white shadow-md">
              <AvatarImage
                src={profile?.photoUrl || ""}
                alt={profile?.name || ""}
              />
              <AvatarFallback className="bg-primary text-primary-foreground">
                {getInitials(profile?.name || "")}
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      )}
    </div>
  );
}
