/**
 * 📱 Tablet Setup Page
 * Admin interface for generating tablet setup QR codes
 */

import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Tablet, 
  QrCode, 
  Settings, 
  Monitor,
  Shield,
  Wifi,
  CheckCircle,
  AlertCircle,
  Plus,
  Eye,
  Trash2
} from "lucide-react";
import { TabletSetupQR } from "@/components/tablet/TabletSetupQR";
import { toast } from "@/lib/utils/toast";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { motion } from "framer-motion";

interface TabletDevice {
  id: string;
  deviceName: string;
  roomName: string;
  schoolName: string;
  isActive: boolean;
  lastSeen: Date;
  connectionStatus: "online" | "offline" | "unknown";
}

export default function TabletSetup() {
  const [activeTab, setActiveTab] = useState("setup");
  const [tablets, setTablets] = useState<TabletDevice[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();

  // Fetch registered tablets
  const fetchTablets = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from("tablet_devices")
        .select(`
          id,
          device_name,
          is_active,
          last_seen,
          rooms (
            name
          ),
          schools (
            name
          )
        `)
        .order("last_seen", { ascending: false });

      if (error) throw error;

      const formattedTablets: TabletDevice[] = data.map((tablet: any) => ({
        id: tablet.id,
        deviceName: tablet.device_name,
        roomName: tablet.rooms?.name || "Unknown Room",
        schoolName: tablet.schools?.name || "Unknown School",
        isActive: tablet.is_active,
        lastSeen: new Date(tablet.last_seen),
        connectionStatus: getConnectionStatus(new Date(tablet.last_seen))
      }));

      setTablets(formattedTablets);
    } catch (error) {
      console.error("Error fetching tablets:", error);
      toast.translateError(
        t,
        "common.error",
        "tablet.fetchDevicesError"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getConnectionStatus = (lastSeen: Date): "online" | "offline" | "unknown" => {
    const now = new Date();
    const diffMinutes = (now.getTime() - lastSeen.getTime()) / (1000 * 60);
    
    if (diffMinutes < 5) return "online";
    if (diffMinutes < 30) return "offline";
    return "unknown";
  };

  const deactivateTablet = async (tabletId: string) => {
    try {
      const { error } = await supabase
        .from("tablet_devices")
        .update({ is_active: false })
        .eq("id", tabletId);

      if (error) throw error;

      toast.translateSuccess(
        t,
        "tablet.deactivated",
        "tablet.deactivatedDescription"
      );

      fetchTablets(); // Refresh the list
    } catch (error) {
      console.error("Error deactivating tablet:", error);
      toast.translateError(
        t,
        "common.error",
        "tablet.deactivateError"
      );
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online": return "text-green-600 bg-green-50 border-green-200";
      case "offline": return "text-orange-600 bg-orange-50 border-orange-200";
      default: return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "online": return <CheckCircle className="w-4 h-4" />;
      case "offline": return <AlertCircle className="w-4 h-4" />;
      default: return <Wifi className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4"
        >
          <div className="flex items-center justify-center gap-3">
            <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
              <Tablet className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-800">
              {t("admin.tablets.title")}
            </h1>
          </div>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            {t("admin.tablets.subtitle")}
          </p>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto">
              <TabsTrigger value="setup" className="flex items-center gap-2">
                <QrCode className="w-4 h-4" />
                {t("admin.tablets.setupQR.title")}
              </TabsTrigger>
              <TabsTrigger value="manage" className="flex items-center gap-2">
                <Monitor className="w-4 h-4" />
                {t("admin.tablets.manage.title")}
              </TabsTrigger>
            </TabsList>

            {/* Setup Tab */}
            <TabsContent value="setup" className="space-y-6">
              <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-2xl">
                    <QrCode className="w-6 h-6 text-blue-600" />
                    {t("admin.tablets.setupQR.generateTitle")}
                  </CardTitle>
                  <p className="text-gray-600">
                    {t("admin.tablets.setupQR.generateSubtitle")}
                  </p>
                </CardHeader>
                <CardContent>
                  <TabletSetupQR />
                </CardContent>
              </Card>

              {/* Setup Instructions */}
              <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-blue-800 mb-4 flex items-center gap-2">
                    <Shield className="w-5 h-5" />
                    Setup Instructions
                  </h3>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <h4 className="font-medium text-blue-700">For Administrators:</h4>
                      <ol className="list-decimal list-inside space-y-2 text-blue-600 text-sm">
                        <li>Select the school, block, and room for the tablet</li>
                        <li>Optionally provide a device name for identification</li>
                        <li>Download or print the generated QR code</li>
                        <li>Place the QR code near the tablet for easy scanning</li>
                      </ol>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-medium text-blue-700">For Tablet Setup:</h4>
                      <ol className="list-decimal list-inside space-y-2 text-blue-600 text-sm">
                        <li>Open the tablet's camera or QR scanner app</li>
                        <li>Scan the setup QR code</li>
                        <li>The tablet will automatically configure itself</li>
                        <li>Verify the setup in the "Manage Tablets" tab</li>
                      </ol>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Manage Tab */}
            <TabsContent value="manage" className="space-y-6">
              <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle className="flex items-center gap-2 text-2xl">
                        <Monitor className="w-6 h-6 text-blue-600" />
                        {t("admin.tablets.manage.title")}
                      </CardTitle>
                      <p className="text-gray-600 mt-1">
                        {t("admin.tablets.manage.subtitle")}
                      </p>
                    </div>
                    <Button onClick={fetchTablets} disabled={isLoading}>
                      <Eye className="w-4 h-4 mr-2" />
                      {isLoading ? t("admin.tablets.manage.loading") : t("admin.tablets.manage.refresh")}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {tablets.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                        <Tablet className="w-8 h-8 text-gray-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-600 mb-2">
                        {t("admin.tablets.manage.noTablets.title")}
                      </h3>
                      <p className="text-gray-500 mb-4">
                        {t("admin.tablets.manage.noTablets.subtitle")}
                      </p>
                      <Button
                        onClick={() => setActiveTab("setup")}
                        variant="outline"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        {t("admin.tablets.manage.noTablets.setupFirst")}
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {tablets.map((tablet, index) => (
                        <motion.div
                          key={tablet.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <h4 className="text-lg font-semibold text-gray-800">
                                  {tablet.deviceName}
                                </h4>
                                <Badge 
                                  variant="outline"
                                  className={getStatusColor(tablet.connectionStatus)}
                                >
                                  {getStatusIcon(tablet.connectionStatus)}
                                  <span className="ml-1 capitalize">{tablet.connectionStatus}</span>
                                </Badge>
                                {!tablet.isActive && (
                                  <Badge variant="secondary">
                                    Inactive
                                  </Badge>
                                )}
                              </div>
                              <div className="space-y-1 text-sm text-gray-600">
                                <p><strong>School:</strong> {tablet.schoolName}</p>
                                <p><strong>Room:</strong> {tablet.roomName}</p>
                                <p><strong>Last Seen:</strong> {tablet.lastSeen.toLocaleString()}</p>
                              </div>
                            </div>
                            <div className="flex gap-2">
                              {tablet.isActive && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => deactivateTablet(tablet.id)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Statistics */}
              {tablets.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-0 shadow-lg bg-green-50">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-green-600 mb-2">
                        {tablets.filter(t => t.connectionStatus === "online").length}
                      </div>
                      <div className="text-green-700 font-medium">Online Tablets</div>
                    </CardContent>
                  </Card>
                  <Card className="border-0 shadow-lg bg-orange-50">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-orange-600 mb-2">
                        {tablets.filter(t => t.connectionStatus === "offline").length}
                      </div>
                      <div className="text-orange-700 font-medium">Offline Tablets</div>
                    </CardContent>
                  </Card>
                  <Card className="border-0 shadow-lg bg-blue-50">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-blue-600 mb-2">
                        {tablets.filter(t => t.isActive).length}
                      </div>
                      <div className="text-blue-700 font-medium">Active Tablets</div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  );
}
