import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { supabase } from "@/lib/supabase";
import { format, subDays, eachDayOfInterval } from "date-fns";

interface AttendanceTrendChartProps {
  days?: number;
  title?: string;
  description?: string;
}

export default function AttendanceTrendChart({
  days = 14,
  title = "Attendance Trends",
  description = "Daily attendance records over time",
}: AttendanceTrendChartProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, [days]);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Calculate date range
      const endDate = new Date();
      const startDate = subDays(endDate, days);

      // Get attendance data from Supabase
      const { data: attendanceData, error: attendanceError } = await supabase
        .from("attendance_records")
        .select("status, timestamp")
        .gte("timestamp", startDate.toISOString())
        .lte("timestamp", endDate.toISOString());

      if (attendanceError) throw attendanceError;

      // Generate all days in the interval
      const dateInterval = eachDayOfInterval({
        start: startDate,
        end: endDate,
      });

      // Create a map to count statuses by day
      const statusByDay = dateInterval.reduce((acc, date) => {
        const dateStr = format(date, "yyyy-MM-dd");
        acc[dateStr] = { present: 0, absent: 0, late: 0, excused: 0 };
        return acc;
      }, {} as Record<string, { present: number; absent: number; late: number; excused: number }>);

      // Count attendance records by day and status
      attendanceData?.forEach((record) => {
        const dateStr = format(new Date(record.timestamp), "yyyy-MM-dd");
        if (statusByDay[dateStr]) {
          const status = record.status.toLowerCase();
          if (
            status === "present" ||
            status === "absent" ||
            status === "late" ||
            status === "excused"
          ) {
            statusByDay[dateStr][
              status as keyof (typeof statusByDay)[typeof dateStr]
            ]++;
          }
        }
      });

      // Transform data for the chart
      const chartData = Object.entries(statusByDay).map(([date, counts]) => ({
        date: format(new Date(date), "MMM d"),
        present: counts.present,
        absent: counts.absent,
        late: counts.late,
        excused: counts.excused,
      }));

      setData(chartData);
      setError(null);
    } catch (err) {
      console.error("Error fetching attendance trends:", err);
      setError("Failed to load attendance trends");
      toast({
        title: "Error",
        description: "Failed to load attendance trends",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <Skeleton className="h-full w-full" />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-muted-foreground">
                <p>{error}</p>
                <button
                  onClick={fetchData}
                  className="mt-4 text-sm text-primary hover:underline"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={data}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "#ffffff",
                    borderColor: "#e2e8f0",
                    borderRadius: "6px",
                    color: "#1e293b",
                  }}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="present"
                  name="Present"
                  stroke="#10b981"
                  activeDot={{ r: 8 }}
                />
                <Line
                  type="monotone"
                  dataKey="absent"
                  name="Absent"
                  stroke="#ef4444"
                />
                <Line
                  type="monotone"
                  dataKey="late"
                  name="Late"
                  stroke="#f59e0b"
                />
                <Line
                  type="monotone"
                  dataKey="excused"
                  name="Excused"
                  stroke="#8b5cf6"
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
