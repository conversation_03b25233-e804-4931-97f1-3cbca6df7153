import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { format } from "date-fns";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import { fetchSchoolAnalytics } from "@/lib/api/system-admin";

interface SchoolAnalyticsChartProps {
  schoolId: string;
  period?: number;
  title?: string;
  description?: string;
  chartType?: "bar" | "line" | "pie";
  dataType?: "growth" | "attendance" | "status";
}

export default function SchoolAnalyticsChart({
  schoolId,
  period = 30,
  title = "School Analytics",
  description = "Analytics data for this school",
  chartType = "bar",
  dataType = "growth",
}: SchoolAnalyticsChartProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, [schoolId, period, dataType]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const analytics = await fetchSchoolAnalytics(schoolId, period);
      setData(analytics);
    } catch (error) {
      console.error("Error fetching school analytics:", error);
      setError("Failed to fetch analytics data");
      toast({
        title: "Error",
        description: "Failed to fetch analytics data. Using default data.",
        variant: "destructive",
      });

      // Set default data as fallback
      setData({
        userCount: 0,
        attendanceCount: 0,
        excuseCount: 0,
        statusCounts: {
          present: 0,
          absent: 0,
          late: 0,
          excused: 0,
        },
        userGrowthSeries: [{ month: format(new Date(), "yyyy-MM"), count: 0 }],
        period: period,
      });
    } finally {
      setLoading(false);
    }
  };

  const renderChart = () => {
    if (loading) {
      return (
        <div className="w-full h-[300px] flex items-center justify-center">
          <Skeleton className="h-[300px] w-full" />
        </div>
      );
    }

    if (error || !data) {
      return (
        <div className="w-full h-[300px] flex items-center justify-center">
          <p className="text-muted-foreground">
            {error || "No data available"}
          </p>
        </div>
      );
    }

    // Prepare data based on dataType
    if (dataType === "growth") {
      return renderGrowthChart();
    } else if (dataType === "attendance") {
      return renderAttendanceChart();
    } else if (dataType === "status") {
      return renderStatusChart();
    }

    return (
      <div className="w-full h-[300px] flex items-center justify-center">
        <p className="text-muted-foreground">Invalid chart type</p>
      </div>
    );
  };

  const renderGrowthChart = () => {
    const growthData = data.userGrowthSeries || [];

    if (chartType === "line") {
      return (
        <ResponsiveContainer width="100%" height={300}>
          <LineChart
            data={growthData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="count"
              name="New Users"
              stroke="#8884d8"
              activeDot={{ r: 8 }}
            />
          </LineChart>
        </ResponsiveContainer>
      );
    }

    return (
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={growthData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="count" name="New Users" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const renderAttendanceChart = () => {
    // Create attendance data for the chart
    const attendanceData = [
      {
        name: "Attendance",
        count: data.attendanceCount || 0,
      },
      {
        name: "Excuses",
        count: data.excuseCount || 0,
      },
    ];

    if (chartType === "pie") {
      const COLORS = ["#0088FE", "#00C49F"];
      return (
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={attendanceData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) =>
                `${name}: ${(percent * 100).toFixed(0)}%`
              }
              outerRadius={100}
              fill="#8884d8"
              dataKey="count"
            >
              {attendanceData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      );
    }

    return (
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={attendanceData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="count" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const renderStatusChart = () => {
    // Transform status counts to array for chart
    const statusData = Object.entries(data.statusCounts || {}).map(
      ([status, count]) => ({
        name: status.charAt(0).toUpperCase() + status.slice(1),
        count,
      })
    );

    if (chartType === "pie") {
      const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];
      return (
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={statusData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) =>
                `${name}: ${(percent * 100).toFixed(0)}%`
              }
              outerRadius={100}
              fill="#8884d8"
              dataKey="count"
            >
              {statusData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      );
    }

    return (
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={statusData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="count" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>{renderChart()}</CardContent>
    </Card>
  );
}
