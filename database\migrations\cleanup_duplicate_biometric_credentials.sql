-- Cleanup script for duplicate biometric credentials
-- Run this ONCE in your Supabase SQL Editor to clean up existing duplicates

-- This script will keep only the most recent biometric credential for each user
-- and delete all older ones

WITH ranked_credentials AS (
  SELECT 
    id,
    user_id,
    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
  FROM public.biometric_credentials
)
DELETE FROM public.biometric_credentials 
WHERE id IN (
  SELECT id 
  FROM ranked_credentials 
  WHERE rn > 1
);

-- Verify the cleanup - this should show only one credential per user
SELECT 
  user_id, 
  COUNT(*) as credential_count,
  MAX(created_at) as latest_credential
FROM public.biometric_credentials 
GROUP BY user_id 
ORDER BY credential_count DESC;
