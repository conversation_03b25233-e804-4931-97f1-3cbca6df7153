import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import { getBranding } from "@/config/branding";

// Import translations
import enTranslation from "./locales/en.json";
import trTranslation from "./locales/tr.json";

// Function to interpolate branding variables in translations
const interpolateBranding = (
  translations: any,
  language: string = "en"
): any => {
  const branding = getBranding(language);
  const brandingVars = {
    APP_NAME: branding.APP_NAME,
    APP_SHORT_NAME: branding.APP_SHORT_NAME,
    APP_DESCRIPTION: branding.APP_DESCRIPTION,
    COMPANY_NAME: branding.COMPANY_NAME,
    CONTACT_EMAIL: branding.CONTACT_EMAIL,
  };

  const interpolateObject = (obj: any): any => {
    if (typeof obj === "string") {
      let result = obj;
      Object.entries(brandingVars).forEach(([key, value]) => {
        result = result.replace(new RegExp(`{{${key}}}`, "g"), value);
      });
      return result;
    } else if (Array.isArray(obj)) {
      return obj.map(interpolateObject);
    } else if (typeof obj === "object" && obj !== null) {
      const newObj: any = {};
      Object.entries(obj).forEach(([key, value]) => {
        newObj[key] = interpolateObject(value);
      });
      return newObj;
    }
    return obj;
  };

  return interpolateObject(translations);
};

// Process translations with language-specific branding interpolation
const processedEnTranslation = interpolateBranding(enTranslation, "en");
const processedTrTranslation = interpolateBranding(trTranslation, "tr");

// Initialize i18next
i18n
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    // Resources contain translations with branding interpolation
    resources: {
      en: {
        translation: processedEnTranslation,
      },
      tr: {
        translation: processedTrTranslation,
      },
    },
    // Default language
    fallbackLng: "en",
    // Debug mode disabled for production
    debug: false,
    // Detect language from localStorage, navigator, etc.
    detection: {
      order: ["localStorage", "navigator"],
      lookupLocalStorage: "i18nextLng",
      caches: ["localStorage"],
    },
    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    // React options
    react: {
      useSuspense: false, // Disable suspense for better compatibility
      bindI18n: "languageChanged", // Re-render components when language changes
    },
    // Ensure translations are loaded before rendering
    load: "languageOnly", // Only load language part of the code (e.g., 'en' instead of 'en-US')
    // Return key if translation is missing
    returnNull: false,
    returnEmptyString: false,
    saveMissing: true,
    missingKeyHandler: (lng, ns, key) => {
      // Missing translation key handler - removed console logs for production
    },
  });

// Initialize language change handler without logging
i18n.on("languageChanged", () => {
  // Language change detected - no logging in production
});

export default i18n;
