import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { toast as sonnerToast } from "sonner";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { useTranslation } from "react-i18next";
import {
  Loader2,
  Plus,
  Trash2,
  Edit,
  Image,
  Eye,
  EyeOff,
  ArrowUp,
  ArrowDown,
  X,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface CarouselItem {
  id: string;
  title: string;
  description: string | null;
  image_url: string;
  active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
  target_audience: string[];
}

export default function SimpleCarouselManager() {
  const { toast } = useToast();
  const { profile } = useAuth();
  const { currentSchool } = useSchool();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [carouselItems, setCarouselItems] = useState<CarouselItem[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<CarouselItem | null>(null);
  const [fileToUpload, setFileToUpload] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string>("");
  const [uploadingImage, setUploadingImage] = useState(false);

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [active, setActive] = useState(true);
  const [targetAudience, setTargetAudience] = useState<string[]>([
    "student",
    "teacher",
    "admin",
  ]);

  // Empty array for carousel items
  const emptyItems: CarouselItem[] = [];

  // Load carousel items
  useEffect(() => {
    if (currentSchool?.id) {
      loadCarouselItems();
    }
  }, [currentSchool]);

  const loadCarouselItems = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("carousel_content")
        .select("*")
        .eq("school_id", currentSchool.id)
        .order("display_order", { ascending: true });

      if (error) {
        throw error;
      }

      // Set carousel items from database, or empty array if none found
      setCarouselItems(data || []);
    } catch (error: any) {
      console.error("Error loading carousel items:", error.message);
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.errorLoading",
          "Failed to load carousel items"
        ),
        position: "top-center",
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    const fileType = file.type.split("/")[0];
    if (fileType !== "image") {
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.invalidFileType",
          "Please select an image file."
        ),
        position: "top-center",
        duration: 5000,
      });
      return;
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.fileTooLarge",
          "Image file is too large. Maximum size is 5MB."
        ),
        position: "top-center",
        duration: 5000,
      });
      return;
    }

    setFileToUpload(file);
    setUploadingImage(true);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setFilePreview(reader.result as string);
      setUploadingImage(false);
    };
    reader.onerror = () => {
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.errorReadingFile",
          "Error reading image file. Please try again."
        ),
        position: "top-center",
        duration: 5000,
      });
      setUploadingImage(false);
    };
    reader.readAsDataURL(file);
  };

  // Reset form
  const resetForm = () => {
    setTitle("");
    setDescription("");
    setActive(true);
    setTargetAudience(["student", "teacher", "admin"]);
    setFileToUpload(null);
    setFilePreview("");
    setEditingItem(null);
  };

  // Open edit dialog
  const handleEdit = (item: CarouselItem) => {
    setEditingItem(item);
    setTitle(item.title);
    setDescription(item.description || "");
    setActive(item.active);
    setTargetAudience(item.target_audience);
    setFilePreview(item.image_url);
    setShowAddDialog(true);
  };

  // Handle audience checkbox change
  const handleAudienceChange = (audience: string, checked: boolean) => {
    if (checked) {
      setTargetAudience([...targetAudience, audience]);
    } else {
      setTargetAudience(targetAudience.filter((a) => a !== audience));
    }
  };

  // Save carousel item
  const handleSave = async () => {
    if (!title) {
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.titleRequired",
          "Title is required"
        ),
        position: "top-center",
        duration: 5000,
      });
      return;
    }

    if (!fileToUpload && !editingItem) {
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.mediaRequired",
          "Please select an image"
        ),
        position: "top-center",
        duration: 5000,
      });
      return;
    }

    if (targetAudience.length === 0) {
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.audienceRequired",
          "Please select at least one audience"
        ),
        position: "top-center",
        duration: 5000,
      });
      return;
    }

    setSaving(true);
    try {
      let imageUrl = editingItem?.image_url || "";

      // Upload file if there's a new one
      if (fileToUpload) {
        const fileExt = fileToUpload.name.split(".").pop();
        const fileName = `${Math.random()
          .toString(36)
          .substring(2, 15)}.${fileExt}`;
        const filePath = `carousel/${currentSchool.id}/${fileName}`;

        const { error: uploadError, data: uploadData } = await supabase.storage
          .from("images")
          .upload(filePath, fileToUpload);

        if (uploadError) {
          // Show error notification for file upload
          sonnerToast.error(t("common.error"), {
            description: t(
              "admin.schoolSettings.carouselSettings.fileUploadError",
              "Failed to upload image: " + uploadError.message
            ),
            position: "top-center",
            duration: 5000,
          });
          throw uploadError;
        }

        // Get public URL
        const { data: urlData } = supabase.storage
          .from("images")
          .getPublicUrl(filePath);
        imageUrl = urlData.publicUrl;

        // Show success notification for file upload
        sonnerToast.success(t("common.success"), {
          description: t(
            "admin.schoolSettings.carouselSettings.fileUploaded",
            "Image uploaded successfully"
          ),
          position: "top-center",
          duration: 2000,
        });
      }

      const itemData = {
        school_id: currentSchool.id,
        title,
        description: description || null,
        image_url: imageUrl,
        active,
        target_audience: targetAudience,
        created_by: profile?.id,
        updated_at: new Date().toISOString(),
      };

      if (editingItem) {
        // Update existing item
        const { error } = await supabase
          .from("carousel_content")
          .update(itemData)
          .eq("id", editingItem.id);

        if (error) throw error;

        // Show toast notification with Sonner
        sonnerToast.success(t("common.success"), {
          description: t(
            "admin.schoolSettings.carouselSettings.itemUpdated",
            "Carousel item updated successfully"
          ),
          position: "top-center",
          duration: 3000,
        });
      } else {
        // Add new item with the next display order
        const nextOrder =
          carouselItems.length > 0
            ? Math.max(...carouselItems.map((item) => item.display_order)) + 1
            : 0;

        const { error } = await supabase.from("carousel_content").insert({
          ...itemData,
          display_order: nextOrder,
          created_at: new Date().toISOString(),
        });

        if (error) throw error;

        // Show toast notification with Sonner
        sonnerToast.success(t("common.success"), {
          description: t(
            "admin.schoolSettings.carouselSettings.itemAdded",
            "Carousel item added successfully"
          ),
          position: "top-center",
          duration: 3000,
        });
      }

      // Reload items and reset form
      await loadCarouselItems();
      resetForm();
      setShowAddDialog(false);
    } catch (error: any) {
      console.error("Error saving carousel item:", error.message);
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.errorSaving",
          "Failed to save carousel item"
        ),
        position: "top-center",
        duration: 5000,
      });
    } finally {
      setSaving(false);
    }
  };

  // Delete carousel item
  const handleDelete = async (id: string) => {
    if (
      !confirm(
        t(
          "admin.schoolSettings.carouselSettings.confirmDelete",
          "Are you sure you want to delete this item?"
        )
      )
    )
      return;

    try {
      // Get the item to delete (for image cleanup)
      const itemToDelete = carouselItems.find((item) => item.id === id);

      // Delete from database
      const { error } = await supabase
        .from("carousel_content")
        .delete()
        .eq("id", id);

      if (error) throw error;

      // If the item has an image, try to delete it from storage
      if (itemToDelete?.image_url) {
        try {
          // Extract the path from the URL
          const urlObj = new URL(itemToDelete.image_url);
          const pathParts = urlObj.pathname.split("/");
          // The path should be in the format /storage/v1/object/public/media/[path]
          const filePath = pathParts.slice(5).join("/");

          if (filePath) {
            console.log("Deleting image:", filePath);
            await supabase.storage.from("images").remove([filePath]);
          }
        } catch (storageError) {
          // Log but don't fail if image deletion fails
          console.error("Error deleting image file:", storageError);
        }
      }

      // Reload items
      await loadCarouselItems();

      // Show success notification with Sonner
      sonnerToast.success(t("common.success"), {
        description: t(
          "admin.schoolSettings.carouselSettings.itemDeleted",
          "Carousel item deleted successfully"
        ),
        position: "top-center",
        duration: 3000,
      });
    } catch (error: any) {
      console.error("Error deleting carousel item:", error.message);
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.errorDeleting",
          "Failed to delete carousel item"
        ),
        position: "top-center",
        duration: 5000,
      });
    }
  };

  // Toggle item active status
  const toggleActive = async (id: string, currentActive: boolean) => {
    try {
      const { error } = await supabase
        .from("carousel_content")
        .update({
          active: !currentActive,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id);

      if (error) throw error;

      // Reload items to reflect the change
      await loadCarouselItems();

      // Show success notification with Sonner
      sonnerToast.success(t("common.success"), {
        description: currentActive
          ? t(
              "admin.schoolSettings.carouselSettings.itemDeactivated",
              "Carousel item deactivated"
            )
          : t(
              "admin.schoolSettings.carouselSettings.itemActivated",
              "Carousel item activated"
            ),
        position: "top-center",
        duration: 3000,
      });
    } catch (error: any) {
      console.error("Error toggling carousel item status:", error.message);
      // Show error notification with Sonner
      sonnerToast.error(t("common.error"), {
        description: t(
          "admin.schoolSettings.carouselSettings.errorToggling",
          "Failed to update carousel item status"
        ),
        position: "top-center",
        duration: 5000,
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {t(
            "admin.schoolSettings.carouselSettings.title",
            "Carousel Management"
          )}
        </CardTitle>
        <CardDescription>
          {t(
            "admin.schoolSettings.carouselSettings.description",
            "Manage carousel content for student and teacher dashboards"
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0 mb-3 sm:mb-4">
          <h3 className="text-base sm:text-lg font-medium">
            {t("admin.schoolSettings.carouselSettings.items", "Carousel Items")}
          </h3>
          <Button onClick={() => setShowAddDialog(true)} size="sm" className="w-full sm:w-auto">
            <Plus className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            <span className="text-xs sm:text-sm">
              {t("admin.schoolSettings.carouselSettings.addItem", "Add Item")}
            </span>
          </Button>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : carouselItems.length === 0 ? (
          <div className="text-center py-8 border rounded-md bg-muted/20">
            <p className="text-muted-foreground">
              {t(
                "admin.schoolSettings.carouselSettings.noItems",
                "No carousel items yet. Add your first item to get started."
              )}
            </p>
          </div>
        ) : (
          <>
            {/* Desktop Table View */}
            <div className="hidden lg:block border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">
                      {t("admin.schoolSettings.carouselSettings.order", "Order")}
                    </TableHead>
                    <TableHead>
                      {t("admin.schoolSettings.carouselSettings.title", "Title")}
                    </TableHead>
                    <TableHead>
                      {t("admin.schoolSettings.carouselSettings.image", "Image")}
                    </TableHead>
                    <TableHead>
                      {t(
                        "admin.schoolSettings.carouselSettings.audience",
                        "Audience"
                      )}
                    </TableHead>
                    <TableHead>
                      {t(
                        "admin.schoolSettings.carouselSettings.status",
                        "Status"
                      )}
                    </TableHead>
                    <TableHead className="text-right">
                      {t("common.actions", "Actions")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {carouselItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">
                        <div className="flex flex-col gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={async () => {
                              // Find the current item and the one above it
                              const currentIndex = carouselItems.findIndex(
                                (i) => i.id === item.id
                              );
                              if (currentIndex > 0) {
                                try {
                                  const currentItem = carouselItems[currentIndex];
                                  const prevItem =
                                    carouselItems[currentIndex - 1];

                                  // Swap display orders in database
                                  const batch = [];

                                  // First update the current item to a temporary order to avoid unique constraint conflicts
                                  await supabase
                                    .from("carousel_content")
                                    .update({
                                      display_order: -1, // Temporary value
                                      updated_at: new Date().toISOString(),
                                    })
                                    .eq("id", currentItem.id);

                                  // Then update the previous item
                                  await supabase
                                    .from("carousel_content")
                                    .update({
                                      display_order: currentItem.display_order,
                                      updated_at: new Date().toISOString(),
                                    })
                                    .eq("id", prevItem.id);

                                  // Finally update the current item to the previous item's order
                                  await supabase
                                    .from("carousel_content")
                                    .update({
                                      display_order: prevItem.display_order,
                                      updated_at: new Date().toISOString(),
                                    })
                                    .eq("id", currentItem.id);

                                  // Reload items
                                  await loadCarouselItems();
                                } catch (error) {
                                  console.error("Error reordering items:", error);
                                  sonnerToast.error(t("common.error"), {
                                    description: t(
                                      "admin.schoolSettings.carouselSettings.errorReordering",
                                      "Failed to reorder carousel items"
                                    ),
                                    position: "top-center",
                                    duration: 5000,
                                  });
                                }
                              }
                            }}
                            disabled={item.display_order === 0}
                          >
                            <ArrowUp className="h-4 w-4" />
                          </Button>
                          <span className="text-center">
                            {item.display_order + 1}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={async () => {
                              // Find the current item and the one below it
                              const currentIndex = carouselItems.findIndex(
                                (i) => i.id === item.id
                              );
                              if (currentIndex < carouselItems.length - 1) {
                                try {
                                  const currentItem = carouselItems[currentIndex];
                                  const nextItem =
                                    carouselItems[currentIndex + 1];

                                  // Swap display orders in database
                                  const batch = [];

                                  // First update the current item to a temporary order to avoid unique constraint conflicts
                                  await supabase
                                    .from("carousel_content")
                                    .update({
                                      display_order: -1, // Temporary value
                                      updated_at: new Date().toISOString(),
                                    })
                                    .eq("id", currentItem.id);

                                  // Then update the next item
                                  await supabase
                                    .from("carousel_content")
                                    .update({
                                      display_order: currentItem.display_order,
                                      updated_at: new Date().toISOString(),
                                    })
                                    .eq("id", nextItem.id);

                                  // Finally update the current item to the next item's order
                                  await supabase
                                    .from("carousel_content")
                                    .update({
                                      display_order: nextItem.display_order,
                                      updated_at: new Date().toISOString(),
                                    })
                                    .eq("id", currentItem.id);

                                  // Reload items
                                  await loadCarouselItems();
                                } catch (error) {
                                  console.error("Error reordering items:", error);
                                  sonnerToast.error(t("common.error"), {
                                    description: t(
                                      "admin.schoolSettings.carouselSettings.errorReordering",
                                      "Failed to reorder carousel items"
                                    ),
                                    position: "top-center",
                                    duration: 5000,
                                  });
                                }
                              }
                            }}
                            disabled={
                              item.display_order === carouselItems.length - 1
                            }
                          >
                            <ArrowDown className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{item.title}</div>
                        {item.description && (
                          <div className="relative">
                            <div
                              className="text-sm text-muted-foreground max-w-[200px] max-h-[60px] overflow-y-auto custom-scrollbar pr-2"
                              ref={(el) => {
                                // Add a subtle indicator if content is scrollable
                                if (el && el.scrollHeight > el.clientHeight) {
                                  el.classList.add(
                                    "after:absolute",
                                    "after:bottom-0",
                                    "after:left-0",
                                    "after:right-0",
                                    "after:h-4",
                                    "after:bg-gradient-to-t",
                                    "after:from-muted/50",
                                    "after:to-transparent",
                                    "after:pointer-events-none"
                                  );
                                }
                              }}
                            >
                              {item.description}
                            </div>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="w-16 h-12 rounded overflow-hidden">
                          <img
                            src={item.image_url}
                            alt={item.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1 flex-wrap">
                          {item.target_audience.includes("student") && (
                            <Badge variant="secondary" className="text-xs">
                              {t(
                                "admin.schoolSettings.carouselSettings.students",
                                "Students"
                              )}
                            </Badge>
                          )}
                          {item.target_audience.includes("teacher") && (
                            <Badge variant="secondary" className="text-xs">
                              {t(
                                "admin.schoolSettings.carouselSettings.teachers",
                                "Teachers"
                              )}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleActive(item.id, item.active)}
                        >
                          {item.active ? (
                            <Badge
                              variant="outline"
                              className="bg-green-50 border-green-200"
                            >
                              <Eye className="h-3 w-3 mr-1 text-green-600" />
                              {t(
                                "admin.schoolSettings.carouselSettings.active",
                                "Active"
                              )}
                            </Badge>
                          ) : (
                            <Badge
                              variant="outline"
                              className="bg-gray-50 border-gray-200"
                            >
                              <EyeOff className="h-3 w-3 mr-1 text-gray-600" />
                              {t(
                                "admin.schoolSettings.carouselSettings.inactive",
                                "Inactive"
                              )}
                            </Badge>
                          )}
                        </Button>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEdit(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDelete(item.id)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Mobile Card View */}
            <div className="lg:hidden space-y-2 -mx-1 sm:-mx-2">
              {carouselItems.map((item) => (
                <Card key={item.id} className="overflow-hidden mx-1 sm:mx-2 w-full max-w-full">
                  <CardContent className="p-2 sm:p-3 w-full max-w-full overflow-hidden">
                    <div className="flex items-start gap-2 sm:gap-3 w-full max-w-full">
                      {/* Image */}
                      <div className="w-12 h-9 sm:w-16 sm:h-12 rounded overflow-hidden flex-shrink-0">
                        <img
                          src={item.image_url}
                          alt={item.title}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-1 sm:gap-2 mb-1 sm:mb-2">
                          <div className="flex-1 min-w-0 overflow-hidden">
                            <h4 className="font-medium text-xs sm:text-sm truncate break-words">
                              {item.title.length > 20 ? `${item.title.substring(0, 20)}...` : item.title}
                            </h4>
                            {item.description && (
                              <p className="text-[10px] sm:text-xs text-muted-foreground mt-0.5 sm:mt-1 line-clamp-1 break-words overflow-hidden">
                                {item.description.length > 40 ? `${item.description.substring(0, 40)}...` : item.description}
                              </p>
                            )}
                          </div>

                          {/* Order Controls */}
                          <div className="flex flex-col items-center gap-0.5 flex-shrink-0 w-6 sm:w-8">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-4 w-4 sm:h-5 sm:w-5 p-0"
                              onClick={async () => {
                                // Same reorder logic as desktop
                                const currentIndex = carouselItems.findIndex(
                                  (i) => i.id === item.id
                                );
                                if (currentIndex > 0) {
                                  try {
                                    const currentItem = carouselItems[currentIndex];
                                    const prevItem = carouselItems[currentIndex - 1];

                                    await supabase
                                      .from("carousel_content")
                                      .update({
                                        display_order: -1,
                                        updated_at: new Date().toISOString(),
                                      })
                                      .eq("id", currentItem.id);

                                    await supabase
                                      .from("carousel_content")
                                      .update({
                                        display_order: currentItem.display_order,
                                        updated_at: new Date().toISOString(),
                                      })
                                      .eq("id", prevItem.id);

                                    await supabase
                                      .from("carousel_content")
                                      .update({
                                        display_order: prevItem.display_order,
                                        updated_at: new Date().toISOString(),
                                      })
                                      .eq("id", currentItem.id);

                                    await loadCarouselItems();
                                  } catch (error) {
                                    console.error("Error reordering items:", error);
                                    sonnerToast.error(t("common.error"), {
                                      description: t(
                                        "admin.schoolSettings.carouselSettings.errorReordering",
                                        "Failed to reorder carousel items"
                                      ),
                                      position: "top-center",
                                      duration: 5000,
                                    });
                                  }
                                }
                              }}
                              disabled={item.display_order === 0}
                            >
                              <ArrowUp className="h-2 w-2 sm:h-2.5 sm:w-2.5" />
                            </Button>
                            <span className="text-[10px] sm:text-xs font-medium text-center">
                              {item.display_order + 1}
                            </span>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-4 w-4 sm:h-5 sm:w-5 p-0"
                              onClick={async () => {
                                // Same reorder logic as desktop
                                const currentIndex = carouselItems.findIndex(
                                  (i) => i.id === item.id
                                );
                                if (currentIndex < carouselItems.length - 1) {
                                  try {
                                    const currentItem = carouselItems[currentIndex];
                                    const nextItem = carouselItems[currentIndex + 1];

                                    await supabase
                                      .from("carousel_content")
                                      .update({
                                        display_order: -1,
                                        updated_at: new Date().toISOString(),
                                      })
                                      .eq("id", currentItem.id);

                                    await supabase
                                      .from("carousel_content")
                                      .update({
                                        display_order: currentItem.display_order,
                                        updated_at: new Date().toISOString(),
                                      })
                                      .eq("id", nextItem.id);

                                    await supabase
                                      .from("carousel_content")
                                      .update({
                                        display_order: nextItem.display_order,
                                        updated_at: new Date().toISOString(),
                                      })
                                      .eq("id", currentItem.id);

                                    await loadCarouselItems();
                                  } catch (error) {
                                    console.error("Error reordering items:", error);
                                    sonnerToast.error(t("common.error"), {
                                      description: t(
                                        "admin.schoolSettings.carouselSettings.errorReordering",
                                        "Failed to reorder carousel items"
                                      ),
                                      position: "top-center",
                                      duration: 5000,
                                    });
                                  }
                                }
                              }}
                              disabled={item.display_order === carouselItems.length - 1}
                            >
                              <ArrowDown className="h-2 w-2 sm:h-2.5 sm:w-2.5" />
                            </Button>
                          </div>
                        </div>

                        {/* Audience and Status */}
                        <div className="flex items-center justify-between gap-1 sm:gap-2 mb-1 sm:mb-2">
                          <div className="flex gap-0.5 sm:gap-1 flex-wrap">
                            {item.target_audience.includes("student") && (
                              <Badge variant="secondary" className="text-[10px] sm:text-xs px-1 sm:px-1.5 py-0.5">
                                {t("admin.schoolSettings.carouselSettings.students", "Students")}
                              </Badge>
                            )}
                            {item.target_audience.includes("teacher") && (
                              <Badge variant="secondary" className="text-[10px] sm:text-xs px-1 sm:px-1.5 py-0.5">
                                {t("admin.schoolSettings.carouselSettings.teachers", "Teachers")}
                              </Badge>
                            )}
                          </div>

                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 sm:h-5 px-1 sm:px-1.5 text-[10px] sm:text-xs"
                            onClick={() => toggleActive(item.id, item.active)}
                          >
                            {item.active ? (
                              <Badge variant="outline" className="bg-green-50 border-green-200 text-[10px] sm:text-xs px-1 sm:px-1.5 py-0.5">
                                <Eye className="h-1.5 w-1.5 sm:h-2 sm:w-2 mr-0.5 sm:mr-1 text-green-600" />
                                {t("admin.schoolSettings.carouselSettings.active", "Active")}
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="bg-gray-50 border-gray-200 text-[10px] sm:text-xs px-1 sm:px-1.5 py-0.5">
                                <EyeOff className="h-1.5 w-1.5 sm:h-2 sm:w-2 mr-0.5 sm:mr-1 text-gray-600" />
                                {t("admin.schoolSettings.carouselSettings.inactive", "Inactive")}
                              </Badge>
                            )}
                          </Button>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 sm:h-7 px-1.5 sm:px-2 text-[10px] sm:text-xs"
                            onClick={() => handleEdit(item)}
                          >
                            <Edit className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-0.5 sm:mr-1" />
                            {t("common.edit", "Edit")}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 sm:h-7 px-1.5 sm:px-2 text-[10px] sm:text-xs text-destructive hover:text-destructive"
                            onClick={() => handleDelete(item.id)}
                          >
                            <Trash2 className="h-2.5 w-2.5 sm:h-3 sm:w-3 mr-0.5 sm:mr-1" />
                            {t("common.delete", "Delete")}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </>
        )}

        {/* Add/Edit Dialog */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="w-[95vw] max-w-[500px] max-h-[90vh] overflow-y-auto custom-scrollbar">
            <DialogHeader className="flex flex-row items-center justify-between">
              <div className="flex flex-col flex-1 min-w-0">
                <DialogTitle className="text-left break-words">
                  {editingItem
                    ? t(
                        "admin.schoolSettings.carouselSettings.editItem",
                        "Edit Carousel Item"
                      )
                    : t(
                        "admin.schoolSettings.carouselSettings.addItem",
                        "Add Carousel Item"
                      )}
                </DialogTitle>
                <DialogDescription className="text-left break-words">
                  {t(
                    "admin.schoolSettings.carouselSettings.itemFormDescription",
                    "Fill in the details for this carousel item."
                  )}
                </DialogDescription>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  resetForm();
                  setShowAddDialog(false);
                }}
                className="h-8 w-8 rounded-full flex-shrink-0 ml-2"
                aria-label={t("common.back", "Back")}
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogHeader>

            <div className="grid gap-4 py-4 w-full">
              <div className="grid gap-2 w-full">
                <Label htmlFor="title">
                  {t(
                    "admin.schoolSettings.carouselSettings.itemTitle",
                    "Title"
                  )}
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder={t(
                    "admin.schoolSettings.carouselSettings.itemTitlePlaceholder",
                    "Enter a title for this item"
                  )}
                  className="w-full break-all overflow-x-hidden"
                  style={{
                    wordBreak: 'break-all',
                    overflowWrap: 'anywhere',
                    maxWidth: '100%',
                    boxSizing: 'border-box'
                  }}
                />
                {title.length > 20 && (
                  <p className="text-xs text-muted-foreground break-all overflow-hidden" style={{ wordBreak: 'break-all', overflowWrap: 'anywhere' }}>
                    Preview: "{title.length > 20 ? `${title.substring(0, 20)}...` : title}"
                  </p>
                )}
              </div>

              <div className="grid gap-2 w-full">
                <Label htmlFor="description">
                  {t(
                    "admin.schoolSettings.carouselSettings.itemDescription",
                    "Description"
                  )}
                </Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder={t(
                    "admin.schoolSettings.carouselSettings.itemDescriptionPlaceholder",
                    "Enter a description (optional)"
                  )}
                  rows={4}
                  className="w-full resize-vertical min-h-[100px] max-h-[200px] break-all overflow-x-hidden"
                  style={{
                    wordBreak: 'break-all',
                    overflowWrap: 'anywhere',
                    whiteSpace: 'pre-wrap',
                    maxWidth: '100%',
                    boxSizing: 'border-box'
                  }}
                />
                {description.length > 40 && (
                  <p className="text-xs text-muted-foreground break-all overflow-hidden w-full" style={{ wordBreak: 'break-all', overflowWrap: 'anywhere', maxWidth: '100%' }}>
                    Preview: "{description.length > 40 ? `${description.substring(0, 40)}...` : description}"
                  </p>
                )}
              </div>

              <div className="grid gap-2">
                <Label>
                  {t(
                    "admin.schoolSettings.carouselSettings.imageUpload",
                    "Image Upload"
                  )}
                </Label>
                <div className="flex items-center gap-4">
                  <div className="relative flex-1">
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className={`flex-1 ${uploadingImage ? "opacity-50" : ""}`}
                      disabled={uploadingImage}
                    />
                    {uploadingImage && (
                      <div className="absolute inset-0 flex items-center justify-center bg-background/50">
                        <Loader2 className="h-5 w-5 animate-spin text-primary" />
                      </div>
                    )}
                  </div>
                </div>

                {filePreview && (
                  <div className="mt-2 border rounded-md p-2 bg-muted/20">
                    <img
                      src={filePreview}
                      alt="Preview"
                      className="max-h-[200px] mx-auto object-contain rounded-md"
                    />
                    {description && description.length > 100 && (
                      <div className="mt-2 p-2 border-t border-muted w-full overflow-hidden">
                        <p className="text-xs text-muted-foreground mb-1">
                          Description Preview:
                        </p>
                        <div className="relative w-full overflow-hidden">
                          <div
                            className="max-h-[80px] overflow-y-auto custom-scrollbar pr-2 text-sm break-all overflow-x-hidden w-full"
                            style={{
                              wordBreak: 'break-all',
                              overflowWrap: 'anywhere',
                              maxWidth: '100%',
                              boxSizing: 'border-box'
                            }}
                            ref={(el) => {
                              // Add a subtle indicator if content is scrollable
                              if (el && el.scrollHeight > el.clientHeight) {
                                el.classList.add(
                                  "after:absolute",
                                  "after:bottom-0",
                                  "after:left-0",
                                  "after:right-0",
                                  "after:h-4",
                                  "after:bg-gradient-to-t",
                                  "after:from-muted/50",
                                  "after:to-transparent",
                                  "after:pointer-events-none"
                                );
                              }
                            }}
                          >
                            {description}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="grid gap-2">
                <Label>
                  {t(
                    "admin.schoolSettings.carouselSettings.targetAudience",
                    "Target Audience"
                  )}
                </Label>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="students"
                      checked={targetAudience.includes("student")}
                      onCheckedChange={(checked) =>
                        handleAudienceChange("student", checked as boolean)
                      }
                    />
                    <Label htmlFor="students" className="text-sm">
                      {t(
                        "admin.schoolSettings.carouselSettings.students",
                        "Students"
                      )}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="teachers"
                      checked={targetAudience.includes("teacher")}
                      onCheckedChange={(checked) =>
                        handleAudienceChange("teacher", checked as boolean)
                      }
                    />
                    <Label htmlFor="teachers" className="text-sm">
                      {t(
                        "admin.schoolSettings.carouselSettings.teachers",
                        "Teachers"
                      )}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="admins"
                      checked={targetAudience.includes("admin")}
                      onCheckedChange={(checked) =>
                        handleAudienceChange("admin", checked as boolean)
                      }
                    />
                    <Label htmlFor="admins" className="text-sm">
                      {t(
                        "admin.schoolSettings.carouselSettings.admins",
                        "School Admins"
                      )}
                    </Label>
                  </div>
                  <div className="w-full mt-1 text-xs text-muted-foreground">
                    {t(
                      "admin.schoolSettings.carouselSettings.adminAudienceHint",
                      "Show this carousel item on your own admin dashboard"
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={active}
                  onCheckedChange={setActive}
                />
                <Label htmlFor="active">
                  {t(
                    "admin.schoolSettings.carouselSettings.activeItem",
                    "Active"
                  )}
                </Label>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  resetForm();
                  setShowAddDialog(false);
                }}
              >
                {t("common.cancel", "Cancel")}
              </Button>
              <Button onClick={handleSave} disabled={saving}>
                {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {editingItem
                  ? t("common.update", "Update")
                  : t("common.save", "Save")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
