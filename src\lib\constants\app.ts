/**
 * Application Constants
 * Central location for all application-wide constants
 */

export const APP_CONFIG = {
  NAME: "Attendance Tracking System",
  SHORT_NAME: "ATS",
  VERSION: "2.0.0",
  DESCRIPTION: "Modern attendance tracking for educational institutions",
  AUTHOR: "Campus Guardian Team",
  HOMEPAGE: "https://attendance-system.com",
} as const;

export const ROUTES = {
  HOME: "/",
  LOGIN: "/login",
  SIGNUP: "/signup",
  STUDENT: "/student",
  TEACHER: "/teacher", 
  ADMIN: "/admin",
  SYSTEM_ADMIN: "/system-admin",
  TABLET_SETUP: "/tablet-setup",
  TABLET_DISPLAY: "/tablet-display",
  PROFILE: "/profile",
  SETTINGS: "/settings",
  NOT_FOUND: "/404",
  OFFLINE: "/offline",
} as const;

export const USER_ROLES = {
  STUDENT: "student",
  TEACHER: "teacher", 
  ADMIN: "admin",
  SYSTEM_ADMIN: "system_admin",
} as const;

export const ATTENDANCE_STATUS = {
  PRESENT: "present",
  ABSENT: "absent",
  LATE: "late",
  EXCUSED: "excused",
} as const;

export const VERIFICATION_METHODS = {
  BIOMETRIC: "biometric",
  PIN: "pin",
  MANUAL: "manual",
  QR_CODE: "qr_code",
} as const;

export const EXCUSE_STATUS = {
  PENDING: "pending",
  APPROVED: "approved", 
  REJECTED: "rejected",
} as const;

export const NOTIFICATION_TYPES = {
  INFO: "info",
  WARNING: "warning",
  ERROR: "error",
  SUCCESS: "success",
  ATTENDANCE: "attendance",
  EXCUSE: "excuse",
  REMINDER: "reminder",
} as const;

export const LANGUAGES = {
  ENGLISH: "en",
  TURKISH: "tr",
} as const;

export const THEME_MODES = {
  LIGHT: "light",
  DARK: "dark",
  SYSTEM: "system",
} as const;

export const LOCAL_STORAGE_KEYS = {
  AUTH_TOKEN: "ats_auth_token",
  USER_PREFERENCES: "ats_user_preferences",
  THEME: "ats_theme",
  LANGUAGE: "ats_language",
  LAST_ROUTE: "ats_last_route",
  OFFLINE_DATA: "ats_offline_data",
} as const;

export const SESSION_STORAGE_KEYS = {
  FORM_DATA: "ats_form_data",
  NAVIGATION_STATE: "ats_navigation_state",
  TEMP_DATA: "ats_temp_data",
} as const;

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: "/auth/login",
    LOGOUT: "/auth/logout",
    SIGNUP: "/auth/signup",
    REFRESH: "/auth/refresh",
    VERIFY: "/auth/verify",
  },
  USERS: {
    PROFILE: "/users/profile",
    UPDATE: "/users/update",
    DELETE: "/users/delete",
    LIST: "/users/list",
  },
  ATTENDANCE: {
    RECORD: "/attendance/record",
    HISTORY: "/attendance/history",
    STATS: "/attendance/stats",
    REPORTS: "/attendance/reports",
  },
  SCHOOLS: {
    LIST: "/schools/list",
    CREATE: "/schools/create",
    UPDATE: "/schools/update",
    DELETE: "/schools/delete",
  },
} as const;

export const ERROR_CODES = {
  UNAUTHORIZED: "UNAUTHORIZED",
  FORBIDDEN: "FORBIDDEN",
  NOT_FOUND: "NOT_FOUND",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  SERVER_ERROR: "SERVER_ERROR",
  NETWORK_ERROR: "NETWORK_ERROR",
  TIMEOUT: "TIMEOUT",
} as const;

export const SUCCESS_CODES = {
  CREATED: "CREATED",
  UPDATED: "UPDATED",
  DELETED: "DELETED",
  VERIFIED: "VERIFIED",
  SENT: "SENT",
} as const;
