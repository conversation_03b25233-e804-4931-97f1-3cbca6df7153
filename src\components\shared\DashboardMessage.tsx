import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { motion, AnimatePresence } from "framer-motion";

interface Message {
  text: string;
  source: "school" | "system" | "system-school-specific";
}

interface DashboardMessageProps {
  userType: "student" | "teacher" | "admin";
}

export default function DashboardMessage({ userType }: DashboardMessageProps) {
  const { profile } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!profile) return;

    const fetchMessages = async () => {
      setLoading(true);
      try {
        const allMessages: Message[] = [];

        // Check for school-specific settings (from school admin)
        if (profile.school_id) {
          const schoolResult = await supabase
            .from("school_settings")
            .select(`custom_${userType}_message`)
            .eq("school_id", profile.school_id)
            .maybeSingle();

          if (schoolResult.error && schoolResult.error.code !== "PGRST116") {
            console.error(
              "Error fetching school settings:",
              schoolResult.error
            );
          }

          // Add school admin message if it exists
          const schoolMessage =
            schoolResult.data?.[`custom_${userType}_message`];
          if (schoolMessage) {
            allMessages.push({
              text: schoolMessage,
              source: "school",
            });
          }
        }

        // Check for system admin messages specifically for this school
        if (profile.school_id) {
          const schoolOverrideResult = await supabase
            .from("system_school_settings_overrides")
            .select("setting_value")
            .eq("setting_name", `custom_${userType}_message`)
            .eq("school_id", profile.school_id)
            .limit(1);

          if (schoolOverrideResult.error) {
            console.error(
              "Error fetching school-specific system overrides:",
              schoolOverrideResult.error
            );
          }

          // Add system admin school-specific message if it exists
          if (
            schoolOverrideResult.data &&
            schoolOverrideResult.data.length > 0 &&
            schoolOverrideResult.data[0].setting_value?.value
          ) {
            allMessages.push({
              text: schoolOverrideResult.data[0].setting_value.value,
              source: "system-school-specific",
            });
          }
        }

        // Check for global system admin messages
        const globalResult = await supabase
          .from("system_school_settings_overrides")
          .select("setting_value")
          .eq("setting_name", `custom_${userType}_message`)
          .eq("applies_to_all", true)
          .limit(1);

        if (globalResult.error) {
          console.error(
            "Error fetching global system overrides:",
            globalResult.error
          );
        }

        // Add global system admin message if it exists
        if (
          globalResult.data &&
          globalResult.data.length > 0 &&
          globalResult.data[0].setting_value?.value
        ) {
          allMessages.push({
            text: globalResult.data[0].setting_value.value,
            source: "system",
          });
        }

        // Only show one message at a time, prioritizing school-specific messages
        // If there are multiple messages, only show the first one (school-specific)
        if (allMessages.length > 0) {
          // Sort messages to prioritize school messages over system messages
          const sortedMessages = allMessages.sort((a, b) => {
            // School messages have higher priority
            if (a.source === "school" && b.source !== "school") return -1;
            if (a.source !== "school" && b.source === "school") return 1;
            return 0;
          });

          // Only take the first (highest priority) message
          setMessages([sortedMessages[0]]);
        } else {
          setMessages([]);
        }
      } catch (error) {
        console.error(`Error fetching ${userType} dashboard messages:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [profile, userType]);

  if (loading || messages.length === 0) return null;

  return (
    <AnimatePresence>
      <div className="space-y-3">
        {messages.map((message, index) => (
          <motion.div
            key={`${message.source}-${index}`}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="mb-3"
          >
            <Alert
              className={`
                bg-gradient-to-r border-l-4 shadow-sm
                ${
                  message.source === "school"
                    ? "from-primary/10 to-primary/5 border-primary"
                    : "from-secondary/10 to-secondary/5 border-secondary"
                }
              `}
            >
              <AlertCircle
                className={`h-4 w-4 ${
                  message.source === "school"
                    ? "text-primary"
                    : "text-secondary"
                }`}
              />
              <AlertDescription
                className={`${
                  message.source === "school"
                    ? "text-primary"
                    : "text-secondary"
                } font-medium`}
              >
                {message.text}
              </AlertDescription>
            </Alert>
          </motion.div>
        ))}
      </div>
    </AnimatePresence>
  );
}
