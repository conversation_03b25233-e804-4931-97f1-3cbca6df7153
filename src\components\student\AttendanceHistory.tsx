import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Calendar,
  MapPin,
  Smartphone,
  Shield,
} from "lucide-react";
import { format } from "date-fns";
import { useAttendanceHistory } from "@/hooks/useAttendanceHistory";
import { useTranslation } from "react-i18next";

export default function AttendanceHistory() {
  const { attendanceRecords, loading, error } = useAttendanceHistory();
  const { t } = useTranslation();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "late":
        return <Clock className="w-4 h-4 text-amber-500" />;
      case "absent":
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case "excused":
        return <Calendar className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  if (loading) {
    return <LoadingSkeleton />;
  }

  if (error) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Calendar className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <h3 className="text-lg font-semibold mb-1">
              {t("attendance.noRecordsAvailable")}
            </h3>
            <p className="text-sm">{t("attendance.historyWillAppear")}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{t("attendance.attendanceHistory")}</CardTitle>
        <CardDescription>
          {t("attendance.yourRecordsForAllClasses")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {attendanceRecords.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="w-12 h-12 mx-auto mb-2 opacity-30" />
            <p>{t("attendance.noData")}</p>
            <p className="text-sm">{t("attendance.historyWillAppear")}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {attendanceRecords.map((record) => (
              <div
                key={record.id}
                className="border rounded-lg p-4 hover:border-primary/50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <h3 className="font-medium">
                      {record.roomName || t("attendance.unknownRoom")}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {record.buildingName || t("attendance.unknownBuilding")}
                    </p>
                  </div>
                  <Badge
                    variant={
                      record.status === "present"
                        ? "default"
                        : record.status === "late"
                        ? "secondary"
                        : record.status === "absent"
                        ? "destructive"
                        : "outline"
                    }
                    className="flex items-center gap-1"
                  >
                    {getStatusIcon(record.status)}
                    <span className="capitalize">
                      {t(`attendance.${record.status}`)}
                    </span>
                  </Badge>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-3">
                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                    <Calendar className="w-3.5 h-3.5" />
                    <span>
                      {format(new Date(record.timestamp), "MMM dd, yyyy")}
                    </span>
                  </div>
                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                    <Clock className="w-3.5 h-3.5" />
                    <span>{format(new Date(record.timestamp), "h:mm a")}</span>
                  </div>
                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                    <Shield className="w-3.5 h-3.5" />
                    <span className="capitalize">
                      {t(`attendance.${record.verificationMethod}Verification`)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function LoadingSkeleton() {
  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <Skeleton className="h-7 w-[180px]" />
        <Skeleton className="h-4 w-[250px] mt-2" />
      </CardHeader>
      <CardContent className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="border rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <Skeleton className="h-5 w-[150px]" />
                <Skeleton className="h-4 w-[100px]" />
              </div>
              <Skeleton className="h-6 w-[70px]" />
            </div>
            <div className="grid grid-cols-3 gap-3 mt-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
