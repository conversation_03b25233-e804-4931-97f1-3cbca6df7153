-- Update RLS policies for other tables to enforce school-based isolation

-- Rooms table policies
DROP POLICY IF EXISTS "Teachers can manage their rooms" ON rooms;
DROP POLICY IF EXISTS "Students can view rooms" ON rooms;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all rooms" ON rooms;

-- Re-enable RLS on rooms
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;

-- Teachers can manage their rooms in their school
CREATE POLICY "Teachers can manage their rooms in their school"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
    AND teacher.id = rooms.teacher_id
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
    AND teacher.id = rooms.teacher_id
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Students can view rooms in their school
CREATE POLICY "Students can view rooms in their school"
ON rooms
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage all rooms in their school
CREATE POLICY "School admins can manage all rooms in their school"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all rooms
CREATE POLICY "System admins can manage all rooms"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Attendance records table policies
DROP POLICY IF EXISTS "manage_own_attendance" ON attendance_records;
DROP POLICY IF EXISTS "teachers_manage_room_attendance" ON attendance_records;
DROP POLICY IF EXISTS "admins_manage_attendance" ON attendance_records;

-- Re-enable RLS on attendance_records
ALTER TABLE attendance_records ENABLE ROW LEVEL SECURITY;

-- Students can manage their own attendance records
CREATE POLICY "Students can manage their own attendance records"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is the student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.id = attendance_records.student_id
  )
  AND
  -- Check if the record belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is the student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.id = attendance_records.student_id
  )
  AND
  -- Check if the record belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Teachers can manage attendance records for their rooms in their school
CREATE POLICY "Teachers can manage attendance records for their rooms in their school"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the room belongs to the teacher
  EXISTS (
    SELECT 1 FROM rooms
    WHERE rooms.id = attendance_records.room_id
    AND rooms.teacher_id = (
      SELECT id FROM profiles
      WHERE user_id = auth.uid()
    )
  )
  AND
  -- Check if the record belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the room belongs to the teacher
  EXISTS (
    SELECT 1 FROM rooms
    WHERE rooms.id = attendance_records.room_id
    AND rooms.teacher_id = (
      SELECT id FROM profiles
      WHERE user_id = auth.uid()
    )
  )
  AND
  -- Check if the record belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage all attendance records in their school
CREATE POLICY "School admins can manage all attendance records in their school"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the record belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the record belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all attendance records
CREATE POLICY "System admins can manage all attendance records"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);
