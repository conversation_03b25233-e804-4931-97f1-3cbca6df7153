-- Add localized notification support
-- This migration updates database triggers to create localized notifications

-- Function to get user's language preference
CREATE OR REPLACE FUNCTION get_user_language(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_lang TEXT;
BEGIN
  SELECT preferred_language INTO user_lang
  FROM profiles
  WHERE profiles.user_id = get_user_language.user_id;
  
  -- Default to English if not set
  RETURN COALESCE(user_lang, 'en');
END;
$$;

-- Function to get localized notification content
CREATE OR REPLACE FUNCTION get_localized_notification(
  template_key TEXT,
  language TEXT,
  param1 TEXT DEFAULT NULL,
  param2 TEXT DEFAULT NULL,
  param3 TEXT DEFAULT NULL,
  param4 TEXT DEFAULT NULL,
  param5 TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
AS $$
DECLARE
  result JSON;
BEGIN
  -- Define notification templates
  CASE template_key
    WHEN 'newExcuseRequest' THEN
      IF language = 'tr' THEN
        result := json_build_object(
          'title', '📝 Yeni Mazeret Talebi',
          'message', param1 || ', ' || param2 || ' sınıfı için ' || param3 || ' - ' || param4 || ' tarihleri arasında mazeret talebi gönderdi. Sebep: ' || param5
        );
      ELSE
        result := json_build_object(
          'title', '📝 New Excuse Request',
          'message', param1 || ' has submitted an excuse request for ' || param2 || ' from ' || param3 || ' to ' || param4 || '. Reason: ' || param5
        );
      END IF;
    
    WHEN 'excuseApproved' THEN
      IF language = 'tr' THEN
        result := json_build_object(
          'title', '✅ Mazeret Talebi Onaylandı',
          'message', param1 || ' - ' || param2 || ' tarihleri için mazeret talebiniz onaylandı. Sebep: ' || param3
        );
      ELSE
        result := json_build_object(
          'title', '✅ Excuse Request Approved',
          'message', 'Your excuse request for ' || param1 || ' to ' || param2 || ' has been approved. Reason: ' || param3
        );
      END IF;
    
    WHEN 'excuseRejected' THEN
      IF language = 'tr' THEN
        result := json_build_object(
          'title', '❌ Mazeret Talebi Reddedildi',
          'message', param1 || ' - ' || param2 || ' tarihleri için mazeret talebiniz reddedildi. Sebep: ' || param3 || '. Daha fazla bilgi için öğretmeninizle iletişime geçin.'
        );
      ELSE
        result := json_build_object(
          'title', '❌ Excuse Request Rejected',
          'message', 'Your excuse request for ' || param1 || ' to ' || param2 || ' has been rejected. Reason: ' || param3 || '. Please contact your teacher for more information.'
        );
      END IF;
    
    WHEN 'attendanceRecorded' THEN
      IF language = 'tr' THEN
        result := json_build_object(
          'title', '✅ Devam Kaydedildi',
          'message', param1 || ' sınıfında ' || param2 || ' olarak işaretlendiniz.'
        );
      ELSE
        result := json_build_object(
          'title', '✅ Attendance Recorded',
          'message', 'You have been marked ' || param2 || ' in ' || param1 || '.'
        );
      END IF;
    
    ELSE
      -- Default fallback
      result := json_build_object(
        'title', 'Notification',
        'message', 'You have a new notification.'
      );
  END CASE;
  
  RETURN result;
END;
$$;

-- Update excuse notification trigger to use localized notifications
CREATE OR REPLACE FUNCTION create_excuse_notification()
RETURNS TRIGGER AS $$
DECLARE
  student_name text;
  room_name text;
  teacher_lang text;
  student_lang text;
  teacher_notification JSON;
  student_notification JSON;
BEGIN
  -- Get student and room information
  SELECT name INTO student_name FROM profiles WHERE user_id = NEW.student_id;
  SELECT name INTO room_name FROM rooms WHERE id = NEW.room_id;
  
  -- If a new excuse is created, notify teacher
  IF TG_OP = 'INSERT' THEN
    -- Get teacher ID for the room
    SELECT teacher_id INTO NEW.teacher_id FROM rooms WHERE id = NEW.room_id;
    
    IF NEW.teacher_id IS NOT NULL THEN
      -- Get teacher's language preference
      teacher_lang := get_user_language(NEW.teacher_id);
      
      -- Get localized content for teacher
      teacher_notification := get_localized_notification(
        'newExcuseRequest',
        teacher_lang,
        student_name,
        room_name,
        NEW.start_date::text,
        NEW.end_date::text,
        NEW.reason
      );
      
      -- Create notification for teacher
      INSERT INTO notifications (
        type,
        title,
        message,
        student_id,
        teacher_id,
        room_number,
        metadata
      ) VALUES (
        'system',
        teacher_notification->>'title',
        teacher_notification->>'message',
        NEW.student_id,
        NEW.teacher_id,
        room_name,
        jsonb_build_object(
          'excuse_id', NEW.id,
          'start_date', NEW.start_date,
          'end_date', NEW.end_date,
          'reason', NEW.reason
        )
      );
    END IF;
  END IF;
  
  -- If excuse status is updated, notify student
  IF TG_OP = 'UPDATE' AND OLD.status != NEW.status AND NEW.status IN ('approved', 'rejected') THEN
    -- Get student's language preference
    student_lang := get_user_language(NEW.student_id);
    
    -- Get localized content for student
    IF NEW.status = 'approved' THEN
      student_notification := get_localized_notification(
        'excuseApproved',
        student_lang,
        NEW.start_date::text,
        NEW.end_date::text,
        NEW.reason
      );
    ELSE
      student_notification := get_localized_notification(
        'excuseRejected',
        student_lang,
        NEW.start_date::text,
        NEW.end_date::text,
        NEW.reason
      );
    END IF;
    
    -- Create notification for student
    INSERT INTO notifications (
      type,
      title,
      message,
      student_id,
      teacher_id,
      room_number,
      metadata
    ) VALUES (
      CASE WHEN NEW.status = 'approved' THEN 'excused' ELSE 'system' END,
      student_notification->>'title',
      student_notification->>'message',
      NEW.student_id,
      NEW.teacher_id,
      room_name,
      jsonb_build_object(
        'excuse_id', NEW.id,
        'start_date', NEW.start_date,
        'end_date', NEW.end_date,
        'reason', NEW.reason,
        'status', NEW.status
      )
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update attendance notification trigger to use localized notifications
CREATE OR REPLACE FUNCTION create_attendance_notification()
RETURNS TRIGGER AS $$
DECLARE
  student_name text;
  teacher_name text;
  room_name text;
  teacher_id uuid;
  student_lang text;
  notification_content JSON;
BEGIN
  -- Only create notifications for manual attendance changes
  IF NEW.verification_method != 'manual' THEN
    RETURN NEW;
  END IF;

  -- Get student name
  SELECT name INTO student_name
  FROM profiles
  WHERE user_id = NEW.student_id;

  -- Get teacher name and room name
  SELECT p.name, r.name, r.teacher_id 
  INTO teacher_name, room_name, teacher_id
  FROM rooms r
  LEFT JOIN profiles p ON p.user_id = r.teacher_id
  WHERE r.id = NEW.room_id;

  -- Get student's language preference
  student_lang := get_user_language(NEW.student_id);
  
  -- Get localized notification content
  notification_content := get_localized_notification(
    'attendanceRecorded',
    student_lang,
    COALESCE(room_name, 'Unknown Room'),
    NEW.status
  );

  -- Create notification for student
  INSERT INTO notifications (
    student_id,
    title,
    message,
    type,
    read,
    timestamp,
    metadata
  ) VALUES (
    NEW.student_id,
    notification_content->>'title',
    notification_content->>'message',
    CASE NEW.status
      WHEN 'present' THEN 'attendance'
      WHEN 'absent' THEN 'absence'
      WHEN 'late' THEN 'late'
      WHEN 'excused' THEN 'excused'
    END,
    false,
    NOW(),
    jsonb_build_object(
      'attendance_id', NEW.id,
      'room_id', NEW.room_id,
      'status', NEW.status,
      'verification_method', NEW.verification_method,
      'teacher_id', teacher_id,
      'room_name', room_name
    )
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop and recreate triggers
DROP TRIGGER IF EXISTS excuse_notification_trigger ON excuses;
DROP TRIGGER IF EXISTS attendance_notification_trigger ON attendance_records;

CREATE TRIGGER excuse_notification_trigger
AFTER INSERT OR UPDATE OF status ON public.excuses
FOR EACH ROW
EXECUTE FUNCTION create_excuse_notification();

CREATE TRIGGER attendance_notification_trigger
  AFTER INSERT OR UPDATE OF status
  ON attendance_records
  FOR EACH ROW
  WHEN (NEW.verification_method = 'manual')
  EXECUTE FUNCTION create_attendance_notification();
