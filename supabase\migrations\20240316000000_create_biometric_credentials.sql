-- Create the biometric_credentials table
CREATE TABLE IF NOT EXISTS public.biometric_credentials (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    credential_id TEXT NOT NULL UNIQUE,
    public_key TEXT NOT NULL,
    counter INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE public.biometric_credentials ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own credentials
CREATE POLICY "Users can read their own credentials"
    ON public.biometric_credentials
    FOR SELECT
    USING (auth.uid() = user_id);

-- Allow users to insert their own credentials
CREATE POLICY "Users can insert their own credentials"
    ON public.biometric_credentials
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Allow users to update their own credentials
CREATE POLICY "Users can update their own credentials"
    ON public.biometric_credentials
    FOR UPDATE
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Create an index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_biometric_credentials_user_id ON public.biometric_credentials(user_id);

-- Add a function to automatically update updated_at
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a trigger to automatically update updated_at
CREATE TRIGGER update_biometric_credentials_updated_at
    BEFORE UPDATE ON public.biometric_credentials
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column(); 