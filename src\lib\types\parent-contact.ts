export interface ParentContact {
  id: string;
  student_id: string;
  parent_name: string;
  email?: string;
  phone?: string;
  notification_method: 'email' | 'sms' | 'both' | 'none';
  notifications_enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface ParentContactFormValues {
  parent_name: string;
  email?: string;
  phone?: string;
  notification_method: 'email' | 'sms' | 'both' | 'none';
  notifications_enabled: boolean;
}
