-- Add new fields to profiles table
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS status VARCHAR DEFAULT 'active',
ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS password_reset_required BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS photo_url VARCHAR,

-- Student specific fields
ADD COLUMN IF NOT EXISTS student_id VARCHAR,
ADD COLUMN IF NOT EXISTS course VARCHAR,
ADD COLUMN IF NOT EXISTS block_name VARCHAR,
ADD COLUMN IF NOT EXISTS room_number VARCHAR,

-- Teacher specific fields
ADD COLUMN IF NOT EXISTS teacher_id VARCHAR,
ADD COLUMN IF NOT EXISTS department VARCHAR,
ADD COLUMN IF NOT EXISTS subject VARCHAR,
ADD COLUMN IF NOT EXISTS position VARCHAR;

-- Create user activity logs table
CREATE TABLE IF NOT EXISTS user_activity_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES profiles(id),
  action_type VARCHAR NOT NULL,
  details JSONB,
  ip_address VARCHAR,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id); 