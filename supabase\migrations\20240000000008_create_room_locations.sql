-- Create room_locations table
CREATE TABLE IF NOT EXISTS public.room_locations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    room_id UUID NOT NULL REFERENCES rooms(id),
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    radius_meters INTEGER NOT NULL DEFAULT 50,
    last_updated TIMESTAMPTZ DEFAULT now(),
    updated_by UUID REFERENCES auth.users(id),
    UNIQUE(room_id)
);

-- Create function to calculate distance between two points
CREATE OR REPLACE FUNCTION calculate_distance(
    lat1 DECIMAL,
    lon1 DECIMAL,
    lat2 DECIMAL,
    lon2 DECIMAL
) RETURNS DECIMAL AS $$
DECLARE
    R DECIMAL := 6371000; -- Earth's radius in meters
    φ1 DECIMAL := radians(lat1);
    φ2 DECIMAL := radians(lat2);
    Δφ DECIMAL := radians(lat2 - lat1);
    Δλ DECIMAL := radians(lon2 - lon1);
    
    a DECIMAL;
    c DECIMAL;
    d DECIMAL;
BEGIN
    a := sin(Δφ/2) * sin(Δφ/2) +
         cos(φ1) * cos(φ2) *
         sin(Δλ/2) * sin(Δλ/2);
    c := 2 * atan2(sqrt(a), sqrt(1-a));
    d := R * c;
    
    RETURN d;
END;
$$ LANGUAGE plpgsql;

-- Create function to verify location
CREATE OR REPLACE FUNCTION verify_attendance_location(
    student_lat DECIMAL,
    student_lon DECIMAL,
    room_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    room_location RECORD;
    distance DECIMAL;
BEGIN
    -- Get room location
    SELECT * INTO room_location
    FROM room_locations
    WHERE room_locations.room_id = $3;
    
    IF room_location IS NULL THEN
        RETURN TRUE; -- If no location set for room, allow attendance
    END IF;
    
    -- Calculate distance
    distance := calculate_distance(
        student_lat,
        student_lon,
        room_location.latitude,
        room_location.longitude
    );
    
    -- Return true if within radius
    RETURN distance <= room_location.radius_meters;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS verify_attendance_location ON attendance_records;
DROP FUNCTION IF EXISTS verify_attendance_location_trigger();

-- Add trigger to verify location before attendance insert
CREATE OR REPLACE FUNCTION verify_attendance_location_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Skip verification for manual attendance by teachers
    IF NEW.verification_method = 'manual' THEN
        RETURN NEW;
    END IF;

    -- Verify location if provided
    IF NEW.location IS NOT NULL AND 
       NEW.location->>'latitude' IS NOT NULL AND 
       NEW.location->>'longitude' IS NOT NULL THEN
        
        IF NOT verify_attendance_location(
            (NEW.location->>'latitude')::DECIMAL,
            (NEW.location->>'longitude')::DECIMAL,
            NEW.room_id
        ) THEN
            RAISE EXCEPTION 'Location verification failed: Student is too far from the classroom';
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER verify_attendance_location
    BEFORE INSERT OR UPDATE ON attendance_records
    FOR EACH ROW
    EXECUTE FUNCTION verify_attendance_location_trigger();

-- Enable RLS
ALTER TABLE public.room_locations ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Teachers can manage their room locations" ON room_locations;
DROP POLICY IF EXISTS "Students can read room locations" ON room_locations;

-- Create policies
CREATE POLICY "Teachers can manage their room locations"
    ON public.room_locations
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM rooms
            JOIN profiles ON profiles.id = rooms.teacher_id
            WHERE rooms.id = room_locations.room_id
            AND profiles.user_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM rooms
            JOIN profiles ON profiles.id = rooms.teacher_id
            WHERE rooms.id = room_locations.room_id
            AND profiles.user_id = auth.uid()
        )
    );

-- Allow students to read room locations
CREATE POLICY "Students can read room locations"
    ON public.room_locations
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'student'
        )
    );

-- Grant permissions
GRANT ALL ON public.room_locations TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Verify policies
SELECT * FROM pg_policies WHERE tablename = 'room_locations';

-- Insert a new location
INSERT INTO room_locations (
    room_id,
    latitude,
    longitude,
    radius_meters
) VALUES (
    'd1cfec14-60ea-441e-8456-7fdd18510b15',
    YOUR_LATITUDE,  -- e.g., 37.7749
    YOUR_LONGITUDE, -- e.g., -122.4194
    50              -- default radius in meters
);

SELECT * FROM rooms WHERE id = 'd1cfec14-60ea-441e-8456-7fdd18510b15';

SELECT * FROM room_locations WHERE room_id = 'd1cfec14-60ea-441e-8456-7fdd18510b15';