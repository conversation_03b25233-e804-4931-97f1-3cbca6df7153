import { supabase } from "@/lib/supabase";

/**
 * Migration to create the system_settings table and system_admin_code functions
 */
export const runSystemAdminCodeMigration = async () => {
  try {
    console.log("Running system admin code migration...");

    // First, try to directly insert the system admin code
    // This will create the record if it doesn't exist or update it if it does
    try {
      // Check if the system_settings table exists by trying to select from it
      const { data: settingsData, error: settingsError } = await supabase
        .from("system_settings")
        .select("id")
        .limit(1);

      if (settingsError) {
        console.error("Error checking system_settings table:", settingsError);
        console.log(
          "System_settings table might not exist, will try to create it"
        );
      } else {
        console.log("System_settings table exists");
      }

      // Try to insert or update the system admin code
      const { error: upsertError } = await supabase
        .from("system_settings")
        .upsert(
          {
            setting_name: "system_admin_code",
            setting_value: { code: "INITIAL_SYSTEM_SETUP_CODE" },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
          { onConflict: "setting_name" }
        );

      if (upsertError) {
        console.error("Error upserting system admin code:", upsertError);
        // If this fails, the table might not exist, so we'll try to create it
        throw upsertError;
      } else {
        console.log("System admin code upserted successfully");
        return true;
      }
    } catch (directError) {
      console.log(
        "Direct upsert failed, trying to create table and insert code"
      );

      // Try to create the system_settings table directly
      try {
        // Create the table
        await supabase.postgrest.rpc("execute_sql", {
          sql: `
            CREATE TABLE IF NOT EXISTS public.system_settings (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              setting_name TEXT NOT NULL UNIQUE,
              setting_value JSONB NOT NULL,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            );

            -- Enable RLS
            ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;

            -- Create policies
            DROP POLICY IF EXISTS "Allow authenticated users to select system_settings" ON public.system_settings;
            CREATE POLICY "Allow authenticated users to select system_settings"
              ON public.system_settings FOR SELECT
              USING (auth.role() = 'authenticated');

            DROP POLICY IF EXISTS "Allow authenticated users to insert system_settings" ON public.system_settings;
            CREATE POLICY "Allow authenticated users to insert system_settings"
              ON public.system_settings FOR INSERT
              WITH CHECK (auth.role() = 'authenticated');

            DROP POLICY IF EXISTS "Allow authenticated users to update system_settings" ON public.system_settings;
            CREATE POLICY "Allow authenticated users to update system_settings"
              ON public.system_settings FOR UPDATE
              USING (auth.role() = 'authenticated');
          `,
        });

        console.log("System_settings table created successfully");

        // Now insert the system admin code
        const { error: insertError } = await supabase
          .from("system_settings")
          .insert({
            setting_name: "system_admin_code",
            setting_value: { code: "INITIAL_SYSTEM_SETUP_CODE" },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });

        if (insertError) {
          console.error("Error inserting system admin code:", insertError);
          throw insertError;
        }

        console.log("System admin code inserted successfully");
      } catch (createTableError) {
        console.error(
          "Error creating system_settings table:",
          createTableError
        );

        // As a last resort, try to use the system_setting function if it exists
        try {
          const { error: updateError } = await supabase.rpc(
            "update_system_setting",
            {
              p_setting_name: "system_admin_code",
              p_setting_value: { code: "INITIAL_SYSTEM_SETUP_CODE" },
            }
          );

          if (updateError) {
            console.error(
              "Error using update_system_setting function:",
              updateError
            );
            throw updateError;
          }

          console.log(
            "System admin code updated using update_system_setting function"
          );
        } catch (updateError) {
          console.error(
            "All attempts to create/update system admin code failed"
          );
          return false;
        }
      }
    }

    console.log("System admin code migration completed successfully");
    return true;
  } catch (error) {
    console.error("Error running system admin code migration:", error);
    return false;
  }
};
