import { useState, useEffect } from 'react';

export function useOnlineStatus() {
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [wasOffline, setWasOffline] = useState<boolean>(false);
  const [offlineDuration, setOfflineDuration] = useState<number>(0);
  const [offlineStartTime, setOfflineStartTime] = useState<number | null>(null);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      
      // Calculate how long the user was offline
      if (offlineStartTime) {
        const duration = Math.floor((Date.now() - offlineStartTime) / 1000);
        setOfflineDuration(duration);
        setOfflineStartTime(null);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
      setOfflineStartTime(Date.now());
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [offlineStartTime]);

  // Function to manually check connection by pinging a reliable endpoint
  const checkConnection = async (): Promise<boolean> => {
    try {
      // Try to fetch a small resource from a reliable CDN
      const response = await fetch('https://www.cloudflare.com/cdn-cgi/trace', { 
        mode: 'no-cors',
        cache: 'no-store',
        headers: { 'Cache-Control': 'no-cache' }
      });
      
      // If we get here, we're online
      if (!isOnline) setIsOnline(true);
      return true;
    } catch (error) {
      // If fetch fails, we're offline
      if (isOnline) setIsOnline(false);
      return false;
    }
  };

  // Function to redirect to offline page
  const goToOfflinePage = () => {
    window.location.href = '/offline';
  };

  return { 
    isOnline, 
    wasOffline, 
    offlineDuration,
    checkConnection,
    goToOfflinePage
  };
}
