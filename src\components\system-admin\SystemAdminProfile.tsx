import { useState, useRef, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertCircle,
  Camera,
  Pencil,
  Shield,
  FootprintsIcon,
  MessageSquare,
  User,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { Badge } from "@/components/ui/badge";
import { <PERSON>bs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import FooterSettings from "./FooterSettings";
import FeedbackManagement from "./FeedbackManagement";

// Define the form schema with Zod
const profileSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  position: z.string().min(2, { message: "Position is required." }),
  adminId: z.string().min(2, { message: "Admin ID is required." }),
  email: z.string().email({ message: "Invalid email address." }).optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function SystemAdminProfile() {
  const [isEditing, setIsEditing] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { profile, updateProfile } = useAuth();

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: "",
      position: "",
      adminId: "",
      email: "",
    },
  });

  // Update form with existing profile data when available
  useEffect(() => {
    if (profile) {
      // Update form values
      form.reset({
        name: profile.name || "",
        position: profile.position || "",
        adminId: profile.adminId || "",
        email: profile.email || "",
      });

      // Set photo preview if exists
      if (profile.photoUrl) {
        setPhotoPreview(profile.photoUrl);
      }

      // Fetch audit logs for this admin
      fetchAuditLogs();
    }
  }, [profile, form]);

  const fetchAuditLogs = async () => {
    if (!profile?.id) return;

    try {
      const { data, error } = await supabase
        .from("audit_logs")
        .select("*")
        .eq("user_id", profile.id)
        .order("created_at", { ascending: false })
        .limit(5);

      if (error) throw error;
      setAuditLogs(data || []);
    } catch (error) {
      console.error("Error fetching audit logs:", error);
    }
  };

  const handlePhotoClick = () => {
    fileInputRef.current?.click();
  };

  const handlePhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return;
    }

    const file = e.target.files[0];
    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image under 5MB",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploadingPhoto(true);

      // Create a preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload to Supabase Storage
      const fileExt = file.name.split(".").pop();
      const fileName = `admin-${profile?.id}-${Date.now()}.${fileExt}`;
      const filePath = `profile-photos/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from("images")
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: publicUrlData } = supabase.storage
        .from("images")
        .getPublicUrl(filePath);

      // Update profile with new photo URL
      await updateProfile({
        photoUrl: publicUrlData.publicUrl,
      });

      toast({
        title: "Success",
        description: "Profile photo updated successfully",
      });
    } catch (error) {
      console.error("Error uploading photo:", error);
      toast({
        title: "Error",
        description: "Failed to upload photo. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUploadingPhoto(false);
    }
  };

  const onSubmit = async (data: ProfileFormValues) => {
    setSubmitAttempted(true);

    try {
      console.log("Updating system admin profile with data:", data);

      // Update the profile
      await updateProfile({
        name: data.name,
        adminId: data.adminId,
        position: data.position,
        email: data.email,
      });

      // Log the profile update in audit logs
      await supabase.from("audit_logs").insert({
        user_id: profile?.id,
        action_type: "update",
        entity_type: "profile",
        entity_id: profile?.id,
        details: { updated_fields: Object.keys(data) },
      });

      setIsEditing(false);
      toast({
        title: "Profile updated",
        description: "Your profile has been successfully updated.",
      });

      // Refresh audit logs
      fetchAuditLogs();
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitAttempted(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">System Admin Profile</h2>
        {!isEditing && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(true)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit Profile
          </Button>
        )}
      </div>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="footer" className="flex items-center gap-2">
            <FootprintsIcon className="h-4 w-4" />
            Footer Settings
          </TabsTrigger>
          <TabsTrigger value="feedback" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Feedback
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Manage your system administrator profile
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {isEditing ? (
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <div className="flex flex-col items-center mb-6">
                      <div
                        className="relative cursor-pointer group"
                        onClick={handlePhotoClick}
                      >
                        <Avatar className="w-24 h-24 border-2 border-primary/20">
                          <AvatarImage
                            src={photoPreview || profile?.photoUrl}
                          />
                          <AvatarFallback className="text-lg">
                            {profile?.name?.charAt(0).toUpperCase() || "A"}
                          </AvatarFallback>
                        </Avatar>
                        <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
                          <Camera className="h-6 w-6 text-white" />
                        </div>
                        <input
                          type="file"
                          ref={fileInputRef}
                          className="hidden"
                          accept="image/*"
                          onChange={handlePhotoChange}
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Click to change photo
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Your name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="<EMAIL>"
                                type="email"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="position"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Position</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g. System Administrator"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="adminId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Admin ID</FormLabel>
                            <FormControl>
                              <Input placeholder="Your admin ID" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsEditing(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={submitAttempted || uploadingPhoto}
                      >
                        Save Changes
                      </Button>
                    </div>
                  </form>
                </Form>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-start gap-6">
                    <Avatar className="w-24 h-24 border-2 border-primary/20">
                      <AvatarImage src={profile?.photoUrl} />
                      <AvatarFallback className="text-lg">
                        {profile?.name?.charAt(0).toUpperCase() || "A"}
                      </AvatarFallback>
                    </Avatar>

                    <div className="space-y-4 flex-1">
                      <div className="space-y-1">
                        <p className="text-2xl font-semibold">
                          {profile?.name}
                        </p>
                        <div className="flex items-center gap-2">
                          <p className="text-muted-foreground">
                            Admin ID: {profile?.adminId}
                          </p>
                          <Badge
                            variant="outline"
                            className="bg-amber-50 text-amber-700 border-amber-200"
                          >
                            <Shield className="h-3.5 w-3.5 mr-1" />
                            System Admin
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label className="text-muted-foreground">
                            Position
                          </Label>
                          <p>{profile?.position || "-"}</p>
                        </div>
                        <div>
                          <Label className="text-muted-foreground">Email</Label>
                          <p>{profile?.email}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your recent actions in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              {auditLogs.length > 0 ? (
                <div className="space-y-2">
                  {auditLogs.map((log) => (
                    <div
                      key={log.id}
                      className="flex justify-between items-center p-2 border-b"
                    >
                      <div>
                        <p className="text-sm font-medium">
                          {log.action_type} {log.entity_type}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(log.created_at).toLocaleString()}
                        </p>
                      </div>
                      <Badge variant="outline">{log.action_type}</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground py-4">
                  No recent activity found
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="footer" className="space-y-6">
          <FooterSettings />
        </TabsContent>

        <TabsContent value="feedback" className="space-y-6">
          <FeedbackManagement />
        </TabsContent>
      </Tabs>
    </div>
  );
}
