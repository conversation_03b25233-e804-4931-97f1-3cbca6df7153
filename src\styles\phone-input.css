/* Custom styles for react-phone-number-input */

.PhoneInput {
  display: flex;
  align-items: center;
  width: 100%;
}

.PhoneInputCountry {
  display: flex;
  align-items: center;
  margin-right: 0.5rem;
}

.PhoneInputCountryIcon {
  width: 1.5rem;
  height: 1rem;
  border-radius: 2px;
  overflow: hidden;
}

.PhoneInputCountrySelectArrow {
  margin-left: 0.25rem;
  width: 0.3rem;
  height: 0.3rem;
  border-style: solid;
  border-color: currentColor transparent transparent;
  border-width: 0.3rem 0.3rem 0;
  opacity: 0.6;
}

.PhoneInputInput {
  flex: 1;
  min-width: 0;
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.875rem;
  line-height: 1.25rem;
  padding: 0;
}

/* Country select dropdown */
.PhoneInputCountrySelect {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 1;
  border: 0;
  opacity: 0;
  cursor: pointer;
}

/* Improve focus styles */
.PhoneInput--focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Validation styles */
.PhoneInput--invalid .PhoneInputInput {
  color: hsl(var(--destructive));
}

/* Dark mode adjustments */
.dark .PhoneInputCountryIcon {
  opacity: 0.8;
}

.dark .PhoneInputCountrySelectArrow {
  opacity: 0.8;
}

/* Hover effect for country selector */
.PhoneInputCountry:hover .PhoneInputCountrySelectArrow {
  opacity: 1;
}
