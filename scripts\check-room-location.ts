const { supabase } = require('../src/lib/supabase');

async function checkRoomLocation() {
  const roomId = 'd1cfec14-60ea-441e-8456-7fdd18510b15';
  
  console.log('Checking room location for room ID:', roomId);
  
  // First check if the room exists
  const { data: roomData, error: roomError } = await supabase
    .from('rooms')
    .select('*')
    .eq('id', roomId)
    .single();

  if (roomError) {
    console.error('Error querying room:', roomError);
    return;
  }

  if (!roomData) {
    console.error('Room not found:', roomId);
    return;
  }

  console.log('Room exists:', roomData);

  // Check if room_locations table exists
  const { error: tableError } = await supabase
    .from('room_locations')
    .select('count(*)', { count: 'exact', head: true });

  if (tableError) {
    console.error('Error checking room_locations table:', tableError);
    return;
  }

  console.log('room_locations table exists');

  // Check RLS policies
  const { data: user } = await supabase.auth.getUser();
  console.log('Current user:', user);

  // Get user's profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('user_id', user.user?.id)
    .single();

  if (profileError) {
    console.error('Error getting user profile:', profileError);
    return;
  }

  console.log('User profile:', profile);

  // Check room location
  const { data: locationData, error: locationError } = await supabase
    .from('room_locations')
    .select('*')
    .eq('room_id', roomId);

  if (locationError) {
    console.error('Error querying room_locations:', locationError);
    return;
  }

  console.log('Room location data:', locationData);

  // Try to insert a test location
  const testLocation = {
    room_id: roomId,
    latitude: 34.9592083,
    longitude: -116.419389,
    radius_meters: 5000
  };

  console.log('Attempting to insert test location:', testLocation);

  const { data: insertData, error: insertError } = await supabase
    .from('room_locations')
    .upsert(testLocation, {
      onConflict: 'room_id'
    });

  if (insertError) {
    console.error('Error inserting test location:', insertError);
    return;
  }

  console.log('Test location insert result:', insertData);

  // Verify the location was saved
  const { data: verifyData, error: verifyError } = await supabase
    .from('room_locations')
    .select('*')
    .eq('room_id', roomId)
    .single();

  if (verifyError) {
    console.error('Error verifying location:', verifyError);
    return;
  }

  console.log('Final verification of saved location:', verifyData);
}

checkRoomLocation(); 