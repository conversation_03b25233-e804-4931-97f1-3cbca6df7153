-- First, drop all existing policies
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can create profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update any profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can delete profiles" ON profiles;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON profiles;

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create a simpler policy for authenticated users
CREATE POLICY "Enable all operations for authenticated users"
ON profiles
FOR ALL
TO authenticated
USING (
  -- Users can access their own profile
  user_id = auth.uid()
  OR 
  -- Or if they are an admin (checking auth.jwt() claim instead of profiles table)
  auth.jwt()->>'role' = 'admin'
)
WITH CHECK (
  -- Users can modify their own profile
  user_id = auth.uid()
  OR 
  -- Or if they are an admin (checking auth.jwt() claim instead of profiles table)
  auth.jwt()->>'role' = 'admin'
);

-- Ensure the admin user exists and has the correct role
INSERT INTO profiles (id, user_id, name, email, role)
VALUES (
  '6cfc05df-41d8-4a37-8115-7376d88f2462',
  '6cfc05df-41d8-4a37-8115-7376d88f2462',
  'Sarah Miller',
  '<EMAIL>',
  'admin'
)
ON CONFLICT (id) DO UPDATE 
SET role = 'admin'
WHERE profiles.id = '6cfc05df-41d8-4a37-8115-7376d88f2462'; 