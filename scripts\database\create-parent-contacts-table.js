// <PERSON>ript to create parent_contacts table in Supabase
const { createClient } = require('@supabase/supabase-js');

// Replace with your Supabase URL and anon key
const supabaseUrl = 'https://wclwxrilybnzkhvqzbmy.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndjbHd4cmlseWJuemtodnF6Ym15Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTk5NTI5NzYsImV4cCI6MjAxNTUyODk3Nn0.Jn9W8YqEwM9LWiIGhEKJQI-yPyoK9Bcr7eu9q6LXqEk';

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTables() {
  console.log('Creating parent_contacts table...');
  
  try {
    // Create parent_contacts table
    const { error: parentContactsError } = await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS parent_contacts (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          parent_name TEXT NOT NULL,
          email TEXT,
          phone TEXT,
          notification_method TEXT NOT NULL DEFAULT 'email',
          notifications_enabled BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          CONSTRAINT notification_method_check CHECK (notification_method IN ('email', 'sms', 'both', 'none')),
          CONSTRAINT contact_info_check CHECK (email IS NOT NULL OR phone IS NOT NULL)
        );
        
        CREATE INDEX IF NOT EXISTS parent_contacts_student_id_idx ON parent_contacts(student_id);
      `
    });
    
    if (parentContactsError) {
      console.error('Error creating parent_contacts table:', parentContactsError);
    } else {
      console.log('parent_contacts table created successfully');
    }
    
    // Create notification_logs table
    const { error: logsError } = await supabase.rpc('execute_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS notification_logs (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          excuse_id UUID NOT NULL,
          notification_type TEXT NOT NULL,
          recipient TEXT NOT NULL,
          success BOOLEAN NOT NULL DEFAULT true,
          error_message TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS notification_logs_student_id_idx ON notification_logs(student_id);
        CREATE INDEX IF NOT EXISTS notification_logs_excuse_id_idx ON notification_logs(excuse_id);
      `
    });
    
    if (logsError) {
      console.error('Error creating notification_logs table:', logsError);
    } else {
      console.log('notification_logs table created successfully');
    }
    
    // Set up RLS policies
    const { error: rlsError } = await supabase.rpc('execute_sql', {
      sql: `
        -- Enable RLS on parent_contacts table
        ALTER TABLE parent_contacts ENABLE ROW LEVEL SECURITY;
        
        -- Create policies for parent_contacts table
        CREATE POLICY "Students can view their own parent contacts"
          ON parent_contacts FOR SELECT
          USING (auth.uid() IN (
            SELECT user_id FROM profiles WHERE id = student_id
          ));
          
        CREATE POLICY "Teachers can view parent contacts for their students"
          ON parent_contacts FOR SELECT
          USING (EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'teacher'
          ));
          
        CREATE POLICY "Admins can manage all parent contacts"
          ON parent_contacts FOR ALL
          USING (EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
          ));
          
        -- Enable RLS on notification_logs table
        ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;
        
        -- Create policies for notification_logs table
        CREATE POLICY "Admins can view all notification logs"
          ON notification_logs FOR SELECT
          USING (EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
          ));
      `
    });
    
    if (rlsError) {
      console.error('Error setting up RLS policies:', rlsError);
    } else {
      console.log('RLS policies set up successfully');
    }
    
    console.log('Database setup completed');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createTables();
