import { useState, useEffect, useRef } from 'react';

interface PersistentState {
  [key: string]: any;
}

// Global state store to persist across component unmounts
const globalStateStore = new Map<string, any>();

export function useStatePersistence<T>(key: string, initialValue: T): [T, (value: T) => void] {
  const [state, setState] = useState<T>(() => {
    // Try to get from global store first
    if (globalStateStore.has(key)) {
      return globalStateStore.get(key);
    }
    
    // Try to get from sessionStorage
    try {
      const item = sessionStorage.getItem(`persist_${key}`);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Failed to parse persisted state for ${key}:`, error);
      return initialValue;
    }
  });

  const updateState = (value: T) => {
    setState(value);
    
    // Store in global store
    globalStateStore.set(key, value);
    
    // Store in sessionStorage
    try {
      sessionStorage.setItem(`persist_${key}`, JSON.stringify(value));
    } catch (error) {
      console.warn(`Failed to persist state for ${key}:`, error);
    }
  };

  // Sync with global store on mount
  useEffect(() => {
    if (globalStateStore.has(key)) {
      const storedValue = globalStateStore.get(key);
      if (JSON.stringify(storedValue) !== JSON.stringify(state)) {
        setState(storedValue);
      }
    }
  }, [key]);

  return [state, updateState];
}

export function usePersistentTabState(tabKey: string, defaultTab: string) {
  const [activeTab, setActiveTab] = useStatePersistence(`tab_${tabKey}`, defaultTab);
  
  return [activeTab, setActiveTab] as const;
}

export function usePersistentFormState<T extends Record<string, any>>(
  formKey: string, 
  initialState: T
): [T, (updates: Partial<T>) => void, () => void] {
  const [formState, setFormState] = useStatePersistence(`form_${formKey}`, initialState);
  
  const updateFormState = (updates: Partial<T>) => {
    setFormState(prev => ({ ...prev, ...updates }));
  };
  
  const resetFormState = () => {
    setFormState(initialState);
  };
  
  return [formState, updateFormState, resetFormState];
}

// Hook to prevent component re-initialization on tab switch
export function useComponentStability(componentKey: string) {
  const initRef = useRef(false);
  const [isInitialized, setIsInitialized] = useStatePersistence(`init_${componentKey}`, false);
  
  useEffect(() => {
    if (!initRef.current && !isInitialized) {
      initRef.current = true;
      setIsInitialized(true);
    }
  }, [isInitialized, setIsInitialized]);
  
  return isInitialized;
}

// Clear all persistent state (useful for logout)
export function clearAllPersistentState() {
  globalStateStore.clear();
  
  // Clear sessionStorage items that start with 'persist_'
  const keysToRemove: string[] = [];
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && key.startsWith('persist_')) {
      keysToRemove.push(key);
    }
  }
  
  keysToRemove.forEach(key => sessionStorage.removeItem(key));
}
