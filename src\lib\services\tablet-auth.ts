/**
 * 🔐 Tablet Authentication Service
 * Provides device certificate-based authentication for tablets
 */

import { supabase } from "@/lib/supabase";
import CryptoJS from "crypto-js";

export interface TabletDevice {
  id: string;
  deviceId: string;
  schoolId: string;
  roomId: string;
  blockId?: string;
  deviceName: string;
  certificate: string;
  isActive: boolean;
  lastSeen: Date;
  createdAt: Date;
  metadata?: {
    userAgent: string;
    screenResolution: string;
    timezone: string;
    language: string;
  };
}

export interface TabletAuthResult {
  success: boolean;
  device?: TabletDevice;
  token?: string;
  error?: string;
}

class TabletAuthService {
  private static instance: TabletAuthService;
  private deviceId: string | null = null;
  private authToken: string | null = null;

  static getInstance(): TabletAuthService {
    if (!TabletAuthService.instance) {
      TabletAuthService.instance = new TabletAuthService();
    }
    return TabletAuthService.instance;
  }

  /**
   * Generate a unique device fingerprint
   */
  generateDeviceFingerprint(): string {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    let canvasFingerprint = "";
    
    if (ctx) {
      ctx.textBaseline = "top";
      ctx.font = "14px Arial";
      ctx.fillText("Tablet Device Fingerprint", 2, 2);
      canvasFingerprint = canvas.toDataURL();
    }

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + "x" + screen.height,
      screen.colorDepth,
      new Date().getTimezoneOffset(),
      canvasFingerprint,
      navigator.hardwareConcurrency || 0,
      navigator.deviceMemory || 0,
      navigator.platform,
    ].join("|");

    return CryptoJS.SHA256(fingerprint).toString(CryptoJS.enc.Hex);
  }

  /**
   * Generate device metadata
   */
  getDeviceMetadata() {
    return {
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
    };
  }

  /**
   * Register a new tablet device
   */
  async registerTablet(
    schoolId: string,
    roomId: string,
    blockId?: string,
    deviceName?: string
  ): Promise<TabletAuthResult> {
    try {
      console.log("🔧 Starting tablet registration...", {
        schoolId,
        roomId,
        blockId,
        deviceName
      });

      const deviceId = this.generateDeviceFingerprint();
      const metadata = this.getDeviceMetadata();

      console.log("📱 Generated device fingerprint:", deviceId);
      console.log("📊 Device metadata:", metadata);

      // Generate device certificate
      const certificateData = {
        deviceId,
        schoolId,
        roomId,
        blockId,
        timestamp: new Date().toISOString(),
        metadata,
      };

      const certificate = CryptoJS.SHA256(
        JSON.stringify(certificateData) + deviceId
      ).toString(CryptoJS.enc.Hex);

      console.log("🔐 Generated certificate:", certificate.substring(0, 16) + "...");

      // Check if device already exists
      const { data: existingDevice } = await supabase
        .from("tablet_devices")
        .select("*")
        .eq("device_id", deviceId)
        .single();

      if (existingDevice) {
        // Update existing device
        const { data: updatedDevice, error } = await supabase
          .from("tablet_devices")
          .update({
            school_id: schoolId,
            room_id: roomId,
            block_id: blockId,
            device_name: deviceName || `Tablet-${deviceId.slice(0, 8)}`,
            certificate,
            is_active: true,
            last_seen: new Date().toISOString(),
            metadata,
          })
          .eq("device_id", deviceId)
          .select()
          .single();

        if (error) throw error;

        this.deviceId = deviceId;
        this.authToken = certificate;
        
        // Store in localStorage for persistence
        localStorage.setItem("tablet_device_id", deviceId);
        localStorage.setItem("tablet_auth_token", certificate);

        return {
          success: true,
          device: this.mapDatabaseToDevice(updatedDevice),
          token: certificate,
        };
      } else {
        // Create new device
        const { data: newDevice, error } = await supabase
          .from("tablet_devices")
          .insert({
            device_id: deviceId,
            school_id: schoolId,
            room_id: roomId,
            block_id: blockId,
            device_name: deviceName || `Tablet-${deviceId.slice(0, 8)}`,
            certificate,
            is_active: true,
            last_seen: new Date().toISOString(),
            metadata,
          })
          .select()
          .single();

        if (error) throw error;

        this.deviceId = deviceId;
        this.authToken = certificate;
        
        // Store in localStorage for persistence
        localStorage.setItem("tablet_device_id", deviceId);
        localStorage.setItem("tablet_auth_token", certificate);

        return {
          success: true,
          device: this.mapDatabaseToDevice(newDevice),
          token: certificate,
        };
      }
    } catch (error) {
      console.error("Error registering tablet:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Registration failed",
      };
    }
  }

  /**
   * Authenticate tablet device
   */
  async authenticateTablet(): Promise<TabletAuthResult> {
    try {
      const storedDeviceId = localStorage.getItem("tablet_device_id");
      const storedToken = localStorage.getItem("tablet_auth_token");

      if (!storedDeviceId || !storedToken) {
        return {
          success: false,
          error: "No stored device credentials",
        };
      }

      // Verify device exists and is active
      const { data: device, error } = await supabase
        .from("tablet_devices")
        .select("*")
        .eq("device_id", storedDeviceId)
        .eq("certificate", storedToken)
        .eq("is_active", true)
        .single();

      if (error || !device) {
        // Clear invalid credentials
        localStorage.removeItem("tablet_device_id");
        localStorage.removeItem("tablet_auth_token");
        
        return {
          success: false,
          error: "Invalid or inactive device credentials",
        };
      }

      // Update last seen
      await supabase
        .from("tablet_devices")
        .update({ last_seen: new Date().toISOString() })
        .eq("device_id", storedDeviceId);

      this.deviceId = storedDeviceId;
      this.authToken = storedToken;

      return {
        success: true,
        device: this.mapDatabaseToDevice(device),
        token: storedToken,
      };
    } catch (error) {
      console.error("Error authenticating tablet:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Authentication failed",
      };
    }
  }

  /**
   * Get current device info
   */
  getCurrentDevice(): { deviceId: string | null; token: string | null } {
    return {
      deviceId: this.deviceId,
      token: this.authToken,
    };
  }

  /**
   * Deactivate tablet device
   */
  async deactivateTablet(): Promise<boolean> {
    try {
      if (!this.deviceId) return false;

      await supabase
        .from("tablet_devices")
        .update({ is_active: false })
        .eq("device_id", this.deviceId);

      // Clear local storage
      localStorage.removeItem("tablet_device_id");
      localStorage.removeItem("tablet_auth_token");
      
      this.deviceId = null;
      this.authToken = null;

      return true;
    } catch (error) {
      console.error("Error deactivating tablet:", error);
      return false;
    }
  }

  /**
   * Map database record to TabletDevice interface
   */
  private mapDatabaseToDevice(dbRecord: any): TabletDevice {
    return {
      id: dbRecord.id,
      deviceId: dbRecord.device_id,
      schoolId: dbRecord.school_id,
      roomId: dbRecord.room_id,
      blockId: dbRecord.block_id,
      deviceName: dbRecord.device_name,
      certificate: dbRecord.certificate,
      isActive: dbRecord.is_active,
      lastSeen: new Date(dbRecord.last_seen),
      createdAt: new Date(dbRecord.created_at),
      metadata: dbRecord.metadata,
    };
  }
}

export const tabletAuthService = TabletAuthService.getInstance();
