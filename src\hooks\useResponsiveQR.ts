/**
 * 📐 Responsive QR Code Hook
 * Dynamically calculates optimal QR code size based on device screen
 */

import { useState, useEffect, useCallback, useMemo } from "react";

export interface QRSizeConfig {
  size: number;
  containerSize: number;
  scale: number;
  orientation: "portrait" | "landscape";
  deviceType: "mobile" | "tablet" | "desktop";
}

export interface ResponsiveQROptions {
  minSize?: number;
  maxSize?: number;
  padding?: number;
  aspectRatio?: number;
  breakpoints?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

const defaultOptions: Required<ResponsiveQROptions> = {
  minSize: 200,
  maxSize: 600,
  padding: 40,
  aspectRatio: 0.4, // QR takes 40% of smaller screen dimension
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200,
  },
};

// Move calculateQRSize function outside the hook to avoid hoisting issues
const calculateQRSize = (opts: Required<ResponsiveQROptions>): QRSizeConfig => {
  const screenWidth = window.innerWidth;
  const screenHeight = window.innerHeight;
  const minDimension = Math.min(screenWidth, screenHeight);
  const maxDimension = Math.max(screenWidth, screenHeight);

  // Determine device type
  let deviceType: "mobile" | "tablet" | "desktop";
  if (screenWidth < opts.breakpoints.mobile) {
    deviceType = "mobile";
  } else if (screenWidth < opts.breakpoints.tablet) {
    deviceType = "tablet";
  } else {
    deviceType = "desktop";
  }

  // Determine orientation
  const orientation: "portrait" | "landscape" = screenWidth < screenHeight ? "portrait" : "landscape";

  // Calculate base size
  let baseSize: number;

  if (deviceType === "mobile") {
    // Mobile: Use 60% of screen width minus padding
    baseSize = (screenWidth - opts.padding * 2) * 0.6;
  } else if (deviceType === "tablet") {
    // Tablet: Use aspect ratio of smaller dimension
    baseSize = minDimension * opts.aspectRatio;
  } else {
    // Desktop: Fixed size based on typical tablet viewing
    baseSize = 400;
  }

  // Apply size constraints
  const size = Math.max(opts.minSize, Math.min(opts.maxSize, baseSize));

  // Calculate container size (includes padding and space for text)
  const containerSize = size + opts.padding * 2;

  // Calculate scale factor for animations/transitions
  const scale = size / opts.maxSize;

  return {
    size: Math.round(size),
    containerSize: Math.round(containerSize),
    scale: Number(scale.toFixed(2)),
    orientation,
    deviceType,
  };
};

export function useResponsiveQR(options: ResponsiveQROptions = {}): QRSizeConfig {
  // Memoize the config to prevent infinite loops
  const config = useMemo(() => ({ ...defaultOptions, ...options }), [
    options.minSize,
    options.maxSize,
    options.padding,
    options.aspectRatio,
    options.breakpoints?.mobile,
    options.breakpoints?.tablet,
    options.breakpoints?.desktop,
  ]);

  const [qrConfig, setQrConfig] = useState<QRSizeConfig>(() =>
    calculateQRSize(config)
  );

  const updateSize = useCallback(() => {
    const newConfig = calculateQRSize(config);
    setQrConfig(newConfig);
  }, [config]);

  useEffect(() => {
    // Listen for resize events
    let timeoutId: NodeJS.Timeout;
    const handleResize = () => {
      // Debounce resize events
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateSize, 150);
    };

    window.addEventListener("resize", handleResize);
    window.addEventListener("orientationchange", handleResize);

    // Listen for fullscreen changes (common on tablets)
    const handleFullscreenChange = () => {
      // Delay to allow fullscreen transition to complete
      setTimeout(updateSize, 300);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleResize);
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
    };
  }, [updateSize]);

  return qrConfig;
}

/**
 * Hook for QR code with adaptive styling based on device
 */
export function useAdaptiveQRStyles(qrConfig: QRSizeConfig) {
  return {
    qrContainer: {
      width: qrConfig.containerSize,
      height: qrConfig.containerSize,
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      margin: "0 auto",
      transition: "all 0.3s ease-in-out",
    },
    qrCode: {
      width: qrConfig.size,
      height: qrConfig.size,
      maxWidth: "100%",
      maxHeight: "100%",
    },
    qrWrapper: {
      padding: qrConfig.deviceType === "mobile" ? "1rem" : "2rem",
      borderRadius: qrConfig.deviceType === "mobile" ? "0.5rem" : "1rem",
      background: "white",
      boxShadow: qrConfig.deviceType === "tablet" 
        ? "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
        : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    },
    instructions: {
      fontSize: qrConfig.deviceType === "mobile" ? "0.875rem" : "1rem",
      marginTop: qrConfig.deviceType === "mobile" ? "1rem" : "1.5rem",
      textAlign: "center" as const,
    },
  };
}

/**
 * Hook for QR code animations based on device capabilities
 */
export function useQRAnimations(qrConfig: QRSizeConfig) {
  const [isVisible, setIsVisible] = useState(false);
  const [isScanning, setIsScanning] = useState(false);

  useEffect(() => {
    // Entrance animation
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const triggerScanAnimation = useCallback(() => {
    setIsScanning(true);
    setTimeout(() => setIsScanning(false), 1000);
  }, []);

  const animationStyles = {
    container: {
      opacity: isVisible ? 1 : 0,
      transform: isVisible 
        ? "scale(1) translateY(0)" 
        : `scale(0.8) translateY(${qrConfig.deviceType === "mobile" ? "20px" : "40px"})`,
      transition: "all 0.6s cubic-bezier(0.4, 0, 0.2, 1)",
    },
    scanEffect: isScanning ? {
      animation: "pulse 1s ease-in-out",
      boxShadow: "0 0 0 4px rgba(59, 130, 246, 0.3)",
    } : {},
    qrCode: {
      transform: isScanning ? "scale(1.05)" : "scale(1)",
      transition: "transform 0.3s ease-in-out",
    },
  };

  return {
    isVisible,
    isScanning,
    triggerScanAnimation,
    animationStyles,
  };
}
