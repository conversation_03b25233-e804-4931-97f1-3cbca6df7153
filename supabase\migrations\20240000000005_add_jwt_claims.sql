-- Create a function to handle setting claims in JWT
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Set the role claim in the user's JWT
  UPDATE auth.users
  SET raw_app_meta_data = jsonb_set(
    raw_app_meta_data,
    '{role}',
    to_jsonb(NEW.role)
  )
  WHERE id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to automatically set claims when a profile is created or updated
DROP TRIGGER IF EXISTS on_profile_change ON profiles;
CREATE TRIGGER on_profile_change
  AFTER INSERT OR UPDATE OF role
  ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- Update the existing admin user's claims
UPDATE auth.users
SET raw_app_meta_data = jsonb_set(
  COALESCE(raw_app_meta_data, '{}'::jsonb),
  '{role}',
  '"admin"'
)
WHERE id = '6cfc05df-41d8-4a37-8115-7376d88f2462'; 