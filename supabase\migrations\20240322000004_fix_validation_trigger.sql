-- Drop the existing trigger and function
DROP TRIGGER IF EXISTS validate_profile_update_trigger ON profiles;
DROP FUNCTION IF EXISTS validate_profile_update;

-- Create a new validation function with better null handling
CREATE OR REPLACE FUNCTION validate_profile_update()
RETURNS TRIGGER AS $$
BEGIN
  -- For student profiles, only validate fields that are being updated
  IF NEW.role = 'student' THEN
    -- Check name if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.name IS DISTINCT FROM OLD.name THEN
      IF NEW.name IS NULL OR LENGTH(TRIM(NEW.name)) = 0 THEN
        RAISE EXCEPTION 'Name cannot be empty for student profiles';
      END IF;
    END IF;
    
    -- Check student_id if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.student_id IS DISTINCT FROM OLD.student_id THEN
      IF NEW.student_id IS NULL OR LENGTH(TRIM(NEW.student_id)) = 0 THEN
        RAISE EXCEPTION 'Student ID cannot be empty for student profiles';
      END IF;
    END IF;
    
    -- Check course if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.course IS DISTINCT FROM OLD.course THEN
      IF NEW.course IS NULL OR LENGTH(TRIM(NEW.course)) = 0 THEN
        RAISE EXCEPTION 'Course cannot be empty for student profiles';
      END IF;
    END IF;
    
    -- Check block_name if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.block_name IS DISTINCT FROM OLD.block_name THEN
      IF NEW.block_name IS NULL OR LENGTH(TRIM(NEW.block_name)) = 0 THEN
        RAISE EXCEPTION 'Block name cannot be empty for student profiles';
      END IF;
    END IF;
    
    -- Check room_number if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.room_number IS DISTINCT FROM OLD.room_number THEN
      IF NEW.room_number IS NULL OR LENGTH(TRIM(NEW.room_number)) = 0 THEN
        RAISE EXCEPTION 'Room number cannot be empty for student profiles';
      END IF;
    END IF;
  END IF;
  
  -- Prevent modification of user_id and role by non-admins
  IF (auth.jwt()->>'role') != 'admin' THEN
    IF NEW.user_id IS DISTINCT FROM OLD.user_id OR NEW.role IS DISTINCT FROM OLD.role THEN
      RAISE EXCEPTION 'Only administrators can modify user_id or role';
    END IF;
  END IF;
  
  -- Always set updated_at when any field changes
  NEW.updated_at = CURRENT_TIMESTAMP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER validate_profile_update_trigger
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION validate_profile_update(); 