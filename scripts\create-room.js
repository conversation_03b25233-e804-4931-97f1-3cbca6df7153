import { createClient } from '@supabase/supabase-js';

const supabaseUrl = "https://wclwxrilybnzkhvqzbmy.supabase.co";
const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndjbHd4cmlseWJuemtodnF6Ym15Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzA5NTksImV4cCI6MjA2MTI0Njk1OX0.MIARsz34RX0EftvwUkWIrEYQqE8VstxaCI31mjLhSHw";

const supabase = createClient(supabaseUrl, supabaseKey);

async function createRoom() {
  console.log('Signing in as teacher...');
  
  // Sign in as teacher
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'password123'
  });

  if (authError) {
    console.error('Error signing in:', authError);
    return;
  }

  console.log('Signed in successfully:', authData);

  // Get teacher's profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('user_id', authData.user.id)
    .single();

  if (profileError) {
    console.error('Error getting profile:', profileError);
    return;
  }

  console.log('Teacher profile:', profile);

  console.log('Creating a test room...');
  
  // Create a room (let Supabase generate the UUID)
  const { data: room, error: roomError } = await supabase
    .from('rooms')
    .insert({
      name: 'Test Room',
      building: 'Main Building',
      floor: 1,
      capacity: 30,
      teacher_id: profile.id
    })
    .select()
    .single();

  if (roomError) {
    console.error('Error creating room:', roomError);
    return;
  }

  console.log('Created room:', room);

  // Create room location
  const { data: location, error: locationError } = await supabase
    .from('room_locations')
    .insert({
      room_id: room.id,
      latitude: 34.9592083,
      longitude: -116.419389,
      radius_meters: 5000
    })
    .select()
    .single();

  if (locationError) {
    console.error('Error creating room location:', locationError);
    return;
  }

  console.log('Created room location:', location);
}

createRoom().catch(console.error); 