/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
}

/* Dark mode specific scrollbar */
.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(243, 146, 40, 0.5); /* Orange color from theme */
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(243, 146, 40, 0.7);
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(243, 146, 40, 0.5) rgba(0, 0, 0, 0.2);
}
