-- Create system_settings table to store configuration values
CREATE TABLE IF NOT EXISTS system_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_name TEXT NOT NULL UNIQUE,
  setting_value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies for system_settings
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Only admins can view system settings
CREATE POLICY "Admins can view system settings" ON system_settings
  FOR SELECT
  USING (auth.jwt() ->> 'role' = 'admin');

-- Only admins can insert system settings
CREATE POLICY "Admins can insert system settings" ON system_settings
  FOR INSERT
  WITH CHECK (auth.jwt() ->> 'role' = 'admin');

-- Only admins can update system settings
CREATE POLICY "Admins can update system settings" ON system_settings
  FOR UPDATE
  USING (auth.jwt() ->> 'role' = 'admin');

-- Only admins can delete system settings
CREATE POLICY "Admins can delete system settings" ON system_settings
  FOR DELETE
  USING (auth.jwt() ->> 'role' = 'admin');

-- Insert default settings
INSERT INTO system_settings (setting_name, setting_value)
VALUES 
  ('email_service_config', '{"apiKey": "", "fromEmail": "<EMAIL>"}'),
  ('sms_service_config', '{"accountSid": "", "authToken": "", "phoneNumber": ""}')
ON CONFLICT (setting_name) DO NOTHING;

-- Create function to securely update system settings
CREATE OR REPLACE FUNCTION update_system_setting(
  p_setting_name TEXT,
  p_setting_value JSONB
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF auth.jwt() ->> 'role' != 'admin' THEN
    RAISE EXCEPTION 'Only administrators can update system settings';
  END IF;

  -- Update or insert the setting
  INSERT INTO system_settings (setting_name, setting_value, updated_at)
  VALUES (p_setting_name, p_setting_value, now())
  ON CONFLICT (setting_name) 
  DO UPDATE SET 
    setting_value = p_setting_value,
    updated_at = now();

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;
