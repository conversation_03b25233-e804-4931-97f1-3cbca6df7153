-- First ensure user_id has a unique constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_user_id_key;
ALTER TABLE profiles ADD CONSTRAINT profiles_user_id_key UNIQUE (user_id);

-- Now modify the notifications table to properly reference the profiles table
ALTER TABLE notifications DROP CONSTRAINT IF EXISTS notifications_student_id_fkey;
ALTER TABLE notifications DROP CONSTRAINT IF EXISTS notifications_teacher_id_fkey;

-- Re-add the foreign key constraints
ALTER TABLE notifications 
ADD CONSTRAINT notifications_student_id_fkey 
FOREIGN KEY (student_id) REFERENCES profiles(user_id);

ALTER TABLE notifications 
ADD CONSTRAINT notifications_teacher_id_fkey 
FOREIGN KEY (teacher_id) REFERENCES profiles(user_id); 