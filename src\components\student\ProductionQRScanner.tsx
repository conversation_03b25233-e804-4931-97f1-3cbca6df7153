import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { toast as sonnerToast } from "sonner";
import {
  Camera,
  Check,
  Loader2,
  KeyRound,
  Fingerprint,
  MapPin,
  Clock,
  AlertCircle,
  Shield,
  X,
  RefreshCw,
  Scan,
  Mic,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { useTranslation } from "react-i18next";
import { useAttendanceSettings } from "@/hooks/useAttendanceSettings";
import QrScanner from "qr-scanner";
import {
  verifyCurrentQRCode,
  qrSecurityAPI,
  type SignedQRData,
} from "@/lib/services/qr-security-api";
import {
  validateRoomAssignment,
  generateDeviceFingerprint,
  validateLocation,
} from "@/lib/utils/qr-security";
import {
  getDeviceFingerprint,
  isLocationSpoofed,
  checkConcurrentSessions,
  hasReachedRateLimit,
  calculateDistance,
  createLocationAlert,
} from "@/lib/utils/security";

import { isWebAuthnAvailable, isPasskeyAvailable, isBiometricOnlySupported, canPerformBiometricAuth } from "@/lib/webauthn";
import SimpleCameraIcon from "./icons/SimpleCameraIcon";
import { motion } from "framer-motion";

// Use the SignedQRData type from security utils
type QRData = SignedQRData;

// Enhanced location types
interface LocationState {
  latitude: number;
  longitude: number;
  accuracy: number;
}

// Define strict types for GeoJSON Point
type GeoJSONPoint = {
  type: "Point";
  coordinates: [number, number]; // [longitude, latitude]
};

interface RoomLocation {
  latitude: number;
  longitude: number;
  radius_meters: number;
}

interface EnhancedLocationState extends LocationState {
  geoJsonPoint?: GeoJSONPoint;
  isWithinRadius?: boolean;
  distance?: number;
}

export default function ProductionQRScanner() {
  // Scanner state
  const [isScanning, setIsScanning] = useState(false);
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [qrData, setQrData] = useState<QRData | null>(null);
  const [scanError, setScanError] = useState<string>("");
  const [permissionState, setPermissionState] = useState<
    "unknown" | "granted" | "denied" | "requesting"
  >("unknown");

  // Verification state
  const [showVerification, setShowVerification] = useState(false);
  const [verificationType, setVerificationType] = useState<
    "biometric" | "pin" | "both" | null
  >(null);
  const [pin, setPin] = useState("");
  const [pinError, setPinError] = useState("");
  const [verificationMethodSettings, setVerificationMethodSettings] = useState<{
    verification_method_requirement: 'biometric_only' | 'pin_only' | 'both_required' | 'either';
  } | null>(null);
  const [biometricCompleted, setBiometricCompleted] = useState(false);
  const [isLoadingVerificationSettings, setIsLoadingVerificationSettings] = useState(false);
  const [locationPermissionBlocked, setLocationPermissionBlocked] = useState(false);
  const [biometricBlocked, setBiometricBlocked] = useState(false);

  // Attendance state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [isAlreadyPresent, setIsAlreadyPresent] = useState(false);
  const [location, setLocation] = useState<EnhancedLocationState | null>(null);

  // Enhanced security and location state
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [securityChecks, setSecurityChecks] = useState({
    rateLimited: false,
    concurrentSessions: false,
    locationSpoofed: false,
  });
  const [locationSettings, setLocationSettings] = useState<any>(null);
  const [smartRoomData, setSmartRoomData] = useState<any>(null);
  const [isCheckingLocation, setIsCheckingLocation] = useState(false);
  const [locationError, setLocationError] = useState("");

  // Room validation state
  const [roomValidation, setRoomValidation] = useState<{
    isValid: boolean;
    message: string;
    roomName?: string;
    blockName?: string;
  } | null>(null);

  const { profile, user } = useAuth();
  const { t } = useTranslation();
  const { settings: attendanceSettings, isWithinRecordingHours } = useAttendanceSettings();
  const [isOutsideRecordingHours, setIsOutsideRecordingHours] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const qrScannerRef = useRef<QrScanner | null>(null);

  // Create default settings if none exist
  const defaultSettings = {
    recording_start_time: "08:00:00",
    recording_end_time: "17:00:00",
  };

  const effectiveSettings = attendanceSettings || defaultSettings;

  // Update isOutsideRecordingHours when settings change or time changes
  useEffect(() => {
    const updateTimeCheck = () => {
      if (attendanceSettings) {
        const isOutside = !isWithinRecordingHours();
        setIsOutsideRecordingHours(isOutside);
      } else {
        setIsOutsideRecordingHours(true);
      }
    };

    // Update immediately
    updateTimeCheck();

    // Set up interval to check every minute
    const intervalId = setInterval(updateTimeCheck, 60000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [attendanceSettings, isWithinRecordingHours]);

  // Format time for display (HH:MM)
  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5);
  };

  // Get current time formatted
  const getCurrentTimeFormatted = () => {
    return new Date().toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Browser-specific location permission instructions
  const getBrowserSettingsInstructions = () => {
    const isChrome = navigator.userAgent.includes("Chrome");
    const isFirefox = navigator.userAgent.includes("Firefox");
    const isSafari = navigator.userAgent.includes("Safari") && !navigator.userAgent.includes("Chrome");
    const isEdge = navigator.userAgent.includes("Edg");

    if (isChrome) {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>{t("qrScanner.locationInstructions.chrome.step1")}</li>
          <li>{t("qrScanner.locationInstructions.chrome.step2")}</li>
          <li>{t("qrScanner.locationInstructions.chrome.step3")}</li>
          <li>{t("qrScanner.locationInstructions.chrome.step4")}</li>
        </ol>
      );
    } else if (isEdge) {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>{t("qrScanner.locationInstructions.edge.step1")}</li>
          <li>{t("qrScanner.locationInstructions.edge.step2")}</li>
          <li>{t("qrScanner.locationInstructions.edge.step3")}</li>
          <li>{t("qrScanner.locationInstructions.edge.step4")}</li>
        </ol>
      );
    } else if (isFirefox) {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>{t("qrScanner.locationInstructions.firefox.step1")}</li>
          <li>{t("qrScanner.locationInstructions.firefox.step2")}</li>
          <li>{t("qrScanner.locationInstructions.firefox.step3")}</li>
          <li>{t("qrScanner.locationInstructions.firefox.step4")}</li>
          <li>{t("qrScanner.locationInstructions.firefox.step5")}</li>
        </ol>
      );
    } else if (isSafari) {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>{t("qrScanner.locationInstructions.safari.step1")}</li>
          <li>{t("qrScanner.locationInstructions.safari.step2")}</li>
          <li>{t("qrScanner.locationInstructions.safari.step3")}</li>
          <li>{t("qrScanner.locationInstructions.safari.step4")}</li>
          <li>{t("qrScanner.locationInstructions.safari.step5")}</li>
        </ol>
      );
    } else {
      return (
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>{t("qrScanner.locationInstructions.generic.step1")}</li>
          <li>{t("qrScanner.locationInstructions.generic.step2")}</li>
          <li>{t("qrScanner.locationInstructions.generic.step3")}</li>
          <li>{t("qrScanner.locationInstructions.generic.step4")}</li>
        </ol>
      );
    }
  };

  const showLocationInstructions = () => {
    sonnerToast.error(t("qrScanner.toasts.locationAccessRequired"), {
      description: t("qrScanner.toasts.locationAccessDescription"),
      duration: 10000,
    });
  };

  // Smart room detection and initialization
  useEffect(() => {
    const initializeComponent = async () => {
      // Check camera permissions
      await checkCameraPermissions();

      // Get device fingerprint for security
      try {
        const deviceFingerprint = await getDeviceFingerprint();
        setDeviceInfo(deviceFingerprint);
      } catch (error) {
        console.warn("Failed to get device fingerprint:", error);
      }

      // Smart room detection
      if (profile) {
        await detectSmartRoom();
      }
    };

    initializeComponent();
  }, [profile]);

  // Smart room detection function
  const detectSmartRoom = async () => {
    if (!profile) return;

    try {
      // Method 1: Direct room_id from profile
      if (profile.room_id) {
        const { data: room, error } = await supabase
          .from("rooms")
          .select("id, name, block_id")
          .eq("id", profile.room_id)
          .single();

        if (!error && room) {
          setSmartRoomData(room);
          return;
        }
      }

      // Method 2: Try using block_name and room_number from profile
      const blockName = profile.blockName;
      const roomNumber = profile.roomNumber;

      if (blockName && roomNumber) {
        const { data: rooms, error } = await supabase
          .from("rooms")
          .select("id, name, block_id")
          .ilike("name", `%${roomNumber}%`)
          .limit(5);

        if (!error && rooms && rooms.length > 0) {
          // Find the best match
          const exactMatch = rooms.find(
            (r) => r.name === roomNumber || r.name === `Room ${roomNumber}`
          );
          const containsMatch = rooms.find((r) => r.name.includes(roomNumber));
          const bestMatch = exactMatch || containsMatch || rooms[0];

          setSmartRoomData(bestMatch);
          return;
        }
      }

      // Method 3: Fallback to any room from student's school
      let query = supabase.from("rooms").select("id, name, block_id");

      if (profile.school_id) {
        query = query.eq("school_id", profile.school_id);
      }

      const { data: anyRooms, error } = await query.limit(1);

      if (!error && anyRooms && anyRooms.length > 0) {
        setSmartRoomData(anyRooms[0]);
      } else {
        // Set placeholder room data
        setSmartRoomData({ id: "placeholder", name: t("qrScanner.defaultRoom"), block_id: null });
      }
    } catch (error) {
      console.error("Error in smart room detection:", error);
      setSmartRoomData({ id: "placeholder", name: t("qrScanner.defaultRoom"), block_id: null });
    }
  };

  // Get verification method settings for the school
  const getVerificationMethodSettings = async () => {
    if (!profile?.school_id) {
      return { verification_method_requirement: 'either' as const };
    }

    try {
      const { data, error } = await supabase
        .from("school_settings")
        .select("verification_method_requirement")
        .eq("school_id", profile.school_id)
        .single();

      if (error) {
        console.error("Error fetching verification method settings:", error);
        return { verification_method_requirement: 'either' as const };
      }

      return {
        verification_method_requirement: data?.verification_method_requirement || 'either' as const
      };
    } catch (error) {
      console.error("Error in getVerificationMethodSettings:", error);
      return { verification_method_requirement: 'either' as const };
    }
  };

  // Get location verification settings for a room
  const getLocationVerificationSettings = async (roomId: string) => {
    try {
      const defaultSettings = {
        globalEnabled: true,
        blockEnabled: true,
        roomEnabled: true,
        blockRadiusMeters: 100,
        roomRadiusMeters: 50,
      };

      // Get room data to find block_id
      const { data: roomData, error: roomError } = await supabase
        .from("rooms")
        .select("block_id")
        .eq("id", roomId)
        .single();

      if (roomError) {
        console.error("Error getting room data:", roomError);
        return defaultSettings;
      }

      const blockId = roomData.block_id;

      // Get settings at each level
      const [globalSettings, blockSettings, roomSettings, blockLocation, roomLocation] = await Promise.all([
        supabase
          .from("location_verification_settings")
          .select("global_enabled")
          .is("block_id", null)
          .is("room_id", null)
          .order("updated_at", { ascending: false })
          .limit(1),
        supabase
          .from("location_verification_settings")
          .select("block_enabled")
          .eq("block_id", blockId)
          .is("room_id", null)
          .order("updated_at", { ascending: false })
          .limit(1),
        supabase
          .from("location_verification_settings")
          .select("room_enabled")
          .eq("room_id", roomId)
          .order("updated_at", { ascending: false })
          .limit(1),
        supabase
          .from("block_locations")
          .select("radius_meters")
          .eq("block_id", blockId)
          .limit(1),
        supabase
          .from("room_locations")
          .select("radius_meters")
          .eq("room_id", roomId)
          .limit(1),
      ]);

      return {
        globalEnabled: globalSettings.data?.[0]?.global_enabled ?? true,
        blockEnabled: blockSettings.data?.[0]?.block_enabled ?? true,
        roomEnabled: roomSettings.data?.[0]?.room_enabled ?? true,
        blockRadiusMeters: blockLocation.data?.[0]?.radius_meters ?? 100,
        roomRadiusMeters: roomLocation.data?.[0]?.radius_meters ?? 50,
      };
    } catch (error) {
      console.error("Error getting location verification settings:", error);
      return {
        globalEnabled: true,
        blockEnabled: true,
        roomEnabled: true,
        blockRadiusMeters: 100,
        roomRadiusMeters: 50,
      };
    }
  };

  // Cleanup camera on unmount
  useEffect(() => {
    return () => {
      if (qrScannerRef.current) {
        qrScannerRef.current.stop();
        qrScannerRef.current.destroy();
      }
    };
  }, []);

  // Check camera permissions
  const checkCameraPermissions = async (): Promise<boolean> => {
    try {
      // Check if permissions API is available
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({
          name: "camera" as PermissionName,
        });

        if (permission.state === "granted") {
          setPermissionState("granted");
          return true;
        } else if (permission.state === "denied") {
          setPermissionState("denied");
          return false;
        }
      }
      return false;
    } catch (error) {
      console.warn("Permissions API not available:", error);
      return false;
    }
  };

  // Request camera permissions
  const requestCameraPermissions = async (): Promise<boolean> => {
    console.log("🎥 requestCameraPermissions called");
    try {
      setPermissionState("requesting");
      console.log("🎥 Permission state set to requesting");

      // Try to get user media to trigger permission prompt
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment", // Prefer back camera
        },
      });

      console.log("🎥 Camera stream obtained successfully");
      // Stop the stream immediately as we just wanted to get permission
      stream.getTracks().forEach((track) => track.stop());
      setPermissionState("granted");
      console.log("🎥 Permission state set to granted");
      return true;
    } catch (error) {
      console.error("🎥 Camera permission denied:", error);
      setPermissionState("denied");
      return false;
    }
  };

  // Start camera and QR scanning
  const startCamera = async () => {
    try {
      // Check if current time is within allowed recording hours
      if (isOutsideRecordingHours) {
        sonnerToast.error(t("qrScanner.attendanceRecordingNotAvailable"), {
          description: t("qrScanner.recordingTimeRestriction", {
            startTime: formatTime(effectiveSettings.recording_start_time),
            endTime: formatTime(effectiveSettings.recording_end_time),
            currentTime: getCurrentTimeFormatted(),
          }),
          duration: 5000,
        });
        return;
      }

      setIsScanning(true);
      setScanError("");

      // First check if we have camera permissions
      const hasPermission = await checkCameraPermissions();
      if (!hasPermission) {
        // Request camera permissions
        const permissionGranted = await requestCameraPermissions();
        if (!permissionGranted) {
          throw new Error(t("qrScanner.errors.cameraPermissionRequired"));
        }
      }

      // Set camera active state to render video element
      setIsCameraActive(true);

      // Wait a bit for the video element to render
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (!videoRef.current) {
        throw new Error(t("qrScanner.errors.videoElementNotFound"));
      }

      // Initialize QR Scanner
      qrScannerRef.current = new QrScanner(
        videoRef.current,
        (result) => handleQRScanResult(result.data),
        {
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: "environment", // Use back camera on mobile
          maxScansPerSecond: 5, // Limit scan rate for performance
        }
      );

      await qrScannerRef.current.start();
    } catch (error) {
      console.error("Error starting camera:", error);
      let errorMessage = t("qrScanner.errors.cameraPermissionDenied");

      if (error instanceof Error) {
        if (error.message.includes("permission")) {
          errorMessage = t("qrScanner.errors.cameraPermissionDenied");
        } else if (error.message.includes("not found")) {
          errorMessage = t("qrScanner.errors.noCameraFound");
        } else if (error.message.includes("NotAllowedError")) {
          errorMessage = t("qrScanner.errors.cameraAccessDenied");
        } else if (error.message.includes("NotFoundError")) {
          errorMessage = t("qrScanner.errors.noCameraDevice");
        } else if (error.message.includes("NotReadableError")) {
          errorMessage = t("qrScanner.errors.cameraInUse");
        } else {
          errorMessage = error.message;
        }
      }

      setScanError(errorMessage);
      setIsScanning(false);
      setIsCameraActive(false);
    }
  };

  // Stop camera
  const stopCamera = () => {
    if (qrScannerRef.current) {
      qrScannerRef.current.stop();
    }
    setIsCameraActive(false);
    setIsScanning(false);
  };

  // Handle QR scan result
  const handleQRScanResult = async (data: string) => {
    try {
      console.log("QR Code scanned:", data);
      console.log("QR Code length:", data.length);
      console.log("QR Code first 100 chars:", data.substring(0, 100));

      // Validate that data is not empty
      if (!data || data.trim() === "") {
        throw new Error(t("qrScanner.errors.emptyQrCode"));
      }

      // Parse FULL SECURE QR format only - no data truncation, maximum security
      let parsedData: QRData;

      // Use server-side API to parse QR data
      parsedData = qrSecurityAPI.parseQRData(data);

      if (!parsedData) {
        console.error("Failed to parse QR data");
        console.log("Raw QR data:", data);
        console.log("QR data length:", data.length);
        console.log("QR data type:", typeof data);

        // Provide more specific error messages based on the data
        if (data.startsWith("http")) {
          throw new Error(t("qrScanner.errors.urlQrCode"));
        } else if (data.includes("wifi") || data.includes("WIFI")) {
          throw new Error(t("qrScanner.errors.wifiQrCode"));
        } else if (data.length < 10) {
          throw new Error(t("qrScanner.errors.qrCodeTooShort"));
        } else if (data.length > 2000) {
          throw new Error(t("qrScanner.errors.qrCodeTooLong"));
        } else {
          throw new Error(t("qrScanner.errors.invalidQrFormat"));
        }
      }

      console.log("Successfully parsed secure QR format");

      // Validate required fields
      if (!parsedData || typeof parsedData !== "object") {
        throw new Error(t("qrScanner.errors.invalidQrStructure"));
      }

      const requiredFields = [
        "room_id",
        "school_id",
        "block_id",
        "timestamp",
        "expires_at",
        "session_id",
        "nonce",
        "signature",
      ];
      const missingFields = requiredFields.filter(
        (field) => !parsedData[field as keyof QRData]
      );

      if (missingFields.length > 0) {
        throw new Error(t("qrScanner.errors.missingQrFields", { fields: missingFields.join(", ") }));
      }

      // Check if QR code has expired
      const now = new Date();
      const expiresAt = new Date(parsedData.expires_at);
      if (now > expiresAt) {
        throw new Error(t("qrScanner.errors.qrCodeExpired"));
      }

      // Enhanced cryptographic verification on SERVER (includes current QR check)
      console.log(
        `Starting server-side QR verification for room ${parsedData.room_id}, session ${parsedData.session_id}`
      );

      if (!user?.id) {
        throw new Error(t("qrScanner.errors.userAuthRequired"));
      }

      const verification = await verifyCurrentQRCode(parsedData, user.id);
      if (!verification.isValid) {
        console.error(`Server QR verification failed: ${verification.error}`);

        // Provide more helpful error messages for specific server errors
        const errorLower = verification.error?.toLowerCase() || "";

        if (errorLower.includes("replaced") || errorLower.includes("newer")) {
          throw new Error(t("qrScanner.errors.qrCodeReplaced"));
        } else if (errorLower.includes("too old")) {
          throw new Error(t("qrScanner.errors.qrCodeTooOld"));
        } else if (errorLower.includes("screenshot attack") || errorLower.includes("challenge verification failed")) {
          throw new Error(t("qrScanner.errors.possibleScreenshotAttack"));
        } else if (errorLower.includes("timestamp is in the future")) {
          throw new Error(t("qrScanner.errors.qrCodeFutureTimestamp"));
        }

        throw new Error(verification.error || t("qrScanner.errors.invalidQrFormat"));
      }
      console.log(
        `Server-side QR verification successful for room ${parsedData.room_id}`
      );

      // Validate school
      if (parsedData.school_id !== profile?.school_id) {
        throw new Error(t("qrScanner.errors.differentSchool"));
      }

      // Replay attack prevention is now handled server-side during verification
      // No additional client-side checks needed

      setQrData(parsedData);
      stopCamera();

      // Validate room assignment
      await validateStudentRoomAssignment(parsedData);
    } catch (error) {
      console.error("QR scan error:", error);
      const errorMessage =
        error instanceof Error ? error.message : t("qrScanner.errors.invalidQrFormat");
      setScanError(errorMessage);
      sonnerToast.error(t("qrScanner.toasts.scanError"), {
        description: errorMessage,
      });
    }
  };

  // Validate if student is assigned to this room
  const validateStudentRoomAssignment = async (qrData: QRData) => {
    try {
      // Get room and block information
      const { data: roomData, error: roomError } = await supabase
        .from("rooms")
        .select(
          `
          id,
          name,
          block_id,
          blocks (
            id,
            name
          )
        `
        )
        .eq("id", qrData.room_id)
        .single();

      if (roomError || !roomData) {
        throw new Error(t("qrScanner.errors.roomNotFound"));
      }

      // Use security utility for room validation
      const validation = validateRoomAssignment(
        qrData,
        profile?.room_id || null,
        profile?.block_id || null
      );

      if (validation.level === "perfect") {
        setRoomValidation({
          isValid: true,
          message: t("qrScanner.roomValidation.perfectMatch", { roomName: roomData.name }),
          roomName: roomData.name,
          blockName: roomData.blocks?.name,
        });
        await handleVerificationFlow();
      } else if (validation.level === "warning") {
        setRoomValidation({
          isValid: true,
          message: t("qrScanner.roomValidation.blockMatch", { blockName: roomData.blocks?.name }),
          roomName: roomData.name,
          blockName: roomData.blocks?.name,
        });
        await handleVerificationFlow();
      } else {
        setRoomValidation({
          isValid: false,
          message: t("qrScanner.roomValidation.wrongRoom", {
            blockName: roomData.blocks?.name,
            roomName: roomData.name
          }),
          roomName: roomData.name,
          blockName: roomData.blocks?.name,
        });
      }
    } catch (error) {
      console.error("Room validation error:", error);
      setScanError(t("qrScanner.errors.roomValidationFailed"));
    }
  };

  // Handle verification flow based on school settings
  const handleVerificationFlow = async () => {
    try {
      setIsLoadingVerificationSettings(true);

      // Fetch verification method settings
      const settings = await getVerificationMethodSettings();
      setVerificationMethodSettings(settings);

      const requirement = settings.verification_method_requirement;

      switch (requirement) {
        case 'biometric_only':
          // Only biometric authentication allowed - start immediately
          await handleVerificationMethod('biometric');
          break;

        case 'pin_only':
          // Only PIN verification allowed - show PIN input directly
          setVerificationType('pin');
          setShowVerification(true);
          break;

        case 'both_required':
          // Both biometric AND PIN required - start with biometric first
          await handleVerificationMethod('biometric');
          break;

        case 'either':
        default:
          // Student can choose between biometric or PIN
          setShowVerification(true);
          break;
      }
    } catch (error) {
      console.error("Error in verification flow:", error);
      // Fallback to choice between methods
      setShowVerification(true);
    } finally {
      setIsLoadingVerificationSettings(false);
    }
  };

  // Handle verification method selection
  const handleVerificationMethod = async (method: "biometric" | "pin") => {
    setVerificationType(method);

    if (method === "biometric") {
      // Check if WebAuthn is available
      if (!isWebAuthnAvailable()) {
        sonnerToast.error(t("qrScanner.toasts.biometricNotSupported"));
        return;
      }

      // Check if user has registered biometrics
      if (user) {
        const hasPasskey = await isPasskeyAvailable(user.id);
        if (!hasPasskey) {
          sonnerToast.error(t("qrScanner.toasts.noBiometricCredentials"));
          return;
        }
      }

      // Start biometric authentication immediately
      await startBiometricAuthentication();
      return;
    }

    // For PIN verification, just show the PIN input
    // The actual verification happens in handlePinSubmit
  };

  // Start biometric authentication directly
  const startBiometricAuthentication = async () => {
    if (!user) return;

    setIsSubmitting(true);

    try {
      // Check if device can perform biometric authentication (more lenient for auth)
      const canAuth = await canPerformBiometricAuth(user.id);
      if (!canAuth) {
        sonnerToast.error(t("biometrics.biometricOnlyRequired"), {
          description: t("biometrics.biometricOnlyNotSupported"),
        });
        setIsSubmitting(false);
        return;
      }



      // Import the startAuthentication function
      const { startAuthentication } = await import("@/lib/webauthn");

      // Start authentication with biometric-only enforcement
      await startAuthentication(user.id);

      // If successful, check if PIN is also required
      sonnerToast.success(t("qrScanner.toasts.biometricAuthSuccess"), {
        description: t("qrScanner.toasts.biometricAuthSuccessDescription"),
      });

      // Check if both biometric and PIN are required
      if (verificationMethodSettings?.verification_method_requirement === 'both_required') {
        // Biometric completed, now require PIN
        setBiometricCompleted(true);
        setVerificationType('pin');
        sonnerToast.info(t("qrScanner.toasts.pinAlsoRequired"), {
          description: t("qrScanner.toasts.pinAlsoRequiredDescription"),
        });
      } else {
        // Only biometric was required, proceed with attendance
        await processAttendance();
      }
    } catch (error) {
      console.error("Biometric authentication failed:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : t("qrScanner.toasts.biometricAuthFailed");

      sonnerToast.error(t("qrScanner.toasts.biometricAuthFailed"), {
        description: errorMessage,
      });

      // Check verification method requirements before allowing fallback
      const requirement = verificationMethodSettings?.verification_method_requirement;

      if (requirement === 'biometric_only') {
        // Biometric only - no fallback allowed, block attendance completely
        setVerificationType(null); // Clear verification type
        setBiometricBlocked(true);
        sonnerToast.error(t("qrScanner.toasts.biometricRequiredBlocked"), {
          description: t("qrScanner.toasts.biometricRequiredBlockedDescription"),
        });
      } else if (requirement === 'both_required') {
        // Both required - biometric must succeed before PIN, block attendance
        setVerificationType(null); // Clear verification type
        setBiometricBlocked(true);
        sonnerToast.error(t("qrScanner.toasts.biometricRequiredForBoth"), {
          description: t("qrScanner.toasts.biometricRequiredForBothDescription"),
        });
      } else {
        // Either method allowed - fallback to PIN is permitted
        setVerificationType("pin");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle PIN submission
  const handlePinSubmit = async () => {
    if (!pin.trim()) {
      setPinError(t("qrScanner.validation.enterPin"));
      return;
    }

    if (!profile?.pin) {
      setPinError(t("qrScanner.validation.noPinSet"));
      return;
    }

    if (pin !== profile.pin) {
      setPinError(t("qrScanner.validation.incorrectPin"));
      setPin("");
      return;
    }

    setPinError("");

    // If both biometric and PIN were required, update verification type to reflect both
    if (biometricCompleted && verificationMethodSettings?.verification_method_requirement === 'both_required') {
      setVerificationType('both');
    }

    await processAttendance();
  };

  // Enhanced process attendance recording with all security features
  const processAttendance = async () => {
    if (!qrData || !user) return;

    setIsSubmitting(true);

    try {
      // Enhanced security checks
      await performSecurityChecks();

      // Enhanced location verification
      await performEnhancedLocationVerification();

      // Check if already present today
      const today = new Date().toISOString().split("T")[0];
      const { data: existingAttendance, error: checkError } = await supabase
        .from("attendance_records")
        .select("*")
        .eq("student_id", user.id)
        .eq("room_id", qrData.room_id)
        .gte("timestamp", `${today}T00:00:00.000Z`)
        .lt("timestamp", `${today}T23:59:59.999Z`);

      if (checkError) {
        throw checkError;
      }

      if (existingAttendance && existingAttendance.length > 0) {
        setIsAlreadyPresent(true);
        setSuccess(true);
        sonnerToast.info(t("qrScanner.toasts.alreadyPresent"), {
          description: t("qrScanner.toasts.alreadyPresentDescription", {
            time: new Date(existingAttendance[0].timestamp).toLocaleTimeString()
          }),
        });
        return;
      }

      // Record new attendance with enhanced data
      await recordEnhancedAttendance();
    } catch (error) {
      console.error("Error processing attendance:", error);
      sonnerToast.error(t("qrScanner.toasts.error"), {
        description: error instanceof Error ? error.message : t("qrScanner.toasts.errorDescription"),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Enhanced security checks
  const performSecurityChecks = async () => {
    if (!user || !profile) return;

    try {
      console.log("Performing enhanced security checks...");

      // Get or use existing device fingerprint
      const enhancedDeviceInfo = deviceInfo || await getDeviceFingerprint();
      if (!deviceInfo) {
        setDeviceInfo(enhancedDeviceInfo);
      }

      // Essential security checks only (no device detection)
      const [
        rateLimited,
        concurrentSessions,
      ] = await Promise.all([
        hasReachedRateLimit(user.id),
        checkConcurrentSessions(user.id),
      ]);

      // Update security state (simplified)
      setSecurityChecks({
        rateLimited,
        concurrentSessions,
        locationSpoofed: false, // Will be checked in location verification
      });

      // Handle rate limiting (blocking)
      if (rateLimited) {
        throw new Error(t("qrScanner.errors.rateLimited"));
      }

      // Handle concurrent sessions (send alert to teacher)
      if (concurrentSessions) {
        console.log("Concurrent sessions detected - sending alert to teacher");
        await createSecurityAlert("concurrent_sessions", t("qrScanner.securityAlerts.multipleActiveSessions"),
          t("qrScanner.securityAlerts.multipleActiveSessionsDescription"), user.id);
      }

      console.log("Security checks completed:", { rateLimited, concurrentSessions });
    } catch (error) {
      console.error("Security check failed:", error);
      throw error;
    }
  };

  // Enhanced location verification with hierarchical checking
  const performEnhancedLocationVerification = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        console.warn("Geolocation not supported");
        resolve();
        return;
      }

      setIsCheckingLocation(true);
      setLocationError("");

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            console.log("Location obtained:", {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy,
            });

            // Log location accuracy for debugging (no user warning needed for indoor scanning)
            if (position.coords.accuracy > 100) {
              console.log(`Location accuracy: ${position.coords.accuracy}m - acceptable for indoor scanning`);
            }

            // Check for location spoofing
            const locationSpoofed = await isLocationSpoofed(
              position.coords.latitude,
              position.coords.longitude
            );

            if (locationSpoofed) {
              setSecurityChecks(prev => ({ ...prev, locationSpoofed: true }));
              console.log("Location spoofing detected - sending alert to teacher");
              await createSecurityAlert("location_spoofing", t("qrScanner.securityAlerts.suspiciousLocationData"),
                t("qrScanner.securityAlerts.suspiciousLocationDataDescription"), user.id);
              // Continue without blocking attendance since indoor GPS can be unreliable
            }

            // Create GeoJSON Point
            const geoJsonPoint: GeoJSONPoint = {
              type: "Point",
              coordinates: [position.coords.longitude, position.coords.latitude],
            };

            // Enhanced location verification with room/block fallback
            await verifyLocationHierarchy(position, geoJsonPoint);

            setIsCheckingLocation(false);
            resolve();
          } catch (error) {
            setIsCheckingLocation(false);
            setLocationError(error instanceof Error ? error.message : t("qrScanner.errors.locationGenericError"));
            reject(error);
          }
        },
        async (error) => {
          setIsCheckingLocation(false);
          console.warn("Location error:", error);

          // Check if location verification is enabled for this room
          let shouldBlockAttendance = false;
          try {
            const settings = await getLocationVerificationSettings(qrData?.room_id || '');
            shouldBlockAttendance = settings.globalEnabled && (settings.roomEnabled || settings.blockEnabled);
          } catch (settingsError) {
            console.error("Error checking location settings:", settingsError);
            // If we can't check settings, assume location verification is enabled for security
            shouldBlockAttendance = true;
          }

          let errorMessage = "";
          switch (error.code) {
            case error.TIMEOUT:
              errorMessage = t("qrScanner.errors.locationTimeout");
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = t("qrScanner.errors.locationUnavailable");
              break;
            case error.PERMISSION_DENIED:
              errorMessage = t("qrScanner.errors.locationPermissionDenied");
              if (shouldBlockAttendance) {
                setLocationPermissionBlocked(true);
              }
              showLocationInstructions();
              break;
            default:
              errorMessage = t("qrScanner.errors.locationGenericError");
          }

          setLocationError(errorMessage);

          // If location verification is enabled, block attendance
          if (shouldBlockAttendance) {
            console.log("Location verification is enabled - blocking attendance due to location error");
            reject(new Error(errorMessage));
          } else {
            console.log("Location verification is disabled - allowing attendance despite location error");
            resolve();
          }
        },
        {
          enableHighAccuracy: true,
          timeout: 30000,
          maximumAge: 0,
        }
      );
    });
  };

  // Verify location with room/block hierarchy
  const verifyLocationHierarchy = async (position: GeolocationPosition, geoJsonPoint: GeoJSONPoint) => {
    if (!qrData) return;

    let distance = 0;
    let isWithinRadius = true;
    let roomOrBlockLocation = null;

    try {
      // Get location verification settings first
      const settings = await getLocationVerificationSettings(qrData.room_id);

      // If location verification is disabled globally, skip all verification
      if (!settings.globalEnabled) {
        console.log("Location verification disabled globally - skipping verification");
        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          geoJsonPoint,
          isWithinRadius: true, // Always true when verification is disabled
          distance: 0,
        });
        return;
      }
      // First try to get room location (only if room-level verification is enabled)
      if (settings.roomEnabled) {
        const { data: roomLocationData, error: roomLocationError } = await supabase
          .from("room_locations")
          .select("latitude, longitude, radius_meters")
          .eq("room_id", qrData.room_id)
          .maybeSingle();

        if (roomLocationError && roomLocationError.code !== "PGRST116") {
          console.error("Error fetching room location:", roomLocationError);
        }

        roomOrBlockLocation = roomLocationData;
      } else {
        console.log("Room-level verification disabled - skipping room location check");
      }

      // If no room location, try block location (only if block-level verification is enabled)
      if (!roomOrBlockLocation && settings.blockEnabled) {
        console.log("No room location found, checking block location...");

        const { data: roomData, error: roomError } = await supabase
          .from("rooms")
          .select("block_id")
          .eq("id", qrData.room_id)
          .single();

        if (roomError) {
          console.error("Error fetching room block:", roomError);
        } else if (roomData?.block_id) {
          const { data: blockLocationData, error: blockLocationError } = await supabase
            .from("block_locations")
            .select("latitude, longitude, radius_meters")
            .eq("block_id", roomData.block_id)
            .maybeSingle();

          if (blockLocationError && blockLocationError.code !== "PGRST116") {
            console.error("Error fetching block location:", blockLocationError);
          }

          roomOrBlockLocation = blockLocationData;
        }
      } else if (!roomOrBlockLocation) {
        console.log("Block-level verification disabled - skipping block location check");
      }

      // If no location verification is configured or enabled, skip verification
      if (!roomOrBlockLocation) {
        console.log("No location verification configured or all verification levels disabled - allowing attendance");
        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          geoJsonPoint,
          isWithinRadius: true, // Always true when no verification is configured
          distance: 0,
        });
        return;
      }

      // If we have a location to check against
      if (roomOrBlockLocation) {
        distance = calculateDistance(
          position.coords.latitude,
          position.coords.longitude,
          roomOrBlockLocation.latitude,
          roomOrBlockLocation.longitude
        );

        console.log(`Distance from room/block: ${Math.round(distance)} meters (allowed radius: ${roomOrBlockLocation.radius_meters} meters)`);

        isWithinRadius = distance <= roomOrBlockLocation.radius_meters;

        // Handle location violations
        if (!isWithinRadius) {
          // Create location alert for teacher
          try {
            await createLocationAlert(
              user.id,
              position.coords.latitude,
              position.coords.longitude,
              roomOrBlockLocation.latitude,
              roomOrBlockLocation.longitude,
              distance,
              smartRoomData?.name || t("qrScanner.unknownRoom"),
              roomOrBlockLocation.radius_meters,
              qrData?.room_id // Pass the room_id from QR data
            );
          } catch (alertError) {
            console.error("Error creating location alert:", alertError);
          }

          // Calculate how far outside the radius the student is
          const excessDistance = Math.round(distance - roomOrBlockLocation.radius_meters);
          const distanceDisplay = distance >= 1000
            ? `${(distance / 1000).toFixed(1)}km`
            : `${Math.round(distance)}m`;

          // If too far away, block attendance
          if (distance > roomOrBlockLocation.radius_meters * 2) {
            throw new Error(t("qrScanner.errors.locationTooFar", {
              distance: distanceDisplay,
              excessDistance,
              allowedRadius: roomOrBlockLocation.radius_meters
            }));
          } else {
            // Close but not quite in range - warn but allow
            sonnerToast.warning(t("qrScanner.toasts.locationWarning"), {
              description: t("qrScanner.toasts.locationWarningDescription", {
                distance: distanceDisplay,
                excessDistance,
                allowedRadius: roomOrBlockLocation.radius_meters
              }),
            });
          }
        }
      }

      // Update location state with enhanced data
      setLocation({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        geoJsonPoint,
        isWithinRadius,
        distance,
      });

    } catch (error) {
      console.error("Location verification error:", error);
      throw error;
    }
  };

  // Enhanced attendance recording with all features
  const recordEnhancedAttendance = async () => {
    if (!qrData || !user) return;

    try {
      const enhancedDeviceInfo = deviceInfo || await getDeviceFingerprint();
      const now = new Date().toISOString();

      const attendanceRecord = {
        student_id: user.id,
        room_id: qrData.room_id,
        qr_session_id: qrData.session_id,
        timestamp: now,
        verification_method: verificationType || "pin",
        location: location?.geoJsonPoint || (location
          ? {
              type: "Point",
              coordinates: [location.longitude, location.latitude],
              accuracy: location.accuracy,
            }
          : null),
        device_info: JSON.stringify({
          user_agent: navigator.userAgent,
          fingerprint: enhancedDeviceInfo,
          screen_resolution: `${screen.width}x${screen.height}`,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          language: navigator.language,
          security_checks: securityChecks,
        }),
        status: "present",
        school_id: profile?.school_id,
        block_id: qrData.block_id,
        created_at: now,
      };

      const { data: insertData, error } = await supabase
        .from("attendance_records")
        .insert(attendanceRecord)
        .select()
        .single();

      if (error) {
        throw error;
      }

      console.log("Successfully recorded enhanced attendance:", insertData);

      // Create enhanced notification
      await createEnhancedNotification(insertData);

      setSuccess(true);
      sonnerToast.success(t("qrScanner.toasts.attendanceRecorded"), {
        description: t("qrScanner.toasts.attendanceRecordedDescription"),
      });
    } catch (error) {
      console.error("Enhanced attendance recording error:", error);
      throw error;
    }
  };

  // Create security alert for teachers
  const createSecurityAlert = async (alertType: string, title: string, message: string, studentId: string) => {
    try {
      // Get student info to find their teacher
      const { data: studentProfile, error: studentError } = await supabase
        .from("profiles")
        .select("name, room_id, school_id")
        .eq("user_id", studentId)
        .single();

      if (studentError) {
        console.error("Error getting student profile for alert:", studentError);
        return;
      }

      // Find the teacher for this student's room
      let teacherId = null;
      if (studentProfile.room_id) {
        const { data: roomData, error: roomError } = await supabase
          .from("rooms")
          .select("teacher_id")
          .eq("id", studentProfile.room_id)
          .single();

        if (!roomError && roomData?.teacher_id) {
          teacherId = roomData.teacher_id;
        }
      }

      // If no specific teacher found, try to find any teacher in the same school
      if (!teacherId && studentProfile.school_id) {
        const { data: schoolTeachers, error: teacherError } = await supabase
          .from("profiles")
          .select("user_id")
          .eq("role", "teacher")
          .eq("school_id", studentProfile.school_id)
          .limit(1);

        if (!teacherError && schoolTeachers && schoolTeachers.length > 0) {
          teacherId = schoolTeachers[0].user_id;
        }
      }

      // Create the security alert for the teacher
      if (teacherId) {
        await supabase.from("notifications").insert({
          student_id: studentId,
          teacher_id: teacherId,
          title: t("qrScanner.securityAlerts.alertTitle", { title }),
          message: t("qrScanner.securityAlerts.alertMessage", {
            studentName: studentProfile.name,
            message
          }),
          type: "system_alert", // Use system_alert type for security alerts
          read: false,
          timestamp: new Date().toISOString(),
          metadata: JSON.stringify({
            alert_type: alertType,
            student_name: studentProfile.name,
            room_id: studentProfile.room_id,
            security_event: true,
            severity: "medium",
          }),
        });

        console.log(`Security alert sent to teacher for ${alertType}`);
      } else {
        console.warn("No teacher found to send security alert to");
      }
    } catch (error) {
      console.error("Error creating security alert:", error);
    }
  };

  // Create enhanced notification with metadata
  const createEnhancedNotification = async (attendanceRecord: any) => {
    if (!user || !profile) return;

    try {
      const roomName = smartRoomData?.name || roomValidation?.roomName || t("qrScanner.unknownRoom");

      await supabase.from("notifications").insert({
        student_id: user.id,
        title: t("qrScanner.notifications.attendanceRecorded"),
        message: t("qrScanner.notifications.attendanceRecordedMessage", {
          roomName,
          time: new Date().toLocaleTimeString()
        }),
        type: "attendance",
        read: false,
        timestamp: new Date().toISOString(),
        metadata: JSON.stringify({
          room_name: roomName,
          room_id: qrData?.room_id,
          verification_method: verificationType || "pin",
          distance_meters: location?.distance ? Math.round(location.distance) : null,
          within_radius: location?.isWithinRadius ?? true,
          location_accuracy: location?.accuracy,
          security_flags: {
            concurrent_sessions: securityChecks.concurrentSessions,
            location_spoofed: securityChecks.locationSpoofed,
          },
          attendance_id: attendanceRecord.id,
        }),
      });

      console.log("Enhanced notification created successfully");
    } catch (error) {
      console.error("Error creating enhanced notification:", error);
    }
  };

  // Reset scanner with enhanced state cleanup
  const resetScanner = () => {
    setQrData(null);
    setShowVerification(false);
    setVerificationType(null);
    setPin("");
    setPinError("");
    setSuccess(false);
    setIsAlreadyPresent(false);
    setRoomValidation(null);
    setScanError("");
    setLocation(null);
    setLocationError("");
    setIsCheckingLocation(false);
    setLocationPermissionBlocked(false);
    setBiometricBlocked(false);
    setBiometricCompleted(false);
    setVerificationMethodSettings(null);
    setIsLoadingVerificationSettings(false);

    // Reset security checks but keep device info
    setSecurityChecks({
      rateLimited: false,
      concurrentSessions: false,
      locationSpoofed: false,
    });

    // Don't reset permission state, device info, or smart room data as they should persist
  };

  // Render success state
  if (success) {
    return (
      <Card className="w-full max-w-md mx-auto mb-8">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <Check className="w-8 h-8 text-green-600" />
          </div>
          <CardTitle className="text-green-600">
            {isAlreadyPresent ? t("qrScanner.success.alreadyPresent") : t("qrScanner.success.attendanceRecorded")}
          </CardTitle>
          <CardDescription>
            {isAlreadyPresent
              ? t("qrScanner.success.alreadyPresentDescription")
              : t("qrScanner.success.attendanceRecordedDescription")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={resetScanner} className="w-full">
            {t("qrScanner.success.scanAnotherCode")}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto mb-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          {t("qrScanner.scanQrCode")}
        </CardTitle>
        <CardDescription>
          {t("qrScanner.scanQrDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Enhanced Status Information */}
        {permissionState === "unknown" && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center gap-2">
              <Camera className="w-4 h-4 text-blue-600" />
              <p className="text-sm text-blue-700">
                {t("qrScanner.checkingCameraPermissions")}
              </p>
            </div>
          </div>
        )}

        {/* Location Status */}
        {isCheckingLocation && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-blue-600 animate-pulse" />
              <p className="text-sm text-blue-700">
                {t("qrScanner.verifyingLocation")}
              </p>
            </div>
          </div>
        )}

        {/* Location Error */}
        {locationError && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-yellow-600" />
              <p className="text-sm text-yellow-700">{locationError}</p>
            </div>
          </div>
        )}

        {/* Security Warnings - Only show location spoofing (no device or concurrent session warnings) */}
        {securityChecks.locationSpoofed && (
          <div className="p-3 bg-orange-50 border border-orange-200 rounded-md">
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-orange-600" />
              <div className="text-sm text-orange-700">
                <p className="font-medium">{t("qrScanner.securityNotice.title")}</p>
                <p>• {t("qrScanner.securityNotice.unusualLocationData")}</p>
                <p className="mt-1 text-xs">{t("qrScanner.securityNotice.teacherNotified")}</p>
              </div>
            </div>
          </div>
        )}

        {/* Smart Room Info */}
        {smartRoomData && !showVerification && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-green-600" />
              <p className="text-sm text-green-700">
                {t("qrScanner.readyToScanFor")} <span className="font-medium">{smartRoomData.name}</span>
              </p>
            </div>
          </div>
        )}

        {permissionState === "granted" &&
          !isCameraActive &&
          !showVerification && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4 text-green-600" />
                <p className="text-sm text-green-700">
                  {t("qrScanner.cameraAccessGranted")}
                </p>
              </div>
            </div>
          )}

        {/* Camera View */}
        {isCameraActive && (
          <div className="relative">
            <video
              ref={videoRef}
              className="w-full h-64 object-cover rounded-lg border"
              playsInline
              muted
            />
            <Button
              onClick={stopCamera}
              variant="outline"
              size="sm"
              className="absolute top-2 right-2"
            >
              <X className="w-4 h-4" />
            </Button>
            <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
              {t("qrScanner.pointCameraAtQr")}
            </div>
          </div>
        )}

        {/* Scan Error */}
        {scanError && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <p className="text-sm text-red-700">{scanError}</p>
            </div>
          </div>
        )}

        {/* Room Validation */}
        {roomValidation && !roomValidation.isValid && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-yellow-600" />
              <p className="text-sm text-yellow-700">
                {roomValidation.message}
              </p>
            </div>
          </div>
        )}

        {/* Biometric Authentication Required */}
        {biometricBlocked && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2 mb-2">
              <Fingerprint className="w-5 h-5 text-red-600" />
              <h4 className="font-medium text-red-800">
                {t("qrScanner.biometricAuthenticationRequired")}
              </h4>
            </div>
            <p className="text-sm text-red-700 mb-3">
              {verificationMethodSettings?.verification_method_requirement === 'biometric_only'
                ? t("qrScanner.biometricOnlyRequiredDescription")
                : t("qrScanner.biometricRequiredForBothDescription")
              }
            </p>
            <div className="space-y-2">
              <Button
                onClick={async () => {
                  setBiometricBlocked(false);
                  // Retry biometric authentication
                  await handleVerificationMethod('biometric');
                }}
                className="w-full"
                variant="outline"
              >
                <Fingerprint className="w-4 h-4 mr-2" />
                {t("qrScanner.retryBiometric")}
              </Button>
              <Button
                onClick={resetScanner}
                className="w-full"
                variant="ghost"
              >
                {t("qrScanner.tryAgain")}
              </Button>
            </div>
          </div>
        )}

        {/* Location Permission Required */}
        {locationPermissionBlocked && !biometricBlocked && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2 mb-2">
              <MapPin className="w-5 h-5 text-red-600" />
              <h4 className="font-medium text-red-800">
                {t("qrScanner.locationPermissionRequired")}
              </h4>
            </div>
            <p className="text-sm text-red-700 mb-3">
              {t("qrScanner.locationPermissionRequiredDescription")}
            </p>
            <div className="space-y-2">
              <Button
                onClick={async () => {
                  try {
                    // Request location permission from browser
                    const position = await new Promise<GeolocationPosition>((resolve, reject) => {
                      navigator.geolocation.getCurrentPosition(
                        resolve,
                        reject,
                        {
                          enableHighAccuracy: true,
                          timeout: 10000,
                          maximumAge: 0
                        }
                      );
                    });

                    // If permission granted successfully, clear blocked state and retry
                    setLocationPermissionBlocked(false);
                    setLocationError("");

                    // Show success message
                    sonnerToast.success(t("qrScanner.toasts.locationPermissionGranted"), {
                      description: t("qrScanner.toasts.locationPermissionGrantedDescription"),
                    });

                    // Retry the verification flow
                    if (qrData) {
                      handleVerificationFlow();
                    }
                  } catch (error: any) {
                    // Permission still denied or other error
                    console.error("Location permission request failed:", error);

                    if (error.code === error.PERMISSION_DENIED) {
                      sonnerToast.error(t("qrScanner.toasts.locationPermissionStillDenied"), {
                        description: t("qrScanner.toasts.locationPermissionStillDeniedDescription"),
                      });
                    } else {
                      sonnerToast.error(t("qrScanner.toasts.locationError"), {
                        description: t("qrScanner.toasts.locationErrorDescription"),
                      });
                    }
                  }
                }}
                className="w-full"
                variant="outline"
              >
                <MapPin className="w-4 h-4 mr-2" />
                {t("qrScanner.enableLocation")}
              </Button>
              <Button
                onClick={resetScanner}
                className="w-full"
                variant="ghost"
              >
                {t("qrScanner.tryAgain")}
              </Button>
            </div>
          </div>
        )}

        {/* Camera Permission Request */}
        {permissionState === "denied" && !isCameraActive && !locationPermissionBlocked && !biometricBlocked && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <h4 className="font-medium text-red-800">
                {t("qrScanner.cameraPermissionRequired")}
              </h4>
            </div>
            <p className="text-sm text-red-700 mb-3">
              {t("qrScanner.cameraPermissionDescription")}
            </p>
            <Button
              onClick={async () => {
                try {
                  const permissionGranted = await requestCameraPermissions();

                  if (permissionGranted) {
                    // Show success message
                    sonnerToast.success(t("qrScanner.toasts.cameraPermissionGranted"), {
                      description: t("qrScanner.toasts.cameraPermissionGrantedDescription"),
                    });

                    // Automatically start camera
                    setTimeout(() => {
                      startCamera();
                    }, 500);
                  } else {
                    // Permission denied
                    sonnerToast.error(t("qrScanner.toasts.cameraPermissionStillDenied"), {
                      description: t("qrScanner.toasts.cameraPermissionStillDeniedDescription"),
                    });
                  }
                } catch (error) {
                  console.error("Camera permission request failed:", error);
                  sonnerToast.error(t("qrScanner.toasts.cameraError"), {
                    description: t("qrScanner.toasts.cameraErrorDescription"),
                  });
                }
              }}
              className="w-full"
              disabled={permissionState === "requesting"}
            >
              {permissionState === "requesting" ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t("qrScanner.requestingCameraPermission")}
                </>
              ) : (
                <>
                  <Camera className="w-4 h-4 mr-2" />
                  {t("qrScanner.allowCameraAccess")}
                </>
              )}
            </Button>
          </div>
        )}

        {/* Enhanced Start Scanning Section */}
        {!isCameraActive &&
          !showVerification &&
          permissionState !== "denied" &&
          !locationPermissionBlocked &&
          !biometricBlocked && (
            <motion.div
              className="flex flex-col items-center justify-center p-8 space-y-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                animate={{
                  scale: [1, 1.05, 1],
                  transition: { duration: 2, repeat: Infinity },
                }}
                className="relative"
              >
                <SimpleCameraIcon size={72} />
                <motion.div
                  className="absolute inset-0 rounded-full"
                  animate={{
                    boxShadow: [
                      "0 0 0 0 rgba(var(--primary), 0)",
                      "0 0 0 10px rgba(var(--primary), 0.2)",
                      "0 0 0 0 rgba(var(--primary), 0)",
                    ],
                    transition: { duration: 2, repeat: Infinity },
                  }}
                />
              </motion.div>

              <p className="text-center">{t("qrScanner.scanQrDescription")}</p>

              <Button
                onClick={startCamera}
                className="w-full animate-pulse"
                disabled={isScanning || permissionState === "requesting"}
              >
                {isScanning ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {t("qrScanner.startingCamera")}
                  </>
                ) : permissionState === "requesting" ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {t("qrScanner.requestingPermission")}
                  </>
                ) : (
                  <>
                    <Camera className="w-4 h-4 mr-2" />
                    {t("qrScanner.startScan")}
                  </>
                )}
              </Button>
            </motion.div>
          )}

        {/* Verification Methods */}
        {showVerification && !verificationType && roomValidation?.isValid && !locationPermissionBlocked && !biometricBlocked && (
          <div className="space-y-3">
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-700">{roomValidation.message}</p>
            </div>

            {/* Loading state while fetching verification settings */}
            {isLoadingVerificationSettings && (
              <div className="flex flex-col items-center justify-center p-8 space-y-4">
                <Loader2 className="w-8 h-8 animate-spin" />
                <p className="text-sm text-muted-foreground">{t("qrScanner.loadingVerificationSettings")}</p>
              </div>
            )}

            {/* Dynamic verification method display based on school settings */}
            {!isLoadingVerificationSettings && verificationMethodSettings?.verification_method_requirement === 'either' && (
              <>
                <p className="text-sm font-medium">{t("qrScanner.chooseVerificationMethod")}</p>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    onClick={() => handleVerificationMethod("biometric")}
                    variant="outline"
                    className="flex flex-col items-center p-4 h-auto"
                    disabled={isSubmitting && verificationType === "biometric"}
                  >
                    {isSubmitting && verificationType === "biometric" ? (
                      <>
                        <Loader2 className="w-6 h-6 mb-1 animate-spin" />
                        <span className="text-xs">{t("qrScanner.authenticating")}</span>
                      </>
                    ) : (
                      <>
                        <Fingerprint className="w-6 h-6 mb-1" />
                        <span className="text-xs">{t("qrScanner.biometric")}</span>
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={() => handleVerificationMethod("pin")}
                    variant="outline"
                    className="flex flex-col items-center p-4 h-auto"
                    disabled={isSubmitting}
                  >
                    <KeyRound className="w-6 h-6 mb-1" />
                    <span className="text-xs">{t("qrScanner.pin")}</span>
                  </Button>
                </div>
              </>
            )}

            {/* Single method display for biometric_only */}
            {!isLoadingVerificationSettings && verificationMethodSettings?.verification_method_requirement === 'biometric_only' && (
              <>
                <p className="text-sm font-medium">{t("qrScanner.biometricRequired")}</p>
                <Button
                  onClick={() => handleVerificationMethod("biometric")}
                  className="w-full flex flex-col items-center p-4 h-auto"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-6 h-6 mb-1 animate-spin" />
                      <span className="text-xs">{t("qrScanner.authenticating")}</span>
                    </>
                  ) : (
                    <>
                      <Fingerprint className="w-6 h-6 mb-1" />
                      <span className="text-xs">{t("qrScanner.biometric")}</span>
                    </>
                  )}
                </Button>
              </>
            )}

            {/* Single method display for pin_only */}
            {!isLoadingVerificationSettings && verificationMethodSettings?.verification_method_requirement === 'pin_only' && (
              <>
                <p className="text-sm font-medium">{t("qrScanner.pinRequired")}</p>
                <Button
                  onClick={() => handleVerificationMethod("pin")}
                  className="w-full flex flex-col items-center p-4 h-auto"
                  disabled={isSubmitting}
                >
                  <KeyRound className="w-6 h-6 mb-1" />
                  <span className="text-xs">{t("qrScanner.pin")}</span>
                </Button>
              </>
            )}

            {/* Both methods required display */}
            {!isLoadingVerificationSettings && verificationMethodSettings?.verification_method_requirement === 'both_required' && (
              <>
                <p className="text-sm font-medium">{t("qrScanner.bothMethodsRequired")}</p>
                <p className="text-xs text-muted-foreground">{t("qrScanner.bothMethodsDescription")}</p>
                <Button
                  onClick={() => handleVerificationMethod("biometric")}
                  className="w-full flex flex-col items-center p-4 h-auto"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-6 h-6 mb-1 animate-spin" />
                      <span className="text-xs">{t("qrScanner.authenticating")}</span>
                    </>
                  ) : (
                    <>
                      <div className="flex items-center gap-2 mb-1">
                        <Fingerprint className="w-5 h-5" />
                        <span className="text-xs">+</span>
                        <KeyRound className="w-5 h-5" />
                      </div>
                      <span className="text-xs">{t("qrScanner.startWithBiometric")}</span>
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        )}

        {/* PIN Input */}
        {verificationType === "pin" &&
         !locationPermissionBlocked &&
         !biometricBlocked &&
         (
           // Only show PIN input when it's actually allowed by the verification method settings
           verificationMethodSettings?.verification_method_requirement === 'pin_only' ||
           verificationMethodSettings?.verification_method_requirement === 'either' ||
           (verificationMethodSettings?.verification_method_requirement === 'both_required' && biometricCompleted)
         ) && (
          <div className="space-y-3">
            {/* Show different messages based on verification flow */}
            {biometricCompleted && verificationMethodSettings?.verification_method_requirement === 'both_required' && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-700">
                  <span className="flex items-center gap-1">
                    <Fingerprint className="w-4 h-4" />
                    {t("qrScanner.biometricCompleted")}
                  </span>
                </p>
                <p className="text-xs text-blue-600 mt-1">{t("qrScanner.nowEnterPin")}</p>
              </div>
            )}

            <div>
              <Input
                type="password"
                placeholder={t("qrScanner.enterYourPin")}
                value={pin}
                onChange={(e) => setPin(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handlePinSubmit()}
                className={pinError ? "border-red-500" : ""}
              />
              {pinError && (
                <p className="text-sm text-red-600 mt-1">{pinError}</p>
              )}
            </div>
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => {
                  setVerificationType(null);
                  setBiometricCompleted(false);
                }}
                variant="outline"
                disabled={verificationMethodSettings?.verification_method_requirement === 'pin_only'}
              >
                {t("qrScanner.back")}
              </Button>
              <Button onClick={handlePinSubmit} disabled={isSubmitting}>
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  biometricCompleted ? t("qrScanner.completeVerification") : t("qrScanner.verify")
                )}
              </Button>
            </div>
          </div>
        )}

        {/* Reset Button */}
        {(scanError || (roomValidation && !roomValidation.isValid)) && (
          <Button onClick={resetScanner} variant="outline" className="w-full">
            {t("qrScanner.tryAgain")}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
