-- First, disable <PERSON><PERSON> temporarily
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Create a basic policy that allows all authenticated users to perform all operations
CREATE POLICY "Allow all operations for authenticated users"
ON profiles
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Add a default admin user if it doesn't exist
INSERT INTO profiles (id, user_id, name, email, role)
SELECT 
  '6cfc05df-41d8-4a37-8115-7376d88f2462',
  '6cfc05df-41d8-4a37-8115-7376d88f2462',
  '<PERSON>',
  '<EMAIL>',
  'admin'
WHERE NOT EXISTS (
  SELECT 1 FROM profiles 
  WHERE user_id = '6cfc05df-41d8-4a37-8115-7376d88f2462'
); 