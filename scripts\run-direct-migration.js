// This script runs the migration directly using the Supabase API
// It doesn't rely on the execute_sql function

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runDirectMigration() {
  try {
    console.log('Starting direct migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../supabase/migrations/20240514000002_fix_notifications_direct.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the SQL directly using the database/query endpoint
    console.log('Executing SQL...');
    
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSql });
    
    if (error) {
      console.error('Error executing SQL:', error);
      
      // Try using the database/query endpoint directly
      console.log('Trying database/query endpoint...');
      
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseServiceKey,
          'Authorization': `Bearer ${supabaseServiceKey}`
        },
        body: JSON.stringify({ sql: migrationSql })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error from database/query endpoint:', errorData);
        
        // Try one more approach - split into smaller statements
        console.log('Trying to split into smaller statements...');
        
        const statements = migrationSql
          .split(';')
          .map(stmt => stmt.trim())
          .filter(stmt => stmt.length > 0);
        
        for (let i = 0; i < statements.length; i++) {
          console.log(`Executing statement ${i + 1} of ${statements.length}...`);
          
          try {
            const { data: stmtData, error: stmtError } = await supabase.rpc('exec_sql', { 
              sql: statements[i] + ';' 
            });
            
            if (stmtError) {
              console.error(`Error executing statement ${i + 1}:`, stmtError);
            } else {
              console.log(`Statement ${i + 1} executed successfully`);
            }
          } catch (stmtErr) {
            console.error(`Error executing statement ${i + 1}:`, stmtErr);
          }
        }
      } else {
        const responseData = await response.json();
        console.log('Database/query endpoint response:', responseData);
      }
    } else {
      console.log('SQL executed successfully:', data);
    }
    
    // Check the notifications table structure
    console.log('Checking notifications table structure...');
    
    const { data: tableInfo, error: tableError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'notifications')
      .eq('table_schema', 'public');
    
    if (tableError) {
      console.error('Error checking table structure:', tableError);
    } else {
      console.log('Notifications table columns:', tableInfo);
    }
    
    // Test creating a notification
    console.log('Testing notification creation...');
    
    // Get a student ID for testing
    const { data: students, error: studentError } = await supabase
      .from('profiles')
      .select('id')
      .eq('role', 'student')
      .limit(1);
    
    if (studentError) {
      console.error('Error fetching student for testing:', studentError);
    } else if (students && students.length > 0) {
      const studentId = students[0].id;
      
      // Create a test notification
      const { data: notification, error: notificationError } = await supabase
        .from('notifications')
        .insert({
          student_id: studentId,
          title: '🧪 Test Notification',
          message: 'This is a test notification to verify the migration worked correctly.',
          type: 'system',
          read: false
        })
        .select();
      
      if (notificationError) {
        console.error('Error creating test notification:', notificationError);
      } else {
        console.log('Test notification created successfully:', notification);
      }
    }
    
    console.log('Direct migration completed');
    
  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

// Execute the migration
runDirectMigration();
