# 🔐 HTTPS Setup for Biometric Authentication

## Why HTTPS is Required

WebAuthn (biometric authentication) requires a **secure context** to work. This means:
- ✅ HTTPS is required for production
- ✅ HTTPS is required for mobile testing (even locally)
- ✅ HTTP only works on `localhost` in desktop browsers

## 🚀 Quick Setup

### 1. Automatic Setup (Recommended)

Run the automated setup script:

```bash
npm run setup-https
```

This will:
- Install `mkcert` (if not already installed)
- Generate trusted SSL certificates
- Set up your development environment

### 2. Start HTTPS Development Server

```bash
npm run dev:https
```

### 3. Get Your Local IP Address

```bash
npm run get-ip
```

This will show you the URLs to use on your phone.

## 📱 Testing on Mobile

1. **Make sure your phone is on the same WiFi network**
2. **Get your local IP address** using `npm run get-ip`
3. **Open the HTTPS URL** on your phone (e.g., `https://*************:5173`)
4. **Accept the security warning** - the certificate is now trusted
5. **Test biometric authentication!**

## 🔧 Manual Setup (If Automatic Fails)

### Windows

#### Option 1: Chocolatey
```bash
choco install mkcert
mkcert -install
```

#### Option 2: Scoop
```bash
scoop install mkcert
mkcert -install
```

#### Option 3: Manual Download
1. Download from [mkcert releases](https://github.com/FiloSottile/mkcert/releases)
2. Add to PATH
3. Run `mkcert -install`

### macOS

```bash
brew install mkcert
mkcert -install
```

### Linux

```bash
# Ubuntu/Debian
sudo apt install libnss3-tools
wget -O mkcert https://github.com/FiloSottile/mkcert/releases/latest/download/mkcert-v*-linux-amd64
chmod +x mkcert
sudo mv mkcert /usr/local/bin/
mkcert -install
```

### Generate Certificates

```bash
# Create certs directory
mkdir certs

# Generate certificates
mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 127.0.0.1 ::1
```

## 🔍 Troubleshooting

### "WebAuth is not supported on sites with TLS certificate errors"

This error occurs when:
1. You're not using HTTPS
2. The SSL certificate is not trusted
3. You're accessing via IP without proper certificate

**Solution**: Follow the setup steps above to generate trusted certificates.

### Certificate Not Trusted on Mobile

1. Make sure you ran `mkcert -install` on your development machine
2. The certificate should be automatically trusted on the same network
3. If still having issues, try accessing `https://localhost:5173` first on your desktop to verify the setup

### Port Already in Use

The setup uses port 5173 (Vite default). If it's in use:
1. Kill the existing process
2. Or modify the port in `vite.config.ts`

### Network Access Issues

Make sure:
1. Your firewall allows connections on port 5173
2. Your phone and computer are on the same WiFi network
3. Your router doesn't block local network communication

## 🎯 Expected Results

After setup, you should be able to:
- ✅ Access your app via HTTPS on mobile
- ✅ Register biometric credentials (fingerprint, face, etc.)
- ✅ Authenticate using biometrics
- ✅ No security warnings or WebAuthn errors

## 📋 Verification Checklist

- [ ] `mkcert` is installed and working
- [ ] Certificates are generated in `certs/` directory
- [ ] Development server starts with HTTPS
- [ ] Can access app on mobile via HTTPS
- [ ] Biometric registration works on mobile
- [ ] Biometric authentication works on mobile

## 🔗 Useful Commands

```bash
# Setup HTTPS
npm run setup-https

# Start HTTPS dev server
npm run dev:https

# Get local IP addresses
npm run get-ip

# Check mkcert status
mkcert -version

# Reinstall mkcert CA (if needed)
mkcert -install
```

## 🆘 Still Having Issues?

1. Check browser console for specific error messages
2. Verify your network setup
3. Try accessing from desktop browser first
4. Ensure all certificates are properly generated
5. Check that your phone trusts the certificate authority

The biometric authentication should work perfectly once HTTPS is properly configured! 🎉
