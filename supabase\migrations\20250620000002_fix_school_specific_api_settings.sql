-- CRITICAL FIX: Move API settings from global system_settings to school-specific storage
-- This ensures Send<PERSON>rid, Twilio, and other API configurations are isolated by school

-- Add API configuration columns to school_settings table
ALTER TABLE school_settings 
ADD COLUMN IF NOT EXISTS sendgrid_api_key TEXT,
ADD COLUMN IF NOT EXISTS sendgrid_from_email TEXT DEFAULT '<EMAIL>',
ADD COLUMN IF NOT EXISTS twilio_account_sid TEXT,
ADD COLUMN IF NOT EXISTS twilio_auth_token TEXT,
ADD COLUMN IF NOT EXISTS twilio_phone_number TEXT,
ADD COLUMN IF NOT EXISTS email_service_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS sms_service_enabled BOOLEAN DEFAULT false;

-- Create a function to migrate existing global API settings to each school
DO $$
DECLARE
    school_record RECORD;
    email_config JSONB;
    sms_config JSONB;
BEGIN
    -- Get existing global email service config
    SELECT setting_value INTO email_config 
    FROM system_settings 
    WHERE setting_name = 'email_service_config';
    
    -- Get existing global SMS service config
    SELECT setting_value INTO sms_config 
    FROM system_settings 
    WHERE setting_name = 'sms_service_config';
    
    -- For each school, create empty API settings (school admins will configure their own)
    FOR school_record IN SELECT id FROM schools LOOP
        -- Insert or update school settings with empty API configurations
        INSERT INTO school_settings (
            school_id,
            sendgrid_api_key,
            sendgrid_from_email,
            twilio_account_sid,
            twilio_auth_token,
            twilio_phone_number,
            email_service_enabled,
            sms_service_enabled,
            created_at,
            updated_at
        ) VALUES (
            school_record.id,
            '', -- Empty API key - school admin must configure
            'noreply@' || LOWER(REPLACE((SELECT name FROM schools WHERE id = school_record.id), ' ', '')) || '.edu',
            '', -- Empty Twilio SID - school admin must configure
            '', -- Empty Twilio token - school admin must configure
            '', -- Empty phone number - school admin must configure
            false, -- Disabled by default
            false, -- Disabled by default
            NOW(),
            NOW()
        ) ON CONFLICT (school_id) DO UPDATE SET
            sendgrid_api_key = COALESCE(school_settings.sendgrid_api_key, ''),
            sendgrid_from_email = COALESCE(school_settings.sendgrid_from_email, 'noreply@' || LOWER(REPLACE((SELECT name FROM schools WHERE id = school_record.id), ' ', '')) || '.edu'),
            twilio_account_sid = COALESCE(school_settings.twilio_account_sid, ''),
            twilio_auth_token = COALESCE(school_settings.twilio_auth_token, ''),
            twilio_phone_number = COALESCE(school_settings.twilio_phone_number, ''),
            email_service_enabled = COALESCE(school_settings.email_service_enabled, false),
            sms_service_enabled = COALESCE(school_settings.sms_service_enabled, false),
            updated_at = NOW();
    END LOOP;
    
    -- Log the migration
    RAISE NOTICE 'Migrated API settings to school-specific configuration for % schools', (SELECT COUNT(*) FROM schools);
END $$;

-- Remove the global API settings from system_settings (they're now school-specific)
DELETE FROM system_settings WHERE setting_name IN ('email_service_config', 'sms_service_config');

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_school_settings_api_enabled ON school_settings(school_id, email_service_enabled, sms_service_enabled);

-- Add comments explaining the new structure
COMMENT ON COLUMN school_settings.sendgrid_api_key IS 'School-specific SendGrid API key for email notifications';
COMMENT ON COLUMN school_settings.twilio_account_sid IS 'School-specific Twilio Account SID for SMS notifications';
COMMENT ON COLUMN school_settings.email_service_enabled IS 'Whether email notifications are enabled for this school';
COMMENT ON COLUMN school_settings.sms_service_enabled IS 'Whether SMS notifications are enabled for this school';
