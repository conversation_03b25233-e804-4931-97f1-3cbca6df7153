-- =====================================================
-- AUTOMATED REMINDER SYSTEM - DATABASE MIGRATIONS
-- =====================================================
-- Run this SQL in your Supabase SQL Editor

-- Step 1: Create Reminder Settings Table
-- =====================================

CREATE TABLE IF NOT EXISTS reminder_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    enabled BOOLEAN NOT NULL DEFAULT false,
    minutes_before_end INTEGER NOT NULL DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(school_id)
);

-- Step 2: Create Reminder Sent Records Table
-- ==========================================

CREATE TABLE IF NOT EXISTS reminder_sent_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
    date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(school_id, date)
);

-- Step 3: Create Indexes for Performance
-- =====================================

-- Indexes for reminder_settings
CREATE INDEX IF NOT EXISTS idx_reminder_settings_school_id ON reminder_settings(school_id);
CREATE INDEX IF NOT EXISTS idx_reminder_settings_enabled ON reminder_settings(enabled);

-- Indexes for reminder_sent_records
CREATE INDEX IF NOT EXISTS idx_reminder_sent_records_school_id ON reminder_sent_records(school_id);
CREATE INDEX IF NOT EXISTS idx_reminder_sent_records_date ON reminder_sent_records(date);
CREATE INDEX IF NOT EXISTS idx_reminder_sent_records_sent_at ON reminder_sent_records(sent_at);

-- Step 4: Set Up Row Level Security (RLS)
-- =======================================

-- Enable RLS on reminder_settings table
ALTER TABLE reminder_settings ENABLE ROW LEVEL SECURITY;

-- Enable RLS on reminder_sent_records table
ALTER TABLE reminder_sent_records ENABLE ROW LEVEL SECURITY;

-- Reminder settings policies
CREATE POLICY "School admins can manage their reminder settings" ON reminder_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = reminder_settings.school_id 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Teachers can view their school reminder settings" ON reminder_settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = reminder_settings.school_id 
            AND profiles.role IN ('teacher', 'admin')
        )
    );

-- Reminder sent records policies
CREATE POLICY "School admins can view their reminder records" ON reminder_sent_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.user_id = auth.uid() 
            AND profiles.school_id = reminder_sent_records.school_id 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "System can insert reminder records" ON reminder_sent_records
    FOR INSERT WITH CHECK (true);

-- Step 5: Create Updated At Triggers
-- ==================================

-- Add trigger for reminder_settings table
DROP TRIGGER IF EXISTS update_reminder_settings_updated_at ON reminder_settings;
CREATE TRIGGER update_reminder_settings_updated_at
    BEFORE UPDATE ON reminder_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Step 6: Create Function for Late Marking Logic
-- ==============================================

CREATE OR REPLACE FUNCTION mark_late_after_reminder()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process if this is a student checking in (not manual teacher entry)
    -- and the status would normally be 'present'
    IF NEW.status = 'present' AND (NEW.verification_method IS NULL OR NEW.verification_method != 'manual') THEN
        -- Check if there was an automated reminder sent today for this school
        -- AND the student is checking in AFTER the reminder was sent
        IF EXISTS (
            SELECT 1 FROM reminder_sent_records
            WHERE school_id = NEW.school_id
            AND date = CURRENT_DATE
            AND sent_at < NEW.timestamp
        ) THEN
            -- Student scanned AFTER automated reminder was sent, mark as late
            NEW.status = 'late';

            -- Log this for debugging (optional)
            -- RAISE NOTICE 'Student % marked as late - checked in after automated reminder', NEW.student_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic late marking
DROP TRIGGER IF EXISTS trigger_mark_late_after_reminder ON attendance_records;
CREATE TRIGGER trigger_mark_late_after_reminder
    BEFORE INSERT ON attendance_records
    FOR EACH ROW
    EXECUTE FUNCTION mark_late_after_reminder();

-- Step 7: Create Cleanup Function for Old Records
-- ===============================================

CREATE OR REPLACE FUNCTION cleanup_old_reminder_records()
RETURNS void AS $$
BEGIN
    -- Delete reminder sent records older than 30 days
    DELETE FROM reminder_sent_records 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- Log cleanup
    RAISE NOTICE 'Cleaned up old reminder records';
END;
$$ LANGUAGE plpgsql;

-- Step 8: Create Scheduled Cleanup (Optional)
-- ===========================================

-- You can set up a cron job to run this daily
-- SELECT cron.schedule('cleanup-reminder-records', '0 2 * * *', 'SELECT cleanup_old_reminder_records();');

-- Step 9: Add Comments for Documentation
-- =====================================

COMMENT ON TABLE reminder_settings IS 'Settings for automated attendance reminders per school';
COMMENT ON TABLE reminder_sent_records IS 'Records of when automated reminders were sent';
COMMENT ON FUNCTION mark_late_after_reminder() IS 'Automatically marks students as late if they check in after reminder was sent';
COMMENT ON FUNCTION cleanup_old_reminder_records() IS 'Cleans up old reminder records to maintain performance';

-- Step 10: Insert Default Settings (Optional)
-- ===========================================

-- Uncomment the following to create default settings for existing schools
-- INSERT INTO reminder_settings (school_id, enabled, minutes_before_end)
-- SELECT id, false, 30 FROM schools
-- ON CONFLICT (school_id) DO NOTHING;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Verification queries (optional - you can run these to check)
-- SELECT 'Reminder tables created successfully' as status;
-- SELECT COUNT(*) as reminder_settings_count FROM reminder_settings;
-- SELECT COUNT(*) as reminder_records_count FROM reminder_sent_records;

-- Check if triggers were created
-- SELECT trigger_name, event_manipulation, event_object_table 
-- FROM information_schema.triggers 
-- WHERE trigger_name IN ('update_reminder_settings_updated_at', 'trigger_mark_late_after_reminder');
