import { createClient } from "@supabase/supabase-js";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error(
    "Error: Supabase URL or service key not found in environment variables"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createExecuteSqlFunction() {
  try {
    console.log("Creating execute_sql function...");

    const { data, error } = await supabase.rpc("execute_sql", {
      sql: `
        CREATE OR REPLACE FUNCTION public.execute_sql(sql text)
        RETURNS JSONB
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          result JSONB;
        BEGIN
          EXECUTE sql INTO result;
          RETURN result;
        EXCEPTION
          WHEN OTHERS THEN
            RETURN jsonb_build_object('error', SQLERRM, 'detail', SQLSTATE);
        END;
        $$;
      `,
    });

    if (error) {
      console.error("Error creating execute_sql function:", error);

      // If the function doesn't exist yet, we need to create it directly
      console.log("Trying to create function using database query...");

      const { data: queryData, error: queryError } = await supabase
        .from("_database")
        .select("*")
        .rpc("query", {
          query: `
            CREATE OR REPLACE FUNCTION public.execute_sql(sql text)
            RETURNS JSONB
            LANGUAGE plpgsql
            SECURITY DEFINER
            AS $$
            DECLARE
              result JSONB;
            BEGIN
              EXECUTE sql INTO result;
              RETURN result;
            EXCEPTION
              WHEN OTHERS THEN
                RETURN jsonb_build_object('error', SQLERRM, 'detail', SQLSTATE);
            END;
            $$;
          `,
        });

      if (queryError) {
        console.error(
          "Error creating function using database query:",
          queryError
        );
        process.exit(1);
      }

      console.log("Function created successfully using database query");
    } else {
      console.log("Function created successfully");
    }

    // Test the function
    console.log("Testing execute_sql function...");

    const { data: testData, error: testError } = await supabase.rpc(
      "execute_sql",
      {
        sql: "SELECT jsonb_build_object('test', 'success') as result",
      }
    );

    if (testError) {
      console.error("Error testing execute_sql function:", testError);
      process.exit(1);
    }

    console.log("Function test result:", testData);
    console.log("execute_sql function created and tested successfully");
  } catch (error) {
    console.error("Unexpected error:", error);
    process.exit(1);
  }
}

// Execute the function
createExecuteSqlFunction();
