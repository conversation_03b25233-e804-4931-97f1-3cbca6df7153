import { supabase } from "@/lib/supabase";

/**
 * Migration to create the database_cleanup_settings table
 * This table stores configuration for automatic database cleanup
 */
export const createDatabaseCleanupSettings = async (): Promise<boolean> => {
  try {
    console.log("Running database cleanup settings migration...");

    // Check if the database_cleanup_settings table exists
    const { data: tableExists, error: tableCheckError } = await supabase
      .from("database_cleanup_settings")
      .select("id")
      .limit(1);

    // If there's no error, the table exists
    if (!tableCheckError) {
      console.log("Database cleanup settings table already exists");
      return true;
    }

    // Create the database_cleanup_settings table
    const { error: createTableError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create database_cleanup_settings table
        CREATE TABLE IF NOT EXISTS public.database_cleanup_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          enabled BOOLEAN DEFAULT false,
          notifications_retention_days INTEGER DEFAULT 90,
          attendance_records_retention_days INTEGER DEFAULT 365,
          audit_logs_retention_days INTEGER DEFAULT 180,
          excuses_retention_days INTEGER DEFAULT 180,
          alerts_retention_days INTEGER DEFAULT 90,
          last_cleanup_at TIMESTAMP WITH TIME ZONE,
          next_cleanup_at TIMESTAMP WITH TIME ZONE,
          cleanup_frequency TEXT DEFAULT 'weekly' CHECK (cleanup_frequency IN ('daily', 'weekly', 'monthly')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
          updated_by UUID REFERENCES profiles(id) ON DELETE SET NULL
        );

        -- Enable RLS
        ALTER TABLE public.database_cleanup_settings ENABLE ROW LEVEL SECURITY;

        -- Only system admins can view database cleanup settings
        CREATE POLICY "System admins can view database cleanup settings" 
        ON public.database_cleanup_settings
        FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.access_level = 3
          )
        );

        -- Only system admins can insert database cleanup settings
        CREATE POLICY "System admins can insert database cleanup settings" 
        ON public.database_cleanup_settings
        FOR INSERT
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.access_level = 3
          )
        );

        -- Only system admins can update database cleanup settings
        CREATE POLICY "System admins can update database cleanup settings" 
        ON public.database_cleanup_settings
        FOR UPDATE
        USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.access_level = 3
          )
        );

        -- Insert default settings
        INSERT INTO public.database_cleanup_settings (
          enabled,
          notifications_retention_days,
          attendance_records_retention_days,
          audit_logs_retention_days,
          excuses_retention_days,
          alerts_retention_days,
          cleanup_frequency,
          next_cleanup_at
        ) VALUES (
          false,
          90,
          365,
          180,
          180,
          90,
          'weekly',
          (now() + interval '7 days')
        );

        -- Create function to perform database cleanup
        CREATE OR REPLACE FUNCTION perform_database_cleanup()
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          settings RECORD;
          deleted_notifications INTEGER := 0;
          deleted_attendance_records INTEGER := 0;
          deleted_audit_logs INTEGER := 0;
          deleted_excuses INTEGER := 0;
          deleted_alerts INTEGER := 0;
          next_cleanup TIMESTAMP WITH TIME ZONE;
        BEGIN
          -- Get the current settings
          SELECT * INTO settings FROM database_cleanup_settings LIMIT 1;
          
          -- If cleanup is not enabled, exit
          IF NOT settings.enabled THEN
            RETURN false;
          END IF;
          
          -- Clean up notifications
          IF settings.notifications_retention_days > 0 THEN
            DELETE FROM notifications
            WHERE created_at < (now() - (settings.notifications_retention_days || ' days')::interval)
            RETURNING COUNT(*) INTO deleted_notifications;
          END IF;
          
          -- Clean up attendance records
          IF settings.attendance_records_retention_days > 0 THEN
            DELETE FROM attendance_records
            WHERE created_at < (now() - (settings.attendance_records_retention_days || ' days')::interval)
            RETURNING COUNT(*) INTO deleted_attendance_records;
          END IF;
          
          -- Clean up audit logs
          IF settings.audit_logs_retention_days > 0 THEN
            DELETE FROM audit_logs
            WHERE created_at < (now() - (settings.audit_logs_retention_days || ' days')::interval)
            RETURNING COUNT(*) INTO deleted_audit_logs;
          END IF;
          
          -- Clean up excuses
          IF settings.excuses_retention_days > 0 THEN
            DELETE FROM excuses
            WHERE created_at < (now() - (settings.excuses_retention_days || ' days')::interval)
            RETURNING COUNT(*) INTO deleted_excuses;
          END IF;
          
          -- Calculate next cleanup date based on frequency
          IF settings.cleanup_frequency = 'daily' THEN
            next_cleanup := now() + interval '1 day';
          ELSIF settings.cleanup_frequency = 'weekly' THEN
            next_cleanup := now() + interval '7 days';
          ELSIF settings.cleanup_frequency = 'monthly' THEN
            next_cleanup := now() + interval '1 month';
          ELSE
            next_cleanup := now() + interval '7 days';
          END IF;
          
          -- Update the settings with the cleanup results
          UPDATE database_cleanup_settings
          SET 
            last_cleanup_at = now(),
            next_cleanup_at = next_cleanup,
            updated_at = now()
          WHERE id = settings.id;
          
          -- Log the cleanup in audit_logs
          INSERT INTO audit_logs (
            action_type,
            entity_type,
            details,
            created_at
          ) VALUES (
            'database_cleanup',
            'system',
            jsonb_build_object(
              'notifications_deleted', deleted_notifications,
              'attendance_records_deleted', deleted_attendance_records,
              'audit_logs_deleted', deleted_audit_logs,
              'excuses_deleted', deleted_excuses,
              'alerts_deleted', deleted_alerts,
              'next_cleanup', next_cleanup
            ),
            now()
          );
          
          RETURN true;
        END;
        $$;

        -- Grant execute permission on the function
        GRANT EXECUTE ON FUNCTION perform_database_cleanup() TO authenticated;
      `,
    });

    if (createTableError) {
      console.error("Error creating database cleanup settings table:", createTableError);
      return false;
    }

    console.log("Database cleanup settings migration completed successfully");
    return true;
  } catch (error) {
    console.error("Error in database cleanup settings migration:", error);
    return false;
  }
};
