/**
 * 📱 Tablet Setup QR Generator
 * Generates QR codes for easy tablet configuration
 */

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import QRCode from "react-qr-code";
import { supabase } from "@/lib/supabase";
import {
  QrCode,
  Tablet,
  Copy,
  Check,
  Download,
  Settings,
  Wifi,
  Shield,
  ExternalLink
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/context/AuthContext";

interface School {
  id: string;
  name: string;
}

interface Block {
  id: string;
  name: string;
  school_id: string;
}

interface Room {
  id: string;
  name: string;
  building?: string;
  floor?: number;
  block_id: string;
}

interface TabletSetupData {
  type: "tablet_setup";
  version: "1.0";
  school_id: string;
  school_name: string;
  room_id: string;
  room_name: string;
  block_id?: string;
  block_name?: string;
  setup_url: string;
  timestamp: string;
  expires_at: string;
}

export function TabletSetupQR() {
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string>("");
  const [selectedRoom, setSelectedRoom] = useState<string>("");
  const [deviceName, setDeviceName] = useState<string>("");
  const [setupQR, setSetupQR] = useState<string>("");
  const [setupURL, setSetupURL] = useState<string>("");
  const [copied, setCopied] = useState(false);
  const [schoolName, setSchoolName] = useState<string>("");
  const [bulkUrls, setBulkUrls] = useState<Array<{
    roomId: string;
    roomName: string;
    url: string;
    qrCode: string;
  }>>([]);
  const [selectedRoomForUrl, setSelectedRoomForUrl] = useState<string>("");
  const [showBulkGeneration, setShowBulkGeneration] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();
  const { profile } = useAuth();

  // Use admin's school automatically
  const selectedSchool = profile?.school_id || "";

  // Fetch school name and blocks when admin's school is available
  useEffect(() => {
    if (selectedSchool) {
      fetchSchoolName(selectedSchool);
      fetchBlocks(selectedSchool);
      setSelectedBlock("");
      setSelectedRoom("");
    }
  }, [selectedSchool]);

  const fetchSchoolName = async (schoolId: string) => {
    try {
      const { data, error } = await supabase
        .from("schools")
        .select("name")
        .eq("id", schoolId)
        .single();

      if (error) throw error;
      setSchoolName(data?.name || "");
    } catch (error) {
      console.error("Error fetching school name:", error);
    }
  };

  // Fetch rooms when block changes
  useEffect(() => {
    if (selectedBlock) {
      fetchRooms(selectedBlock);
      setSelectedRoom("");
    }
  }, [selectedBlock]);

  // Generate QR when all required fields are selected
  useEffect(() => {
    if (selectedSchool && selectedRoom) {
      generateSetupQR();
    }
  }, [selectedSchool, selectedRoom, selectedBlock, deviceName]);

  // Remove fetchSchools function - no longer needed

  const fetchBlocks = async (schoolId: string) => {
    try {
      const { data, error } = await supabase
        .from("blocks")
        .select("id, name, school_id")
        .eq("school_id", schoolId)
        .order("name");

      if (error) throw error;
      setBlocks(data || []);
    } catch (error) {
      console.error("Error fetching blocks:", error);
      toast({
        title: "Error",
        description: "Failed to fetch blocks",
        variant: "destructive",
      });
    }
  };

  const fetchRooms = async (blockId: string) => {
    try {
      const { data, error } = await supabase
        .from("rooms")
        .select("id, name, building, floor, block_id")
        .eq("block_id", blockId)
        .order("name");

      if (error) throw error;
      setRooms(data || []);
    } catch (error) {
      console.error("Error fetching rooms:", error);
      toast({
        title: "Error",
        description: "Failed to fetch rooms",
        variant: "destructive",
      });
    }
  };

  const generateSetupQR = () => {
    const block = blocks.find(b => b.id === selectedBlock);
    const room = rooms.find(r => r.id === selectedRoom);

    if (!selectedSchool || !room) return;

    // Use the current origin for the setup URL
    const baseURL = window.location.origin;

    // Create the setup URL that will automatically configure the tablet
    const setupURL = `${baseURL}/tablet?school=${selectedSchool}&room=${selectedRoom}${selectedBlock ? `&block=${selectedBlock}` : ''}&setup=auto${deviceName ? `&name=${encodeURIComponent(deviceName)}` : ''}`;

    // The QR code should contain just the URL, not JSON data
    // This way when scanned, it will automatically open the URL
    setSetupQR(setupURL);
    setSetupURL(setupURL);
  };

  // Generate simple tablet URL (without auto-setup)
  const generateSimpleTabletUrl = (roomId: string) => {
    const baseUrl = window.location.origin;
    const params = new URLSearchParams({
      school: selectedSchool || "",
      room: roomId,
    });
    return `${baseUrl}/tablet?${params.toString()}`;
  };

  // Generate URLs for entire block
  const generateBlockUrls = () => {
    if (!selectedBlock) return;

    const blockRooms = rooms.filter(room => room.block_id === selectedBlock);
    const urls = blockRooms.map(room => ({
      roomId: room.id,
      roomName: room.name,
      url: generateSimpleTabletUrl(room.id),
      qrCode: generateSimpleTabletUrl(room.id),
    }));

    setBulkUrls(urls);
  };

  // Generate URL for single room
  const generateSingleRoomUrl = () => {
    if (!selectedRoomForUrl) return;

    const room = rooms.find(r => r.id === selectedRoomForUrl);
    if (!room) return;

    const url = generateSimpleTabletUrl(room.id);
    setBulkUrls([{
      roomId: room.id,
      roomName: room.name,
      url: url,
      qrCode: url,
    }]);
  };

  // Open URL in new tab
  const openInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: "Copied!",
        description: "Setup URL copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const downloadQR = () => {
    const svg = document.getElementById("setup-qr-code");
    if (!svg) return;

    const svgData = new XMLSerializer().serializeToString(svg);
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx?.drawImage(img, 0, 0);
      
      const link = document.createElement("a");
      link.download = `tablet-setup-${selectedRoom}.png`;
      link.href = canvas.toDataURL();
      link.click();
    };

    img.src = "data:image/svg+xml;base64," + btoa(svgData);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <QrCode className="w-5 h-5" />
            {t("admin.tablets.setupQR.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* School Info Display */}
          {profile?.school_id && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <Label className="text-sm font-medium text-blue-900">
                {t("admin.tablets.setupQR.school")}: {schoolName || profile.school_name || profile.schoolName || "Your School"}
              </Label>
              <p className="text-xs text-blue-700 mt-1">
                Tablets will be configured for your school automatically
              </p>
            </div>
          )}

          {/* Block Selection */}
          {selectedSchool && (
            <div className="space-y-2">
              <Label htmlFor="block">{t("admin.tablets.setupQR.block")}</Label>
              <Select value={selectedBlock} onValueChange={setSelectedBlock}>
                <SelectTrigger>
                  <SelectValue placeholder={t("admin.tablets.setupQR.selectBlock")} />
                </SelectTrigger>
                <SelectContent>
                  {blocks.map((block) => (
                    <SelectItem key={block.id} value={block.id}>
                      {block.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Room Selection */}
          {selectedSchool && (
            <div className="space-y-2">
              <Label htmlFor="room">{t("admin.tablets.setupQR.room")}</Label>
              <Select value={selectedRoom} onValueChange={setSelectedRoom}>
                <SelectTrigger>
                  <SelectValue placeholder={t("admin.tablets.setupQR.selectRoom")} />
                </SelectTrigger>
                <SelectContent>
                  {rooms.map((room) => (
                    <SelectItem key={room.id} value={room.id}>
                      {room.name}
                      {room.building && ` - ${room.building}`}
                      {room.floor && ` (Floor ${room.floor})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Device Name */}
          <div className="space-y-2">
            <Label htmlFor="deviceName">{t("admin.tablets.setupQR.deviceName")}</Label>
            <Input
              id="deviceName"
              placeholder={t("admin.tablets.setupQR.deviceNamePlaceholder")}
              value={deviceName}
              onChange={(e) => setDeviceName(e.target.value)}
            />
          </div>

          {/* Bulk Generation Toggle */}
          <div className="border-t pt-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Bulk URL Generation</Label>
                <p className="text-xs text-muted-foreground">Generate tablet URLs for multiple rooms</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowBulkGeneration(!showBulkGeneration)}
              >
                {showBulkGeneration ? "Hide" : "Show"} Bulk Generation
              </Button>
            </div>
          </div>

          {/* Bulk Generation Section */}
          {showBulkGeneration && (
            <div className="space-y-4 border rounded-lg p-4 bg-gray-50">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Generate URLs for Block</Label>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button
                      onClick={generateBlockUrls}
                      disabled={!selectedBlock}
                      className="w-full sm:w-auto text-sm"
                      size="sm"
                    >
                      Generate Block URLs
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Generate URL for Single Room</Label>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Select value={selectedRoomForUrl} onValueChange={setSelectedRoomForUrl}>
                      <SelectTrigger className="w-full sm:flex-1">
                        <SelectValue placeholder="Select Room" />
                      </SelectTrigger>
                      <SelectContent>
                        {rooms.map((room) => (
                          <SelectItem key={room.id} value={room.id}>
                            {room.name}
                            {room.building && ` - ${room.building}`}
                            {room.floor && ` Floor ${room.floor}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={generateSingleRoomUrl}
                      disabled={!selectedRoomForUrl}
                      className="w-full sm:w-auto text-sm"
                      size="sm"
                    >
                      Generate
                    </Button>
                  </div>
                </div>
              </div>

              {/* Instructions */}
              <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2 text-sm sm:text-base">Setup Instructions:</h4>
                <ol className="text-xs sm:text-sm text-blue-700 list-decimal list-inside space-y-1">
                  <li>Select a block or room to generate tablet URLs</li>
                  <li>Copy the generated URLs or scan the QR codes</li>
                  <li>Open the URLs on tablets to configure them</li>
                  <li>Tablets will automatically connect to the attendance system</li>
                </ol>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generated QR Code */}
      {setupQR && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <Tablet className="w-5 h-5 flex-shrink-0" />
              {t("admin.tablets.setupQR.setupQRCode")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 sm:space-y-6">
            {/* QR Code Display */}
            <div className="flex justify-center">
              <div className="bg-white p-4 sm:p-6 rounded-xl shadow-lg border-2 border-blue-200 max-w-full">
                <QRCode
                  id="setup-qr-code"
                  value={setupQR}
                  size={window.innerWidth < 640 ? 200 : 256}
                  style={{
                    height: "auto",
                    maxWidth: "100%",
                    width: "100%",
                  }}
                />
              </div>
            </div>

            {/* Setup Instructions */}
            <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2 text-sm sm:text-base">{t("admin.tablets.setupQR.setupInstructions")}</h4>
              <ol className="list-decimal list-inside space-y-1 text-blue-800 text-xs sm:text-sm">
                <li>{t("admin.tablets.setupQR.instruction1")}</li>
                <li>{t("admin.tablets.setupQR.instruction2")}</li>
                <li>{t("admin.tablets.setupQR.instruction3")}</li>
                <li>{t("admin.tablets.setupQR.instruction4")}</li>
                <li>{t("admin.tablets.setupQR.instruction5")}</li>
              </ol>
              <div className="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-700">
                <strong>{t("admin.tablets.setupQR.note")}</strong> {t("admin.tablets.setupQR.noteText")}
              </div>
            </div>

            {/* Manual Setup URL */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">{t("admin.tablets.setupQR.manualSetupURL")}</Label>
              <div className="flex flex-col sm:flex-row gap-2">
                <Input
                  value={setupURL}
                  readOnly
                  className="font-mono text-xs sm:text-sm flex-1"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(setupURL)}
                  className="w-full sm:w-auto"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={downloadQR} variant="outline" className="w-full sm:w-auto">
                <Download className="w-4 h-4 mr-2 flex-shrink-0" />
                <span className="truncate">{t("admin.tablets.setupQR.downloadQR")}</span>
              </Button>
              <Button onClick={() => copyToClipboard(setupURL)} variant="outline" className="w-full sm:w-auto">
                <Copy className="w-4 h-4 mr-2 flex-shrink-0" />
                <span className="truncate">{t("admin.tablets.setupQR.copySetupURL")}</span>
              </Button>
            </div>

            {/* Security Info */}
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="flex items-center gap-2 text-green-800 text-xs sm:text-sm">
                <Shield className="w-4 h-4 flex-shrink-0" />
                <span className="font-medium">{t("admin.tablets.setupQR.secureSetup")}</span>
              </div>
              <p className="text-green-700 text-xs sm:text-sm mt-1">
                {t("admin.tablets.setupQR.secureSetupText")}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generated Bulk URLs */}
      {bulkUrls.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-base sm:text-lg font-semibold">Generated Tablet URLs</h3>

          {bulkUrls.map((item, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="text-sm sm:text-base flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <span className="truncate">{item.roomName}</span>
                  <Badge variant="outline" className="self-start sm:self-center text-xs">
                    Room {item.roomId.slice(-8)}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4">
                {/* URL */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Tablet URL:</Label>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Input
                      value={item.url}
                      readOnly
                      className="font-mono text-xs sm:text-sm flex-1"
                    />
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(item.url)}
                        className="flex-1 sm:flex-none"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openInNewTab(item.url)}
                        className="flex-1 sm:flex-none"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* QR Code for URL */}
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <Label className="text-sm font-medium">Setup QR Code:</Label>
                    <p className="text-xs text-gray-600 mb-2">
                      Scan this QR code with the tablet to open the setup URL
                    </p>
                  </div>
                  <div className="flex justify-center lg:justify-end">
                    <div className="bg-white p-2 sm:p-3 rounded-lg border">
                      <QRCode
                        value={item.qrCode}
                        size={window.innerWidth < 640 ? 100 : 120}
                        style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                      />
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex flex-col sm:flex-row gap-2 pt-2 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(item.url)}
                    className="flex-1 text-xs sm:text-sm"
                  >
                    <Copy className="w-4 h-4 mr-1 flex-shrink-0" />
                    <span className="truncate">Copy URL</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openInNewTab(item.url)}
                    className="flex-1 text-xs sm:text-sm"
                  >
                    <ExternalLink className="w-4 h-4 mr-1 flex-shrink-0" />
                    <span className="truncate">Test Tablet</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Bulk Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm sm:text-base">Bulk Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    const allUrls = bulkUrls.map(item =>
                      `${item.roomName}: ${item.url}`
                    ).join('\n');
                    copyToClipboard(allUrls);
                  }}
                  className="w-full sm:w-auto text-xs sm:text-sm"
                >
                  <Copy className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">Copy All URLs</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    bulkUrls.forEach(item => {
                      setTimeout(() => openInNewTab(item.url), 100);
                    });
                  }}
                  className="w-full sm:w-auto text-xs sm:text-sm"
                >
                  <ExternalLink className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">Open All Tablets</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
