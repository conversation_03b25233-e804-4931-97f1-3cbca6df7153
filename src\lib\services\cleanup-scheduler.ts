import { supabase } from '@/lib/supabase';
import { checkAndTriggerCleanup } from './database-cleanup-service';

/**
 * Automatic cleanup scheduler service
 * This service runs periodic checks and triggers cleanup when needed
 */
export class CleanupScheduler {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private checkInterval = 60 * 60 * 1000; // Check every hour

  /**
   * Start the cleanup scheduler
   */
  start(): void {
    if (this.isRunning) {
      console.log('Cleanup scheduler is already running');
      return;
    }

    console.log('Starting cleanup scheduler...');
    this.isRunning = true;

    // Run initial check
    this.performCleanupCheck();

    // Set up periodic checks
    this.intervalId = setInterval(() => {
      this.performCleanupCheck();
    }, this.checkInterval);

    console.log(`Cleanup scheduler started with ${this.checkInterval / 1000 / 60} minute intervals`);
  }

  /**
   * Stop the cleanup scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('Cleanup scheduler is not running');
      return;
    }

    console.log('Stopping cleanup scheduler...');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    console.log('Cleanup scheduler stopped');
  }

  /**
   * Check if cleanup is needed and trigger if necessary
   */
  private async performCleanupCheck(): Promise<void> {
    try {
      console.log('Performing cleanup check...');
      
      // Check if cleanup is due
      const cleanupTriggered = await checkAndTriggerCleanup(false);
      
      if (cleanupTriggered) {
        console.log('✅ Automatic cleanup was triggered and completed');
      } else {
        console.log('ℹ️ Cleanup check completed - no cleanup needed at this time');
      }
    } catch (error) {
      console.error('❌ Error during cleanup check:', error);
    }
  }

  /**
   * Force a cleanup check immediately
   */
  async forceCleanupCheck(): Promise<boolean> {
    try {
      console.log('Forcing immediate cleanup check...');
      return await checkAndTriggerCleanup(true);
    } catch (error) {
      console.error('Error during forced cleanup check:', error);
      return false;
    }
  }

  /**
   * Get scheduler status
   */
  getStatus(): { isRunning: boolean; checkInterval: number } {
    return {
      isRunning: this.isRunning,
      checkInterval: this.checkInterval,
    };
  }

  /**
   * Update check interval
   */
  setCheckInterval(intervalMs: number): void {
    this.checkInterval = intervalMs;
    
    if (this.isRunning) {
      // Restart with new interval
      this.stop();
      this.start();
    }
  }
}

// Global scheduler instance
export const cleanupScheduler = new CleanupScheduler();

/**
 * Initialize cleanup scheduler on app start
 */
export const initializeCleanupScheduler = (): void => {
  // Only start in production or when explicitly enabled
  const shouldStart = process.env.NODE_ENV === 'production' || 
                     process.env.ENABLE_CLEANUP_SCHEDULER === 'true';

  if (shouldStart) {
    cleanupScheduler.start();
    
    // Set up cleanup on app shutdown
    const cleanup = () => {
      console.log('App shutting down, stopping cleanup scheduler...');
      cleanupScheduler.stop();
    };

    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    process.on('beforeExit', cleanup);
  } else {
    console.log('Cleanup scheduler disabled (not in production)');
  }
};

/**
 * Manual cleanup trigger for admin use
 */
export const triggerManualCleanup = async (): Promise<boolean> => {
  try {
    console.log('Manual cleanup triggered by admin...');
    return await cleanupScheduler.forceCleanupCheck();
  } catch (error) {
    console.error('Error during manual cleanup:', error);
    return false;
  }
};

/**
 * Health check for cleanup system
 */
export const getCleanupSystemHealth = async (): Promise<{
  scheduler_running: boolean;
  last_check: string | null;
  next_scheduled_cleanup: string | null;
  system_status: 'healthy' | 'warning' | 'error';
  issues: string[];
}> => {
  const issues: string[] = [];
  let systemStatus: 'healthy' | 'warning' | 'error' = 'healthy';

  try {
    // Check scheduler status
    const schedulerStatus = cleanupScheduler.getStatus();
    
    // Check database connectivity
    const { error: dbError } = await supabase
      .from('database_cleanup_settings')
      .select('id')
      .limit(1);

    if (dbError) {
      issues.push('Database connectivity issue');
      systemStatus = 'error';
    }

    // Check if cleanup function exists
    try {
      const { error: functionError } = await supabase.rpc('get_cleanup_statistics');
      if (functionError) {
        issues.push('Cleanup database functions not available');
        systemStatus = 'error';
      }
    } catch (err) {
      issues.push('Error checking database functions');
      systemStatus = 'error';
    }

    // Get last cleanup info
    let lastCheck = null;
    let nextScheduledCleanup = null;
    
    try {
      const { data: settings } = await supabase
        .from('database_cleanup_settings')
        .select('last_cleanup_at, next_cleanup_at')
        .limit(1)
        .single();

      if (settings) {
        lastCheck = settings.last_cleanup_at;
        nextScheduledCleanup = settings.next_cleanup_at;
      }
    } catch (err) {
      issues.push('Could not retrieve cleanup settings');
      if (systemStatus === 'healthy') systemStatus = 'warning';
    }

    // Check if cleanup is overdue
    if (nextScheduledCleanup) {
      const nextCleanup = new Date(nextScheduledCleanup);
      const now = new Date();
      
      if (now > nextCleanup) {
        const hoursOverdue = Math.floor((now.getTime() - nextCleanup.getTime()) / (1000 * 60 * 60));
        if (hoursOverdue > 24) {
          issues.push(`Cleanup is ${hoursOverdue} hours overdue`);
          if (systemStatus === 'healthy') systemStatus = 'warning';
        }
      }
    }

    return {
      scheduler_running: schedulerStatus.isRunning,
      last_check: lastCheck,
      next_scheduled_cleanup: nextScheduledCleanup,
      system_status: systemStatus,
      issues,
    };
  } catch (error) {
    console.error('Error checking cleanup system health:', error);
    return {
      scheduler_running: false,
      last_check: null,
      next_scheduled_cleanup: null,
      system_status: 'error',
      issues: ['System health check failed'],
    };
  }
};
