-- Create a function to check user permissions
CREATE OR REPLACE FUNCTION public.check_profile_permissions(target_user_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
  calling_user_role text;
  target_user_role text;
BEGIN
  -- Get the calling user's role
  SELECT role INTO calling_user_role
  FROM public.profiles
  WHERE user_id = auth.uid();
  
  -- Get the target user's role
  SELECT role INTO target_user_role
  FROM public.profiles
  WHERE user_id = target_user_id;
  
  -- Build the result object
  result = jsonb_build_object(
    'calling_user_role', calling_user_role,
    'target_user_role', target_user_role,
    'can_view', CASE 
      WHEN calling_user_role = 'admin' THEN true
      WHEN auth.uid() = target_user_id THEN true
      ELSE false
    END,
    'can_update', CASE 
      WHEN calling_user_role = 'admin' THEN true
      WHEN auth.uid() = target_user_id THEN true
      ELSE false
    END,
    'can_delete', CASE 
      WHEN calling_user_role = 'admin' THEN true
      ELSE false
    END,
    'jwt_role', auth.jwt()->>'role'
  );
  
  RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.check_profile_permissions(uuid) TO authenticated; 