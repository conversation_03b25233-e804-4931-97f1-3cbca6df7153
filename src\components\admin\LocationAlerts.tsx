import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "@/lib/utils/toast";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { AlertCircle, Check, X } from "lucide-react";
import { formatDistanceToNow } from 'date-fns';

interface Alert {
  id: string;
  student_id: string;
  room_id: string;
  timestamp: string;
  distance_meters: number;
  student_location: {
    latitude: number;
    longitude: number;
  };
  room_location: {
    latitude: number;
    longitude: number;
  };
  status: 'pending' | 'reviewed' | 'dismissed';
  student: {
    full_name: string;
    email: string;
  };
  room: {
    name: string;
  };
}

export default function LocationAlerts() {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();
  const { profile } = useAuth();

  useEffect(() => {
    fetchAlerts();
    // Set up real-time subscription
    const subscription = supabase
      .channel('attendance_alerts')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'attendance_alerts'
      }, () => {
        fetchAlerts();
      })
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchAlerts = async () => {
    try {
      const { data, error } = await supabase
        .from('attendance_alerts')
        .select(`
          *,
          student:student_id(full_name, email),
          room:room_id(name)
        `)
        .eq('status', 'pending')
        .order('timestamp', { ascending: false });

      if (error) throw error;
      setAlerts(data || []);
    } catch (error) {
      console.error('Error fetching alerts:', error);
      toast.translateError(
        t,
        "common.error",
        "admin.alerts.fetchFailed"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleAlertAction = async (alertId: string, action: 'reviewed' | 'dismissed') => {
    try {
      const { error } = await supabase
        .from('attendance_alerts')
        .update({
          status: action,
          reviewed_by: profile?.id,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', alertId);

      if (error) throw error;

      toast.success(
        t("common.success"),
        {
          description: t("admin.alerts.markedAs", { action })
        }
      );

      fetchAlerts();
    } catch (error) {
      console.error('Error updating alert:', error);
      toast.translateError(
        t,
        "common.error",
        "admin.alerts.updateFailed"
      );
    }
  };

  const getGoogleMapsUrl = (location: { latitude: number; longitude: number }) => {
    return `https://www.google.com/maps?q=${location.latitude},${location.longitude}`;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-yellow-500" />
          Location Alerts
        </CardTitle>
        <CardDescription>
          View and manage alerts for students attempting to mark attendance outside their rooms
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-4">Loading alerts...</div>
        ) : alerts.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            No pending alerts
          </div>
        ) : (
          <div className="space-y-4">
            {alerts.map((alert) => (
              <Card key={alert.id} className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium">{alert.student.full_name}</h4>
                    <p className="text-sm text-muted-foreground">{alert.student.email}</p>
                    <p className="text-sm mt-2">
                      Attempted to mark attendance in <strong>{alert.room.name}</strong>
                    </p>
                    <p className="text-sm text-yellow-600">
                      Distance from room: {Math.round(alert.distance_meters)} meters
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(alert.timestamp), { addSuffix: true })}
                    </p>
                    <div className="mt-2 space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(getGoogleMapsUrl(alert.student_location), '_blank')}
                      >
                        View Student Location
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(getGoogleMapsUrl(alert.room_location), '_blank')}
                      >
                        View Room Location
                      </Button>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAlertAction(alert.id, 'reviewed')}
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Review
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAlertAction(alert.id, 'dismissed')}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Dismiss
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 