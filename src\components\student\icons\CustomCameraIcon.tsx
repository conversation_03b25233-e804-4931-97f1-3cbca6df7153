import React from "react";

interface CustomCameraIconProps {
  size?: number;
  color?: string;
  className?: string;
}

const CustomCameraIcon: React.FC<CustomCameraIconProps> = ({
  size = 72,
  color = "#000000",
  className = "",
}) => {
  return (
    <div className={className}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{
          minWidth: size,
          minHeight: size,
          fill: "none",
          color: color,
        }}
      >
        <path
          d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"
          fill="none"
          stroke={color}
        />
        <circle cx="12" cy="13" r="3" fill="none" stroke={color} />
      </svg>
    </div>
  );
};

export default CustomCameraIcon;
