/**
 * 🌍 Notification Localization Utility
 * =====================================================
 * This utility handles creating localized notifications for students
 * based on their language preference stored in their profile.
 */

import { supabase } from "@/lib/supabase";
import i18n from "@/lib/i18n";
import { pushNotificationService } from "@/lib/services/push-notification-service";

// Comprehensive notification templates in both languages
const NOTIFICATION_TEMPLATES = {
  en: {
    // Attendance notifications
    attendanceReminder: {
      title: "⏰ Attendance Reminder",
      message: (teacherName: string, roomName: string) =>
        `Hello! Your teacher ${teacherName} has sent a reminder for you to verify your attendance in ${roomName}. Please scan the QR code or use your PIN to check in.`,
    },
    automatedAttendanceReminder: {
      title: "🔔 Attendance Session Ending Soon",
      message: (roomName: string, timeString: string) =>
        `Attention! The attendance session for ${roomName} will end soon. Please check in now to avoid being marked as late. Time: ${timeString}`,
    },
    attendanceRecorded: {
      title: "✅ Attendance Recorded",
      message: (roomName: string, time: string) =>
        `You have successfully checked in to ${roomName} at ${time}.`,
    },
    markedPresent: {
      title: "✅ Marked Present",
      message: (roomName: string, teacherName: string) =>
        `You have been marked present in ${roomName} by ${teacherName}.`,
    },
    markedAbsent: {
      title: "❌ Marked Absent",
      message: (roomName: string, teacherName: string) =>
        `You have been marked absent in ${roomName} by ${teacherName}. If this is incorrect, please contact your teacher.`,
    },
    markedLate: {
      title: "⏰ Marked Late",
      message: (roomName: string, teacherName: string) =>
        `You have been marked late in ${roomName} by ${teacherName}.`,
    },
    markedExcused: {
      title: "✅ Marked Excused",
      message: (roomName: string, teacherName: string) =>
        `You have been marked excused in ${roomName} by ${teacherName}.`,
    },

    // Admin notifications
    markedPresentByAdmin: {
      title: "✅ Marked Present by Admin",
      message: (roomName: string, adminName: string) =>
        `You have been marked present in ${roomName} by ${adminName}.`,
    },
    markedAbsentByAdmin: {
      title: "❌ Marked Absent by Admin",
      message: (roomName: string, adminName: string) =>
        `You have been marked absent in ${roomName} by ${adminName}. If this is incorrect, please contact your administrator.`,
    },
    markedLateByAdmin: {
      title: "⏰ Marked Late by Admin",
      message: (roomName: string, adminName: string) =>
        `You have been marked late in ${roomName} by ${adminName}.`,
    },
    markedExcusedByAdmin: {
      title: "✅ Marked Excused by Admin",
      message: (roomName: string, adminName: string) =>
        `You have been marked excused in ${roomName} by ${adminName}.`,
    },

    // Excuse notifications
    excuseSubmitted: {
      title: "📝 Excuse Request Submitted",
      message: (startDate: string, endDate: string, reason: string) =>
        `Your excuse request for ${startDate} to ${endDate} has been submitted. Reason: ${reason}. You will be notified when it's reviewed.`,
    },
    excuseApproved: {
      title: "✅ Excuse Request Approved",
      message: (startDate: string, endDate: string, reason: string) =>
        `Your excuse request for ${startDate} to ${endDate} has been approved. Reason: ${reason}.`,
    },
    excuseRejected: {
      title: "❌ Excuse Request Rejected",
      message: (startDate: string, endDate: string, reason: string) =>
        `Your excuse request for ${startDate} to ${endDate} has been rejected. Reason: ${reason}. Please contact your teacher for more information.`,
    },
    excuseExpired: {
      title: "⏰ Excuse Expired",
      message: (startDate: string, endDate: string) =>
        `Your approved excuse for ${startDate} to ${endDate} has expired. Your attendance status has been updated accordingly.`,
    },

    // Location and security alerts
    locationAlert: {
      title: "📍 Location Alert",
      message: (distance: number, roomName: string) =>
        `You are ${distance}m away from ${roomName}. Please move closer to verify your attendance.`,
    },
    securityAlert: {
      title: "🔒 Security Alert",
      message: (alertType: string, details: string) =>
        `Security alert: ${alertType}. ${details}`,
    },

    // System notifications
    systemMaintenance: {
      title: "🔧 System Maintenance",
      message: (startTime: string, endTime: string) =>
        `System maintenance is scheduled from ${startTime} to ${endTime}. Some features may be unavailable during this time.`,
    },
    accountUpdate: {
      title: "👤 Account Updated",
      message: (updateType: string) =>
        `Your account has been updated: ${updateType}.`,
    },
    passwordChanged: {
      title: "🔑 Password Changed",
      message: () =>
        `Your password has been successfully changed. If you didn't make this change, please contact support immediately.`,
    },
  },
  tr: {
    // Attendance notifications
    attendanceReminder: {
      title: "⏰ Devam Hatırlatması",
      message: (teacherName: string, roomName: string) =>
        `Merhaba! Öğretmeniniz ${teacherName}, ${roomName} odasında devamınızı doğrulamanız için bir hatırlatma gönderdi. Lütfen QR kodunu tarayın veya PIN kodunuzu kullanarak giriş yapın.`,
    },
    automatedAttendanceReminder: {
      title: "🔔 Devam Oturumu Yakında Bitiyor",
      message: (roomName: string, timeString: string) =>
        `Dikkat! ${roomName} için devam oturumu yakında sona erecek. Geç olarak işaretlenmemek için lütfen şimdi giriş yapın. Saat: ${timeString}`,
    },
    attendanceRecorded: {
      title: "✅ Devam Kaydedildi",
      message: (roomName: string, time: string) =>
        `${roomName} odasına ${time} saatinde başarıyla giriş yaptınız.`,
    },
    markedPresent: {
      title: "✅ Mevcut Olarak İşaretlendi",
      message: (roomName: string, teacherName: string) =>
        `${teacherName} tarafından ${roomName} odasında mevcut olarak işaretlendiniz.`,
    },
    markedAbsent: {
      title: "❌ Devamsız Olarak İşaretlendi",
      message: (roomName: string, teacherName: string) =>
        `${teacherName} tarafından ${roomName} odasında devamsız olarak işaretlendiniz. Bu yanlışsa, lütfen öğretmeninizle iletişime geçin.`,
    },
    markedLate: {
      title: "⏰ Geç Kaldı Olarak İşaretlendi",
      message: (roomName: string, teacherName: string) =>
        `${teacherName} tarafından ${roomName} odasında geç kaldı olarak işaretlendiniz.`,
    },
    markedExcused: {
      title: "✅ Mazeret Olarak İşaretlendi",
      message: (roomName: string, teacherName: string) =>
        `${teacherName} tarafından ${roomName} odasında mazeret olarak işaretlendiniz.`,
    },

    // Admin notifications
    markedPresentByAdmin: {
      title: "✅ Yönetici Tarafından Mevcut İşaretlendi",
      message: (roomName: string, adminName: string) =>
        `${adminName} tarafından ${roomName} odasında mevcut olarak işaretlendiniz.`,
    },
    markedAbsentByAdmin: {
      title: "❌ Yönetici Tarafından Devamsız İşaretlendi",
      message: (roomName: string, adminName: string) =>
        `${adminName} tarafından ${roomName} odasında devamsız olarak işaretlendiniz. Bu yanlışsa, lütfen yöneticinizle iletişime geçin.`,
    },
    markedLateByAdmin: {
      title: "⏰ Yönetici Tarafından Geç Kaldı İşaretlendi",
      message: (roomName: string, adminName: string) =>
        `${adminName} tarafından ${roomName} odasında geç kaldı olarak işaretlendiniz.`,
    },
    markedExcusedByAdmin: {
      title: "✅ Yönetici Tarafından Mazeret İşaretlendi",
      message: (roomName: string, adminName: string) =>
        `${adminName} tarafından ${roomName} odasında mazeret olarak işaretlendiniz.`,
    },

    // Excuse notifications
    excuseSubmitted: {
      title: "📝 Mazeret Talebi Gönderildi",
      message: (startDate: string, endDate: string, reason: string) =>
        `${startDate} - ${endDate} tarihleri için mazeret talebiniz gönderildi. Sebep: ${reason}. İncelendiğinde bilgilendirileceksiniz.`,
    },
    excuseApproved: {
      title: "✅ Mazeret Talebi Onaylandı",
      message: (startDate: string, endDate: string, reason: string) =>
        `${startDate} - ${endDate} tarihleri için mazeret talebiniz onaylandı. Sebep: ${reason}.`,
    },
    excuseRejected: {
      title: "❌ Mazeret Talebi Reddedildi",
      message: (startDate: string, endDate: string, reason: string) =>
        `${startDate} - ${endDate} tarihleri için mazeret talebiniz reddedildi. Sebep: ${reason}. Daha fazla bilgi için öğretmeninizle iletişime geçin.`,
    },
    excuseExpired: {
      title: "⏰ Mazeret Süresi Doldu",
      message: (startDate: string, endDate: string) =>
        `${startDate} - ${endDate} tarihleri için onaylanan mazeretinizin süresi doldu. Devam durumunuz buna göre güncellendi.`,
    },

    // Location and security alerts
    locationAlert: {
      title: "📍 Konum Uyarısı",
      message: (distance: number, roomName: string) =>
        `${roomName} odasından ${distance}m uzaktasınız. Devamınızı doğrulamak için lütfen daha yakına gelin.`,
    },
    securityAlert: {
      title: "🔒 Güvenlik Uyarısı",
      message: (alertType: string, details: string) =>
        `Güvenlik uyarısı: ${alertType}. ${details}`,
    },

    // System notifications
    systemMaintenance: {
      title: "🔧 Sistem Bakımı",
      message: (startTime: string, endTime: string) =>
        `${startTime} - ${endTime} saatleri arasında sistem bakımı planlanmıştır. Bu süre zarfında bazı özellikler kullanılamayabilir.`,
    },
    accountUpdate: {
      title: "👤 Hesap Güncellendi",
      message: (updateType: string) =>
        `Hesabınız güncellendi: ${updateType}.`,
    },
    passwordChanged: {
      title: "🔑 Şifre Değiştirildi",
      message: () =>
        `Şifreniz başarıyla değiştirildi. Bu değişikliği siz yapmadıysanız, lütfen hemen destekle iletişime geçin.`,
    },
  },
};

// Teacher notification templates
const TEACHER_NOTIFICATION_TEMPLATES = {
  en: {
    // Excuse notifications for teachers
    newExcuseRequest: {
      title: "📝 New Excuse Request",
      message: (studentName: string, roomName: string, startDate: string, endDate: string, reason: string) =>
        `${studentName} has submitted an excuse request for ${roomName} from ${startDate} to ${endDate}. Reason: ${reason}`,
    },
    excuseStatusChanged: {
      title: "📝 Excuse Status Updated",
      message: (studentName: string, status: string, startDate: string, endDate: string) =>
        `${studentName}'s excuse request for ${startDate} to ${endDate} has been ${status}.`,
    },

    // Location alerts for teachers
    studentLocationAlert: {
      title: "📍 Student Location Alert",
      message: (studentName: string, distance: number, roomName: string) =>
        `${studentName} is ${distance}m away from ${roomName} while trying to check in.`,
    },

    // Attendance alerts for teachers
    attendanceAlert: {
      title: "⚠️ Attendance Alert",
      message: (studentName: string, alertType: string, details: string) =>
        `Attendance alert for ${studentName}: ${alertType}. ${details}`,
    },

    // System alerts for teachers
    systemAlert: {
      title: "🔔 System Alert",
      message: (alertType: string, details: string) =>
        `System alert: ${alertType}. ${details}`,
    },
  },
  tr: {
    // Excuse notifications for teachers
    newExcuseRequest: {
      title: "📝 Yeni Mazeret Talebi",
      message: (studentName: string, roomName: string, startDate: string, endDate: string, reason: string) =>
        `${studentName}, ${roomName} odası için ${startDate} - ${endDate} tarihleri arasında mazeret talebi gönderdi. Sebep: ${reason}`,
    },
    excuseStatusChanged: {
      title: "📝 Mazeret Durumu Güncellendi",
      message: (studentName: string, status: string, startDate: string, endDate: string) =>
        `${studentName}'in ${startDate} - ${endDate} tarihleri için mazeret talebi ${status}.`,
    },

    // Location alerts for teachers
    studentLocationAlert: {
      title: "📍 Öğrenci Konum Uyarısı",
      message: (studentName: string, distance: number, roomName: string) =>
        `${studentName}, ${roomName} odasına giriş yapmaya çalışırken ${distance}m uzakta.`,
    },

    // Attendance alerts for teachers
    attendanceAlert: {
      title: "⚠️ Devam Uyarısı",
      message: (studentName: string, alertType: string, details: string) =>
        `${studentName} için devam uyarısı: ${alertType}. ${details}`,
    },

    // System alerts for teachers
    systemAlert: {
      title: "🔔 Sistem Uyarısı",
      message: (alertType: string, details: string) =>
        `Sistem uyarısı: ${alertType}. ${details}`,
    },
  },
};

// Admin notification templates
const ADMIN_NOTIFICATION_TEMPLATES = {
  en: {
    // Location alerts for admins
    studentLocationAlert: {
      title: "📍 Student Location Alert",
      message: (studentName: string, distance: number, roomName: string) =>
        `${studentName} is ${distance}m away from ${roomName} while trying to check in.`,
    },

    // Attendance alerts for admins
    attendanceAlert: {
      title: "⚠️ Attendance Alert",
      message: (studentName: string, alertType: string, details: string) =>
        `Attendance alert for ${studentName}: ${alertType}. ${details}`,
    },

    // Security alerts for admins
    securityAlert: {
      title: "🚨 Security Alert",
      message: (studentName: string, alertType: string, details: string) =>
        `Security alert for ${studentName}: ${alertType}. ${details}`,
    },

    // System alerts for admins
    systemAlert: {
      title: "⚙️ System Alert",
      message: (details: string) => `System alert: ${details}`,
    },
  },
  tr: {
    // Location alerts for admins
    studentLocationAlert: {
      title: "📍 Öğrenci Konum Uyarısı",
      message: (studentName: string, distance: number, roomName: string) =>
        `${studentName}, ${roomName} odasına giriş yapmaya çalışırken ${distance}m uzakta.`,
    },

    // Attendance alerts for admins
    attendanceAlert: {
      title: "⚠️ Devam Uyarısı",
      message: (studentName: string, alertType: string, details: string) =>
        `${studentName} için devam uyarısı: ${alertType}. ${details}`,
    },

    // Security alerts for admins
    securityAlert: {
      title: "🚨 Güvenlik Uyarısı",
      message: (studentName: string, alertType: string, details: string) =>
        `${studentName} için güvenlik uyarısı: ${alertType}. ${details}`,
    },

    // System alerts for admins
    systemAlert: {
      title: "⚙️ Sistem Uyarısı",
      message: (details: string) => `Sistem uyarısı: ${details}`,
    },
  },
};

/**
 * Get user's language preference from their profile
 */
async function getUserLanguage(userId: string): Promise<'en' | 'tr'> {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("preferred_language")
      .eq("user_id", userId)
      .single();

    if (error || !data) {
      console.warn(`Could not get language preference for user ${userId}, defaulting to English`);
      return 'en';
    }

    // Return the language preference, defaulting to 'en' if not set or invalid
    return data.preferred_language === 'tr' ? 'tr' : 'en';
  } catch (error) {
    console.error("Error fetching user language preference:", error);
    return 'en';
  }
}

/**
 * Get student's language preference from their profile (alias for backward compatibility)
 */
async function getStudentLanguage(studentId: string): Promise<'en' | 'tr'> {
  return getUserLanguage(studentId);
}

/**
 * Create a localized notification for a student
 */
export async function createLocalizedNotification({
  studentId,
  teacherId,
  type,
  templateKey,
  templateParams = [],
  metadata = {},
  roomNumber,
}: {
  studentId: string;
  teacherId?: string;
  type: 'attendance' | 'absence' | 'system' | 'distance_alert' | 'late' | 'excused';
  templateKey: keyof typeof NOTIFICATION_TEMPLATES.en;
  templateParams?: any[];
  metadata?: Record<string, any>;
  roomNumber?: string;
}) {
  try {
    // Get student's language preference
    const language = await getStudentLanguage(studentId);
    
    // Get the appropriate template
    const template = NOTIFICATION_TEMPLATES[language][templateKey];
    
    if (!template) {
      console.error(`Template ${templateKey} not found for language ${language}`);
      return { success: false, error: 'Template not found' };
    }

    // Generate localized title and message
    const title = template.title;
    const message = typeof template.message === 'function' 
      ? template.message(...templateParams)
      : template.message;

    // Create the notification
    const { data, error } = await supabase
      .from("notifications")
      .insert({
        student_id: studentId,
        teacher_id: teacherId || null,
        title,
        message,
        type,
        read: false,
        timestamp: new Date().toISOString(),
        metadata: JSON.stringify(metadata),
        room_number: roomNumber || null,
      })
      .select();

    if (error) {
      console.error("Error creating localized notification:", error);
      return { success: false, error };
    }

    console.log(`Created localized notification in ${language} for student ${studentId}`);

    // Push notifications will be triggered automatically when students receive the notification
    // via the real-time subscription in Student.tsx

    return { success: true, data };

  } catch (error) {
    console.error("Error in createLocalizedNotification:", error);
    return { success: false, error };
  }
}

/**
 * Create multiple localized notifications for different students
 */
export async function createBulkLocalizedNotifications({
  students,
  teacherId,
  type,
  templateKey,
  getTemplateParams,
  getMetadata,
  getRoomNumber,
}: {
  students: Array<{ id: string; name: string; [key: string]: any }>;
  teacherId?: string;
  type: 'attendance' | 'absence' | 'system' | 'distance_alert' | 'late' | 'excused';
  templateKey: keyof typeof NOTIFICATION_TEMPLATES.en;
  getTemplateParams: (student: any) => any[];
  getMetadata?: (student: any) => Record<string, any>;
  getRoomNumber?: (student: any) => string;
}) {
  const results = await Promise.allSettled(
    students.map(async (student) => {
      const templateParams = getTemplateParams(student);
      const metadata = getMetadata ? getMetadata(student) : {};
      const roomNumber = getRoomNumber ? getRoomNumber(student) : undefined;

      return createLocalizedNotification({
        studentId: student.id,
        teacherId,
        type,
        templateKey,
        templateParams,
        metadata,
        roomNumber,
      });
    })
  );

  const successful = results.filter(result => 
    result.status === 'fulfilled' && result.value.success
  ).length;

  const failed = results.length - successful;

  console.log(`Bulk notification creation: ${successful} successful, ${failed} failed`);
  
  return {
    successful,
    failed,
    results,
  };
}

/**
 * Create a localized notification for a teacher
 */
export async function createLocalizedTeacherNotification({
  teacherId,
  studentId,
  type,
  templateKey,
  templateParams = [],
  metadata = {},
  roomNumber,
}: {
  teacherId: string;
  studentId?: string;
  type: 'system' | 'distance_alert' | 'attendance_alert' | 'excuse_alert';
  templateKey: keyof typeof TEACHER_NOTIFICATION_TEMPLATES.en;
  templateParams?: any[];
  metadata?: Record<string, any>;
  roomNumber?: string;
}) {
  try {
    // Get teacher's language preference
    const language = await getUserLanguage(teacherId);

    // Get the appropriate template
    const template = TEACHER_NOTIFICATION_TEMPLATES[language][templateKey];

    if (!template) {
      console.error(`Teacher template ${templateKey} not found for language ${language}`);
      return { success: false, error: 'Template not found' };
    }

    // Generate localized title and message
    const title = template.title;
    const message = typeof template.message === 'function'
      ? template.message(...templateParams)
      : template.message;

    // Create the notification
    const { data, error } = await supabase
      .from("notifications")
      .insert({
        teacher_id: teacherId,
        student_id: studentId || null,
        title,
        message,
        type,
        read: false,
        timestamp: new Date().toISOString(),
        metadata: JSON.stringify(metadata),
        room_number: roomNumber || null,
      })
      .select();

    if (error) {
      console.error("Error creating localized teacher notification:", error);
      return { success: false, error };
    }

    console.log(`Created localized teacher notification in ${language} for teacher ${teacherId}`);
    return { success: true, data };

  } catch (error) {
    console.error("Error in createLocalizedTeacherNotification:", error);
    return { success: false, error };
  }
}

/**
 * Create a localized notification for a school admin
 */
export async function createLocalizedAdminNotification({
  adminId,
  studentId,
  type,
  templateKey,
  templateParams = [],
  metadata = {},
  roomNumber,
}: {
  adminId: string;
  studentId?: string;
  type: 'system' | 'distance_alert' | 'attendance_alert' | 'security_alert';
  templateKey: keyof typeof ADMIN_NOTIFICATION_TEMPLATES.en;
  templateParams?: any[];
  metadata?: Record<string, any>;
  roomNumber?: string;
}) {
  try {
    // Get admin's language preference
    const language = await getUserLanguage(adminId);

    // Get the appropriate template
    const template = ADMIN_NOTIFICATION_TEMPLATES[language][templateKey];

    if (!template) {
      console.error(`Admin template ${templateKey} not found for language ${language}`);
      return { success: false, error: 'Template not found' };
    }

    // Generate localized title and message
    const title = template.title;
    const message = typeof template.message === 'function'
      ? template.message(...templateParams)
      : template.message;

    // Get admin's school_id to ensure proper school isolation
    const { data: adminProfile, error: adminError } = await supabase
      .from("profiles")
      .select("school_id")
      .eq("user_id", adminId)
      .single();

    if (adminError || !adminProfile?.school_id) {
      console.error("Error getting admin school_id:", adminError);
      return { success: false, error: 'Admin school not found' };
    }

    // Create the notification (use teacher_id field to store admin_id for proper filtering)
    const { data, error } = await supabase
      .from("notifications")
      .insert({
        teacher_id: adminId, // Store admin ID in teacher_id field for filtering
        student_id: studentId || null,
        title,
        message,
        type,
        read: false,
        timestamp: new Date().toISOString(),
        metadata: JSON.stringify(metadata),
        room_number: roomNumber || null,
      })
      .select();

    if (error) {
      console.error("Error creating localized admin notification:", error);
      return { success: false, error };
    }

    console.log(`Created localized admin notification in ${language} for admin ${adminId}`);
    return { success: true, data };

  } catch (error) {
    console.error("Error in createLocalizedAdminNotification:", error);
    return { success: false, error };
  }
}
