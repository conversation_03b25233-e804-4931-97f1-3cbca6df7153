import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/context/AuthContext";
import { submitFeedback, FeedbackSubmissionInput } from "@/lib/api/feedback";
import { toast } from "sonner";
import { MessageSquare, Send, X, CheckCircle, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

// Define the form schema with Zod
const feedbackFormSchema = z.object({
  name: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  message: z.string().min(5, {
    message: "Message must be at least 5 characters.",
  }),
});

type FeedbackFormValues = z.infer<typeof feedbackFormSchema>;

interface FeedbackFormProps {
  variant?: "button" | "fab";
  className?: string;
}

export default function FeedbackForm({
  variant = "button",
  className,
}: FeedbackFormProps) {
  const { t } = useTranslation();
  const { profile } = useAuth();
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackFormSchema),
    defaultValues: {
      name: profile?.name || "",
      email: profile?.email || "",
      phone: "",
      message: "",
    },
  });

  // Reset success state when dialog closes
  useEffect(() => {
    if (!open) {
      setTimeout(() => {
        setIsSuccess(false);
      }, 300);
    }
  }, [open]);

  // Handle form submission
  const onSubmit = async (values: FeedbackFormValues) => {
    setIsSubmitting(true);
    try {
      const feedback: FeedbackSubmissionInput = {
        name: values.name,
        email: values.email,
        phone: values.phone,
        message: values.message,
      };

      const result = await submitFeedback(feedback, profile);

      if (result) {
        // Show success state in the form
        setIsSuccess(true);

        // Show toast notification
        toast.success(t("feedback.feedback"), {
          description: t("feedback.feedbackSubmitted"),
        });

        // Reset form after a delay to show success state
        setTimeout(() => {
          form.reset();
          setOpen(false);
        }, 1500);
      } else {
        toast.error(t("common.error"), {
          description: t("feedback.feedbackSubmissionError"),
        });
      }
    } catch (error) {
      console.error("Error submitting feedback:", error);
      toast.error(t("common.error"), {
        description: t("feedback.unexpectedError"),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {variant === "button" ? (
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn("gap-2", className)}
          >
            <MessageSquare className="h-4 w-4" />
            {t("feedback.feedback")}
          </Button>
        </DialogTrigger>
      ) : (
        <DialogTrigger asChild>
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: "spring", stiffness: 260, damping: 20 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="fixed z-50"
            style={{ bottom: "1.5rem", right: "1.5rem" }}
          >
            <Button
              size="icon"
              className={cn(
                "h-14 w-14 rounded-full shadow-lg bg-primary hover:bg-primary/90",
                className
              )}
            >
              <MessageSquare className="h-6 w-6" />
            </Button>
          </motion.div>
        </DialogTrigger>
      )}

      <DialogContent className="sm:max-w-[425px]">
        <AnimatePresence mode="wait">
          {isSuccess ? (
            <motion.div
              key="success"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="flex flex-col items-center justify-center py-8 text-center"
            >
              <div className="rounded-full bg-green-100 p-3 mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">
                {t("feedback.thankYou")}
              </h3>
              <p className="text-muted-foreground mb-6">
                {t("feedback.feedbackSubmitted")}
              </p>
              <Button onClick={() => setOpen(false)}>
                {t("common.close")}
              </Button>
            </motion.div>
          ) : (
            <motion.div
              key="form"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <DialogHeader>
                <DialogTitle>{t("feedback.sendFeedback")}</DialogTitle>
                <DialogDescription>
                  {t("feedback.feedbackDescription")}
                </DialogDescription>
              </DialogHeader>

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4 mt-4"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("common.name")} ({t("common.optional")})
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("feedback.yourName")}
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("common.email")} ({t("common.optional")})
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("feedback.yourEmail")}
                            type="email"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormDescription>
                          {t("feedback.emailDescription")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("common.phone")} ({t("common.optional")})
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("feedback.yourPhone")}
                            type="tel"
                            {...field}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("feedback.message")} *</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder={t("feedback.yourFeedback")}
                            className="min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter className="gap-2 sm:gap-0 mt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setOpen(false)}
                      disabled={isSubmitting}
                    >
                      <X className="mr-2 h-4 w-4" />
                      {t("common.cancel")}
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {t("feedback.submitting")}
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          {t("feedback.submitFeedback")}
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
}
