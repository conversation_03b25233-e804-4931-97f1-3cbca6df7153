-- First, drop all existing policies on profiles table
DROP POLICY IF EXISTS "Users can manage their own profile" ON profiles;
DROP POLICY IF EXISTS "Teachers can view student profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can create profiles" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can update any profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can delete profiles" ON profiles;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Allow reading profiles for alerts" ON profiles;

-- Re-enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create a simple policy for users to access their own profile
CREATE POLICY "Users can manage their own profile"
ON profiles
FOR ALL
TO authenticated
USING (
  -- Users can access their own profile
  user_id = auth.uid()
)
WITH CHECK (
  -- Users can modify their own profile
  user_id = auth.uid()
);

-- Create a policy for teachers to view student profiles in their rooms
CREATE POLICY "Teachers can view student profiles"
ON profiles
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE auth.users.id = auth.uid()
    AND (auth.users.raw_app_meta_data->>'role')::text = 'teacher'
  )
  AND
  -- Check if the profile being accessed belongs to a student
  role = 'student'
);

-- Create separate policies for admin operations
-- Admin read policy
CREATE POLICY "Admins can view all profiles"
ON profiles
FOR SELECT
TO authenticated
USING ((auth.jwt()->>'role')::text = 'admin');

-- Admin insert policy
CREATE POLICY "Admins can create profiles"
ON profiles
FOR INSERT
TO authenticated
WITH CHECK ((auth.jwt()->>'role')::text = 'admin');

-- Admin update policy for student profiles
CREATE POLICY "Admins can update student profiles"
ON profiles
FOR UPDATE
TO authenticated
USING (
  -- Check if the current user is an admin
  (auth.jwt()->>'role')::text = 'admin'
  AND
  -- Check if the profile being updated belongs to a student
  role = 'student'
)
WITH CHECK (
  -- Check if the current user is an admin
  (auth.jwt()->>'role')::text = 'admin'
  AND
  -- Ensure we're updating a student profile
  role = 'student'
  AND
  -- Ensure required fields are not null
  name IS NOT NULL
  AND student_id IS NOT NULL
  AND course IS NOT NULL
  AND block_name IS NOT NULL
  AND room_number IS NOT NULL
);

-- Admin delete policy
CREATE POLICY "Admins can delete profiles"
ON profiles
FOR DELETE
TO authenticated
USING ((auth.jwt()->>'role')::text = 'admin');

-- Create a trigger function to validate profile updates
CREATE OR REPLACE FUNCTION validate_profile_update()
RETURNS TRIGGER AS $$
BEGIN
  -- For student profiles, ensure required fields are not null
  IF NEW.role = 'student' THEN
    IF NEW.name IS NULL OR 
       NEW.student_id IS NULL OR 
       NEW.course IS NULL OR 
       NEW.block_name IS NULL OR 
       NEW.room_number IS NULL THEN
      RAISE EXCEPTION 'Required fields cannot be null for student profiles';
    END IF;
  END IF;
  
  -- Prevent modification of user_id and role by non-admins
  IF (auth.jwt()->>'role') != 'admin' THEN
    IF NEW.user_id != OLD.user_id OR NEW.role != OLD.role THEN
      RAISE EXCEPTION 'Cannot modify user_id or role';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS validate_profile_update_trigger ON profiles;
CREATE TRIGGER validate_profile_update_trigger
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION validate_profile_update();

-- Grant necessary permissions
GRANT ALL ON profiles TO authenticated; 