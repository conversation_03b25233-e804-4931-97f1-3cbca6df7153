-- Create alerts table
CREATE TABLE IF NOT EXISTS attendance_alerts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES auth.users(id) NOT NULL,
    room_id UUID REFERENCES rooms(id) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    alert_type TEXT NOT NULL,
    distance_meters FLOAT,
    student_location JSONB,
    room_location JSONB,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'dismissed')),
    reviewed_by UUID REFERENCES auth.users(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes if they don't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_attendance_alerts_student_id') THEN
        CREATE INDEX idx_attendance_alerts_student_id ON attendance_alerts(student_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_attendance_alerts_room_id') THEN
        CREATE INDEX idx_attendance_alerts_room_id ON attendance_alerts(room_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_attendance_alerts_status') THEN
        CREATE INDEX idx_attendance_alerts_status ON attendance_alerts(status);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_attendance_alerts_timestamp') THEN
        CREATE INDEX idx_attendance_alerts_timestamp ON attendance_alerts(timestamp);
    END IF;
END $$;

-- Enable RLS
ALTER TABLE attendance_alerts ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Teachers can view alerts for their rooms" ON attendance_alerts;
DROP POLICY IF EXISTS "Admins can view all alerts" ON attendance_alerts;
DROP POLICY IF EXISTS "Teachers can update alerts for their rooms" ON attendance_alerts;
DROP POLICY IF EXISTS "Admins can update all alerts" ON attendance_alerts;

-- Allow teachers to view alerts for their rooms
CREATE POLICY "Teachers can view alerts for their rooms"
    ON attendance_alerts
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM rooms 
            WHERE rooms.id = attendance_alerts.room_id
            AND rooms.teacher_id = (
                SELECT id FROM profiles 
                WHERE user_id = auth.uid()
                AND role = 'teacher'
            )
        )
    );

-- Allow admins to view all alerts
CREATE POLICY "Admins can view all alerts"
    ON attendance_alerts
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid()
            AND role = 'admin'
        )
    );

-- Allow teachers to update alerts for their rooms
CREATE POLICY "Teachers can update alerts for their rooms"
    ON attendance_alerts
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM rooms 
            WHERE rooms.id = attendance_alerts.room_id
            AND rooms.teacher_id = (
                SELECT id FROM profiles 
                WHERE user_id = auth.uid()
                AND role = 'teacher'
            )
        )
    );

-- Allow admins to update all alerts
CREATE POLICY "Admins can update all alerts"
    ON attendance_alerts
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid()
            AND role = 'admin'
        )
    );

-- Grant necessary permissions
GRANT ALL ON attendance_alerts TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Create policy to allow reading profiles for alert details
DROP POLICY IF EXISTS "Allow reading profiles for alerts" ON profiles;
CREATE POLICY "Allow reading profiles for alerts"
    ON profiles
    FOR SELECT
    TO authenticated
    USING (
        -- Allow reading if the user is an admin
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE user_id = auth.uid() 
            AND role = 'admin'
        )
        OR
        -- Allow reading if the user is a teacher and the profile is for their student
        EXISTS (
            SELECT 1 FROM rooms 
            WHERE teacher_id = (
                SELECT id FROM profiles 
                WHERE user_id = auth.uid()
                AND role = 'teacher'
            )
            AND EXISTS (
                SELECT 1 FROM attendance_alerts 
                WHERE room_id = rooms.id 
                AND student_id = profiles.user_id
            )
        )
    ); 