/**
 * School and Location Types
 * Type definitions for schools, blocks, rooms, and location-related entities
 */

export interface School {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  invitationCode?: string;
  isActive?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Block {
  id: string;
  name: string;
  school_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Room {
  id: string;
  name: string;
  building?: string;
  block_id: string;
  floor: number;
  capacity: number;
  teacher_id: string;
  current_qr_code: string | null;
  qr_expiry: string | null;
  school_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: string;
}

export interface LocationSettings {
  enabled: boolean;
  radius: number; // meters
  strictMode: boolean;
  allowedLocations: Location[];
}

export interface QRCodeData {
  roomId: string;
  timestamp: string;
  expiresAt: string;
  signature: string;
  schoolId: string;
}

export interface QRSecuritySettings {
  enabled: boolean;
  expirationMinutes: number;
  locationVerification: boolean;
  deviceFingerprinting: boolean;
  maxScansPerCode: number;
}

export interface SchoolSettings {
  id: string;
  school_id: string;
  attendance_start_time?: string;
  attendance_end_time?: string;
  late_threshold_minutes?: number;
  excuse_advance_days?: number;
  excuse_max_duration_days?: number;
  location_verification?: LocationSettings;
  qr_security?: QRSecuritySettings;
  notification_settings?: NotificationSettings;
  api_settings?: APISettings;
  created_at?: string;
  updated_at?: string;
}

export interface NotificationSettings {
  email_enabled: boolean;
  sms_enabled: boolean;
  push_enabled: boolean;
  parent_notifications: boolean;
  reminder_notifications: boolean;
  absence_notifications: boolean;
}

export interface APISettings {
  sendgrid_api_key?: string;
  twilio_account_sid?: string;
  twilio_auth_token?: string;
  twilio_phone_number?: string;
}

export interface SocialMediaSettings {
  facebook_page_url?: string;
  facebook_embed_code?: string;
  twitter_handle?: string;
  instagram_handle?: string;
  youtube_channel?: string;
  enabled: boolean;
}
