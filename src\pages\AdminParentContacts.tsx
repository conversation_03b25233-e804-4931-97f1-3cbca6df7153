import { useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { useTab } from "@/context/TabContext";
import ParentContactsManager from "@/components/admin/ParentContactsManager";
import NotificationServiceSettings from "@/components/admin/NotificationServiceSettings";
import ParentNotificationSettings from "@/components/admin/ParentNotificationSettings";
import { <PERSON>bs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Bell, Settings, Info, Mail, MessageSquare } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";

export default function AdminParentContacts() {
  const { profile } = useAuth();
  const { activeTab, setActiveTab } = useTab();
  const [showInfo, setShowInfo] = useState(true);
  const { t } = useTranslation();

  if (!profile || profile.role !== "admin") {
    return (
      <div className="flex items-center justify-center h-screen">
        <p>You do not have permission to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t("admin.parentNotifications.title")}
          </h1>
          <p className="text-muted-foreground">
            {t("admin.parentNotifications.description")}
          </p>
        </div>
      </div>

      {showInfo && (
        <Alert className="bg-blue-50 border-blue-200 mb-4">
          <Info className="h-4 w-4 text-blue-600 flex-shrink-0" />
          <AlertTitle className="text-blue-800 text-sm sm:text-base font-medium">
            {t("admin.parentNotifications.aboutParentNotifications")}
          </AlertTitle>
          <AlertDescription className="text-blue-700 text-xs sm:text-sm">
            {t("admin.parentNotifications.aboutDescription")}
          </AlertDescription>
          <button
            className="absolute top-2 right-2 text-blue-600 hover:text-blue-800"
            onClick={() => setShowInfo(false)}
          >
            ×
          </button>
        </Alert>
      )}

      <Tabs defaultValue="contacts" className="space-y-4">
        <TabsList className="flex w-full overflow-x-auto scrollbar-hide gap-1 sm:gap-2">
          <TabsTrigger
            value="contacts"
            className="flex items-center justify-center whitespace-nowrap text-xs sm:text-sm py-2 px-2 sm:px-4 min-w-[100px] sm:min-w-0 flex-1"
          >
            <Bell className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
            <span className="truncate max-w-[80px] sm:max-w-none">
              {t("admin.parentNotifications.parentContacts")}
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="templates"
            className="flex items-center justify-center whitespace-nowrap text-xs sm:text-sm py-2 px-2 sm:px-4 min-w-[100px] sm:min-w-0 flex-1"
          >
            <MessageSquare className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
            <span className="truncate max-w-[80px] sm:max-w-none">
              {t("admin.parentNotifications.messageTemplates")}
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="services"
            className="flex items-center justify-center whitespace-nowrap text-xs sm:text-sm py-2 px-2 sm:px-4 min-w-[100px] sm:min-w-0 flex-1"
          >
            <Mail className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
            <span className="truncate max-w-[80px] sm:max-w-none">
              {t("admin.parentNotifications.emailSmsServices")}
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="contacts" className="space-y-4">
          <ParentContactsManager />
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <ParentNotificationSettings />
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <NotificationServiceSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
}
