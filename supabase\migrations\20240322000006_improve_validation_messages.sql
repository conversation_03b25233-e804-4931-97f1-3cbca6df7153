-- Drop the existing trigger and function
DROP TRIGGER IF EXISTS validate_profile_update_trigger ON profiles;
DROP FUNCTION IF EXISTS validate_profile_update;

-- Create a new validation function with better error messages
CREATE OR REPLACE FUNCTION validate_profile_update()
RETURNS TRIGGER AS $$
BEGIN
  -- For student profiles, only validate fields that are being updated
  IF NEW.role = 'student' THEN
    -- Check name if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.name IS DISTINCT FROM OLD.name THEN
      IF NEW.name IS NULL OR LENGTH(TRIM(NEW.name)) = 0 THEN
        RAISE EXCEPTION USING 
          message = 'Name cannot be empty for student profiles',
          detail = 'The name field must contain a non-empty string',
          hint = 'Please provide a valid name';
      END IF;
    END IF;
    
    -- Check student_id if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.student_id IS DISTINCT FROM OLD.student_id THEN
      IF NEW.student_id IS NULL OR LENGTH(TRIM(NEW.student_id)) = 0 THEN
        RAISE EXCEPTION USING 
          message = 'Student ID cannot be empty for student profiles',
          detail = 'The student_id field must contain a non-empty string',
          hint = 'Please provide a valid student ID';
      END IF;
    END IF;
    
    -- Check course if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.course IS DISTINCT FROM OLD.course THEN
      IF NEW.course IS NULL OR LENGTH(TRIM(NEW.course)) = 0 THEN
        RAISE EXCEPTION USING 
          message = 'Course cannot be empty for student profiles',
          detail = 'The course field must contain a non-empty string',
          hint = 'Please provide a valid course name';
      END IF;
    END IF;
    
    -- Check block_name if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.block_name IS DISTINCT FROM OLD.block_name THEN
      IF NEW.block_name IS NULL OR LENGTH(TRIM(NEW.block_name)) = 0 THEN
        RAISE EXCEPTION USING 
          message = 'Block name cannot be empty for student profiles',
          detail = 'The block_name field must contain a non-empty string',
          hint = 'Please provide a valid block or corridor name';
      END IF;
    END IF;
    
    -- Check room_number if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.room_number IS DISTINCT FROM OLD.room_number THEN
      IF NEW.room_number IS NULL OR LENGTH(TRIM(NEW.room_number)) = 0 THEN
        RAISE EXCEPTION USING 
          message = 'Room number cannot be empty for student profiles',
          detail = 'The room_number field must contain a non-empty string',
          hint = 'Please provide a valid room number';
      END IF;
    END IF;
  END IF;
  
  -- Prevent modification of user_id and role by non-admins
  IF (auth.jwt()->>'role') != 'admin' THEN
    IF NEW.user_id IS DISTINCT FROM OLD.user_id OR NEW.role IS DISTINCT FROM OLD.role THEN
      RAISE EXCEPTION USING 
        message = 'Only administrators can modify user_id or role',
        detail = 'Attempted to modify restricted fields',
        hint = 'Contact an administrator if you need to change these fields';
    END IF;
  END IF;
  
  -- Always set updated_at when any field changes
  NEW.updated_at = CURRENT_TIMESTAMP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER validate_profile_update_trigger
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION validate_profile_update(); 