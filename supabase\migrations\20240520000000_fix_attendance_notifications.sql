-- Create or replace function to create attendance notifications
CREATE OR REPLACE FUNCTION create_attendance_notification()
RETURNS TRIGGER AS $$
DECLARE
  student_name text;
  teacher_name text;
  room_name text;
  teacher_id uuid;
BEGIN
  -- Get student name
  SELECT name INTO student_name
  FROM profiles
  WHERE id::text = NEW.student_id::text;

  -- Get teacher name and room name
  SELECT p.name, r.name, r.teacher_id 
  INTO teacher_name, room_name, teacher_id
  FROM rooms r
  JOIN profiles p ON p.id::text = r.teacher_id::text
  WHERE r.id::text = NEW.room_id::text;

  -- Create notification
  INSERT INTO notifications (
    student_id,
    teacher_id,
    title,
    message,
    type,
    read,
    timestamp,
    metadata
  )
  VALUES (
    NEW.student_id,
    teacher_id,
    CASE NEW.status
      WHEN 'present' THEN '✅ Marked Present'
      WHEN 'absent' THEN '❌ Marked Absent'
      WHEN 'late' THEN '⏰ Marked Late'
      WHEN 'excused' THEN '📝 Marked Excused'
    END,
    CASE NEW.status
      WHEN 'present' THEN format('You were marked present in Room %s by %s', room_name, teacher_name)
      WHEN 'absent' THEN format('You were marked absent in Room %s by %s. If this is incorrect, please contact your teacher.', room_name, teacher_name)
      WHEN 'late' THEN format('You were marked late for Room %s by %s', room_name, teacher_name)
      WHEN 'excused' THEN format('Your absence in Room %s has been excused by %s', room_name, teacher_name)
    END,
    CASE NEW.status
      WHEN 'present' THEN 'attendance'
      WHEN 'absent' THEN 'absence'
      WHEN 'late' THEN 'late'
      WHEN 'excused' THEN 'excused'
    END,
    false,
    NOW(),
    jsonb_build_object(
      'attendance_id', NEW.id::text,
      'room_id', NEW.room_id::text,
      'status', NEW.status,
      'verification_method', NEW.verification_method,
      'teacher_id', teacher_id::text,
      'room_name', room_name
    )
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS attendance_notification_trigger ON attendance_records;

-- Create new trigger
CREATE TRIGGER attendance_notification_trigger
  AFTER INSERT OR UPDATE OF status
  ON attendance_records
  FOR EACH ROW
  WHEN (NEW.verification_method = 'manual')
  EXECUTE FUNCTION create_attendance_notification();

-- Create functions to enable/disable the trigger
CREATE OR REPLACE FUNCTION enable_attendance_notification_trigger()
RETURNS void AS $$
BEGIN
  ALTER TABLE attendance_records ENABLE TRIGGER attendance_notification_trigger;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION disable_attendance_notification_trigger()
RETURNS void AS $$
BEGIN
  ALTER TABLE attendance_records DISABLE TRIGGER attendance_notification_trigger;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION enable_attendance_notification_trigger() TO authenticated;
GRANT EXECUTE ON FUNCTION disable_attendance_notification_trigger() TO authenticated;
