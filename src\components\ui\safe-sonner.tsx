import { useTheme } from "next-themes";
import { Toaster as Son<PERSON> } from "sonner";
import { safeString } from "@/lib/utils/safe-toast";

type ToasterProps = React.ComponentPropsWithoutRef<typeof Sonner>;

/**
 * A safe wrapper for the Sonner toast component that ensures all toasts are properly rendered
 */
const SafeSonner = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      richColors
      closeButton
      position="top-right"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
        },
        // Transform all toast data to ensure it's safe
        transform: (toast) => {
          return {
            ...toast,
            title: safeString(toast.title),
            description: safeString(toast.description),
          };
        },
      }}
      {...props}
    />
  );
};

export { SafeSonner };
