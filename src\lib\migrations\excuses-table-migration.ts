import { supabase } from "@/lib/supabase";

/**
 * Checks if the excuses table exists and creates it if it doesn't
 * @returns Promise that resolves when the migration is complete
 */
export const ensureExcusesTableExists = async (): Promise<boolean> => {
  try {
    // First, check if the table exists by trying to select from it
    const { error } = await supabase.from("excuses").select("id").limit(1);

    // If there's no error, the table exists
    if (!error) {
      return true;
    }

    // If the error is not a "relation does not exist" error, something else is wrong
    if (
      !error.message.includes("relation") &&
      !error.message.includes("does not exist")
    ) {
      console.error("Unexpected error checking excuses table:", error);
      return false;
    }

    // Excuses table does not exist. Creating it...

    // Create the excuses table
    const { error: createError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create excuses table
        CREATE TABLE IF NOT EXISTS public.excuses (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
          block_id UUID REFERENCES blocks(id) ON DELETE SET NULL,
          room_id UUID REFERENCES rooms(id) ON DELETE SET NULL,
          reason TEXT NOT NULL,
          start_date DATE NOT NULL,
          end_date DATE NOT NULL,
          status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
          approved_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Create indexes for faster lookups
        CREATE INDEX IF NOT EXISTS idx_excuses_student_id ON public.excuses(student_id);
        CREATE INDEX IF NOT EXISTS idx_excuses_school_id ON public.excuses(school_id);
        CREATE INDEX IF NOT EXISTS idx_excuses_status ON public.excuses(status);
        CREATE INDEX IF NOT EXISTS idx_excuses_dates ON public.excuses(start_date, end_date);

        -- Enable RLS on excuses table
        ALTER TABLE public.excuses ENABLE ROW LEVEL SECURITY;

        -- Students can view their own excuses
        CREATE POLICY "Students can view their own excuses"
        ON public.excuses
        FOR SELECT
        TO authenticated
        USING (
          student_id = auth.uid() OR
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND (profiles.role = 'admin' OR profiles.role = 'teacher')
            AND profiles.school_id = excuses.school_id
          )
        );

        -- Students can create their own excuses
        CREATE POLICY "Students can create their own excuses"
        ON public.excuses
        FOR INSERT
        TO authenticated
        WITH CHECK (
          student_id = auth.uid()
        );

        -- Students can update their own pending excuses
        CREATE POLICY "Students can update their own pending excuses"
        ON public.excuses
        FOR UPDATE
        TO authenticated
        USING (
          student_id = auth.uid() AND
          status = 'pending'
        )
        WITH CHECK (
          student_id = auth.uid() AND
          status = 'pending'
        );

        -- Teachers and admins can update excuses for their school
        CREATE POLICY "Teachers and admins can update excuses for their school"
        ON public.excuses
        FOR UPDATE
        TO authenticated
        USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND (profiles.role = 'admin' OR profiles.role = 'teacher')
            AND profiles.school_id = excuses.school_id
          )
        )
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND (profiles.role = 'admin' OR profiles.role = 'teacher')
            AND profiles.school_id = excuses.school_id
          )
        );

        -- Grant necessary permissions
        GRANT ALL ON public.excuses TO authenticated;
      `,
    });

    if (createError) {
      console.error("Error creating excuses table:", createError);
      return false;
    }

    // Excuses table created successfully
    return true;
  } catch (error) {
    console.error("Error in excuses table migration:", error);
    return false;
  }
};
