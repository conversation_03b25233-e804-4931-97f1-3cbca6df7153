import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';

interface QuickAction {
  id: string;
  label: string;
  message: string;
}

interface QuickActionsProps {
  actions: QuickAction[];
  onActionClick: (message: string) => void;
}

export default function QuickActions({ actions, onActionClick }: QuickActionsProps) {
  return (
    <div className="grid grid-cols-1 gap-2">
      {actions.map((action, index) => (
        <motion.div
          key={action.id}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1, duration: 0.3 }}
        >
          <Button
            onClick={() => onActionClick(action.message)}
            variant="outline"
            size="sm"
            className="w-full justify-start h-auto py-2 px-2 md:px-3 bg-white/5 border-white/20 text-white hover:bg-white/10 hover:border-[#EE0D09]/50 transition-all duration-200 text-xs"
          >
            <motion.span
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="text-left"
            >
              {action.label}
            </motion.span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}
