import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Supabase URL or service key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixNotificationsTable() {
  try {
    console.log('Starting notifications table fix...');
    
    // First, check if the execute_sql function exists
    console.log('Checking if execute_sql function exists...');
    
    const { data: functionExists, error: functionError } = await supabase
      .from('pg_proc')
      .select('*')
      .eq('proname', 'execute_sql')
      .maybeSingle();
    
    if (functionError) {
      console.log('Error checking for execute_sql function, creating it now...');
      
      // Create the execute_sql function
      const { data: createFunctionData, error: createFunctionError } = await supabase
        .rpc('exec_sql', {
          sql: `
            CREATE OR REPLACE FUNCTION public.execute_sql(sql text) 
            RETURNS JSONB 
            LANGUAGE plpgsql 
            SECURITY DEFINER 
            AS $$ 
            DECLARE 
              result JSONB; 
            BEGIN 
              EXECUTE sql INTO result; 
              RETURN result; 
            EXCEPTION 
              WHEN OTHERS THEN 
                RETURN jsonb_build_object('error', SQLERRM, 'detail', SQLSTATE); 
            END; 
            $$;
          `
        });
      
      if (createFunctionError) {
        console.error('Error creating execute_sql function:', createFunctionError);
        
        // Try direct database query
        const { data: directQueryData, error: directQueryError } = await supabase
          .from('_database')
          .select('*')
          .rpc('query', {
            query: `
              CREATE OR REPLACE FUNCTION public.execute_sql(sql text) 
              RETURNS JSONB 
              LANGUAGE plpgsql 
              SECURITY DEFINER 
              AS $$ 
              DECLARE 
                result JSONB; 
              BEGIN 
                EXECUTE sql INTO result; 
                RETURN result; 
              EXCEPTION 
                WHEN OTHERS THEN 
                  RETURN jsonb_build_object('error', SQLERRM, 'detail', SQLSTATE); 
              END; 
              $$;
            `
          });
        
        if (directQueryError) {
          console.error('Error creating function using direct query:', directQueryError);
          process.exit(1);
        }
      }
      
      console.log('execute_sql function created successfully');
    } else {
      console.log('execute_sql function already exists');
    }
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../supabase/migrations/20240514000001_fix_notifications_structure.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Running migration to fix notifications table...');
    
    const { data: migrationData, error: migrationError } = await supabase
      .rpc('execute_sql', { sql: migrationSql });
    
    if (migrationError) {
      console.error('Error running migration:', migrationError);
      process.exit(1);
    }
    
    console.log('Migration completed successfully');
    
    // Check the notifications table structure
    console.log('Checking notifications table structure...');
    
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('execute_sql', { 
        sql: `
          SELECT column_name, data_type, is_nullable 
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'notifications'
        ` 
      });
    
    if (tableError) {
      console.error('Error checking table structure:', tableError);
    } else {
      console.log('Notifications table columns:', tableInfo);
    }
    
    // Test creating a notification
    console.log('Testing notification creation...');
    
    // Get a student ID for testing
    const { data: students, error: studentError } = await supabase
      .from('profiles')
      .select('id')
      .eq('role', 'student')
      .limit(1);
    
    if (studentError) {
      console.error('Error fetching student for testing:', studentError);
    } else if (students && students.length > 0) {
      const studentId = students[0].id;
      
      // Create a test notification
      const { data: notification, error: notificationError } = await supabase
        .from('notifications')
        .insert({
          student_id: studentId,
          title: '🧪 Test Notification',
          message: 'This is a test notification to verify the table structure is correct.',
          type: 'system',
          read: false,
          timestamp: new Date().toISOString(),
          metadata: JSON.stringify({
            test: true,
            created_by: 'migration_script'
          })
        })
        .select();
      
      if (notificationError) {
        console.error('Error creating test notification:', notificationError);
      } else {
        console.log('Test notification created successfully:', notification);
      }
    }
    
    console.log('Notifications table fix completed');
    
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Execute the function
fixNotificationsTable();
