import { createClient } from '@supabase/supabase-js';

const supabaseUrl = "https://wclwxrilybnzkhvqzbmy.supabase.co";
const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MIARsz34RX0EftvwUkWIrEYQqE8VstxaCI31mjLhSHw";

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkStudentAccess() {
  console.log('Creating student account...');
  
  // Create student account
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email: '<EMAIL>',
    password: 'password123',
    options: {
      data: {
        role: 'student',
        name: 'Test Student'
      }
    }
  });

  if (authError) {
    if (authError.message === 'User already registered') {
      console.log('Student account already exists, signing in...');
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'password123'
      });

      if (signInError) {
        console.error('Error signing in:', signInError);
        return;
      }

      authData = signInData;
    } else {
      console.error('Error creating student account:', authError);
      return;
    }
  }

  console.log('Student authenticated:', authData);

  // Create or get student profile
  let profile;
  const { data: existingProfile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('user_id', authData.user.id)
    .single();

  if (profileError && profileError.code !== 'PGRST116') {
    console.error('Error checking profile:', profileError);
    return;
  }

  if (!existingProfile) {
    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        user_id: authData.user.id,
        name: 'Test Student',
        email: '<EMAIL>',
        role: 'student'
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating profile:', createError);
      return;
    }

    profile = newProfile;
  } else {
    profile = existingProfile;
  }

  console.log('Student profile:', profile);

  // Get all rooms
  const { data: rooms, error: roomsError } = await supabase
    .from('rooms')
    .select('*');

  if (roomsError) {
    console.error('Error getting rooms:', roomsError);
    return;
  }

  console.log('Available rooms:', rooms);

  // Try to get location for each room
  for (const room of rooms) {
    console.log(`\nChecking location for room ${room.id}...`);
    const { data: location, error: locationError } = await supabase
      .from('room_locations')
      .select('*')
      .eq('room_id', room.id)
      .single();

    if (locationError) {
      console.error('Error getting room location:', locationError);
      continue;
    }

    console.log('Room location:', location);
  }
}

checkStudentAccess().catch(console.error); 