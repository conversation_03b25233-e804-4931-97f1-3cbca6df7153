-- <PERSON>reate function to clean up expired excuses and revert attendance status
CREATE OR REPLACE FUNCTION cleanup_expired_excuses()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  expired_excuse RECORD;
  attendance_record RECORD;
  cleanup_count INTEGER := 0;
  current_timestamp TIMESTAMP WITH TIME ZONE := NOW();
  current_date DATE := CURRENT_DATE;
  current_time TIME := CURRENT_TIME;
BEGIN
  -- Log the start of cleanup
  RAISE NOTICE 'Starting excuse expiry cleanup at %', current_timestamp;
  
  -- Find all approved excuses that have expired
  -- An excuse is expired if:
  -- 1. Status is 'approved' 
  -- 2. Current date is after end_date, OR
  -- 3. Current date equals end_date AND current time is after end_time
  FOR expired_excuse IN
    SELECT e.*, p.name as student_name, r.name as room_name
    FROM excuses e
    JOIN profiles p ON e.student_id = p.id
    JOIN rooms r ON e.room_id = r.id
    WHERE e.status = 'approved'
    AND (
      -- Case 1: Current date is after the end date
      current_date > e.end_date
      OR
      -- Case 2: Current date equals end date and current time is after end time
      (current_date = e.end_date AND current_time > e.end_time)
    )
  LOOP
    RAISE NOTICE 'Processing expired excuse: ID=%, Student=%, Room=%, End Date=%, End Time=%', 
      expired_excuse.id, expired_excuse.student_name, expired_excuse.room_name, 
      expired_excuse.end_date, expired_excuse.end_time;
    
    -- Find all attendance records that were marked as 'excused' for this excuse
    -- and revert them back to 'absent'
    FOR attendance_record IN
      SELECT ar.*
      FROM attendance_records ar
      WHERE ar.student_id = expired_excuse.student_id
      AND ar.room_id = expired_excuse.room_id
      AND ar.status = 'excused'
      AND ar.verification_method = 'manual'
      AND ar.device_info LIKE '%ID: ' || expired_excuse.id || '%'
      AND DATE(ar.timestamp) BETWEEN expired_excuse.start_date AND expired_excuse.end_date
    LOOP
      -- Revert attendance status from 'excused' back to 'absent'
      UPDATE attendance_records
      SET 
        status = 'absent',
        updated_at = current_timestamp,
        device_info = COALESCE(device_info, '') || ' [Excuse expired: ' || current_timestamp || ']'
      WHERE id = attendance_record.id;
      
      RAISE NOTICE 'Reverted attendance record ID=% from excused to absent', attendance_record.id;
    END LOOP;
    
    -- Create a notification for the student about the expired excuse
    INSERT INTO notifications (
      type,
      title,
      message,
      student_id,
      read,
      created_at,
      metadata
    ) VALUES (
      'system',
      'Excuse Expired',
      'Your excuse for ' || expired_excuse.room_name || ' from ' || 
      expired_excuse.start_date || ' to ' || expired_excuse.end_date || ' has expired. ' ||
      'Your attendance status has been reverted to absent.',
      expired_excuse.student_id,
      false,
      current_timestamp,
      jsonb_build_object(
        'excuse_id', expired_excuse.id,
        'start_date', expired_excuse.start_date,
        'end_date', expired_excuse.end_date,
        'reason', expired_excuse.reason,
        'expired_at', current_timestamp
      )
    );
    
    -- Delete the expired excuse
    DELETE FROM excuses WHERE id = expired_excuse.id;
    
    cleanup_count := cleanup_count + 1;
    
    RAISE NOTICE 'Deleted expired excuse ID=% and created notification', expired_excuse.id;
  END LOOP;
  
  -- Also clean up rejected excuses that are older than 7 days
  -- This keeps the database clean and removes old rejected requests
  DELETE FROM excuses 
  WHERE status = 'rejected' 
  AND updated_at < (current_timestamp - INTERVAL '7 days');
  
  -- Log the completion
  RAISE NOTICE 'Excuse expiry cleanup completed. Processed % expired excuses', cleanup_count;
  
  RETURN cleanup_count;
END;
$$;

-- Create function to schedule excuse cleanup (can be called by cron or manually)
CREATE OR REPLACE FUNCTION schedule_excuse_cleanup()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  cleanup_result INTEGER;
BEGIN
  -- Run the cleanup
  SELECT cleanup_expired_excuses() INTO cleanup_result;
  
  -- Log the result
  INSERT INTO audit_logs (
    action,
    table_name,
    details,
    created_at
  ) VALUES (
    'EXCUSE_CLEANUP',
    'excuses',
    jsonb_build_object(
      'expired_excuses_processed', cleanup_result,
      'cleanup_timestamp', NOW()
    ),
    NOW()
  );
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    -- Log any errors
    INSERT INTO audit_logs (
      action,
      table_name,
      details,
      created_at
    ) VALUES (
      'EXCUSE_CLEANUP_ERROR',
      'excuses',
      jsonb_build_object(
        'error_message', SQLERRM,
        'error_timestamp', NOW()
      ),
      NOW()
    );
    
    RETURN false;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION cleanup_expired_excuses() TO authenticated;
GRANT EXECUTE ON FUNCTION schedule_excuse_cleanup() TO authenticated;

-- Create a settings entry to control automatic cleanup
INSERT INTO system_settings (setting_name, setting_value, description)
VALUES (
  'excuse_auto_cleanup',
  '{"enabled": true, "check_interval_minutes": 60, "notify_students": true}',
  'Settings for automatic excuse expiry cleanup'
) ON CONFLICT (setting_name) DO UPDATE SET
  setting_value = EXCLUDED.setting_value,
  updated_at = NOW();

-- Add comment explaining the cleanup system
COMMENT ON FUNCTION cleanup_expired_excuses() IS 
'Automatically cleans up expired approved excuses by reverting attendance status from excused to absent and deleting the excuse records. Also removes old rejected excuses.';

COMMENT ON FUNCTION schedule_excuse_cleanup() IS 
'Wrapper function to schedule excuse cleanup with proper logging and error handling.';
