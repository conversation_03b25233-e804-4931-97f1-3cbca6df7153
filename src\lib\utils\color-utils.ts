/**
 * Converts a hex color string to HSL format for CSS variables
 * @param hex Hex color string (e.g., "#ff0000" or "#f00")
 * @returns HSL string in format "H S% L%" (e.g., "0 100% 50%")
 */
export function hexToHSL(hex: string): string {
  // Remove the # if present
  hex = hex.replace(/^#/, '');

  // Convert 3-digit hex to 6-digit
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  // Convert hex to RGB
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  // Find the maximum and minimum values to calculate the lightness
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  
  // Calculate the lightness
  const l = (max + min) / 2;

  // Calculate the saturation
  let s = 0;
  if (max !== min) {
    s = l > 0.5 
      ? (max - min) / (2 - max - min) 
      : (max - min) / (max + min);
  }

  // Calculate the hue
  let h = 0;
  if (max !== min) {
    if (max === r) {
      h = (g - b) / (max - min) + (g < b ? 6 : 0);
    } else if (max === g) {
      h = (b - r) / (max - min) + 2;
    } else {
      h = (r - g) / (max - min) + 4;
    }
    h /= 6;
  }

  // Convert to degrees, percentage, percentage
  h = Math.round(h * 360);
  s = Math.round(s * 100);
  const lightness = Math.round(l * 100);

  return `${h} ${s}% ${lightness}%`;
}

/**
 * Converts HSL values to a hex color string
 * @param h Hue (0-360)
 * @param s Saturation (0-100)
 * @param l Lightness (0-100)
 * @returns Hex color string (e.g., "#ff0000")
 */
export function hslToHex(h: number, s: number, l: number): string {
  // Convert to 0-1 range
  s /= 100;
  l /= 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  const m = l - c / 2;
  
  let r = 0, g = 0, b = 0;
  
  if (0 <= h && h < 60) {
    r = c; g = x; b = 0;
  } else if (60 <= h && h < 120) {
    r = x; g = c; b = 0;
  } else if (120 <= h && h < 180) {
    r = 0; g = c; b = x;
  } else if (180 <= h && h < 240) {
    r = 0; g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; g = 0; b = c;
  } else if (300 <= h && h < 360) {
    r = c; g = 0; b = x;
  }
  
  // Convert to 0-255 range
  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);
  
  // Convert to hex
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
}

/**
 * Parses a CSS HSL string into its components
 * @param hsl HSL string in format "H S% L%" (e.g., "0 100% 50%")
 * @returns Object with h, s, l values
 */
export function parseHSL(hsl: string): { h: number, s: number, l: number } {
  const [h, s, l] = hsl.split(' ').map(val => parseInt(val));
  return { h, s, l: l };
}

/**
 * Generates a color palette based on a primary color
 * @param primaryColor Primary color in hex format
 * @returns Object with various shades and complementary colors
 */
export function generateColorPalette(primaryColor: string) {
  const hsl = hexToHSL(primaryColor);
  const { h, s, l } = parseHSL(hsl);
  
  return {
    primary: primaryColor,
    lighter: hslToHex(h, Math.max(0, s - 10), Math.min(100, l + 15)),
    darker: hslToHex(h, Math.min(100, s + 10), Math.max(0, l - 15)),
    complementary: hslToHex((h + 180) % 360, s, l),
    analogous1: hslToHex((h + 30) % 360, s, l),
    analogous2: hslToHex((h + 330) % 360, s, l),
    triadic1: hslToHex((h + 120) % 360, s, l),
    triadic2: hslToHex((h + 240) % 360, s, l),
  };
}

/**
 * Determines if a color is light or dark
 * @param color Hex color string
 * @returns Boolean indicating if the color is light (true) or dark (false)
 */
export function isLightColor(color: string): boolean {
  const hex = color.replace(/^#/, '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  // Calculate relative luminance using the formula from WCAG 2.0
  const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;
  
  // Return true if the color is light (luminance > 128)
  return luminance > 128;
}

/**
 * Calculates the contrast ratio between two colors
 * @param color1 First color in hex format
 * @param color2 Second color in hex format
 * @returns Contrast ratio (1-21)
 */
export function getContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string) => {
    const hex = color.replace(/^#/, '');
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;
    
    const rgb = [r, g, b].map(val => {
      return val <= 0.03928
        ? val / 12.92
        : Math.pow((val + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2];
  };
  
  const l1 = getLuminance(color1);
  const l2 = getLuminance(color2);
  
  // Ensure the lighter color is always the first one
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Suggests a text color (black or white) based on background color
 * @param backgroundColor Background color in hex format
 * @returns "#000000" for dark text or "#ffffff" for light text
 */
export function suggestTextColor(backgroundColor: string): string {
  return isLightColor(backgroundColor) ? "#000000" : "#ffffff";
}
