-- Verify profiles table structure
DO $$ 
BEGIN
  -- Check if the table exists, if not create it
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'profiles') THEN
    CREATE TABLE profiles (
      id UUID PRIMARY KEY,
      user_id UUID NOT NULL UNIQUE,
      role VARCHAR NOT NULL DEFAULT 'student',
      name VARCHAR NOT NULL,
      email VARCHAR NOT NULL,
      photo_url VARCHAR,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', now()),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', now()),
      student_id VARCHAR,
      teacher_id VARCHAR,
      admin_id VARCHAR,
      department VARCHAR,
      position VARCHAR,
      subject VARCHAR,
      course VARCHAR,
      biometric_registered BOOLEAN DEFAULT false,
      block_name VARCHAR,
      room_number VARCHAR,
      pin VARCHAR
    );
  END IF;

  -- Add any missing columns
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'user_id') THEN
    ALTER TABLE profiles ADD COLUMN user_id UUID NOT NULL UNIQUE;
  END IF;

  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
    ALTER TABLE profiles ADD COLUMN role VARCHAR NOT NULL DEFAULT 'student';
  END IF;

  -- Add more column checks as needed...

  -- Create index on user_id if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'profiles' AND indexname = 'profiles_user_id_idx') THEN
    CREATE INDEX profiles_user_id_idx ON profiles(user_id);
  END IF;

  -- Create index on role if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'profiles' AND indexname = 'profiles_role_idx') THEN
    CREATE INDEX profiles_role_idx ON profiles(role);
  END IF;
END $$; 