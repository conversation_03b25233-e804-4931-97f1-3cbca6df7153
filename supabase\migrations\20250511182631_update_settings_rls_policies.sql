-- Add school_id to settings table if it doesn't exist
ALTER TABLE settings ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id);

-- Update existing settings to use the default school
DO $$
DECLARE
  default_school_id UUID;
BEGIN
  -- Get the default school ID
  SELECT id INTO default_school_id FROM public.schools LIMIT 1;

  -- Update settings table
  UPDATE public.settings
  SET school_id = default_school_id
  WHERE school_id IS NULL;
END $$;

-- Enable RLS on settings table
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Teachers can view settings in their school" ON settings;
DROP POLICY IF EXISTS "School admins can manage settings in their school" ON settings;
DROP POLICY IF EXISTS "System admins can manage all settings" ON settings;
DROP POLICY IF EXISTS "Students can view settings in their school" ON settings;

-- Teachers can view settings in their school
CREATE POLICY "Teachers can view settings in their school"
ON settings
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the setting belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage settings in their school
CREATE POLICY "School admins can manage settings in their school"
ON settings
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the setting belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Ensure school_id matches the admin's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all settings
CREATE POLICY "System admins can manage all settings"
ON settings
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Students can view settings in their school
CREATE POLICY "Students can view settings in their school"
ON settings
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
  )
  AND
  -- Check if the setting belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Create default settings for each school if they don't exist
INSERT INTO settings (key, value, description, school_id)
SELECT
  'default_radius',
  '100',
  'Default radius for geolocation verification (in meters)',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'default_radius' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_location_verification',
  'true',
  'Enable geolocation verification for attendance',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_location_verification' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_wifi_verification',
  'false',
  'Enable WiFi verification for attendance',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_wifi_verification' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_biometric_verification',
  'false',
  'Enable biometric verification for attendance',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_biometric_verification' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_pin_verification',
  'true',
  'Enable PIN verification for attendance',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_pin_verification' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'attendance_time_start',
  '08:00',
  'Start time for attendance recording',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'attendance_time_start' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'attendance_time_end',
  '17:00',
  'End time for attendance recording',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'attendance_time_end' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'excuse_time_start',
  '08:00',
  'Start time for excuse submissions',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'excuse_time_start' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'excuse_time_end',
  '17:00',
  'End time for excuse submissions',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'excuse_time_end' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_parent_notifications',
  'true',
  'Enable parent notifications for excuses',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_parent_notifications' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'parent_notification_method',
  'email',
  'Method for parent notifications (email or sms)',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'parent_notification_method' AND school_id = s.id
  );