import { differenceInDays, addDays } from "date-fns";

/**
 * Calculate the number of days between two dates, inclusive of both start and end dates
 */
export function calculateDaysBetween(startDate: Date | string, endDate: Date | string): number {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
  
  // Add 1 to include both the start and end dates
  return differenceInDays(end, start) + 1;
}

/**
 * Format the duration in days as a human-readable string
 */
export function formatDuration(days: number): string {
  if (days === 1) {
    return "1 day";
  } else {
    return `${days} days`;
  }
}
