import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useSchool } from "@/context/SchoolContext";
import { supabase } from "@/lib/supabase";
import { createSchool, updateSchool } from "@/lib/api/schools";
import SecondaryNavigation from "./navigation/SecondaryNavigation";
import {
  Building2,
  Search,
  Plus,
  RefreshCw,
  Settings,
  Key,
  BarChart4,
  Copy,
  Check,
  AlertCircle,
  X,
  RotateCw,
  Calendar,
  Trash2,
  Upload,
  Pie<PERSON>hart,
  LineChart,
  Bar<PERSON>hart as BarChartIcon,
  Users,
  Wrench as WrenchIcon,
  Ban,
  CheckCircle,
  Edit,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format, isAfter, parseISO } from "date-fns";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  fetchAllInvitationCodes,
  generateInvitationCode,
  revokeInvitationCode,
  fetchSchoolSettings,
  updateSchoolSettings,
  fetchSchoolAnalytics,
  fetchAllSchoolsAnalytics,
  setSchoolMaintenanceMode,
  setSchoolActiveStatus,
  deleteSchool,
} from "@/lib/api/system-admin";
import SchoolAnalyticsChart from "./charts/SchoolAnalyticsChart";
import { runMigrations } from "@/lib/migrations/run-migrations";
import {
  MaintenanceDialog,
  BlockDialog,
  DeleteDialog,
} from "./dialogs/SchoolActionDialogs";
import { SchoolFormDialog, SchoolFormValues } from "./dialogs/SchoolFormDialog";

import SystemSchoolSettings from "./SystemSchoolSettings";

interface School {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  status: "active" | "inactive" | "pending";
  invitation_code: string | null;
  invitation_expiry?: string | null;
  created_at: string;
  updated_at: string;
  user_count?: number;
  logo_url?: string | null;
  primary_color?: string | null;
  secondary_color?: string | null;
  maintenance_mode?: boolean;
  maintenance_message?: string | null;
  maintenance_estimated_time?: string | null;
}

export default function SchoolManagement() {
  const { toast } = useToast();
  const { switchSchool } = useSchool();
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState("schools");

  // Invitation codes state
  const [invitationCodes, setInvitationCodes] = useState<School[]>([]);
  const [loadingCodes, setLoadingCodes] = useState(true);
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isRevokingCode, setIsRevokingCode] = useState(false);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [showRevokeDialog, setShowRevokeDialog] = useState(false);
  const [codeCopied, setCodeCopied] = useState<string | null>(null);

  // School settings state
  const [selectedSchoolSettings, setSelectedSchoolSettings] =
    useState<any>(null);
  const [loadingSettings, setLoadingSettings] = useState(false);
  const [savingSettings, setSavingSettings] = useState(false);

  // Analytics state
  const [schoolsAnalytics, setSchoolsAnalytics] = useState<any[]>([]);
  const [loadingAnalytics, setLoadingAnalytics] = useState(false);
  const [analyticsPeriod, setAnalyticsPeriod] = useState(30);

  // School actions state
  const [showMaintenanceDialog, setShowMaintenanceDialog] = useState(false);
  const [showBlockDialog, setShowBlockDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isProcessingMaintenance, setIsProcessingMaintenance] = useState(false);
  const [isProcessingBlock, setIsProcessingBlock] = useState(false);
  const [isProcessingDelete, setIsProcessingDelete] = useState(false);
  const [maintenanceMessage, setMaintenanceMessage] = useState("");
  const [maintenanceTime, setMaintenanceTime] = useState("a few hours");

  // Add school state
  const [showAddSchoolDialog, setShowAddSchoolDialog] = useState(false);
  const [isAddingSchool, setIsAddingSchool] = useState(false);
  const [newSchoolInvitationCode, setNewSchoolInvitationCode] = useState("");

  // Edit school state
  const [showEditSchoolDialog, setShowEditSchoolDialog] = useState(false);
  const [isEditingSchool, setIsEditingSchool] = useState(false);
  const [schoolToEdit, setSchoolToEdit] = useState<School | null>(null);

  useEffect(() => {
    // Run migrations first, then fetch schools
    const initializeComponent = async () => {
      try {
        // Run database migrations
        const migrationsSuccessful = await runMigrations();
        console.log("Database migrations completed:", migrationsSuccessful);
        // Then fetch schools
        await fetchSchools();
      } catch (error) {
        console.error("Error initializing component:", error);
        toast({
          title: "Initialization Error",
          description:
            "There was an error initializing the component. Some features may not work correctly.",
          variant: "destructive",
        });
      }
    };

    initializeComponent();
  }, []);

  // Fetch invitation codes when the invitations tab is active
  useEffect(() => {
    if (activeTab === "invitations") {
      fetchInvitationCodes();
    }
  }, [activeTab]);

  // Fetch analytics data when the analytics tab is active
  useEffect(() => {
    if (activeTab === "analytics") {
      fetchSchoolsAnalyticsData();
    }
  }, [activeTab, analyticsPeriod]);

  // Fetch school settings when a school is selected in the settings tab
  useEffect(() => {
    if (activeTab === "settings" && selectedSchool) {
      fetchSchoolSettingsData(selectedSchool.id);
    }
  }, [activeTab, selectedSchool]);

  const fetchSchools = async () => {
    setLoading(true);
    try {
      // Fetch schools
      const { data, error } = await supabase
        .from("schools")
        .select("*")
        .order("name");

      if (error) throw error;

      // Fetch user counts for each school
      const schoolsWithUserCounts = await Promise.all(
        (data || []).map(async (school) => {
          const { count, error: countError } = await supabase
            .from("profiles")
            .select("*", { count: "exact", head: true })
            .eq("school_id", school.id);

          if (countError) {
            console.error("Error fetching user count:", countError);
            return { ...school, user_count: 0 };
          }

          return { ...school, user_count: count || 0 };
        })
      );

      setSchools(schoolsWithUserCounts);
    } catch (error) {
      console.error("Error fetching schools:", error);
      toast({
        title: "Error",
        description: "Failed to fetch schools",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);

    if (activeTab === "schools") {
      await fetchSchools();
    } else if (activeTab === "invitations") {
      await fetchInvitationCodes();
    } else if (activeTab === "settings" && selectedSchool) {
      await fetchSchoolSettingsData(selectedSchool.id);
    } else if (activeTab === "analytics") {
      await fetchSchoolsAnalyticsData();
    }

    setRefreshing(false);
  };

  // Fetch invitation codes for all schools
  const fetchInvitationCodes = async () => {
    setLoadingCodes(true);
    try {
      const codesData = await fetchAllInvitationCodes();
      setInvitationCodes(codesData);
    } catch (error) {
      console.error("Error fetching invitation codes:", error);
      toast({
        title: "Error",
        description: "Failed to fetch invitation codes. Using local data.",
        variant: "destructive",
      });
      // Use the schools data we already have as a fallback
      setInvitationCodes(schools);
    } finally {
      setLoadingCodes(false);
    }
  };

  // Generate a new invitation code for a school
  const handleGenerateCode = async (schoolId: string) => {
    setIsGeneratingCode(true);
    try {
      await generateInvitationCode(schoolId);
      await fetchInvitationCodes(); // Refresh the list
      toast({
        title: "Success",
        description: "Invitation code generated successfully",
      });
    } catch (error) {
      console.error("Error generating invitation code:", error);
      toast({
        title: "Error",
        description: "Failed to generate invitation code",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingCode(false);
      setShowGenerateDialog(false);
    }
  };

  // Revoke an invitation code for a school
  const handleRevokeCode = async (schoolId: string) => {
    setIsRevokingCode(true);
    try {
      await revokeInvitationCode(schoolId);
      await fetchInvitationCodes(); // Refresh the list
      toast({
        title: "Success",
        description: "Invitation code revoked successfully",
      });
    } catch (error) {
      console.error("Error revoking invitation code:", error);
      toast({
        title: "Error",
        description: "Failed to revoke invitation code",
        variant: "destructive",
      });
    } finally {
      setIsRevokingCode(false);
      setShowRevokeDialog(false);
    }
  };

  // Copy invitation code to clipboard
  const copyToClipboard = (code: string, schoolId: string) => {
    navigator.clipboard.writeText(code);
    setCodeCopied(schoolId);
    setTimeout(() => setCodeCopied(null), 2000);
    toast({
      title: "Copied",
      description: "Invitation code copied to clipboard",
    });
  };

  // Fetch school settings data
  const fetchSchoolSettingsData = async (schoolId: string) => {
    setLoadingSettings(true);
    try {
      const settings = await fetchSchoolSettings(schoolId);
      setSelectedSchoolSettings(settings);
    } catch (error) {
      console.error("Error fetching school settings:", error);
      toast({
        title: "Error",
        description: "Failed to fetch school settings",
        variant: "destructive",
      });
    } finally {
      setLoadingSettings(false);
    }
  };

  // Fetch analytics data for all schools
  const fetchSchoolsAnalyticsData = async () => {
    setLoadingAnalytics(true);
    try {
      const analytics = await fetchAllSchoolsAnalytics(analyticsPeriod);
      setSchoolsAnalytics(analytics);
    } catch (error) {
      console.error("Error fetching schools analytics:", error);
      toast({
        title: "Error",
        description:
          "Failed to fetch schools analytics. Using basic school data.",
        variant: "destructive",
      });

      // Create basic analytics data from schools as a fallback
      const fallbackAnalytics = schools.map((school) => ({
        ...school,
        analytics: {
          userCount: school.user_count || 0,
          attendanceCount: 0,
          excuseCount: 0,
          statusCounts: {},
          userGrowthSeries: [],
          period: analyticsPeriod,
        },
      }));

      setSchoolsAnalytics(fallbackAnalytics);
    } finally {
      setLoadingAnalytics(false);
    }
  };

  // Handle setting a school to maintenance mode
  const handleMaintenanceMode = async (schoolId: string, enable: boolean) => {
    setIsProcessingMaintenance(true);
    try {
      await setSchoolMaintenanceMode(
        schoolId,
        enable,
        enable ? maintenanceMessage : null,
        enable ? maintenanceTime : null
      );

      // Refresh the schools list
      await fetchSchools();

      toast({
        title: "Success",
        description: enable
          ? "School set to maintenance mode"
          : "Maintenance mode disabled",
      });
    } catch (error) {
      console.error("Error setting maintenance mode:", error);
      toast({
        title: "Error",
        description: "Failed to set maintenance mode",
        variant: "destructive",
      });
    } finally {
      setIsProcessingMaintenance(false);
      setShowMaintenanceDialog(false);
    }
  };

  // Handle blocking/activating a school
  const handleBlockActivate = async (schoolId: string, activate: boolean) => {
    setIsProcessingBlock(true);
    try {
      await setSchoolActiveStatus(schoolId, activate);

      // Refresh the schools list
      await fetchSchools();

      toast({
        title: "Success",
        description: activate
          ? "School activated successfully"
          : "School blocked successfully",
      });
    } catch (error) {
      console.error("Error setting school status:", error);
      toast({
        title: "Error",
        description: "Failed to update school status",
        variant: "destructive",
      });
    } finally {
      setIsProcessingBlock(false);
      setShowBlockDialog(false);
    }
  };

  // Handle deleting a school
  const handleDeleteSchool = async (schoolId: string) => {
    setIsProcessingDelete(true);
    try {
      await deleteSchool(schoolId);

      // Refresh the schools list
      await fetchSchools();

      toast({
        title: "Success",
        description: "School deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting school:", error);
      toast({
        title: "Error",
        description: "Failed to delete school",
        variant: "destructive",
      });
    } finally {
      setIsProcessingDelete(false);
      setShowDeleteDialog(false);
    }
  };

  // Generate a new invitation code
  const generateInvitationCode = () => {
    const code =
      "INV-" + Math.random().toString(36).substring(2, 10).toUpperCase();
    setNewSchoolInvitationCode(code);
    return code;
  };

  // Handle editing a school
  const handleEditSchool = (school: School) => {
    setSchoolToEdit(school);
    setShowEditSchoolDialog(true);
  };

  // Handle saving edited school
  const handleSaveEditedSchool = async (data: SchoolFormValues) => {
    setIsEditingSchool(true);
    try {
      if (!schoolToEdit) return;

      // Prepare school data for update
      const schoolData = {
        name: data.name,
        address: data.address || null,
        city: data.city || null,
        state: data.state || null,
        zip: data.zip || null,
        country: data.country || null,
        phone: data.phone || null,
        email: data.email || null,
        website: data.website || null,
        primary_color: data.primaryColor || null,
        secondary_color: data.secondaryColor || null,
        status: data.isActive ? "active" : "inactive",
        updated_at: new Date().toISOString(),
      };

      // Update the school
      const updatedSchool = await updateSchool(schoolToEdit.id, schoolData);

      if (updatedSchool) {
        // Refresh the schools list
        await fetchSchools();

        toast({
          title: "Success",
          description: "School updated successfully",
        });

        // Log the action in audit logs
        await supabase.from("audit_logs").insert({
          action_type: "update",
          entity_type: "school",
          details: {
            message: "School updated",
            school_name: data.name,
            school_id: schoolToEdit.id,
          },
          created_at: new Date().toISOString(),
        });

        // Close the dialog
        setShowEditSchoolDialog(false);
        setSchoolToEdit(null);
      } else {
        throw new Error("Failed to update school");
      }
    } catch (error) {
      console.error("Error updating school:", error);
      toast({
        title: "Error",
        description: "Failed to update school",
        variant: "destructive",
      });
    } finally {
      setIsEditingSchool(false);
    }
  };

  // Handle adding a new school
  const handleAddSchool = async (data: SchoolFormValues) => {
    setIsAddingSchool(true);
    try {
      // Generate invitation code
      const code = generateInvitationCode();

      // Map form data to match the schools table schema
      const schoolData = {
        name: data.name,
        address: data.address,
        city: data.city,
        state: data.state,
        zip: data.zip,
        country: data.country,
        phone: data.phone,
        email: data.email,
        website: data.website,
        primary_color: data.primaryColor,
        secondary_color: data.secondaryColor,
        is_active: data.isActive,
        invitation_code: code,
        status: data.isActive ? "active" : "inactive",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Create new school
      const newSchool = await createSchool(schoolData);

      if (newSchool) {
        // Refresh the schools list
        await fetchSchools();

        toast({
          title: "Success",
          description: "School created successfully",
        });

        // Log the action in audit logs
        await supabase.from("audit_logs").insert({
          action_type: "create",
          entity_type: "school",
          details: {
            message: "School created",
            school_name: data.name,
            school_id: newSchool.id,
          },
          created_at: new Date().toISOString(),
        });

        // Close the dialog
        setShowAddSchoolDialog(false);
      } else {
        throw new Error("Failed to create school");
      }
    } catch (error) {
      console.error("Error creating school:", error);
      toast({
        title: "Error",
        description: "Failed to create school",
        variant: "destructive",
      });
    } finally {
      setIsAddingSchool(false);
    }
  };

  // Filter schools based on search query
  const filteredSchools = schools.filter(
    (school) =>
      school.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (school.city &&
        school.city.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (school.state &&
        school.state.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Secondary navigation items
  const navItems = [
    {
      id: "schools",
      label: "Schools",
      icon: <Building2 className="h-4 w-4" />,
    },
    {
      id: "invitations",
      label: "Invitation Codes",
      icon: <Key className="h-4 w-4" />,
    },
    {
      id: "settings",
      label: "School Settings",
      icon: <Settings className="h-4 w-4" />,
    },
    {
      id: "analytics",
      label: "School Analytics",
      icon: <BarChart4 className="h-4 w-4" />,
    },
  ];

  // Handle viewing a school as admin
  const handleViewAsAdmin = async (schoolId: string) => {
    try {
      // First switch the school context
      await switchSchool(schoolId);

      // Then navigate to the admin dashboard
      window.location.href = "/admin";
    } catch (error) {
      console.error("Error switching to school admin view:", error);
      toast({
        title: "Error",
        description: "Failed to access school admin dashboard",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Action Dialogs */}
      <MaintenanceDialog
        school={selectedSchool}
        open={showMaintenanceDialog}
        onOpenChange={setShowMaintenanceDialog}
        onConfirm={handleMaintenanceMode}
        isProcessing={isProcessingMaintenance}
      />

      <BlockDialog
        school={selectedSchool}
        open={showBlockDialog}
        onOpenChange={setShowBlockDialog}
        onConfirm={handleBlockActivate}
        isProcessing={isProcessingBlock}
      />

      <DeleteDialog
        school={selectedSchool}
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDeleteSchool}
        isProcessing={isProcessingDelete}
      />

      <SchoolFormDialog
        open={showAddSchoolDialog}
        onOpenChange={setShowAddSchoolDialog}
        onSubmit={handleAddSchool}
        isProcessing={isAddingSchool}
        invitationCode={newSchoolInvitationCode}
      />

      <SchoolFormDialog
        open={showEditSchoolDialog}
        onOpenChange={setShowEditSchoolDialog}
        onSubmit={handleSaveEditedSchool}
        isProcessing={isEditingSchool}
        school={schoolToEdit}
        isEditing={true}
      />

      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">School Management</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      <SecondaryNavigation
        items={navItems}
        activeItem={activeTab}
        onItemChange={setActiveTab}
        className="mt-4"
      >
        {{
          schools: (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="relative w-full max-w-sm">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search schools..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Button
                  onClick={() => {
                    generateInvitationCode();
                    setShowAddSchoolDialog(true);
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add School
                </Button>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Schools</CardTitle>
                  <CardDescription>
                    Manage all schools in the system
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="space-y-2">
                      {[...Array(5)].map((_, i) => (
                        <Skeleton key={i} className="h-12 w-full" />
                      ))}
                    </div>
                  ) : filteredSchools.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>School Name</TableHead>
                          <TableHead>Location</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Users</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredSchools.map((school) => (
                          <TableRow key={school.id}>
                            <TableCell className="font-medium">
                              <div className="flex flex-col">
                                <div className="font-medium">{school.name}</div>
                                <Button
                                  variant="link"
                                  className="p-0 h-auto text-sm text-primary flex items-center gap-1 hover:underline"
                                  onClick={() => handleViewAsAdmin(school.id)}
                                >
                                  <Users className="h-3 w-3" />
                                  View School Admin Dashboard
                                </Button>
                              </div>
                            </TableCell>
                            <TableCell>
                              {[school.city, school.state, school.country]
                                .filter(Boolean)
                                .join(", ")}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  school.status === "active"
                                    ? "success"
                                    : school.status === "inactive"
                                    ? "destructive"
                                    : "outline"
                                }
                              >
                                {school.status}
                              </Badge>
                            </TableCell>
                            <TableCell>{school.user_count}</TableCell>
                            <TableCell>
                              {format(
                                new Date(school.created_at),
                                "MMM d, yyyy"
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditSchool(school)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <Settings className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>
                                      School Actions
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() => {
                                        setSelectedSchool(school);
                                        setMaintenanceMessage(
                                          school.maintenance_message || ""
                                        );
                                        setMaintenanceTime(
                                          school.maintenance_estimated_time ||
                                            "a few hours"
                                        );
                                        setShowMaintenanceDialog(true);
                                      }}
                                    >
                                      {school.maintenance_mode ? (
                                        <>
                                          <X className="mr-2 h-4 w-4 text-red-500" />
                                          <span>Disable Maintenance</span>
                                        </>
                                      ) : (
                                        <>
                                          <WrenchIcon className="mr-2 h-4 w-4 text-amber-500" />
                                          <span>Maintenance Mode</span>
                                        </>
                                      )}
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() => {
                                        setSelectedSchool(school);
                                        setShowBlockDialog(true);
                                      }}
                                    >
                                      {school.status === "active" ? (
                                        <>
                                          <Ban className="mr-2 h-4 w-4 text-red-500" />
                                          <span>Block School</span>
                                        </>
                                      ) : (
                                        <>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Activate School</span>
                                        </>
                                      )}
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() => {
                                        setSelectedSchool(school);
                                        setShowDeleteDialog(true);
                                      }}
                                      className="text-red-600"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      <span>Delete School</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      No schools found
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredSchools.length} of {schools.length} schools
                  </div>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </CardFooter>
              </Card>
            </div>
          ),
          invitations: (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="relative w-full max-w-sm">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search schools..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Invitation Codes</CardTitle>
                  <CardDescription>
                    Manage invitation codes for schools
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loadingCodes ? (
                    <div className="space-y-2">
                      {[...Array(5)].map((_, i) => (
                        <Skeleton key={i} className="h-12 w-full" />
                      ))}
                    </div>
                  ) : invitationCodes.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>School Name</TableHead>
                          <TableHead>Invitation Code</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Last Updated</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {invitationCodes
                          .filter(
                            (school) =>
                              school.name
                                .toLowerCase()
                                .includes(searchQuery.toLowerCase()) ||
                              (school.city &&
                                school.city
                                  .toLowerCase()
                                  .includes(searchQuery.toLowerCase())) ||
                              (school.state &&
                                school.state
                                  .toLowerCase()
                                  .includes(searchQuery.toLowerCase()))
                          )
                          .map((school) => (
                            <TableRow key={school.id}>
                              <TableCell className="font-medium">
                                {school.name}
                              </TableCell>
                              <TableCell>
                                {school.invitation_code ? (
                                  <div className="flex items-center space-x-2">
                                    <code className="bg-muted px-1 py-0.5 rounded text-sm">
                                      {school.invitation_code}
                                    </code>
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-6 w-6"
                                            onClick={() =>
                                              copyToClipboard(
                                                school.invitation_code!,
                                                school.id
                                              )
                                            }
                                          >
                                            {codeCopied === school.id ? (
                                              <Check className="h-3 w-3" />
                                            ) : (
                                              <Copy className="h-3 w-3" />
                                            )}
                                          </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          <p>Copy to clipboard</p>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  </div>
                                ) : (
                                  <span className="text-muted-foreground text-sm">
                                    No code
                                  </span>
                                )}
                              </TableCell>
                              <TableCell>
                                {school.invitation_code ? (
                                  <Badge
                                    variant="success"
                                    className="bg-green-500"
                                  >
                                    Active
                                  </Badge>
                                ) : (
                                  <Badge variant="outline">None</Badge>
                                )}
                              </TableCell>
                              <TableCell>
                                {format(
                                  new Date(school.updated_at),
                                  "MMM d, yyyy"
                                )}
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Dialog
                                    open={
                                      showGenerateDialog &&
                                      selectedSchool?.id === school.id
                                    }
                                    onOpenChange={(open) => {
                                      if (!open) setShowGenerateDialog(false);
                                      if (open) {
                                        setSelectedSchool(school);
                                        setShowGenerateDialog(true);
                                      }
                                    }}
                                  >
                                    <DialogTrigger asChild>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="h-8"
                                      >
                                        <RotateCw className="h-3.5 w-3.5 mr-1" />
                                        {school.invitation_code
                                          ? "Regenerate"
                                          : "Generate"}
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent>
                                      <DialogHeader>
                                        <DialogTitle>
                                          Generate Invitation Code
                                        </DialogTitle>
                                        <DialogDescription>
                                          {school.invitation_code
                                            ? "This will replace the existing invitation code for this school."
                                            : "Generate a new invitation code for this school."}
                                        </DialogDescription>
                                      </DialogHeader>
                                      <div className="py-4">
                                        <p className="mb-2">
                                          School: <strong>{school.name}</strong>
                                        </p>
                                        {school.invitation_code && (
                                          <div className="flex items-center space-x-2 mb-4">
                                            <p>Current code:</p>
                                            <code className="bg-muted px-2 py-1 rounded">
                                              {school.invitation_code}
                                            </code>
                                          </div>
                                        )}
                                        <p className="text-sm text-muted-foreground">
                                          Invitation codes persist indefinitely
                                          until revoked or regenerated.
                                        </p>
                                      </div>
                                      <DialogFooter>
                                        <Button
                                          variant="outline"
                                          onClick={() =>
                                            setShowGenerateDialog(false)
                                          }
                                        >
                                          Cancel
                                        </Button>
                                        <Button
                                          onClick={() =>
                                            handleGenerateCode(school.id)
                                          }
                                          disabled={isGeneratingCode}
                                        >
                                          {isGeneratingCode && (
                                            <RotateCw className="h-4 w-4 mr-2 animate-spin" />
                                          )}
                                          Generate Code
                                        </Button>
                                      </DialogFooter>
                                    </DialogContent>
                                  </Dialog>

                                  {school.invitation_code && (
                                    <Dialog
                                      open={
                                        showRevokeDialog &&
                                        selectedSchool?.id === school.id
                                      }
                                      onOpenChange={(open) => {
                                        if (!open) setShowRevokeDialog(false);
                                        if (open) {
                                          setSelectedSchool(school);
                                          setShowRevokeDialog(true);
                                        }
                                      }}
                                    >
                                      <DialogTrigger asChild>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-8"
                                        >
                                          <Trash2 className="h-3.5 w-3.5 mr-1" />
                                          Revoke
                                        </Button>
                                      </DialogTrigger>
                                      <DialogContent>
                                        <DialogHeader>
                                          <DialogTitle>
                                            Revoke Invitation Code
                                          </DialogTitle>
                                          <DialogDescription>
                                            This will revoke the current
                                            invitation code for this school.
                                          </DialogDescription>
                                        </DialogHeader>
                                        <div className="py-4">
                                          <p className="mb-2">
                                            School:{" "}
                                            <strong>{school.name}</strong>
                                          </p>
                                          <div className="flex items-center space-x-2 mb-4">
                                            <p>Current code:</p>
                                            <code className="bg-muted px-2 py-1 rounded">
                                              {school.invitation_code}
                                            </code>
                                          </div>
                                          <div className="bg-destructive/10 p-3 rounded-md border border-destructive/20">
                                            <div className="flex items-start gap-2">
                                              <AlertCircle className="h-5 w-5 text-destructive mt-0.5" />
                                              <div>
                                                <p className="font-medium text-destructive">
                                                  Warning
                                                </p>
                                                <p className="text-sm text-muted-foreground">
                                                  After revoking, users will not
                                                  be able to sign up using this
                                                  code. You can generate a new
                                                  code at any time.
                                                </p>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                        <DialogFooter>
                                          <Button
                                            variant="outline"
                                            onClick={() =>
                                              setShowRevokeDialog(false)
                                            }
                                          >
                                            Cancel
                                          </Button>
                                          <Button
                                            variant="destructive"
                                            onClick={() =>
                                              handleRevokeCode(school.id)
                                            }
                                            disabled={isRevokingCode}
                                          >
                                            {isRevokingCode && (
                                              <RotateCw className="h-4 w-4 mr-2 animate-spin" />
                                            )}
                                            Revoke Code
                                          </Button>
                                        </DialogFooter>
                                      </DialogContent>
                                    </Dialog>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      No schools found
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing{" "}
                    {
                      invitationCodes.filter(
                        (school) =>
                          school.name
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase()) ||
                          (school.city &&
                            school.city
                              .toLowerCase()
                              .includes(searchQuery.toLowerCase())) ||
                          (school.state &&
                            school.state
                              .toLowerCase()
                              .includes(searchQuery.toLowerCase()))
                      ).length
                    }{" "}
                    of {invitationCodes.length} schools
                  </div>
                </CardFooter>
              </Card>
            </div>
          ),
          settings: (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="relative w-full max-w-sm">
                  <Select
                    onValueChange={(value) => {
                      const school = schools.find((s) => s.id === value);
                      if (school) {
                        setSelectedSchool(school);
                      }
                    }}
                  >
                    <SelectTrigger className="w-[280px]">
                      <SelectValue placeholder="Select a school" />
                    </SelectTrigger>
                    <SelectContent>
                      {schools.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <SystemSchoolSettings
                schools={schools}
                selectedSchool={selectedSchool}
                onSchoolSelect={(schoolId) => {
                  if (schoolId) {
                    const school = schools.find((s) => s.id === schoolId);
                    if (school) {
                      setSelectedSchool(school);
                    }
                  } else {
                    setSelectedSchool(null);
                  }
                }}
              />
            </div>
          ),
          analytics: (
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
                <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 sm:gap-4 w-full sm:w-auto">
                  <Select
                    value={`${analyticsPeriod}days`}
                    onValueChange={(value) => {
                      const days = parseInt(value.replace("days", ""));
                      setAnalyticsPeriod(days);
                    }}
                  >
                    <SelectTrigger className="w-full xs:w-[150px] sm:w-[180px] h-9 text-xs sm:text-sm">
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">Last 7 days</SelectItem>
                      <SelectItem value="30days">Last 30 days</SelectItem>
                      <SelectItem value="90days">Last 90 days</SelectItem>
                      <SelectItem value="180days">Last 180 days</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    onValueChange={(value) => {
                      const school = schools.find((s) => s.id === value);
                      if (school) {
                        setSelectedSchool(school);
                      } else {
                        setSelectedSchool(null);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full xs:w-[200px] sm:w-[280px] h-9 text-xs sm:text-sm">
                      <Building2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                      <SelectValue placeholder="All Schools" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Schools</SelectItem>
                      {schools.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2 w-full sm:w-auto mt-2 sm:mt-0">
                  <Tabs defaultValue="bar" className="w-full sm:w-auto">
                    <TabsList className="w-full sm:w-auto grid grid-cols-3 h-9">
                      <TabsTrigger value="bar" className="text-xs px-2 sm:px-3">
                        <BarChartIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                        <span>Bar</span>
                      </TabsTrigger>
                      <TabsTrigger
                        value="line"
                        className="text-xs px-2 sm:px-3"
                      >
                        <LineChart className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                        <span>Line</span>
                      </TabsTrigger>
                      <TabsTrigger value="pie" className="text-xs px-2 sm:px-3">
                        <PieChart className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                        <span>Pie</span>
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>
              </div>

              {loadingAnalytics ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <Skeleton key={i} className="h-[300px] w-full" />
                  ))}
                </div>
              ) : selectedSchool ? (
                // Single school analytics
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SchoolAnalyticsChart
                    schoolId={selectedSchool.id}
                    period={analyticsPeriod}
                    title="User Growth"
                    description={`New users over the last ${analyticsPeriod} days`}
                    chartType="bar"
                    dataType="growth"
                  />
                  <SchoolAnalyticsChart
                    schoolId={selectedSchool.id}
                    period={analyticsPeriod}
                    title="Attendance Overview"
                    description={`Attendance records over the last ${analyticsPeriod} days`}
                    chartType="pie"
                    dataType="attendance"
                  />
                  <SchoolAnalyticsChart
                    schoolId={selectedSchool.id}
                    period={analyticsPeriod}
                    title="Attendance Status"
                    description={`Breakdown by status over the last ${analyticsPeriod} days`}
                    chartType="pie"
                    dataType="status"
                  />
                  <Card>
                    <CardHeader>
                      <CardTitle>School Summary</CardTitle>
                      <CardDescription>
                        Key metrics for {selectedSchool.name}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {schoolsAnalytics
                          .filter((s) => s.id === selectedSchool.id)
                          .map((school) => (
                            <div key={school.id} className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div className="flex flex-col space-y-1">
                                  <span className="text-sm text-muted-foreground">
                                    Total Users
                                  </span>
                                  <span className="text-2xl font-bold">
                                    {school.analytics?.userCount || 0}
                                  </span>
                                </div>
                                <div className="flex flex-col space-y-1">
                                  <span className="text-sm text-muted-foreground">
                                    Attendance Records
                                  </span>
                                  <span className="text-2xl font-bold">
                                    {school.analytics?.attendanceCount || 0}
                                  </span>
                                </div>
                                <div className="flex flex-col space-y-1">
                                  <span className="text-sm text-muted-foreground">
                                    Excuse Submissions
                                  </span>
                                  <span className="text-2xl font-bold">
                                    {school.analytics?.excuseCount || 0}
                                  </span>
                                </div>
                                <div className="flex flex-col space-y-1">
                                  <span className="text-sm text-muted-foreground">
                                    Status
                                  </span>
                                  <Badge
                                    variant={
                                      school.status === "active"
                                        ? "success"
                                        : "destructive"
                                    }
                                    className={
                                      school.status === "active"
                                        ? "bg-green-500 w-fit"
                                        : "w-fit"
                                    }
                                  >
                                    {school.status}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                // All schools analytics
                <div className="space-y-6">
                  <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
                    <Card>
                      <CardHeader className="pb-1 sm:pb-2 px-3 sm:px-6">
                        <CardTitle className="text-xs sm:text-sm font-medium">
                          Total Schools
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6 py-2 sm:py-4">
                        <div className="text-xl sm:text-2xl font-bold">
                          {schoolsAnalytics.length}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-1 sm:pb-2 px-3 sm:px-6">
                        <CardTitle className="text-xs sm:text-sm font-medium">
                          Total Users
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6 py-2 sm:py-4">
                        <div className="text-xl sm:text-2xl font-bold">
                          {schoolsAnalytics.reduce(
                            (sum, school) =>
                              sum + (school.analytics?.userCount || 0),
                            0
                          )}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-1 sm:pb-2 px-3 sm:px-6">
                        <CardTitle className="text-xs sm:text-sm font-medium">
                          Attendance Records
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6 py-2 sm:py-4">
                        <div className="text-xl sm:text-2xl font-bold">
                          {schoolsAnalytics.reduce(
                            (sum, school) =>
                              sum + (school.analytics?.attendanceCount || 0),
                            0
                          )}
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-1 sm:pb-2 px-3 sm:px-6">
                        <CardTitle className="text-xs sm:text-sm font-medium">
                          Excuse Submissions
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 sm:px-6 py-2 sm:py-4">
                        <div className="text-xl sm:text-2xl font-bold">
                          {schoolsAnalytics.reduce(
                            (sum, school) =>
                              sum + (school.analytics?.excuseCount || 0),
                            0
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Card>
                    <CardHeader className="px-3 sm:px-6 py-3 sm:py-4">
                      <CardTitle className="text-base sm:text-lg">
                        School Comparison
                      </CardTitle>
                      <CardDescription className="text-xs sm:text-sm">
                        Analytics across all schools
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6 py-2 sm:py-4">
                      <div className="space-y-3 sm:space-y-4">
                        {schoolsAnalytics.map((school) => (
                          <div
                            key={school.id}
                            className="flex flex-col xs:flex-row items-start xs:items-center justify-between p-2 sm:p-3 rounded-md border gap-2 sm:gap-0"
                          >
                            <div className="flex items-center gap-2 sm:gap-3 w-full xs:w-auto">
                              <Building2 className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground flex-shrink-0" />
                              <div>
                                <p className="font-medium text-sm sm:text-base">
                                  {school.name}
                                </p>
                                <p className="text-xs sm:text-sm text-muted-foreground">
                                  {school.analytics?.userCount || 0} users
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center justify-between xs:justify-end w-full xs:w-auto gap-2 sm:gap-4">
                              <div className="text-left xs:text-right">
                                <p className="text-xs sm:text-sm font-medium">
                                  Attendance
                                </p>
                                <p className="text-xs sm:text-sm text-muted-foreground">
                                  {school.analytics?.attendanceCount || 0}{" "}
                                  records
                                </p>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-8 px-2 sm:px-3"
                                onClick={() => {
                                  setSelectedSchool(school);
                                }}
                              >
                                View Details
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          ),
        }}
      </SecondaryNavigation>
    </div>
  );
}
