import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useSchool } from "@/context/SchoolContext";
import { School } from "@/lib/types";
import { getAllSchools } from "@/lib/utils/school-context";
import { Building2, RefreshCw } from "lucide-react";

export default function SchoolSelector() {
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(true);
  const { currentSchool, isSystemAdmin, switchSchool } = useSchool();
  const { toast } = useToast();

  // Only show school selector for system admins
  if (!isSystemAdmin) {
    return null;
  }

  // Fetch schools on component mount
  useEffect(() => {
    fetchSchools();
  }, []);

  // Fetch all schools
  const fetchSchools = async () => {
    setLoading(true);
    try {
      const schoolsData = await getAllSchools();
      setSchools(schoolsData.filter(school => school.isActive !== false));
    } catch (error) {
      console.error("Error fetching schools:", error);
      toast({
        title: "Error",
        description: "Failed to fetch schools",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle school change
  const handleSchoolChange = async (schoolId: string) => {
    try {
      await switchSchool(schoolId);
      toast({
        title: "School Changed",
        description: "You are now viewing a different school",
      });
    } catch (error) {
      console.error("Error switching school:", error);
      toast({
        title: "Error",
        description: "Failed to switch school",
        variant: "destructive",
      });
    }
  };

  // If not a system admin, don't show the selector
  if (!isSystemAdmin) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-2 bg-muted/50 px-3 py-1 rounded-md">
        <Building2 className="h-4 w-4 text-muted-foreground" />
        <Select
          value={currentSchool?.id || ""}
          onValueChange={handleSchoolChange}
          disabled={loading}
        >
          <SelectTrigger className="w-[200px] border-none bg-transparent">
            <SelectValue placeholder="Select a school" />
          </SelectTrigger>
          <SelectContent>
            {schools.map((school) => (
              <SelectItem key={school.id} value={school.id}>
                {school.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Button
        variant="ghost"
        size="icon"
        onClick={fetchSchools}
        disabled={loading}
        className="h-8 w-8"
      >
        <RefreshCw className="h-4 w-4" />
      </Button>
    </div>
  );
}
