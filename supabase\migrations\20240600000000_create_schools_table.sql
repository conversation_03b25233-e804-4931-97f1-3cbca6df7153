-- Create schools table
CREATE TABLE IF NOT EXISTS public.schools (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  address VARCHAR,
  city VARCHAR,
  state VARCHAR,
  zip VARCHAR,
  country VARCHAR,
  phone VARCHAR,
  email VARCHAR,
  website VARCHAR,
  logo_url VARCHAR,
  primary_color VARCHAR,
  secondary_color VARCHAR,
  invitation_code VARCHAR UNIQUE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies for schools table
ALTER TABLE public.schools ENABLE ROW LEVEL SECURITY;

-- System admins can manage all schools
CREATE POLICY "System admins can manage all schools"
  ON public.schools
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.user_id = auth.uid()
      AND profiles.role = 'admin'
      AND profiles.access_level = 3 -- System admin level
    )
  );

-- All authenticated users can view active schools
CREATE POLICY "Users can view active schools"
  ON public.schools
  FOR SELECT
  TO authenticated
  USING (is_active = true);

-- Add access_level column to profiles if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'access_level'
  ) THEN
    ALTER TABLE profiles ADD COLUMN access_level INTEGER DEFAULT 1;
  END IF;
END $$;

-- Add school_id to profiles table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE profiles ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;

-- Add school_id to rooms table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'rooms' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE rooms ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;

-- Add school_id to attendance_records table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'attendance_records' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE attendance_records ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;

-- Add school_id to notifications table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'notifications' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE notifications ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;

-- Add school_id to excuses table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'excuses' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE excuses ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;

-- Add school_id to parent_contacts table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'parent_contacts' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE parent_contacts ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;

-- Add school_id to room_locations table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'room_locations' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE room_locations ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;

-- Add school_id to system_settings table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'system_settings' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE system_settings ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;
