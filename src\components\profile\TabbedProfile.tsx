import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { User, Fingerprint, Globe } from "lucide-react";
import { useTranslation } from "react-i18next";
import ProfileEditor from "./ProfileEditor";
import BiometricSettings from "./BiometricSettings";
import LanguageSettings from "./LanguageSettings";

interface TabbedProfileProps {
  isSetupMode?: boolean;
  userRole: "student" | "teacher" | "admin";
}

export default function TabbedProfile({ 
  isSetupMode = false, 
  userRole 
}: TabbedProfileProps) {
  const [activeTab, setActiveTab] = useState("profile");
  const { t } = useTranslation();

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 rounded-none border-b bg-transparent h-auto p-0 overflow-x-auto min-w-0">
            <TabsTrigger
              value="profile"
              className="flex items-center justify-center gap-1 sm:gap-2 py-3 sm:py-4 px-1 sm:px-6 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent text-xs sm:text-sm whitespace-nowrap min-w-0 flex-1"
            >
              <User className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">{t("profile.tabs.profile")}</span>
            </TabsTrigger>
            <TabsTrigger
              value="biometrics"
              className="flex items-center justify-center gap-1 sm:gap-2 py-3 sm:py-4 px-1 sm:px-6 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent text-xs sm:text-sm whitespace-nowrap min-w-0 flex-1"
            >
              <Fingerprint className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">{t("profile.tabs.biometrics")}</span>
            </TabsTrigger>
            <TabsTrigger
              value="language"
              className="flex items-center justify-center gap-1 sm:gap-2 py-3 sm:py-4 px-1 sm:px-6 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent text-xs sm:text-sm whitespace-nowrap min-w-0 flex-1"
            >
              <Globe className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
              <span className="text-xs sm:text-sm truncate">{t("profile.tabs.language")}</span>
            </TabsTrigger>
          </TabsList>

          <div className="p-3 sm:p-6">
            <TabsContent value="profile" className="mt-0">
              <ProfileEditor
                isSetupMode={isSetupMode}
                userRole={userRole}
              />
            </TabsContent>

            <TabsContent value="biometrics" className="mt-0">
              <BiometricSettings />
            </TabsContent>

            <TabsContent value="language" className="mt-0">
              <LanguageSettings />
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}
