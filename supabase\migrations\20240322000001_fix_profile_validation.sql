-- Drop the existing trigger and function
DROP TRIGGER IF EXISTS validate_profile_update_trigger ON profiles;
DROP FUNCTION IF EXISTS validate_profile_update;

-- Create a new validation function that only checks fields that are being updated
CREATE OR REPLACE FUNCTION validate_profile_update()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  -- For student profiles, only validate fields that are being updated
  IF NEW.role = 'student' THEN
    -- Check name if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.name IS DISTINCT FROM OLD.name AND NEW.name IS NULL THEN
      RAISE EXCEPTION 'Name cannot be null for student profiles';
    END IF;
    
    -- Check student_id if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.student_id IS DISTINCT FROM OLD.student_id AND NEW.student_id IS NULL THEN
      RAISE EXCEPTION 'Student ID cannot be null for student profiles';
    END IF;
    
    -- Check course if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.course IS DISTINCT FROM OLD.course AND NEW.course IS NULL THEN
      RAISE EXCEPTION 'Course cannot be null for student profiles';
    END IF;
    
    -- Check block_name if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.block_name IS DISTINCT FROM OLD.block_name AND NEW.block_name IS NULL THEN
      RAISE EXCEPTION 'Block name cannot be null for student profiles';
    END IF;
    
    -- Check room_number if it's being updated
    IF TG_OP = 'UPDATE' AND NEW.room_number IS DISTINCT FROM OLD.room_number AND NEW.room_number IS NULL THEN
      RAISE EXCEPTION 'Room number cannot be null for student profiles';
    END IF;
  END IF;
  
  -- Prevent modification of user_id and role by non-admins
  IF (auth.jwt()->>'role') != 'admin' THEN
    IF NEW.user_id != OLD.user_id OR NEW.role != OLD.role THEN
      RAISE EXCEPTION 'Cannot modify user_id or role';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER validate_profile_update_trigger
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION validate_profile_update(); 