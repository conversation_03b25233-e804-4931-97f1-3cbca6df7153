import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Copy, ExternalLink, Tablet, QrCode, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "react-i18next";
import QRCode from "react-qr-code";

interface Block {
  id: string;
  name: string;
}

interface Room {
  id: string;
  name: string;
  building?: string;
  floor?: number;
  block_id: string;
}

export default function TabletSetupHelper() {
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [selectedBlock, setSelectedBlock] = useState<string>("");
  const [selectedRoom, setSelectedRoom] = useState<string>("");
  const [tabletUrls, setTabletUrls] = useState<Array<{
    roomId: string;
    roomName: string;
    url: string;
    qrCode: string;
  }>>([]);

  const { profile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Fetch blocks and rooms
  useEffect(() => {
    const fetchData = async () => {
      if (!profile?.school_id) return;

      try {
        // Fetch blocks
        const { data: blocksData, error: blocksError } = await supabase
          .from("blocks")
          .select("*")
          .eq("school_id", profile.school_id)
          .order("name");

        if (blocksError) throw blocksError;
        setBlocks(blocksData || []);

        // Fetch all rooms for this school
        const { data: roomsData, error: roomsError } = await supabase
          .from("rooms")
          .select(`
            id,
            name,
            building,
            floor,
            block_id,
            blocks (
              name
            )
          `)
          .eq("school_id", profile.school_id)
          .order("name");

        if (roomsError) throw roomsError;
        setRooms(roomsData || []);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load blocks and rooms",
          variant: "destructive",
        });
      }
    };

    fetchData();
  }, [profile?.school_id]);

  // Generate tablet URL
  const generateTabletUrl = (roomId: string) => {
    const baseUrl = window.location.origin;
    const params = new URLSearchParams({
      school: profile?.school_id || "",
      room: roomId,
    });
    return `${baseUrl}/tablet?${params.toString()}`;
  };

  // Generate URLs for selected block
  const generateBlockUrls = () => {
    if (!selectedBlock) return;

    const blockRooms = rooms.filter(room => room.block_id === selectedBlock);
    const urls = blockRooms.map(room => ({
      roomId: room.id,
      roomName: room.name,
      url: generateTabletUrl(room.id),
      qrCode: generateTabletUrl(room.id),
    }));

    setTabletUrls(urls);
  };

  // Generate URL for single room
  const generateSingleRoomUrl = () => {
    if (!selectedRoom) return;

    const room = rooms.find(r => r.id === selectedRoom);
    if (!room) return;

    const url = generateTabletUrl(room.id);
    setTabletUrls([{
      roomId: room.id,
      roomName: room.name,
      url: url,
      qrCode: url,
    }]);
  };

  // Copy URL to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: t("admin.tabletSetup.copied"),
        description: t("admin.tabletSetup.urlCopied"),
      });
    } catch (error) {
      toast({
        title: t("common.error"),
        description: t("admin.tabletSetup.failedToCopy"),
        variant: "destructive",
      });
    }
  };

  // Open URL in new tab
  const openInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  // Get filtered rooms for selected block
  const filteredRooms = selectedBlock 
    ? rooms.filter(room => room.block_id === selectedBlock)
    : rooms;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
            <Tablet className="w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0" />
            {t("admin.tabletSetup.title")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6">
          {/* Block Selection */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">{t("admin.tabletSetup.generateURLsForBlock")}</Label>
              <div className="flex flex-col sm:flex-row gap-2">
                <Select value={selectedBlock} onValueChange={setSelectedBlock}>
                  <SelectTrigger className="w-full sm:flex-1">
                    <SelectValue placeholder={t("admin.tabletSetup.selectBlock")} />
                  </SelectTrigger>
                  <SelectContent>
                    {blocks.map((block) => (
                      <SelectItem key={block.id} value={block.id}>
                        {t("admin.tabletSetup.block")} {block.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={generateBlockUrls}
                  disabled={!selectedBlock}
                  className="w-full sm:w-auto text-sm"
                  size="sm"
                >
                  {t("admin.tabletSetup.generate")}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">{t("admin.tabletSetup.generateURLForRoom")}</Label>
              <div className="flex flex-col sm:flex-row gap-2">
                <Select value={selectedRoom} onValueChange={setSelectedRoom}>
                  <SelectTrigger className="w-full sm:flex-1">
                    <SelectValue placeholder={t("admin.tabletSetup.selectRoom")} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredRooms.map((room) => (
                      <SelectItem key={room.id} value={room.id}>
                        {room.name}
                        {room.building && ` - ${room.building}`}
                        {room.floor && ` ${t("admin.tabletSetup.floor")} ${room.floor}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={generateSingleRoomUrl}
                  disabled={!selectedRoom}
                  className="w-full sm:w-auto text-sm"
                  size="sm"
                >
                  {t("admin.tabletSetup.generate")}
                </Button>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2 text-sm sm:text-base">{t("admin.tabletSetup.setupInstructions")}:</h4>
            <ol className="text-xs sm:text-sm text-blue-700 list-decimal list-inside space-y-1">
              <li>{t("admin.tabletSetup.instruction1")}</li>
              <li>{t("admin.tabletSetup.instruction2")}</li>
              <li>{t("admin.tabletSetup.instruction3")}</li>
              <li>{t("admin.tabletSetup.instruction4")}</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Generated URLs */}
      {tabletUrls.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-base sm:text-lg font-semibold">{t("admin.tabletSetup.generatedTabletURLs")}</h3>

          {tabletUrls.map((item, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="text-sm sm:text-base flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <span className="truncate">{item.roomName}</span>
                  <Badge variant="outline" className="self-start sm:self-center text-xs">
                    {t("admin.tabletSetup.room")} {item.roomId.slice(-8)}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4">
                {/* URL */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">{t("admin.tabletSetup.tabletURL")}:</Label>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Input
                      value={item.url}
                      readOnly
                      className="font-mono text-xs sm:text-sm flex-1"
                    />
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(item.url)}
                        className="flex-1 sm:flex-none"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openInNewTab(item.url)}
                        className="flex-1 sm:flex-none"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* QR Code for URL */}
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <Label className="text-sm font-medium">{t("admin.tabletSetup.setupQRCode")}:</Label>
                    <p className="text-xs text-gray-600 mb-2">
                      {t("admin.tabletSetup.scanQRCodeInstruction")}
                    </p>
                  </div>
                  <div className="flex justify-center lg:justify-end">
                    <div className="bg-white p-2 sm:p-3 rounded-lg border">
                      <QRCode
                        value={item.qrCode}
                        size={window.innerWidth < 640 ? 100 : 120}
                        style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                      />
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="flex flex-col sm:flex-row gap-2 pt-2 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(item.url)}
                    className="flex-1 text-xs sm:text-sm"
                  >
                    <Copy className="w-4 h-4 mr-1 flex-shrink-0" />
                    <span className="truncate">{t("admin.tabletSetup.copyURL")}</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openInNewTab(item.url)}
                    className="flex-1 text-xs sm:text-sm"
                  >
                    <ExternalLink className="w-4 h-4 mr-1 flex-shrink-0" />
                    <span className="truncate">{t("admin.tabletSetup.testTablet")}</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Bulk Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm sm:text-base">{t("admin.tabletSetup.bulkActions")}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    const allUrls = tabletUrls.map(item =>
                      `${item.roomName}: ${item.url}`
                    ).join('\n');
                    copyToClipboard(allUrls);
                  }}
                  className="w-full sm:w-auto text-xs sm:text-sm"
                >
                  <Copy className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{t("admin.tabletSetup.copyAllURLs")}</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    tabletUrls.forEach(item => {
                      setTimeout(() => openInNewTab(item.url), 100);
                    });
                  }}
                  className="w-full sm:w-auto text-xs sm:text-sm"
                >
                  <ExternalLink className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{t("admin.tabletSetup.openAllTablets")}</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
