import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Wrench, Clock, LogOut, Sparkles, AlertCircle } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { getBranding } from "@/config/branding";

interface MaintenanceModePageProps {
  userName?: string;
}

const MaintenanceModePage: React.FC<MaintenanceModePageProps> = ({ userName }) => {
  const { signOut } = useAuth();
  const { t, i18n } = useTranslation();
  const branding = getBranding(i18n.language);

  useEffect(() => {
    // Set document title
    document.title = `${t("maintenance.title")} | ${branding.APP_NAME}`;
  }, [t, branding.APP_NAME]);

  const handleSignOut = async () => {
    await signOut();
    window.location.href = "/";
  };

  const displayMessage = userName
    ? `${t("common.hello")} ${userName}, ${t("maintenance.defaultMessage")}`
    : t("maintenance.defaultMessage");

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="w-full max-w-lg"
      >
        <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-md overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-amber-600 to-orange-600 text-white p-6 sm:p-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="flex flex-col items-center space-y-4"
            >
              <div className="relative">
                <div className="h-16 w-16 sm:h-20 sm:w-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <Wrench className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
                </div>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "linear",
                  }}
                  className="absolute -top-1 -right-1 h-5 w-5 sm:h-6 sm:w-6 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg"
                >
                  <Sparkles className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-yellow-800" />
                </motion.div>
              </div>

              <div className="text-center space-y-2">
                <h1 className="text-xl sm:text-2xl font-bold">
                  {t("maintenance.title")}
                </h1>
                <p className="text-amber-100 text-sm sm:text-base opacity-90">
                  {t("maintenance.maintenanceInProgress")}
                </p>
              </div>
            </motion.div>
          </CardHeader>

          <CardContent className="p-6 sm:p-8 space-y-6">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-center space-y-4"
            >
              <p className="text-gray-700 text-sm sm:text-base leading-relaxed">
                {displayMessage}
              </p>

              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-blue-800 text-left">
                    {t("maintenance.workingToImprove")}
                  </p>
                </div>
              </div>

              <div className="bg-amber-50 p-4 rounded-xl border border-amber-200">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-amber-800 text-left">
                    {t("maintenance.contactAdmin")}
                  </p>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="space-y-4"
            >
              <div className="flex items-center justify-center gap-2">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="h-2 w-2 bg-amber-500 rounded-full"
                />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
                  className="h-2 w-2 bg-orange-500 rounded-full"
                />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 1.5, repeat: Infinity, delay: 0.4 }}
                  className="h-2 w-2 bg-yellow-500 rounded-full"
                />
              </div>

              <Button
                onClick={handleSignOut}
                className="w-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white shadow-lg transition-all duration-200 transform hover:scale-105"
                size="lg"
              >
                <LogOut className="mr-2 h-4 w-4" />
                {t("blockedAccount.signOut")}
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.9 }}
              className="text-center"
            >
              <p className="text-xs sm:text-sm text-gray-500 leading-relaxed">
                {t("maintenance.thankYou")}
              </p>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default MaintenanceModePage;
