import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { School } from "@/lib/types";
import { supabase } from "@/lib/supabase";
import {
  Building,
  Plus,
  Edit,
  Trash,
  Co<PERSON>,
  Check,
  X,
  <PERSON>f<PERSON><PERSON><PERSON>,
  <PERSON>,
  School as SchoolIcon,
  <PERSON>,
  Setting<PERSON>,
} from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  getAllSchools,
  createSchool,
  updateSchool,
  isSystemAdmin,
} from "@/lib/utils/school-context";

// Form schema for school creation/editing
const schoolFormSchema = z.object({
  name: z.string().min(2, "School name must be at least 2 characters"),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  country: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  website: z.string().url("Invalid website URL").optional().or(z.literal("")),
  primaryColor: z.string().optional(),
  secondaryColor: z.string().optional(),
  isActive: z.boolean().default(true),
});

type SchoolFormValues = z.infer<typeof schoolFormSchema>;

export default function SchoolManagement() {
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSystemAdminUser, setIsSystemAdminUser] = useState(false);
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [invitationCode, setInvitationCode] = useState("");
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();
  const { profile } = useAuth();

  // Initialize form
  const form = useForm<SchoolFormValues>({
    resolver: zodResolver(schoolFormSchema),
    defaultValues: {
      name: "",
      address: "",
      city: "",
      state: "",
      zip: "",
      country: "",
      phone: "",
      email: "",
      website: "",
      primaryColor: "#4f46e5", // Default indigo color
      secondaryColor: "#f97316", // Default orange color
      isActive: true,
    },
  });

  // Fetch schools on component mount
  useEffect(() => {
    fetchSchools();
    checkSystemAdmin();
  }, [profile]);

  // Check if current user is a system admin
  const checkSystemAdmin = async () => {
    if (profile) {
      const isAdmin = await isSystemAdmin(profile.id);
      setIsSystemAdminUser(isAdmin);
    }
  };

  // Fetch all schools
  const fetchSchools = async () => {
    setLoading(true);
    try {
      const schoolsData = await getAllSchools();
      setSchools(schoolsData);
    } catch (error) {
      console.error("Error fetching schools:", error);
      toast({
        title: "Error",
        description: "Failed to fetch schools",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Generate a new invitation code
  const generateInvitationCode = () => {
    const code =
      "INV-" + Math.random().toString(36).substring(2, 10).toUpperCase();
    setInvitationCode(code);
    return code;
  };

  // Copy invitation code to clipboard
  const copyInvitationCode = () => {
    navigator.clipboard.writeText(invitationCode);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Handle form submission for creating/editing a school
  const onSubmit = async (data: SchoolFormValues) => {
    try {
      if (isCreating) {
        // Generate invitation code if creating a new school
        const code = generateInvitationCode();

        // Map form data to match the schools table schema
        const schoolData = {
          name: data.name,
          address: data.address,
          city: data.city,
          state: data.state,
          zip: data.zip,
          country: data.country,
          phone: data.phone,
          email: data.email,
          website: data.website,
          primaryColor: data.primaryColor,
          secondaryColor: data.secondaryColor,
          isActive: data.isActive,
          invitation_code: code,
          status: data.isActive ? "active" : "inactive",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        console.log("Creating school with data:", schoolData);

        // Create new school
        const newSchool = await createSchool(schoolData);

        if (newSchool) {
          toast({
            title: "Success",
            description: "School created successfully",
          });
          setInvitationCode(code);
          setIsCreating(false); // Close the dialog
          fetchSchools();
        } else {
          toast({
            title: "Error",
            description: "Failed to create school. Check console for details.",
            variant: "destructive",
          });
        }
      } else if (isEditing && selectedSchool) {
        // Update existing school
        const updatedSchool = await updateSchool(selectedSchool.id, {
          ...data,
          status: data.isActive ? "active" : "inactive",
          updated_at: new Date().toISOString(),
        });

        if (updatedSchool) {
          toast({
            title: "Success",
            description: "School updated successfully",
          });
          setIsEditing(false); // Close the dialog
          fetchSchools();
        } else {
          toast({
            title: "Error",
            description: "Failed to update school. Check console for details.",
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      console.error("Error saving school:", error);
      toast({
        title: "Error",
        description: "Failed to save school",
        variant: "destructive",
      });
    }
  };

  // Handle editing a school
  const handleEditSchool = (school: School) => {
    setSelectedSchool(school);
    setIsEditing(true);
    setIsCreating(false);

    // Reset form with school data
    form.reset({
      name: school.name,
      address: school.address || "",
      city: school.city || "",
      state: school.state || "",
      zip: school.zip || "",
      country: school.country || "",
      phone: school.phone || "",
      email: school.email || "",
      website: school.website || "",
      primaryColor: school.primaryColor || "#4f46e5",
      secondaryColor: school.secondaryColor || "#f97316",
      isActive: school.isActive !== false, // Default to true if undefined
    });
  };

  // Handle creating a new school
  const handleCreateSchool = () => {
    setSelectedSchool(null);
    setIsEditing(false);
    setIsCreating(true);
    generateInvitationCode();

    // Reset form with default values
    form.reset({
      name: "",
      address: "",
      city: "",
      state: "",
      zip: "",
      country: "",
      phone: "",
      email: "",
      website: "",
      primaryColor: "#4f46e5",
      secondaryColor: "#f97316",
      isActive: true,
    });
  };

  // Filter schools based on search query
  const filteredSchools = schools.filter(
    (school) =>
      school.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (school.city &&
        school.city.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (school.state &&
        school.state.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Render loading skeleton
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>School Management</CardTitle>
          <CardDescription>Manage schools in the system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // If not a system admin, show access denied message
  if (!isSystemAdminUser) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>School Management</CardTitle>
          <CardDescription>Manage schools in the system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <X className="h-16 w-16 text-destructive mb-4" />
            <h3 className="text-xl font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">
              You need system administrator privileges to access this section.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>School Management</CardTitle>
          <CardDescription>Manage schools in the system</CardDescription>
        </div>
        <Button
          onClick={handleCreateSchool}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add School
        </Button>
      </CardHeader>
      <CardContent>
        <div className="flex items-center mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search schools..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          <Button variant="outline" className="ml-2" onClick={fetchSchools}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>

        {filteredSchools.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <Building className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Schools Found</h3>
            <p className="text-muted-foreground">
              {searchQuery
                ? "No schools match your search criteria."
                : "There are no schools in the system yet."}
            </p>
            {searchQuery && (
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setSearchQuery("")}
              >
                Clear Search
              </Button>
            )}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>School Name</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSchools.map((school) => (
                <TableRow key={school.id}>
                  <TableCell className="font-medium">{school.name}</TableCell>
                  <TableCell>
                    {[school.city, school.state, school.country]
                      .filter(Boolean)
                      .join(", ")}
                  </TableCell>
                  <TableCell>
                    {school.email || school.phone || "No contact info"}
                  </TableCell>
                  <TableCell>
                    {school.isActive !== false ? (
                      <Badge variant="success" className="bg-green-500">
                        Active
                      </Badge>
                    ) : (
                      <Badge variant="destructive">Inactive</Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditSchool(school)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* School Form Dialog */}
      <Dialog
        open={isCreating || isEditing}
        onOpenChange={(open) => {
          if (!open) {
            setIsCreating(false);
            setIsEditing(false);
          }
        }}
      >
        <DialogContent className="max-w-2xl max-h-[70vh] overflow-y-auto p-4">
          <DialogHeader className="py-1 space-y-0.5">
            <DialogTitle className="text-lg">
              {isCreating ? "Add New School" : "Edit School"}
            </DialogTitle>
            <DialogDescription className="text-xs">
              {isCreating
                ? "Create a new school in the system."
                : "Update school information."}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
              <div className="grid grid-cols-2 gap-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem className="col-span-2 space-y-1">
                      <FormLabel className="text-sm">School Name*</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter school name"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm">Address</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Street address"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm">City</FormLabel>
                      <FormControl>
                        <Input placeholder="City" {...field} className="h-8" />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm">State/Province</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="State/Province"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="zip"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm">ZIP/Postal Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="ZIP/Postal Code"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm">Country</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Country"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm">Phone</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Phone number"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm">Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Email address"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="website"
                  render={({ field }) => (
                    <FormItem className="space-y-1">
                      <FormLabel className="text-sm">Website</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Website URL"
                          {...field}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  )}
                />

                <div className="col-span-2 grid grid-cols-2 gap-2">
                  <FormField
                    control={form.control}
                    name="primaryColor"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <FormLabel className="text-sm">Primary Color</FormLabel>
                        <div className="flex gap-2 items-center">
                          <FormControl>
                            <Input
                              type="color"
                              {...field}
                              className="w-10 h-8 p-1"
                            />
                          </FormControl>
                          <Input
                            value={field.value}
                            onChange={field.onChange}
                            className="h-8 text-xs"
                          />
                        </div>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="secondaryColor"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <FormLabel className="text-sm">
                          Secondary Color
                        </FormLabel>
                        <div className="flex gap-2 items-center">
                          <FormControl>
                            <Input
                              type="color"
                              {...field}
                              className="w-10 h-8 p-1"
                            />
                          </FormControl>
                          <Input
                            value={field.value}
                            onChange={field.onChange}
                            className="h-8 text-xs"
                          />
                        </div>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-2">
                      <div>
                        <FormLabel className="text-sm">Active Status</FormLabel>
                        <FormDescription className="text-xs">
                          Inactive schools won't be available for new users
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {isCreating && (
                  <div className="flex flex-row items-center justify-between rounded-lg border p-2">
                    <div>
                      <Label className="text-sm">Invitation Code</Label>
                      <div className="text-xs text-muted-foreground">
                        Share this code with users to join this school
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Input
                        value={invitationCode}
                        readOnly
                        className="w-36 h-8 text-xs font-mono"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        className="h-8 w-8"
                        onClick={copyInvitationCode}
                      >
                        {copied ? (
                          <Check className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter className="py-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setIsCreating(false);
                    setIsEditing(false);
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit" size="sm">
                  {isCreating ? "Create School" : "Update School"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
