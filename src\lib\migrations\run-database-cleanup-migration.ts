import { supabase } from "@/lib/supabase";
import { runDatabaseCleanupMigrationV2 } from "./run-database-cleanup-migration-v2";
import { runDatabaseCleanupMigrationDirect } from "./run-database-cleanup-migration-direct";

/**
 * Manual migration to create the database_cleanup_settings table
 * This function directly executes the SQL to create the table
 */
export const runDatabaseCleanupMigration = async (): Promise<boolean> => {
  // Try the direct approach first
  try {
    const directResult = await runDatabaseCleanupMigrationDirect();
    if (directResult) {
      return true;
    }
  } catch (error) {
    console.log("Error running direct migration, trying v2:", error);
  }

  // Try the v2 version next
  try {
    const v2Result = await runDatabaseCleanupMigrationV2();
    if (v2Result) {
      return true;
    }
  } catch (error) {
    console.log("Error running v2 migration, falling back to v1:", error);
  }

  // Fall back to the original version if both fail
  try {
    console.log("Running database cleanup settings migration manually...");

    // Create the execute_sql function directly using a SQL query
    try {
      // First try to create the execute_sql function directly
      const { error: directFunctionError } = await supabase
        .from("_temp_migration")
        .select("*");

      // If we get here, we can try to create the function using a direct SQL query
      if (directFunctionError && directFunctionError.code === "42P01") {
        // Table doesn't exist, which is expected
        console.log("Creating execute_sql function directly...");

        // Use the Supabase Management API to create the function
        const { error: managementError } = await supabase
          .rpc("postgres_execute", {
            query: `
            -- Create execute_sql function if it doesn't exist
            CREATE OR REPLACE FUNCTION execute_sql(sql text)
            RETURNS void
            LANGUAGE plpgsql
            SECURITY DEFINER
            AS $$
            BEGIN
              EXECUTE sql;
            END;
            $$;

            -- Grant execute permission
            GRANT EXECUTE ON FUNCTION execute_sql(text) TO authenticated;
          `,
          })
          .catch(() => ({
            error: { message: "Function postgres_execute does not exist" },
          }));

        if (managementError) {
          console.log(
            "Note: Could not create execute_sql function directly:",
            managementError
          );
          // We'll try to continue anyway
        }
      }
    } catch (e) {
      console.log("Error checking for _temp_migration table:", e);
      // Continue anyway
    }

    // Now try to use the execute_sql function
    const { error: functionError } = await supabase
      .rpc("execute_sql", {
        sql: `
        -- Create execute_sql function if it doesn't exist
        CREATE OR REPLACE FUNCTION execute_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$;

        -- Grant execute permission
        GRANT EXECUTE ON FUNCTION execute_sql(text) TO authenticated;
      `,
      })
      .catch(() => ({
        error: { message: "Function execute_sql does not exist" },
      }));

    if (functionError) {
      // If the function doesn't exist, we need to create it another way
      console.log("Note: execute_sql function may not exist:", functionError);

      // Try to create the function using a direct SQL query through the Supabase Management API
      try {
        const { error: managementError } = await supabase
          .rpc("postgres_execute", {
            query: `
            -- Create execute_sql function if it doesn't exist
            CREATE OR REPLACE FUNCTION execute_sql(sql text)
            RETURNS void
            LANGUAGE plpgsql
            SECURITY DEFINER
            AS $$
            BEGIN
              EXECUTE sql;
            END;
            $$;

            -- Grant execute permission
            GRANT EXECUTE ON FUNCTION execute_sql(text) TO authenticated;
          `,
          })
          .catch(() => ({
            error: { message: "Function postgres_execute does not exist" },
          }));

        if (managementError) {
          console.log(
            "Note: Could not create execute_sql function:",
            managementError
          );
          // We'll try to continue anyway
        }
      } catch (e) {
        console.log("Error creating execute_sql function:", e);
        // Continue anyway
      }
    }

    // Create the database_cleanup_settings table
    const { error: createTableError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create database_cleanup_settings table
        CREATE TABLE IF NOT EXISTS public.database_cleanup_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          enabled BOOLEAN DEFAULT false,
          notifications_retention_days INTEGER DEFAULT 90,
          attendance_records_retention_days INTEGER DEFAULT 365,
          audit_logs_retention_days INTEGER DEFAULT 180,
          excuses_retention_days INTEGER DEFAULT 180,
          alerts_retention_days INTEGER DEFAULT 90,
          history_retention_days INTEGER DEFAULT 180,
          user_activity_logs_retention_days INTEGER DEFAULT 90,
          notification_logs_retention_days INTEGER DEFAULT 90,
          selected_data_types JSONB DEFAULT '["notifications", "attendance_records", "audit_logs", "excuses", "alerts", "history", "user_activity_logs", "notification_logs"]'::jsonb,
          last_cleanup_at TIMESTAMP WITH TIME ZONE,
          next_cleanup_at TIMESTAMP WITH TIME ZONE,
          cleanup_frequency TEXT DEFAULT 'weekly' CHECK (cleanup_frequency IN ('daily', 'weekly', 'monthly')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
          updated_by UUID REFERENCES profiles(id) ON DELETE SET NULL
        );

        -- Enable RLS
        ALTER TABLE public.database_cleanup_settings ENABLE ROW LEVEL SECURITY;

        -- Only system admins can view database cleanup settings
        CREATE POLICY "System admins can view database cleanup settings"
        ON public.database_cleanup_settings
        FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.access_level = 3
          )
        );

        -- Only system admins can insert database cleanup settings
        CREATE POLICY "System admins can insert database cleanup settings"
        ON public.database_cleanup_settings
        FOR INSERT
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.access_level = 3
          )
        );

        -- Only system admins can update database cleanup settings
        CREATE POLICY "System admins can update database cleanup settings"
        ON public.database_cleanup_settings
        FOR UPDATE
        USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.access_level = 3
          )
        );

        -- Insert default settings if not exists
        INSERT INTO public.database_cleanup_settings (
          enabled,
          notifications_retention_days,
          attendance_records_retention_days,
          audit_logs_retention_days,
          excuses_retention_days,
          alerts_retention_days,
          history_retention_days,
          user_activity_logs_retention_days,
          notification_logs_retention_days,
          selected_data_types,
          cleanup_frequency,
          next_cleanup_at
        )
        SELECT
          false,
          90,
          365,
          180,
          180,
          90,
          180,
          90,
          90,
          '["notifications", "attendance_records", "audit_logs", "excuses", "alerts", "history", "user_activity_logs", "notification_logs"]'::jsonb,
          'weekly',
          (now() + interval '7 days')
        WHERE NOT EXISTS (
          SELECT 1 FROM public.database_cleanup_settings
        );
      `,
    });

    if (createTableError) {
      console.error(
        "Error creating database cleanup settings table:",
        createTableError
      );
      return false;
    }

    // Create the database cleanup function
    const { error: createFunctionError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create function to perform database cleanup
        CREATE OR REPLACE FUNCTION perform_database_cleanup()
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          settings RECORD;
          deleted_notifications INTEGER := 0;
          deleted_attendance_records INTEGER := 0;
          deleted_audit_logs INTEGER := 0;
          deleted_excuses INTEGER := 0;
          deleted_alerts INTEGER := 0;
          deleted_history INTEGER := 0;
          deleted_user_activity_logs INTEGER := 0;
          deleted_notification_logs INTEGER := 0;
          next_cleanup TIMESTAMP WITH TIME ZONE;
          selected_types JSONB;
        BEGIN
          -- Get the current settings
          SELECT * INTO settings FROM database_cleanup_settings LIMIT 1;

          -- If cleanup is not enabled, exit
          IF NOT settings.enabled THEN
            RETURN false;
          END IF;

          -- Get selected data types
          selected_types := settings.selected_data_types;

          -- Clean up notifications if selected
          IF settings.notifications_retention_days > 0 AND selected_types ? 'notifications' THEN
            DELETE FROM notifications
            WHERE created_at < (now() - (settings.notifications_retention_days || ' days')::interval)
            RETURNING COUNT(*) INTO deleted_notifications;
          END IF;

          -- Clean up attendance records if selected
          IF settings.attendance_records_retention_days > 0 AND selected_types ? 'attendance_records' THEN
            DELETE FROM attendance_records
            WHERE created_at < (now() - (settings.attendance_records_retention_days || ' days')::interval)
            RETURNING COUNT(*) INTO deleted_attendance_records;
          END IF;

          -- Clean up audit logs if selected
          IF settings.audit_logs_retention_days > 0 AND selected_types ? 'audit_logs' THEN
            DELETE FROM audit_logs
            WHERE created_at < (now() - (settings.audit_logs_retention_days || ' days')::interval)
            RETURNING COUNT(*) INTO deleted_audit_logs;
          END IF;

          -- Clean up excuses if selected
          IF settings.excuses_retention_days > 0 AND selected_types ? 'excuses' THEN
            DELETE FROM excuses
            WHERE created_at < (now() - (settings.excuses_retention_days || ' days')::interval)
            RETURNING COUNT(*) INTO deleted_excuses;
          END IF;

          -- Clean up alerts if selected
          IF settings.alerts_retention_days > 0 AND selected_types ? 'alerts' THEN
            -- Check if attendance_alerts table exists
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'attendance_alerts') THEN
              DELETE FROM attendance_alerts
              WHERE created_at < (now() - (settings.alerts_retention_days || ' days')::interval)
              RETURNING COUNT(*) INTO deleted_alerts;
            END IF;
          END IF;

          -- Clean up history if selected
          IF settings.history_retention_days > 0 AND selected_types ? 'history' THEN
            -- Check if history table exists
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'history') THEN
              DELETE FROM history
              WHERE created_at < (now() - (settings.history_retention_days || ' days')::interval)
              RETURNING COUNT(*) INTO deleted_history;
            END IF;
          END IF;

          -- Clean up user activity logs if selected
          IF settings.user_activity_logs_retention_days > 0 AND selected_types ? 'user_activity_logs' THEN
            -- Check if user_activity_logs table exists
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_activity_logs') THEN
              DELETE FROM user_activity_logs
              WHERE created_at < (now() - (settings.user_activity_logs_retention_days || ' days')::interval)
              RETURNING COUNT(*) INTO deleted_user_activity_logs;
            END IF;
          END IF;

          -- Clean up notification logs if selected
          IF settings.notification_logs_retention_days > 0 AND selected_types ? 'notification_logs' THEN
            -- Check if notification_logs table exists
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notification_logs') THEN
              DELETE FROM notification_logs
              WHERE created_at < (now() - (settings.notification_logs_retention_days || ' days')::interval)
              RETURNING COUNT(*) INTO deleted_notification_logs;
            END IF;
          END IF;

          -- Calculate next cleanup date based on frequency
          IF settings.cleanup_frequency = 'daily' THEN
            next_cleanup := now() + interval '1 day';
          ELSIF settings.cleanup_frequency = 'weekly' THEN
            next_cleanup := now() + interval '7 days';
          ELSIF settings.cleanup_frequency = 'monthly' THEN
            next_cleanup := now() + interval '1 month';
          ELSE
            next_cleanup := now() + interval '7 days';
          END IF;

          -- Update the settings with the cleanup results
          UPDATE database_cleanup_settings
          SET
            last_cleanup_at = now(),
            next_cleanup_at = next_cleanup,
            updated_at = now()
          WHERE id = settings.id;

          -- Log the cleanup in audit_logs
          INSERT INTO audit_logs (
            action_type,
            entity_type,
            details,
            created_at
          ) VALUES (
            'database_cleanup',
            'system',
            jsonb_build_object(
              'notifications_deleted', deleted_notifications,
              'attendance_records_deleted', deleted_attendance_records,
              'audit_logs_deleted', deleted_audit_logs,
              'excuses_deleted', deleted_excuses,
              'alerts_deleted', deleted_alerts,
              'history_deleted', deleted_history,
              'user_activity_logs_deleted', deleted_user_activity_logs,
              'notification_logs_deleted', deleted_notification_logs,
              'next_cleanup', next_cleanup
            ),
            now()
          );

          RETURN true;
        END;
        $$;

        -- Grant execute permission on the function
        GRANT EXECUTE ON FUNCTION perform_database_cleanup() TO authenticated;
      `,
    });

    if (createFunctionError) {
      console.error(
        "Error creating database cleanup function:",
        createFunctionError
      );
      return false;
    }

    console.log("Database cleanup settings migration completed successfully");
    return true;
  } catch (error) {
    console.error("Error in database cleanup settings migration:", error);
    return false;
  }
};
