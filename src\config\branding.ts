/**
 * 🎨 APP BRANDING CONFIGURATION
 * =====================================================
 * This file centralizes all app branding and naming.
 * All values come from environment variables for easy customization.
 *
 * 🚀 TO CHANGE APP NAME:
 * 1. Edit the .env file
 * 2. Restart dev server: npm run dev
 * 3. That's it! ✨
 */

// Get environment variables with fallbacks
const getEnvVar = (key: string, fallback: string): string => {
  const value = import.meta.env[key];
  if (!value || value === "") {
    return fallback;
  }
  return value;
};

// 🌐 Language-Specific Branding
export const BRANDING_EN = {
  // Core App Identity
  APP_NAME: getEnvVar("VITE_APP_NAME", "Attendance Tracking System"),
  APP_SHORT_NAME: getEnvVar("VITE_APP_SHORT_NAME", "ATS"),
  APP_DESCRIPTION: getEnvVar(
    "VITE_APP_DESCRIPTION",
    "Secure and efficient attendance tracking system for educational institutions"
  ),

  // Company/Organization Info
  COMPANY_NAME: getEnvVar(
    "VITE_COMPANY_NAME",
    "Attendance Tracking System Team"
  ),
  COMPANY_WEBSITE: getEnvVar(
    "VITE_COMPANY_WEBSITE",
    "https://attendancetracking.edu"
  ),

  // Contact Information
  CONTACT_EMAIL: getEnvVar(
    "VITE_CONTACT_EMAIL",
    "<EMAIL>"
  ),
  SUPPORT_EMAIL: getEnvVar(
    "VITE_SUPPORT_EMAIL",
    "<EMAIL>"
  ),
  NOREPLY_EMAIL: getEnvVar(
    "VITE_NOREPLY_EMAIL",
    "<EMAIL>"
  ),
} as const;

export const BRANDING_TR = {
  // Core App Identity
  APP_NAME: getEnvVar("VITE_APP_NAME_TR", "Devam Takip Sistemi"),
  APP_SHORT_NAME: getEnvVar("VITE_APP_SHORT_NAME_TR", "DTS"),
  APP_DESCRIPTION: getEnvVar(
    "VITE_APP_DESCRIPTION_TR",
    "Eğitim kurumları için güvenli ve etkili devam takip sistemi"
  ),

  // Company/Organization Info
  COMPANY_NAME: getEnvVar("VITE_COMPANY_NAME_TR", "Devam Takip Sistemi Ekibi"),
  COMPANY_WEBSITE: getEnvVar(
    "VITE_COMPANY_WEBSITE_TR",
    "https://devamtakip.edu.tr"
  ),

  // Contact Information
  CONTACT_EMAIL: getEnvVar(
    "VITE_CONTACT_EMAIL_TR",
    "<EMAIL>"
  ),
  SUPPORT_EMAIL: getEnvVar("VITE_SUPPORT_EMAIL_TR", "<EMAIL>"),
  NOREPLY_EMAIL: getEnvVar(
    "VITE_NOREPLY_EMAIL_TR",
    "<EMAIL>"
  ),
} as const;

// 🎨 Dynamic Branding (language-aware)
export const getBranding = (language: string = "en") => {
  return language === "tr" ? BRANDING_TR : BRANDING_EN;
};

// 🎨 Default Branding (English)
export const BRANDING = BRANDING_EN;

// 📧 Email Templates (Language-aware)
export const getEmailTemplates = (language: string = "en") => {
  const branding = getBranding(language);
  return {
    // Email signatures
    SIGNATURE: branding.APP_NAME,
    FOOTER:
      language === "tr"
        ? `Teşekkürler,\n${branding.APP_NAME}`
        : `Thank you,\n${branding.APP_NAME}`,

    // Subject prefixes
    SUBJECT_PREFIX: `${branding.APP_SHORT_NAME} -`,

    // Common email content
    SUPPORT_CONTACT:
      language === "tr"
        ? `Herhangi bir sorunuz varsa, lütfen ${branding.SUPPORT_EMAIL} adresinden bize ulaşın`
        : `If you have any questions, please contact us at ${branding.SUPPORT_EMAIL}`,
  };
};

// 🌐 SEO & Meta Tags (Language-aware)
export const getSEO = (language: string = "en") => {
  const branding = getBranding(language);
  const keywords =
    language === "tr"
      ? `devam, takip, okul, eğitim, ${branding.APP_SHORT_NAME.toLowerCase()}`
      : `attendance, tracking, school, education, ${branding.APP_SHORT_NAME.toLowerCase()}`;

  return {
    TITLE: branding.APP_NAME,
    DESCRIPTION: branding.APP_DESCRIPTION,
    KEYWORDS: keywords,
    AUTHOR: branding.COMPANY_NAME,

    // Open Graph
    OG_TITLE: branding.APP_NAME,
    OG_DESCRIPTION: branding.APP_DESCRIPTION,
    OG_SITE_NAME: branding.APP_NAME,
  };
};

// 🎯 UI Text Helpers (Language-aware)
export const getUIText = (language: string = "en") => {
  const branding = getBranding(language);

  if (language === "tr") {
    return {
      // Loading messages
      LOADING: `${branding.APP_NAME} yükleniyor...`,
      WELCOME: `${branding.APP_NAME}'e hoş geldiniz!`,

      // Login/Auth
      LOGIN_TITLE: branding.APP_NAME,
      LOGIN_SUBTITLE: "Devam takip sistemine erişmek için giriş yapın",
      SIGNUP_SUBTITLE: `${branding.APP_NAME} Kayıt`,

      // Dashboard
      DASHBOARD_TITLE: `${branding.APP_NAME} Kontrol Paneli`,

      // Footer
      FOOTER_COPYRIGHT: `© ${new Date().getFullYear()} ${
        branding.COMPANY_NAME
      }. Tüm hakları saklıdır.`,
    };
  }

  // English (default)
  return {
    // Loading messages
    LOADING: `Loading ${branding.APP_NAME}...`,
    WELCOME: `Welcome to ${branding.APP_NAME}!`,

    // Login/Auth
    LOGIN_TITLE: branding.APP_NAME,
    LOGIN_SUBTITLE: "Login to access the attendance system",
    SIGNUP_SUBTITLE: `${branding.APP_NAME} Registration`,

    // Dashboard
    DASHBOARD_TITLE: `${branding.APP_NAME} Dashboard`,

    // Footer
    FOOTER_COPYRIGHT: `© ${new Date().getFullYear()} ${
      branding.COMPANY_NAME
    }. All rights reserved.`,
  };
};

// 📧 Default Email Templates (English)
export const EMAIL_TEMPLATES = getEmailTemplates("en");

// 🌐 Default SEO (English)
export const SEO = getSEO("en");

// 🎯 Default UI Text (English)
export const UI_TEXT = getUIText("en");

// 🔧 Development Helpers
export const DEV_INFO = {
  // For debugging
  ENV_CHECK: () => {
    // Development helper - removed console logs for production
  },

  // Validate configuration
  VALIDATE: () => {
    const issues: string[] = [];

    if (BRANDING.APP_NAME.length > 50) {
      issues.push("App name is too long (>50 chars)");
    }

    if (!BRANDING.CONTACT_EMAIL.includes("@")) {
      issues.push("Contact email appears invalid");
    }

    if (issues.length > 0) {
      console.warn("⚠️ Branding Configuration Issues:", issues);
    } else {
      console.log("✅ Branding configuration looks good!");
    }

    return issues.length === 0;
  },
} as const;

// 🚀 Export everything for easy importing
export default BRANDING;

// Type definitions for TypeScript
export type BrandingConfig = typeof BRANDING;
export type EmailTemplates = typeof EMAIL_TEMPLATES;
export type SEOConfig = typeof SEO;
export type UITextConfig = typeof UI_TEXT;
