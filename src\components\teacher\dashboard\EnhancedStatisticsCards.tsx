import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { UserChe<PERSON>, UserX, Clock, FileCheck } from "lucide-react";
import { useTranslation } from "react-i18next";

interface StatisticsProps {
  stats: {
    present: number;
    absent: number;
    late: number;
    excused: number;
    total: number;
  };
}

export function EnhancedStatisticsCards({ stats }: StatisticsProps) {
  const { t } = useTranslation();
  
  // Calculate percentages
  const presentPercentage = stats.total > 0 ? Math.round((stats.present / stats.total) * 100) : 0;
  const absentPercentage = stats.total > 0 ? Math.round((stats.absent / stats.total) * 100) : 0;
  const latePercentage = stats.total > 0 ? Math.round((stats.late / stats.total) * 100) : 0;
  const excusedPercentage = stats.total > 0 ? Math.round((stats.excused / stats.total) * 100) : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card className="md:col-span-1 stats-card stats-card-present">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium text-green-700 dark:text-green-400 flex items-center gap-2">
            <div className="p-2 bg-green-100 rounded-full dark:bg-green-900/30">
              <UserCheck className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
            {t("Present")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-2">
            <div className="flex items-baseline">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {stats.present}
              </div>
              <div className="text-sm text-muted-foreground font-normal ml-1">
                / {stats.total}
              </div>
            </div>
            
            <div className="w-full h-2 bg-green-100 rounded-full overflow-hidden dark:bg-green-900/30">
              <div 
                className="h-full bg-green-500 dark:bg-green-400 rounded-full"
                style={{ width: `${presentPercentage}%` }}
              />
            </div>
            
            <div className="text-xs text-muted-foreground">
              {presentPercentage}% {t("of students present")}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="md:col-span-1 stats-card stats-card-absent">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium text-red-700 dark:text-red-400 flex items-center gap-2">
            <div className="p-2 bg-red-100 rounded-full dark:bg-red-900/30">
              <UserX className="h-4 w-4 text-red-600 dark:text-red-400" />
            </div>
            {t("Absent")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-2">
            <div className="flex items-baseline">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {stats.absent}
              </div>
              <div className="text-sm text-muted-foreground font-normal ml-1">
                / {stats.total}
              </div>
            </div>
            
            <div className="w-full h-2 bg-red-100 rounded-full overflow-hidden dark:bg-red-900/30">
              <div 
                className="h-full bg-red-500 dark:bg-red-400 rounded-full"
                style={{ width: `${absentPercentage}%` }}
              />
            </div>
            
            <div className="text-xs text-muted-foreground">
              {absentPercentage}% {t("of students absent")}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="md:col-span-1 stats-card stats-card-late">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium text-amber-700 dark:text-amber-400 flex items-center gap-2">
            <div className="p-2 bg-amber-100 rounded-full dark:bg-amber-900/30">
              <Clock className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            </div>
            {t("Late")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-2">
            <div className="flex items-baseline">
              <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
                {stats.late}
              </div>
              <div className="text-sm text-muted-foreground font-normal ml-1">
                / {stats.total}
              </div>
            </div>
            
            <div className="w-full h-2 bg-amber-100 rounded-full overflow-hidden dark:bg-amber-900/30">
              <div 
                className="h-full bg-amber-500 dark:bg-amber-400 rounded-full"
                style={{ width: `${latePercentage}%` }}
              />
            </div>
            
            <div className="text-xs text-muted-foreground">
              {latePercentage}% {t("of students late")}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="md:col-span-1 stats-card stats-card-excused">
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-400 flex items-center gap-2">
            <div className="p-2 bg-blue-100 rounded-full dark:bg-blue-900/30">
              <FileCheck className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            {t("Excused")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-2">
            <div className="flex items-baseline">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {stats.excused}
              </div>
              <div className="text-sm text-muted-foreground font-normal ml-1">
                / {stats.total}
              </div>
            </div>
            
            <div className="w-full h-2 bg-blue-100 rounded-full overflow-hidden dark:bg-blue-900/30">
              <div 
                className="h-full bg-blue-500 dark:bg-blue-400 rounded-full"
                style={{ width: `${excusedPercentage}%` }}
              />
            </div>
            
            <div className="text-xs text-muted-foreground">
              {excusedPercentage}% {t("of students excused")}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
