import React, { useEffect, useState } from "react";

interface SimpleCameraIconProps {
  size?: number;
  color?: string;
  isOutsideRecordingHours?: boolean;
}

const SimpleCameraIcon: React.FC<SimpleCameraIconProps> = ({
  size = 72,
  color,
  isOutsideRecordingHours = false,
}) => {
  // Get the primary color from CSS variables
  const [primaryColor, setPrimaryColor] = useState("");

  useEffect(() => {
    // Get the primary color from CSS variables
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    const primaryHsl = computedStyle.getPropertyValue("--primary").trim();

    // Convert HSL to a usable color format
    if (primaryHsl) {
      // For HSL format like "222.2 47.4% 11.2%"
      const hslColor = `hsl(${primaryHsl})`;
      setPrimaryColor(hslColor);
    } else {
      // Fallback color
      setPrimaryColor("#f39228");
    }
  }, []);

  // Use the provided color, or primary color from theme, or fallback to muted if outside recording hours
  const iconColor = isOutsideRecordingHours
    ? "#9ca3af"
    : color || primaryColor || "#f39228";
  // Create a simple camera icon using HTML/CSS
  return (
    <div
      style={{
        width: size,
        height: size,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <div
        style={{
          width: Math.round(size * 0.7),
          height: Math.round(size * 0.5),
          backgroundColor: iconColor,
          borderRadius: Math.round(size * 0.1),
          position: "relative",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {/* Camera lens */}
        <div
          style={{
            width: Math.round(size * 0.25),
            height: Math.round(size * 0.25),
            borderRadius: "50%",
            border: `${Math.round(size * 0.03)}px solid white`,
            backgroundColor: "transparent",
          }}
        />

        {/* Camera top part */}
        <div
          style={{
            width: Math.round(size * 0.3),
            height: Math.round(size * 0.15),
            backgroundColor: iconColor,
            borderRadius: `${Math.round(size * 0.05)}px ${Math.round(
              size * 0.05
            )}px 0 0`,
            position: "absolute",
            top: Math.round(size * -0.1),
            left: "50%",
            transform: "translateX(-50%)",
          }}
        />
      </div>
    </div>
  );
};

export default SimpleCameraIcon;
