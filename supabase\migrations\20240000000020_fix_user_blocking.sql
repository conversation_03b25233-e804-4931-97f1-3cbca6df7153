-- Fix user blocking issue by ensuring proper default values
-- Add missing columns if they don't exist and set proper defaults

-- Add is_blocked column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'is_blocked'
  ) THEN
    ALTER TABLE profiles ADD COLUMN is_blocked BOOLEAN DEFAULT false;
  END IF;
END $$;

-- Add blocked_at column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'blocked_at'
  ) THEN
    ALTER TABLE profiles ADD COLUMN blocked_at TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Add is_deleted column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'is_deleted'
  ) THEN
    ALTER TABLE profiles ADD COLUMN is_deleted BOOLEAN DEFAULT false;
  END IF;
END $$;

-- Add deleted_at column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'deleted_at'
  ) THEN
    ALTER TABLE profiles ADD COLUMN deleted_at TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Add deleted_by column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'deleted_by'
  ) THEN
    ALTER TABLE profiles ADD COLUMN deleted_by UUID REFERENCES profiles(id);
  END IF;
END $$;

-- Add is_verified column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'is_verified'
  ) THEN
    ALTER TABLE profiles ADD COLUMN is_verified BOOLEAN DEFAULT true;
  END IF;
END $$;

-- Ensure profile_completed column exists with proper default
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'profile_completed'
  ) THEN
    ALTER TABLE profiles ADD COLUMN profile_completed BOOLEAN DEFAULT true;
  ELSE
    -- Update the default value for existing column
    ALTER TABLE profiles ALTER COLUMN profile_completed SET DEFAULT true;
  END IF;
END $$;

-- Fix existing users who might be incorrectly blocked
-- Set profile_completed = true for all existing admin users (system admins and school admins)
UPDATE profiles 
SET 
  profile_completed = true,
  is_blocked = false,
  is_deleted = false,
  is_verified = true
WHERE role = 'admin' AND access_level IN (1, 2, 3);

-- Set profile_completed = true for all existing teacher users
UPDATE profiles 
SET 
  profile_completed = true,
  is_blocked = false,
  is_deleted = false,
  is_verified = true
WHERE role = 'teacher';

-- For students, only update if they have basic required information
UPDATE profiles 
SET 
  profile_completed = true,
  is_blocked = false,
  is_deleted = false,
  is_verified = true
WHERE role = 'student' 
  AND name IS NOT NULL 
  AND email IS NOT NULL 
  AND student_id IS NOT NULL;

-- Create a function to ensure new users are not blocked by default
CREATE OR REPLACE FUNCTION ensure_user_not_blocked()
RETURNS TRIGGER AS $$
BEGIN
  -- Set default values for new users
  IF NEW.profile_completed IS NULL THEN
    NEW.profile_completed := true;
  END IF;
  
  IF NEW.is_blocked IS NULL THEN
    NEW.is_blocked := false;
  END IF;
  
  IF NEW.is_deleted IS NULL THEN
    NEW.is_deleted := false;
  END IF;
  
  IF NEW.is_verified IS NULL THEN
    NEW.is_verified := true;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to ensure new users are not blocked
DROP TRIGGER IF EXISTS ensure_user_not_blocked_trigger ON profiles;
CREATE TRIGGER ensure_user_not_blocked_trigger
  BEFORE INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION ensure_user_not_blocked();

-- Create a function to toggle block status (for admin use)
CREATE OR REPLACE FUNCTION toggle_block_status(p_id UUID, p_block BOOLEAN)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE profiles 
  SET 
    is_blocked = p_block,
    blocked_at = CASE WHEN p_block THEN NOW() ELSE NULL END,
    updated_at = NOW()
  WHERE id = p_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION toggle_block_status TO authenticated;
