-- Check if the notifications table exists
DO $$
BEGIN
    -- Add school_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'notifications' 
        AND column_name = 'school_id'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
        CREATE INDEX IF NOT EXISTS notifications_school_id_idx ON notifications(school_id);
    END IF;
    
    -- Make sure metadata is JSONB
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'notifications' 
        AND column_name = 'metadata' 
        AND data_type != 'jsonb'
    ) THEN
        ALTER TABLE public.notifications ALTER COLUMN metadata TYPE JSONB USING metadata::JSONB;
    END IF;
    
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'notifications' 
        AND column_name = 'created_at'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN created_at TIMESTAMPTZ NOT NULL DEFAULT NOW();
    END IF;
    
    -- Add updated_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'notifications' 
        AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN updated_at TIMESTAMPTZ;
    END IF;
    
    -- Create a trigger to update the updated_at column
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_trigger 
        WHERE tgname = 'set_notifications_updated_at'
    ) THEN
        CREATE OR REPLACE FUNCTION set_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        
        CREATE TRIGGER set_notifications_updated_at
        BEFORE UPDATE ON notifications
        FOR EACH ROW
        EXECUTE FUNCTION set_updated_at();
    END IF;
    
    -- Ensure RLS policies exist
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE tablename = 'notifications' 
        AND policyname = 'Students can view their own notifications'
    ) THEN
        ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
        
        -- Students can view their own notifications
        CREATE POLICY "Students can view their own notifications"
        ON notifications FOR SELECT
        USING (auth.uid()::text = student_id::text);
        
        -- Students can update their own notifications (to mark as read)
        CREATE POLICY "Students can update their own notifications"
        ON notifications FOR UPDATE
        USING (auth.uid()::text = student_id::text)
        WITH CHECK (auth.uid()::text = student_id::text);
        
        -- Teachers can view notifications they created
        CREATE POLICY "Teachers can view notifications they created"
        ON notifications FOR SELECT
        USING (auth.uid()::text = teacher_id::text);
        
        -- Teachers can create notifications
        CREATE POLICY "Teachers can create notifications"
        ON notifications FOR INSERT
        WITH CHECK (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role IN ('teacher', 'admin')
            )
        );
        
        -- Admins can view all notifications in their school
        CREATE POLICY "Admins can view all notifications in their school"
        ON notifications FOR SELECT
        USING (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role = 'admin' AND
                (
                    school_id IS NULL OR
                    school_id = (SELECT school_id FROM profiles WHERE id = auth.uid())
                )
            )
        );
        
        -- System admins can view all notifications
        CREATE POLICY "System admins can view all notifications"
        ON notifications FOR SELECT
        USING (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role = 'admin' AND access_level >= 2
            )
        );
    END IF;
END$$;
