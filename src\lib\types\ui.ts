/**
 * UI and Theme Types
 * Type definitions for user interface, theming, and visual components
 */

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
  muted: string;
  card: string;
  border: string;
  destructive?: string;
  warning?: string;
  success?: string;
  info?: string;
}

export interface ThemeSettings {
  lightTheme: ThemeColors;
  darkTheme: ThemeColors;
  applyToAllSchools: boolean;
  targetSchoolId: string | null;
  overrideSchoolCustomizations?: boolean;
  updated_at?: string;
}

export interface BrandingSettings {
  school_id: string;
  logo_url?: string;
  primary_color?: string;
  secondary_color?: string;
  accent_color?: string;
  font_family?: string;
  custom_css?: string;
  favicon_url?: string;
  updated_at?: string;
}

export interface CarouselContent {
  id: string;
  school_id: string;
  title: string;
  description?: string;
  image_url: string;
  active: boolean;
  display_order: number;
  start_date?: string;
  end_date?: string;
  target_audience: string[];
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  student_id: string;
  title: string;
  message: string;
  type: "info" | "warning" | "error" | "success" | "attendance" | "excuse" | "reminder";
  read: boolean;
  timestamp: string;
  metadata?: Record<string, any>;
  school_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Toast {
  id: string;
  title?: string;
  message: string;
  type: "success" | "error" | "warning" | "info";
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface Modal {
  id: string;
  title: string;
  content: React.ReactNode;
  size?: "sm" | "md" | "lg" | "xl";
  closable?: boolean;
  onClose?: () => void;
}

export interface TabItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  content: React.ReactNode;
  disabled?: boolean;
  badge?: string | number;
}

export interface MenuItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  href?: string;
  onClick?: () => void;
  children?: MenuItem[];
  disabled?: boolean;
  badge?: string | number;
  role?: string[];
}

export interface DataTableColumn<T = any> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  width?: string;
  align?: "left" | "center" | "right";
}

export interface DataTableProps<T = any> {
  data: T[];
  columns: DataTableColumn<T>[];
  loading?: boolean;
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
  };
  sorting?: {
    column: keyof T;
    direction: "asc" | "desc";
    onSort: (column: keyof T, direction: "asc" | "desc") => void;
  };
  filtering?: {
    filters: Record<string, any>;
    onFilter: (filters: Record<string, any>) => void;
  };
  selection?: {
    selectedRows: T[];
    onSelectionChange: (rows: T[]) => void;
  };
}

export interface FormField {
  name: string;
  label: string;
  type: "text" | "email" | "password" | "number" | "select" | "textarea" | "checkbox" | "radio" | "date" | "time";
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null;
  };
}

export interface FormProps {
  fields: FormField[];
  initialValues?: Record<string, any>;
  onSubmit: (values: Record<string, any>) => void | Promise<void>;
  loading?: boolean;
  submitLabel?: string;
  cancelLabel?: string;
  onCancel?: () => void;
}
