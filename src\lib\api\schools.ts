import { supabase } from "@/lib/supabase";
import { School } from "@/lib/types";
import { User } from "@/lib/types";
import { withSchoolContext } from "./queries";

/**
 * Fetches the list of schools from the schools table
 * @param user The current user for access control
 * @returns Array of School objects
 */
export const fetchSchools = async (user?: User | null): Promise<School[]> => {
  try {
    // Query the schools table
    let query = supabase.from("schools").select("*").order("name");

    // If user is provided and not a system admin, filter by their school
    if (user && user.accessLevel !== 3) {
      query = await withSchoolContext(query, user);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching schools:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching schools:", error);
    return [];
  }
};

/**
 * Fetches a school by ID
 * @param schoolId The school ID to fetch
 * @returns The school or null if not found
 */
export const fetchSchoolById = async (
  schoolId: string
): Promise<School | null> => {
  try {
    const { data, error } = await supabase
      .from("schools")
      .select("*")
      .eq("id", schoolId)
      .single();

    if (error) {
      console.error("Error fetching school by ID:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error fetching school by ID:", error);
    return null;
  }
};

/**
 * Creates a new school
 * @param schoolData The school data to create
 * @returns The created school or null if failed
 */
export const createSchool = async (
  schoolData: Partial<School>
): Promise<School | null> => {
  try {
    const { data, error } = await supabase
      .from("schools")
      .insert(schoolData)
      .select()
      .single();

    if (error) {
      console.error("Error creating school:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error creating school:", error);
    return null;
  }
};

/**
 * Updates a school
 * @param schoolId The school ID to update
 * @param schoolData The school data to update
 * @returns The updated school or null if failed
 */
export const updateSchool = async (
  schoolId: string,
  schoolData: Partial<School>
): Promise<School | null> => {
  try {
    const { data, error } = await supabase
      .from("schools")
      .update(schoolData)
      .eq("id", schoolId)
      .select()
      .single();

    if (error) {
      console.error("Error updating school:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error updating school:", error);
    return null;
  }
};

/**
 * Checks if there are any schools available
 * @returns Boolean indicating if schools are available
 */
export const hasSchools = async (): Promise<boolean> => {
  const schools = await fetchSchools();
  return schools.length > 0;
};

/**
 * Validates a school invitation code
 * @param schoolId The school ID to validate
 * @param invitationCode The invitation code to validate
 * @returns Boolean indicating if the invitation code is valid
 */
export const validateInvitationCode = async (
  schoolId: string,
  invitationCode: string
): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from("schools")
      .select("invitation_code")
      .eq("id", schoolId)
      .single();

    if (error) {
      console.error("Error validating invitation code:", error);
      return false;
    }

    return data.invitation_code === invitationCode;
  } catch (error) {
    console.error("Error validating invitation code:", error);
    return false;
  }
};
