import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface School {
  id: string;
  name: string;
}

interface AddUserDialogProps {
  open: boolean;
  onClose: () => void;
  onUserAdded: () => void;
  defaultRole?: "admin" | "teacher" | "student";
}

export default function AddUserDialog({
  open,
  onClose,
  onUserAdded,
  defaultRole = "student",
}: AddUserDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [schools, setSchools] = useState<School[]>([]);
  const [loadingSchools, setLoadingSchools] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: defaultRole,
    school_id: "none",
  });

  useEffect(() => {
    if (open) {
      fetchSchools();
      // Reset form when dialog opens
      setFormData({
        name: "",
        email: "",
        password: "",
        confirmPassword: "",
        role: defaultRole,
        school_id: "none",
      });
    }
  }, [open, defaultRole]);

  const fetchSchools = async () => {
    setLoadingSchools(true);
    try {
      const { data, error } = await supabase
        .from("schools")
        .select("id, name")
        .order("name");

      if (error) throw error;
      console.log("Schools fetched successfully:", data);
      setSchools(data || []);
    } catch (error) {
      console.error("Error fetching schools:", error);
      toast({
        title: "Error",
        description: "Failed to fetch schools",
        variant: "destructive",
      });
    } finally {
      setLoadingSchools(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    // Validate inputs
    if (
      !formData.name ||
      !formData.email ||
      !formData.password ||
      !formData.confirmPassword
    ) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    // Validate password confirmation
    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "Please make sure both password fields match",
        variant: "destructive",
      });
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address",
        variant: "destructive",
      });
      return;
    }

    // Validate password length
    if (formData.password.length < 8) {
      toast({
        title: "Password too short",
        description: "Password must be at least 8 characters long",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Check if email already exists
      const { data: existingUser, error: checkError } = await supabase
        .from("profiles")
        .select("email")
        .eq("email", formData.email)
        .single();

      if (existingUser) {
        toast({
          title: "Email already exists",
          description: "A user with this email address already exists",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // Ignore "no rows returned" error as that's what we want
      if (checkError && checkError.code !== "PGRST116") {
        console.error("Error checking existing email:", checkError);
        throw new Error("Failed to validate email address");
      }

      // 1. Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            name: formData.name,
            role: formData.role,
          },
        },
      });

      if (authError) {
        console.error("Auth error:", authError);
        // Provide more specific error messages
        if (authError.message.includes("already registered")) {
          throw new Error("This email address is already registered");
        } else if (authError.message.includes("password")) {
          throw new Error("Password does not meet requirements");
        } else if (authError.message.includes("email")) {
          throw new Error("Invalid email address format");
        } else {
          throw new Error(authError.message);
        }
      }

      if (!authData.user) {
        throw new Error("User creation failed - no user returned");
      }

      // 2. Create profile record
      const profileData = {
        id: authData.user.id,
        user_id: authData.user.id,
        name: formData.name,
        email: formData.email,
        role: formData.role,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // Add role-specific IDs
        student_id: formData.role === "student" ? `S-${Date.now()}` : null,
        teacher_id: formData.role === "teacher" ? `T-${Date.now()}` : null,
        admin_id: formData.role === "admin" ? `A-${Date.now()}` : null,
        // Add school ID (only if not "none")
        school_id: formData.school_id !== "none" ? formData.school_id : null,
        // Set access level for admin users (default to school admin)
        access_level: formData.role === "admin" ? 1 : null,
        // New users need to complete their profile setup
        profile_completed: false,
        is_blocked: false,
        is_deleted: false,
        is_verified: true,
      };

      // Use upsert to handle potential duplicate key conflicts
      const { error: profileError } = await supabase
        .from("profiles")
        .upsert(profileData, { onConflict: "id" });

      if (profileError) {
        console.error("Profile error:", profileError);
        throw new Error(
          `Failed to create user profile: ${profileError.message}`
        );
      }

      // Success!
      toast({
        title: "User created successfully",
        description: `${formData.name} has been added as a ${formData.role}`,
      });

      // Reset form
      setFormData({
        name: "",
        email: "",
        password: "",
        confirmPassword: "",
        role: defaultRole,
        school_id: "none",
      });

      // Notify parent component
      if (onUserAdded) {
        onUserAdded();
      }

      // Close the dialog
      onClose();
    } catch (error: any) {
      console.error("Error creating user:", error);
      toast({
        title: "Failed to create user",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Add New{" "}
            {formData.role.charAt(0).toUpperCase() + formData.role.slice(1)}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              name="name"
              placeholder="Enter full name"
              value={formData.name}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="Enter email address"
              value={formData.email}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              name="password"
              type="password"
              placeholder="Enter password"
              value={formData.password}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              placeholder="Confirm password"
              value={formData.confirmPassword}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Select
              value={formData.role}
              onValueChange={(value) => handleSelectChange("role", value)}
            >
              <SelectTrigger id="role">
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="student">Student</SelectItem>
                <SelectItem value="teacher">Teacher</SelectItem>
                <SelectItem value="admin">Administrator</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="school">School</Label>
            <Select
              value={formData.school_id}
              onValueChange={(value) => handleSelectChange("school_id", value)}
            >
              <SelectTrigger id="school">
                <SelectValue placeholder="Select school" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No School</SelectItem>
                {loadingSchools ? (
                  <SelectItem value="loading" disabled>
                    Loading schools...
                  </SelectItem>
                ) : schools.length > 0 ? (
                  schools.map((school) => (
                    <SelectItem key={school.id} value={school.id}>
                      {school.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-schools" disabled>
                    No schools available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? "Creating..." : "Create User"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
