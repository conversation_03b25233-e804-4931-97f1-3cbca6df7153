import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Bot, Sparkles, Zap, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

interface ChatBubbleProps {
  onClick: () => void;
  isOpen: boolean;
  hasUnreadMessages?: boolean;
  isTyping?: boolean;
  showWelcomeMessage?: boolean;
  onWelcomeMessageDismiss?: () => void;
}

export default function ChatBubble({
  onClick,
  isOpen,
  hasUnreadMessages = false,
  isTyping = false,
  showWelcomeMessage = false,
  onWelcomeMessageDismiss
}: ChatBubbleProps) {
  const { t } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);
  const [showPulse, setShowPulse] = useState(true);

  useEffect(() => {
    // Show pulse animation for first 10 seconds, then every 30 seconds
    const initialTimer = setTimeout(() => {
      setShowPulse(false);
    }, 10000);

    const intervalTimer = setInterval(() => {
      setShowPulse(true);
      setTimeout(() => setShowPulse(false), 3000);
    }, 30000);

    return () => {
      clearTimeout(initialTimer);
      clearInterval(intervalTimer);
    };
  }, []);

  return (
    <div className="fixed bottom-3 right-3 sm:bottom-4 sm:right-4 md:bottom-6 md:right-6 z-50">
      {/* Welcome Message for First-Time Users */}
      <AnimatePresence>
        {showWelcomeMessage && !isOpen && (
          <motion.div
            initial={{ opacity: 0, x: 20, y: 10, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, y: 0, scale: 1 }}
            exit={{ opacity: 0, x: 20, y: 10, scale: 0.8 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="absolute bottom-full right-0 mb-3 w-56 xs:w-64 sm:w-72 md:w-80 max-w-[calc(100vw-2rem)]"
          >
            <div className="relative bg-gradient-to-br from-[#08194A] via-[#0A1B4D] to-[#0C1E50] text-white rounded-xl shadow-2xl border border-[#EE0D09]/30 p-4">
              {/* Sparkle animation background */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse rounded-xl"></div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A] flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="text-sm font-semibold text-white">ATS Assistant</h4>
                      <Sparkles className="w-3 h-3 text-[#EE0D09]" />
                    </div>
                    <p className="text-xs text-gray-300 leading-relaxed">
                      {t('aiAssistant.welcomeDescription')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Arrow pointing to chat bubble */}
              <div className="absolute bottom-0 right-6 transform translate-y-full">
                <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-[#0C1E50]"></div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="relative"
          >
            {/* Gentle floating particles around the button */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{
                    x: Math.random() * 80 - 40,
                    y: Math.random() * 80 - 40,
                    opacity: 0
                  }}
                  animate={{
                    x: Math.random() * 80 - 40,
                    y: Math.random() * 80 - 40,
                    opacity: [0, 0.6, 0]
                  }}
                  transition={{
                    duration: Math.random() * 4 + 6,
                    repeat: Infinity,
                    delay: Math.random() * 3,
                    ease: "easeInOut"
                  }}
                  className="absolute top-1/2 left-1/2 w-1 h-1 bg-[#EE0D09]/60 rounded-full"
                />
              ))}
            </div>

            {/* Gentle pulse ring for notifications */}
            <AnimatePresence>
              {(showPulse || hasUnreadMessages || isTyping) && (
                <motion.div
                  initial={{ scale: 1, opacity: 0.3 }}
                  animate={{
                    scale: [1, 1.8, 2.2],
                    opacity: [0.3, 0.1, 0]
                  }}
                  exit={{ scale: 1, opacity: 0 }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    ease: "easeOut"
                  }}
                  className="absolute inset-0 rounded-full bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A]"
                />
              )}
            </AnimatePresence>

            {/* Main chat bubble with gentle floating animation */}
            <motion.div
              animate={{
                y: [0, -8, 0],
                rotate: [0, 2, -2, 0]
              }}
              whileHover={{
                scale: 1.1,
                y: [0, -12, 0]
              }}
              whileTap={{ scale: 0.95 }}
              onHoverStart={() => setIsHovered(true)}
              onHoverEnd={() => setIsHovered(false)}
              className="relative"
              transition={{
                y: { duration: 3, repeat: Infinity, ease: "easeInOut" },
                rotate: { duration: 4, repeat: Infinity, ease: "easeInOut" },
                scale: { duration: 0.2 }
              }}
            >
              {/* Gentle glow effect */}
              <motion.div
                animate={{
                  boxShadow: isHovered
                    ? [
                        "0 0 20px rgba(238, 13, 9, 0.4)",
                        "0 0 30px rgba(255, 26, 26, 0.3)",
                        "0 0 20px rgba(238, 13, 9, 0.4)"
                      ]
                    : "0 0 15px rgba(238, 13, 9, 0.2)"
                }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="absolute inset-0 rounded-full"
              />

              <Button
                onClick={onClick}
                size="lg"
                className="relative w-16 h-16 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A] hover:from-[#FF1A1A] hover:to-[#EE0D09] shadow-2xl transition-all duration-300 overflow-hidden"
              >
                <div className="relative">
                  {/* Gentle sparkle effect on hover */}
                  {isHovered && (
                    <>
                      {[...Array(3)].map((_, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{
                            opacity: [0, 0.8, 0],
                            scale: [0, 1, 0],
                            x: [0, (i - 1) * 15],
                            y: [0, Math.sin(i * 60 * Math.PI / 180) * 15]
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            delay: i * 0.3,
                            ease: "easeInOut"
                          }}
                          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                        >
                          <Sparkles className="w-2 h-2 text-white/80" />
                        </motion.div>
                      ))}
                    </>
                  )}

                  {/* Main icon */}
                  <AnimatePresence mode="wait">
                    {isTyping ? (
                      <motion.div
                        key="typing"
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{
                          scale: 1,
                          rotate: [0, 360],
                        }}
                        exit={{ scale: 0, rotate: 180 }}
                        transition={{
                          duration: 0.3,
                          rotate: { duration: 2, repeat: Infinity, ease: "linear" }
                        }}
                      >
                        <Zap className="w-7 h-7 sm:w-6 sm:h-6 md:w-7 md:h-7 text-white" />
                      </motion.div>
                    ) : (
                      <motion.div
                        key="bot"
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{
                          scale: 1,
                          rotate: 0
                        }}
                        exit={{ scale: 0, rotate: 180 }}
                        transition={{
                          duration: 0.3
                        }}
                      >
                        <Bot className="w-12 h-12 sm:w-10 sm:h-10 md:w-14 md:h-14 text-white" />
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Corner sparkle effect on hover */}
                  <AnimatePresence>
                    {isHovered && (
                      <motion.div
                        initial={{ scale: 0, opacity: 0, rotate: 0 }}
                        animate={{
                          scale: [0, 1, 0],
                          opacity: [0, 1, 0],
                          rotate: 360
                        }}
                        exit={{ scale: 0, opacity: 0 }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="absolute -top-1 -right-1"
                      >
                        <Sparkles className="w-3 h-3 text-white" />
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Unread indicator */}
                  <AnimatePresence>
                    {hasUnreadMessages && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        exit={{ scale: 0 }}
                        className="absolute -top-2 -right-2 w-4 h-4 bg-[#EE0D09] rounded-full border-2 border-white"
                      >
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                          className="w-full h-full bg-[#EE0D09] rounded-full"
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </Button>

              {/* Floating tooltip */}
              <AnimatePresence>
                {isHovered && (
                  <motion.div
                    initial={{ opacity: 0, x: 20, scale: 0.8 }}
                    animate={{ opacity: 1, x: 0, scale: 1 }}
                    exit={{ opacity: 0, x: 20, scale: 0.8 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-full mr-2 md:mr-4 top-1/2 -translate-y-1/2 whitespace-nowrap hidden sm:block"
                  >
                    <div className="bg-gradient-to-r from-[#08194A] to-[#0C1E50] text-white px-3 md:px-4 py-2 rounded-lg shadow-lg border border-[#EE0D09]/20">
                      <div className="flex items-center gap-2">
                        <Sparkles className="w-3 h-3 md:w-4 md:h-4 text-[#EE0D09]" />
                        <span className="text-xs md:text-sm font-medium">
                          {isTyping ? t('aiAssistant.floatingButtonThinking') : t('aiAssistant.floatingButtonText')}
                        </span>
                      </div>

                      {/* Tooltip arrow */}
                      <div className="absolute left-full top-1/2 -translate-y-1/2 border-l-6 md:border-l-8 border-l-[#08194A] border-y-3 md:border-y-4 border-y-transparent" />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>


          </motion.div>
        )}
      </AnimatePresence>

      {/* Close button when chat is open */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            <Button
              onClick={onClick}
              size="sm"
              variant="outline"
              className="w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 p-0"
            >
              <X className="w-4 h-4 text-white" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
