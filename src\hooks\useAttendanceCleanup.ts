import { useEffect } from "react";
import { attendanceCleanupService } from "@/lib/services/attendance-cleanup-service";
import { useAuth } from "@/context/AuthContext";

/**
 * Hook to initialize and manage attendance cleanup service
 * Only starts the service for admin users
 */
export function useAttendanceCleanup() {
  const { profile } = useAuth();

  useEffect(() => {
    // Only start cleanup service for admin users
    if (profile?.role === "admin") {
      attendanceCleanupService.start();
    }

    // Cleanup on unmount
    return () => {
      if (profile?.role === "admin") {
        attendanceCleanupService.stop();
      }
    };
  }, [profile?.role]);

  return {
    manualCleanup: attendanceCleanupService.manualCleanup.bind(attendanceCleanupService),
    getStatus: attendanceCleanupService.getStatus.bind(attendanceCleanupService),
    cleanupForSchool: attendanceCleanupService.cleanupForSchool.bind(attendanceCleanupService),
    getRecordsCount: attendanceCleanupService.getRecordsCount.bind(attendanceCleanupService),
  };
}
