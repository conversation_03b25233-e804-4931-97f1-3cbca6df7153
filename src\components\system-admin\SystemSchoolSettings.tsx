import React, { useState, useEffect, Suspense } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import {
  Loader2,
  Save,
  RefreshCw,
  AlertTriangle,
  Info,
  Check,
  Globe,
} from "lucide-react";
import {
  fetchSchoolSettings,
  fetchSystemSchoolSettingsOverrides,
  setSystemSchoolSettingsOverride,
  removeSystemSchoolSettingsOverride,
  applySystemSettingToAllSchools,
} from "@/lib/api/system-admin";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface School {
  id: string;
  name: string;
}

interface SystemSchoolSettingsProps {
  schools: School[];
  selectedSchool: School | null;
  onSchoolSelect: (schoolId: string | null) => void;
}

export default function SystemSchoolSettings({
  schools,
  selectedSchool,
  onSchoolSelect,
}: SystemSchoolSettingsProps) {
  const { toast } = useToast();
  const { profile } = useAuth();
  const [activeTab, setActiveTab] = useState("notifications");
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Import ThemeCustomization component
  const ThemeCustomization = React.lazy(() => import("./ThemeCustomization"));

  // School settings
  const [schoolSettings, setSchoolSettings] = useState<any>(null);

  // System overrides
  const [systemOverrides, setSystemOverrides] = useState<any[]>([]);

  // Notification settings
  const [emailNotificationsEnabled, setEmailNotificationsEnabled] =
    useState(true);
  const [emailOverrideEnabled, setEmailOverrideEnabled] = useState(false);
  const [smsNotificationsEnabled, setSmsNotificationsEnabled] = useState(false);
  const [smsOverrideEnabled, setSmsOverrideEnabled] = useState(false);

  // Verification settings
  const [locationVerificationEnabled, setLocationVerificationEnabled] =
    useState(true);
  const [locationOverrideEnabled, setLocationOverrideEnabled] = useState(false);
  const [biometricVerificationEnabled, setBiometricVerificationEnabled] =
    useState(false);
  const [biometricOverrideEnabled, setBiometricOverrideEnabled] =
    useState(false);
  const [pinVerificationEnabled, setPinVerificationEnabled] = useState(true);
  const [pinOverrideEnabled, setPinOverrideEnabled] = useState(false);
  const [wifiVerificationEnabled, setWifiVerificationEnabled] = useState(true);
  const [wifiOverrideEnabled, setWifiOverrideEnabled] = useState(false);

  // Custom text settings
  const [customLoginMessage, setCustomLoginMessage] = useState("");
  const [
    customLoginMessageOverrideEnabled,
    setCustomLoginMessageOverrideEnabled,
  ] = useState(false);

  // Custom dashboard messages for different user types
  const [customStudentMessage, setCustomStudentMessage] = useState("");
  const [
    customStudentMessageOverrideEnabled,
    setCustomStudentMessageOverrideEnabled,
  ] = useState(false);

  const [customTeacherMessage, setCustomTeacherMessage] = useState("");
  const [
    customTeacherMessageOverrideEnabled,
    setCustomTeacherMessageOverrideEnabled,
  ] = useState(false);

  const [customAdminMessage, setCustomAdminMessage] = useState("");
  const [
    customAdminMessageOverrideEnabled,
    setCustomAdminMessageOverrideEnabled,
  ] = useState(false);

  // Target audience selection for dashboard messages
  const [messageTargetAudience, setMessageTargetAudience] = useState<string[]>([
    "student",
    "teacher",
    "admin",
  ]);

  // School selection for each user type
  const [loginMessageSchool, setLoginMessageSchool] = useState<string | null>(
    null
  );
  const [studentMessageSchool, setStudentMessageSchool] = useState<
    string | null
  >(null);
  const [teacherMessageSchool, setTeacherMessageSchool] = useState<
    string | null
  >(null);
  const [adminMessageSchool, setAdminMessageSchool] = useState<string | null>(
    null
  );

  // Load settings when selected school changes
  useEffect(() => {
    if (selectedSchool) {
      loadSettings(selectedSchool.id);
    } else {
      loadGlobalSettings();
    }
  }, [selectedSchool]);

  // Load settings for a specific school
  const loadSettings = async (schoolId: string) => {
    setLoading(true);
    try {
      // Load school settings
      const settings = await fetchSchoolSettings(schoolId);
      setSchoolSettings(settings);

      // Load system overrides
      const overrides = await fetchSystemSchoolSettingsOverrides(schoolId);
      setSystemOverrides(overrides);

      // Set notification settings
      setEmailNotificationsEnabled(
        settings?.email_notifications_enabled ?? true
      );
      setSmsNotificationsEnabled(settings?.sms_notifications_enabled ?? false);

      // Set verification settings
      setLocationVerificationEnabled(
        settings?.require_location_verification ?? true
      );
      setBiometricVerificationEnabled(
        settings?.require_biometric_verification ?? false
      );
      setPinVerificationEnabled(settings?.allow_pin_verification ?? true);
      setWifiVerificationEnabled(settings?.allow_wifi_verification ?? true);

      // Set custom text settings
      setCustomLoginMessage(settings?.custom_login_message || "");
      setCustomStudentMessage(settings?.custom_student_message || "");
      setCustomTeacherMessage(settings?.custom_teacher_message || "");
      setCustomAdminMessage(settings?.custom_admin_message || "");

      // Set override states
      const emailOverride = overrides.find(
        (o) => o.setting_name === "email_notifications_enabled"
      );
      setEmailOverrideEnabled(emailOverride?.override_enabled ?? false);

      const smsOverride = overrides.find(
        (o) => o.setting_name === "sms_notifications_enabled"
      );
      setSmsOverrideEnabled(smsOverride?.override_enabled ?? false);

      const locationOverride = overrides.find(
        (o) => o.setting_name === "require_location_verification"
      );
      setLocationOverrideEnabled(locationOverride?.override_enabled ?? false);

      const biometricOverride = overrides.find(
        (o) => o.setting_name === "require_biometric_verification"
      );
      setBiometricOverrideEnabled(biometricOverride?.override_enabled ?? false);

      const pinOverride = overrides.find(
        (o) => o.setting_name === "allow_pin_verification"
      );
      setPinOverrideEnabled(pinOverride?.override_enabled ?? false);

      const wifiOverride = overrides.find(
        (o) => o.setting_name === "allow_wifi_verification"
      );
      setWifiOverrideEnabled(wifiOverride?.override_enabled ?? false);

      // Check for custom message overrides
      const loginMessageOverride = overrides.find(
        (o) => o.setting_name === "custom_login_message"
      );
      setCustomLoginMessageOverrideEnabled(
        loginMessageOverride?.override_enabled ?? false
      );

      const studentMessageOverride = overrides.find(
        (o) => o.setting_name === "custom_student_message"
      );
      setCustomStudentMessageOverrideEnabled(
        studentMessageOverride?.override_enabled ?? false
      );

      const teacherMessageOverride = overrides.find(
        (o) => o.setting_name === "custom_teacher_message"
      );
      setCustomTeacherMessageOverrideEnabled(
        teacherMessageOverride?.override_enabled ?? false
      );

      const adminMessageOverride = overrides.find(
        (o) => o.setting_name === "custom_admin_message"
      );
      setCustomAdminMessageOverrideEnabled(
        adminMessageOverride?.override_enabled ?? false
      );
    } catch (error) {
      console.error("Error loading settings:", error);
      toast({
        title: "Error",
        description: "Failed to load settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load global settings (when "All Schools" is selected)
  const loadGlobalSettings = async () => {
    setLoading(true);
    try {
      // Load system overrides that apply to all schools
      const overrides = await fetchSystemSchoolSettingsOverrides();
      const globalOverrides = overrides.filter((o) => o.applies_to_all);
      setSystemOverrides(globalOverrides);

      // Set default values
      setEmailNotificationsEnabled(true);
      setSmsNotificationsEnabled(false);
      setLocationVerificationEnabled(true);
      setBiometricVerificationEnabled(false);
      setPinVerificationEnabled(true);
      setWifiVerificationEnabled(true);
      setCustomLoginMessage("");
      setCustomStudentMessage("");
      setCustomTeacherMessage("");
      setCustomAdminMessage("");

      // Update values from global overrides
      const emailOverride = globalOverrides.find(
        (o) => o.setting_name === "email_notifications_enabled"
      );
      if (emailOverride) {
        setEmailNotificationsEnabled(emailOverride.setting_value.value);
        setEmailOverrideEnabled(emailOverride.override_enabled);
      }

      const smsOverride = globalOverrides.find(
        (o) => o.setting_name === "sms_notifications_enabled"
      );
      if (smsOverride) {
        setSmsNotificationsEnabled(smsOverride.setting_value.value);
        setSmsOverrideEnabled(smsOverride.override_enabled);
      }

      const locationOverride = globalOverrides.find(
        (o) => o.setting_name === "require_location_verification"
      );
      if (locationOverride) {
        setLocationVerificationEnabled(locationOverride.setting_value.value);
        setLocationOverrideEnabled(locationOverride.override_enabled);
      }

      const biometricOverride = globalOverrides.find(
        (o) => o.setting_name === "require_biometric_verification"
      );
      if (biometricOverride) {
        setBiometricVerificationEnabled(biometricOverride.setting_value.value);
        setBiometricOverrideEnabled(biometricOverride.override_enabled);
      }

      const pinOverride = globalOverrides.find(
        (o) => o.setting_name === "allow_pin_verification"
      );
      if (pinOverride) {
        setPinVerificationEnabled(pinOverride.setting_value.value);
        setPinOverrideEnabled(pinOverride.override_enabled);
      }

      const wifiOverride = globalOverrides.find(
        (o) => o.setting_name === "allow_wifi_verification"
      );
      if (wifiOverride) {
        setWifiVerificationEnabled(wifiOverride.setting_value.value);
        setWifiOverrideEnabled(wifiOverride.override_enabled);
      }

      // Check for custom message overrides
      const loginMessageOverride = globalOverrides.find(
        (o) => o.setting_name === "custom_login_message"
      );
      if (loginMessageOverride) {
        setCustomLoginMessage(loginMessageOverride.setting_value.value || "");
        setCustomLoginMessageOverrideEnabled(
          loginMessageOverride.override_enabled
        );
      } else {
        // If no global override, check if there's a default value in any school
        try {
          const { data: schoolSettings, error: schoolError } = await supabase
            .from("school_settings")
            .select("custom_login_message")
            .not("custom_login_message", "is", null)
            .limit(1);

          if (schoolError) throw schoolError;

          if (
            schoolSettings &&
            schoolSettings.length > 0 &&
            schoolSettings[0].custom_login_message
          ) {
            setCustomLoginMessage(schoolSettings[0].custom_login_message);
          }
        } catch (error) {
          console.error("Error fetching school settings:", error);
        }
      }

      // Student message override
      const studentMessageOverride = globalOverrides.find(
        (o) => o.setting_name === "custom_student_message"
      );
      if (studentMessageOverride) {
        setCustomStudentMessage(
          studentMessageOverride.setting_value.value || ""
        );
        setCustomStudentMessageOverrideEnabled(
          studentMessageOverride.override_enabled
        );
      } else {
        try {
          const { data: schoolSettings, error: schoolError } = await supabase
            .from("school_settings")
            .select("custom_student_message")
            .not("custom_student_message", "is", null)
            .limit(1);

          if (schoolError) throw schoolError;

          if (
            schoolSettings &&
            schoolSettings.length > 0 &&
            schoolSettings[0].custom_student_message
          ) {
            setCustomStudentMessage(schoolSettings[0].custom_student_message);
          }
        } catch (error) {
          console.error("Error fetching school settings:", error);
        }
      }

      // Teacher message override
      const teacherMessageOverride = globalOverrides.find(
        (o) => o.setting_name === "custom_teacher_message"
      );
      if (teacherMessageOverride) {
        setCustomTeacherMessage(
          teacherMessageOverride.setting_value.value || ""
        );
        setCustomTeacherMessageOverrideEnabled(
          teacherMessageOverride.override_enabled
        );
      } else {
        try {
          const { data: schoolSettings, error: schoolError } = await supabase
            .from("school_settings")
            .select("custom_teacher_message")
            .not("custom_teacher_message", "is", null)
            .limit(1);

          if (schoolError) throw schoolError;

          if (
            schoolSettings &&
            schoolSettings.length > 0 &&
            schoolSettings[0].custom_teacher_message
          ) {
            setCustomTeacherMessage(schoolSettings[0].custom_teacher_message);
          }
        } catch (error) {
          console.error("Error fetching school settings:", error);
        }
      }

      // Admin message override
      const adminMessageOverride = globalOverrides.find(
        (o) => o.setting_name === "custom_admin_message"
      );
      if (adminMessageOverride) {
        setCustomAdminMessage(adminMessageOverride.setting_value.value || "");
        setCustomAdminMessageOverrideEnabled(
          adminMessageOverride.override_enabled
        );
      } else {
        try {
          const { data: schoolSettings, error: schoolError } = await supabase
            .from("school_settings")
            .select("custom_admin_message")
            .not("custom_admin_message", "is", null)
            .limit(1);

          if (schoolError) throw schoolError;

          if (
            schoolSettings &&
            schoolSettings.length > 0 &&
            schoolSettings[0].custom_admin_message
          ) {
            setCustomAdminMessage(schoolSettings[0].custom_admin_message);
          }
        } catch (error) {
          console.error("Error fetching school settings:", error);
        }
      }
    } catch (error) {
      console.error("Error loading global settings:", error);
      toast({
        title: "Error",
        description: "Failed to load global settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Refresh settings
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (selectedSchool) {
        await loadSettings(selectedSchool.id);
      } else {
        await loadGlobalSettings();
      }

      toast({
        title: "Settings Refreshed",
        description: "Settings have been refreshed successfully",
      });
    } catch (error) {
      console.error("Error refreshing settings:", error);
      toast({
        title: "Error",
        description: "Failed to refresh settings",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Save notification settings
  const saveNotificationSettings = async () => {
    if (!profile) return;

    setSaving(true);
    try {
      if (selectedSchool) {
        // Save for specific school
        if (emailOverrideEnabled) {
          await setSystemSchoolSettingsOverride(
            "email_notifications_enabled",
            emailNotificationsEnabled,
            selectedSchool.id,
            true,
            false
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "email_notifications_enabled",
            selectedSchool.id
          );
        }

        if (smsOverrideEnabled) {
          await setSystemSchoolSettingsOverride(
            "sms_notifications_enabled",
            smsNotificationsEnabled,
            selectedSchool.id,
            true,
            false
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "sms_notifications_enabled",
            selectedSchool.id
          );
        }
      } else {
        // Save for all schools
        if (emailOverrideEnabled) {
          await applySystemSettingToAllSchools(
            "email_notifications_enabled",
            emailNotificationsEnabled,
            true
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "email_notifications_enabled",
            null
          );
        }

        if (smsOverrideEnabled) {
          await applySystemSettingToAllSchools(
            "sms_notifications_enabled",
            smsNotificationsEnabled,
            true
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "sms_notifications_enabled",
            null
          );
        }
      }

      toast({
        title: "Settings Saved",
        description: "Notification settings have been updated successfully",
      });

      // Refresh settings
      if (selectedSchool) {
        await loadSettings(selectedSchool.id);
      } else {
        await loadGlobalSettings();
      }
    } catch (error) {
      console.error("Error saving notification settings:", error);
      toast({
        title: "Save Failed",
        description:
          "There was an error saving your settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Save verification settings
  const saveVerificationSettings = async () => {
    if (!profile) return;

    setSaving(true);
    try {
      if (selectedSchool) {
        // Save for specific school
        if (locationOverrideEnabled) {
          await setSystemSchoolSettingsOverride(
            "require_location_verification",
            locationVerificationEnabled,
            selectedSchool.id,
            true,
            false
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "require_location_verification",
            selectedSchool.id
          );
        }

        if (biometricOverrideEnabled) {
          await setSystemSchoolSettingsOverride(
            "require_biometric_verification",
            biometricVerificationEnabled,
            selectedSchool.id,
            true,
            false
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "require_biometric_verification",
            selectedSchool.id
          );
        }

        if (pinOverrideEnabled) {
          await setSystemSchoolSettingsOverride(
            "allow_pin_verification",
            pinVerificationEnabled,
            selectedSchool.id,
            true,
            false
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "allow_pin_verification",
            selectedSchool.id
          );
        }

        if (wifiOverrideEnabled) {
          await setSystemSchoolSettingsOverride(
            "allow_wifi_verification",
            wifiVerificationEnabled,
            selectedSchool.id,
            true,
            false
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "allow_wifi_verification",
            selectedSchool.id
          );
        }
      } else {
        // Save for all schools
        if (locationOverrideEnabled) {
          await applySystemSettingToAllSchools(
            "require_location_verification",
            locationVerificationEnabled,
            true
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "require_location_verification",
            null
          );
        }

        if (biometricOverrideEnabled) {
          await applySystemSettingToAllSchools(
            "require_biometric_verification",
            biometricVerificationEnabled,
            true
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "require_biometric_verification",
            null
          );
        }

        if (pinOverrideEnabled) {
          await applySystemSettingToAllSchools(
            "allow_pin_verification",
            pinVerificationEnabled,
            true
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "allow_pin_verification",
            null
          );
        }

        if (wifiOverrideEnabled) {
          await applySystemSettingToAllSchools(
            "allow_wifi_verification",
            wifiVerificationEnabled,
            true
          );
        } else {
          await removeSystemSchoolSettingsOverride(
            "allow_wifi_verification",
            null
          );
        }
      }

      toast({
        title: "Settings Saved",
        description: "Verification settings have been updated successfully",
      });

      // Refresh settings
      if (selectedSchool) {
        await loadSettings(selectedSchool.id);
      } else {
        await loadGlobalSettings();
      }
    } catch (error) {
      console.error("Error saving verification settings:", error);
      toast({
        title: "Save Failed",
        description:
          "There was an error saving your settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Helper function to save a custom message
  const saveCustomMessage = async (
    settingName: string,
    message: string,
    overrideEnabled: boolean, // This parameter is kept for compatibility but not used for dashboard messages
    schoolId: string | null,
    specificSchoolId: string | null = null
  ) => {
    // If a specific school is selected for this message type, use that instead of the global schoolId
    const targetSchoolId = specificSchoolId || schoolId;
    // Check if the message is empty or only whitespace
    const isMessageEmpty = !message || message.trim() === "";

    // For dashboard messages, we always save without override
    const isDashboardMessage =
      settingName.startsWith("custom_") &&
      (settingName.includes("_student_message") ||
        settingName.includes("_teacher_message") ||
        settingName.includes("_admin_message"));

    // If this is a dashboard message and it's not empty, clear all existing messages first
    if (isDashboardMessage && !isMessageEmpty) {
      try {
        // First, clear any existing system-wide message for this setting
        await removeSystemSchoolSettingsOverride(settingName, null);

        // Then, clear any school-specific messages for all schools
        const { data: schools } = await supabase.from("schools").select("id");

        if (schools && schools.length > 0) {
          for (const school of schools) {
            // Skip the target school if we're setting a specific school message
            if (targetSchoolId && school.id === targetSchoolId) continue;

            // Clear the system override for this school
            await removeSystemSchoolSettingsOverride(settingName, school.id);

            // Clear the message in school_settings
            const updateData: any = {
              updated_at: new Date().toISOString(),
            };
            updateData[settingName] = null;

            await supabase
              .from("school_settings")
              .update(updateData)
              .eq("school_id", school.id);
          }
        }
      } catch (error) {
        console.error(
          `Error clearing existing ${settingName} messages:`,
          error
        );
        // Continue even if clearing fails
      }
    }

    if (targetSchoolId) {
      // Save for specific school
      if (!isMessageEmpty) {
        // For dashboard messages, we always save to system_school_settings_overrides without override
        if (isDashboardMessage) {
          await setSystemSchoolSettingsOverride(
            settingName,
            { value: message.trim() },
            targetSchoolId,
            false, // No override for dashboard messages
            false
          );
        }
        // For other messages like login message, use the override flag
        else if (overrideEnabled) {
          await setSystemSchoolSettingsOverride(
            settingName,
            { value: message.trim() },
            targetSchoolId,
            true,
            false
          );
        }

        // Then, save directly to the school_settings table
        const { data: existingSettings, error: fetchError } = await supabase
          .from("school_settings")
          .select("id")
          .eq("school_id", targetSchoolId)
          .maybeSingle();

        if (fetchError && fetchError.code !== "PGRST116") {
          throw fetchError;
        }

        const updateData: any = {
          updated_at: new Date().toISOString(),
        };
        updateData[settingName] = message.trim();

        if (existingSettings) {
          // Update existing settings
          const { error: updateError } = await supabase
            .from("school_settings")
            .update(updateData)
            .eq("school_id", targetSchoolId);

          if (updateError) throw updateError;
        } else {
          // Insert new settings
          const insertData: any = {
            school_id: targetSchoolId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          insertData[settingName] = message.trim();

          const { error: insertError } = await supabase
            .from("school_settings")
            .insert(insertData);

          if (insertError) throw insertError;
        }
      } else {
        // Remove the override or if message is empty
        await removeSystemSchoolSettingsOverride(settingName, targetSchoolId);

        // Clear the custom message in school_settings
        const updateData: any = {
          updated_at: new Date().toISOString(),
        };
        updateData[settingName] = null;

        const { error: updateError } = await supabase
          .from("school_settings")
          .update(updateData)
          .eq("school_id", targetSchoolId);

        if (updateError) throw updateError;
      }
    } else {
      // Save for all schools
      if (!isMessageEmpty) {
        // For dashboard messages, we always save to system_school_settings_overrides without override
        if (isDashboardMessage) {
          await applySystemSettingToAllSchools(
            settingName,
            { value: message.trim() },
            false // No override for dashboard messages
          );
        }
        // For other messages like login message, use the override flag
        else if (overrideEnabled) {
          await applySystemSettingToAllSchools(
            settingName,
            { value: message.trim() },
            true
          );
        }

        // Then, update all school_settings records
        // First, get all schools
        const { data: schools, error: schoolsError } = await supabase
          .from("schools")
          .select("id");

        if (schoolsError) throw schoolsError;

        if (schools && schools.length > 0) {
          // For each school, update or insert settings
          for (const school of schools) {
            const { data: existingSettings, error: fetchError } = await supabase
              .from("school_settings")
              .select("id")
              .eq("school_id", school.id)
              .maybeSingle();

            if (fetchError && fetchError.code !== "PGRST116") {
              console.error(
                `Error checking settings for school ${school.id}:`,
                fetchError
              );
              continue;
            }

            const updateData: any = {
              updated_at: new Date().toISOString(),
            };
            updateData[settingName] = message.trim();

            if (existingSettings) {
              // Update existing settings
              const { error: updateError } = await supabase
                .from("school_settings")
                .update(updateData)
                .eq("school_id", school.id);

              if (updateError) {
                console.error(
                  `Error updating settings for school ${school.id}:`,
                  updateError
                );
              }
            } else {
              // Insert new settings
              const insertData: any = {
                school_id: school.id,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              };
              insertData[settingName] = message.trim();

              const { error: insertError } = await supabase
                .from("school_settings")
                .insert(insertData);

              if (insertError) {
                console.error(
                  `Error inserting settings for school ${school.id}:`,
                  insertError
                );
              }
            }
          }
        }
      } else {
        // Remove the system-wide override
        await removeSystemSchoolSettingsOverride(settingName, null);

        // If message is empty, clear all custom messages
        try {
          const updateData: any = {
            updated_at: new Date().toISOString(),
          };
          updateData[settingName] = null;

          const { error: updateError } = await supabase
            .from("school_settings")
            .update(updateData);

          if (updateError) {
            console.error(`Error clearing ${settingName}:`, updateError);
          }
        } catch (error) {
          console.error(`Error clearing ${settingName}:`, error);
        }
      }
    }
  };

  // Save customization settings
  const saveCustomizationSettings = async () => {
    if (!profile) return;

    setSaving(true);

    try {
      const schoolId = selectedSchool ? selectedSchool.id : null;

      // Save login message
      await saveCustomMessage(
        "custom_login_message",
        customLoginMessage,
        customLoginMessageOverrideEnabled,
        schoolId,
        loginMessageSchool
      );

      // Save student message if it's in the target audience
      if (messageTargetAudience.includes("student")) {
        await saveCustomMessage(
          "custom_student_message",
          customStudentMessage,
          false, // Override flag is ignored for dashboard messages
          schoolId,
          studentMessageSchool
        );
      } else {
        // If not in target audience, clear the message
        await saveCustomMessage(
          "custom_student_message",
          "", // Empty message to clear it
          false,
          schoolId,
          studentMessageSchool
        );
      }

      // Save teacher message if it's in the target audience
      if (messageTargetAudience.includes("teacher")) {
        await saveCustomMessage(
          "custom_teacher_message",
          customTeacherMessage,
          false, // Override flag is ignored for dashboard messages
          schoolId,
          teacherMessageSchool
        );
      } else {
        // If not in target audience, clear the message
        await saveCustomMessage(
          "custom_teacher_message",
          "", // Empty message to clear it
          false,
          schoolId,
          teacherMessageSchool
        );
      }

      // Save admin message if it's in the target audience
      if (messageTargetAudience.includes("admin")) {
        await saveCustomMessage(
          "custom_admin_message",
          customAdminMessage,
          false, // Override flag is ignored for dashboard messages
          schoolId,
          adminMessageSchool
        );
      } else {
        // If not in target audience, clear the message
        await saveCustomMessage(
          "custom_admin_message",
          "", // Empty message to clear it
          false,
          schoolId,
          adminMessageSchool
        );
      }

      toast({
        title: "Settings saved",
        description: "Customization settings have been saved successfully.",
      });

      // Refresh settings
      if (selectedSchool) {
        await loadSettings(selectedSchool.id);
      } else {
        await loadGlobalSettings();
      }

      setSaving(false);
    } catch (error) {
      console.error("Error saving customization settings:", error);
      toast({
        title: "Error",
        description: "Failed to save customization settings.",
        variant: "destructive",
      });
      setSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0 pb-2">
        <div>
          <CardTitle>School Settings</CardTitle>
          <CardDescription>
            Configure settings for{" "}
            {selectedSchool ? selectedSchool.name : "all schools"}
          </CardDescription>
        </div>
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
          <Select
            value={selectedSchool ? selectedSchool.id : "all"}
            onValueChange={(value) =>
              onSchoolSelect(value === "all" ? null : value)
            }
          >
            <SelectTrigger className="w-full sm:w-[200px]">
              <SelectValue placeholder="Select a school" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                <div className="flex items-center">
                  <Globe className="mr-2 h-4 w-4" />
                  All Schools
                </div>
              </SelectItem>
              {schools.map((school) => (
                <SelectItem key={school.id} value={school.id}>
                  {school.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="w-full sm:w-auto"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {!selectedSchool && !loading && (
          <Alert className="mb-4">
            <Info className="h-4 w-4" />
            <AlertTitle>Global Settings</AlertTitle>
            <AlertDescription>
              You are configuring settings for all schools. These settings will
              override individual school settings if enabled.
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 gap-1 sm:gap-0">
            <TabsTrigger value="notifications" className="text-xs sm:text-sm">
              Notifications
            </TabsTrigger>
            <TabsTrigger value="verification" className="text-xs sm:text-sm">
              Verification
            </TabsTrigger>
            <TabsTrigger value="customization" className="text-xs sm:text-sm">
              Customization
            </TabsTrigger>
            <TabsTrigger value="appearance" className="text-xs sm:text-sm">
              Appearance
            </TabsTrigger>
          </TabsList>

          <TabsContent value="notifications" className="space-y-4 pt-4">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : (
              <>
                <div className="flex flex-col space-y-4">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between rounded-lg border p-4 gap-4">
                    <div className="space-y-0.5">
                      <Label className="text-base">Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Enable email notifications for attendance updates and
                        alerts
                      </p>
                    </div>
                    <div className="flex flex-col xs:flex-row items-start xs:items-center gap-4 w-full sm:w-auto">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={emailNotificationsEnabled}
                          onCheckedChange={setEmailNotificationsEnabled}
                          disabled={!emailOverrideEnabled}
                        />
                        <span className="text-sm">
                          {emailNotificationsEnabled ? "Enabled" : "Disabled"}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="email-override" className="text-sm">
                          Override
                        </Label>
                        <Switch
                          id="email-override"
                          checked={emailOverrideEnabled}
                          onCheckedChange={setEmailOverrideEnabled}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between rounded-lg border p-4 gap-4">
                    <div className="space-y-0.5">
                      <Label className="text-base">SMS Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Enable SMS notifications for attendance updates and
                        alerts
                      </p>
                    </div>
                    <div className="flex flex-col xs:flex-row items-start xs:items-center gap-4 w-full sm:w-auto">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={smsNotificationsEnabled}
                          onCheckedChange={setSmsNotificationsEnabled}
                          disabled={!smsOverrideEnabled}
                        />
                        <span className="text-sm">
                          {smsNotificationsEnabled ? "Enabled" : "Disabled"}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Label htmlFor="sms-override" className="text-sm">
                          Override
                        </Label>
                        <Switch
                          id="sms-override"
                          checked={smsOverrideEnabled}
                          onCheckedChange={setSmsOverrideEnabled}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <CardFooter className="flex justify-end px-0 pt-4">
                  <Button onClick={saveNotificationSettings} disabled={saving}>
                    {saving && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save Notification Settings
                  </Button>
                </CardFooter>
              </>
            )}
          </TabsContent>

          <TabsContent value="verification" className="space-y-4 pt-4">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : (
              <>
                <div className="flex flex-col space-y-4">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between rounded-lg border p-3 sm:p-4 gap-3 sm:gap-0">
                    <div className="space-y-0.5">
                      <Label className="text-sm sm:text-base">
                        Location Verification
                      </Label>
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        Require geolocation verification for attendance
                      </p>
                    </div>
                    <div className="flex flex-col xs:flex-row items-start xs:items-center gap-3 xs:gap-4 w-full sm:w-auto">
                      <div className="flex items-center justify-between xs:justify-start w-full xs:w-auto gap-2">
                        <Switch
                          checked={locationVerificationEnabled}
                          onCheckedChange={setLocationVerificationEnabled}
                          disabled={!locationOverrideEnabled}
                        />
                        <span className="text-xs sm:text-sm">
                          {locationVerificationEnabled
                            ? "Required"
                            : "Optional"}
                        </span>
                      </div>
                      <div className="flex items-center justify-between xs:justify-start w-full xs:w-auto gap-2">
                        <Label
                          htmlFor="location-override"
                          className="text-xs sm:text-sm"
                        >
                          Override
                        </Label>
                        <Switch
                          id="location-override"
                          checked={locationOverrideEnabled}
                          onCheckedChange={setLocationOverrideEnabled}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between rounded-lg border p-3 sm:p-4 gap-3 sm:gap-0">
                    <div className="space-y-0.5">
                      <Label className="text-sm sm:text-base">
                        Biometric Verification
                      </Label>
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        Require biometric verification for attendance
                      </p>
                    </div>
                    <div className="flex flex-col xs:flex-row items-start xs:items-center gap-3 xs:gap-4 w-full sm:w-auto">
                      <div className="flex items-center justify-between xs:justify-start w-full xs:w-auto gap-2">
                        <Switch
                          checked={biometricVerificationEnabled}
                          onCheckedChange={setBiometricVerificationEnabled}
                          disabled={!biometricOverrideEnabled}
                        />
                        <span className="text-xs sm:text-sm">
                          {biometricVerificationEnabled
                            ? "Required"
                            : "Optional"}
                        </span>
                      </div>
                      <div className="flex items-center justify-between xs:justify-start w-full xs:w-auto gap-2">
                        <Label
                          htmlFor="biometric-override"
                          className="text-xs sm:text-sm"
                        >
                          Override
                        </Label>
                        <Switch
                          id="biometric-override"
                          checked={biometricOverrideEnabled}
                          onCheckedChange={setBiometricOverrideEnabled}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between rounded-lg border p-3 sm:p-4 gap-3 sm:gap-0">
                    <div className="space-y-0.5">
                      <Label className="text-sm sm:text-base">
                        PIN Verification
                      </Label>
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        Allow PIN verification for attendance
                      </p>
                    </div>
                    <div className="flex flex-col xs:flex-row items-start xs:items-center gap-3 xs:gap-4 w-full sm:w-auto">
                      <div className="flex items-center justify-between xs:justify-start w-full xs:w-auto gap-2">
                        <Switch
                          checked={pinVerificationEnabled}
                          onCheckedChange={setPinVerificationEnabled}
                          disabled={!pinOverrideEnabled}
                        />
                        <span className="text-xs sm:text-sm">
                          {pinVerificationEnabled ? "Allowed" : "Disabled"}
                        </span>
                      </div>
                      <div className="flex items-center justify-between xs:justify-start w-full xs:w-auto gap-2">
                        <Label
                          htmlFor="pin-override"
                          className="text-xs sm:text-sm"
                        >
                          Override
                        </Label>
                        <Switch
                          id="pin-override"
                          checked={pinOverrideEnabled}
                          onCheckedChange={setPinOverrideEnabled}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between rounded-lg border p-3 sm:p-4 gap-3 sm:gap-0">
                    <div className="space-y-0.5">
                      <Label className="text-sm sm:text-base">
                        WiFi Verification
                      </Label>
                      <p className="text-xs sm:text-sm text-muted-foreground">
                        Allow WiFi network verification for attendance
                      </p>
                    </div>
                    <div className="flex flex-col xs:flex-row items-start xs:items-center gap-3 xs:gap-4 w-full sm:w-auto">
                      <div className="flex items-center justify-between xs:justify-start w-full xs:w-auto gap-2">
                        <Switch
                          checked={wifiVerificationEnabled}
                          onCheckedChange={setWifiVerificationEnabled}
                          disabled={!wifiOverrideEnabled}
                        />
                        <span className="text-xs sm:text-sm">
                          {wifiVerificationEnabled ? "Allowed" : "Disabled"}
                        </span>
                      </div>
                      <div className="flex items-center justify-between xs:justify-start w-full xs:w-auto gap-2">
                        <Label
                          htmlFor="wifi-override"
                          className="text-xs sm:text-sm"
                        >
                          Override
                        </Label>
                        <Switch
                          id="wifi-override"
                          checked={wifiOverrideEnabled}
                          onCheckedChange={setWifiOverrideEnabled}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <CardFooter className="flex justify-end px-0 pt-4">
                  <Button onClick={saveVerificationSettings} disabled={saving}>
                    {saving && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save Verification Settings
                  </Button>
                </CardFooter>
              </>
            )}
          </TabsContent>

          <TabsContent value="customization" className="space-y-4 pt-4">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-36 w-full" />
              </div>
            ) : (
              <>
                <div className="flex flex-col space-y-4">
                  {/* Login Message */}
                  <div className="flex flex-col space-y-4 rounded-lg border p-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-base">
                          Custom Login Message
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Customize the message displayed on the login page
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Label
                          htmlFor="login-message-override"
                          className="text-sm"
                        >
                          Override
                        </Label>
                        <Switch
                          id="login-message-override"
                          checked={customLoginMessageOverrideEnabled}
                          onCheckedChange={setCustomLoginMessageOverrideEnabled}
                        />
                      </div>
                    </div>
                    <div>
                      {/* School selection for login message */}
                      <div className="flex items-center space-x-2 mb-3">
                        <Label className="text-xs whitespace-nowrap">
                          Target School:
                        </Label>
                        <Select
                          value={loginMessageSchool || "all"}
                          onValueChange={(value) =>
                            setLoginMessageSchool(
                              value === "all" ? null : value
                            )
                          }
                          disabled={!customLoginMessageOverrideEnabled}
                        >
                          <SelectTrigger className="h-8 text-xs">
                            <SelectValue placeholder="Select a school" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">
                              <div className="flex items-center">
                                <Globe className="mr-2 h-3 w-3" />
                                All Schools
                              </div>
                            </SelectItem>
                            {schools.map((school) => (
                              <SelectItem key={school.id} value={school.id}>
                                {school.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <textarea
                          className="w-full min-h-[100px] p-2 border rounded-md"
                          value={customLoginMessage}
                          onChange={(e) =>
                            setCustomLoginMessage(e.target.value)
                          }
                          placeholder="Enter a custom message to display on the login page..."
                          disabled={!customLoginMessageOverrideEnabled}
                        />

                        {customLoginMessageOverrideEnabled && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              setCustomLoginMessage(
                                "Welcome to Attendance Tracking System! Please log in to access the attendance system."
                              )
                            }
                            disabled={!customLoginMessageOverrideEnabled}
                          >
                            Use Default Message
                          </Button>
                        )}
                      </div>

                      {/* Preview of the message */}
                      {customLoginMessageOverrideEnabled &&
                        customLoginMessage && (
                          <div className="mt-2 p-3 bg-muted/50 rounded-md text-sm border border-primary/20">
                            <div className="font-semibold mb-1">Preview:</div>
                            {customLoginMessage}
                            {loginMessageSchool && (
                              <div className="mt-1 text-xs text-muted-foreground">
                                Target:{" "}
                                {schools.find(
                                  (s) => s.id === loginMessageSchool
                                )?.name || "Unknown School"}
                              </div>
                            )}
                          </div>
                        )}
                      <p className="text-xs text-muted-foreground mt-1">
                        This message will be displayed on the login page. HTML
                        is not supported.
                      </p>
                    </div>
                  </div>

                  {/* Dashboard Messages */}
                  <div className="flex flex-col space-y-4 rounded-lg border p-4">
                    <div className="space-y-0.5 mb-2">
                      <Label className="text-base">Dashboard Messages</Label>
                      <p className="text-sm text-muted-foreground">
                        Customize messages displayed on user dashboards
                      </p>
                    </div>

                    {/* Target Audience Selection */}
                    <div className="space-y-2 mb-4">
                      <Label className="text-sm">Target Audience</Label>
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant={
                            messageTargetAudience.includes("student")
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() => {
                            if (messageTargetAudience.includes("student")) {
                              setMessageTargetAudience(
                                messageTargetAudience.filter(
                                  (t) => t !== "student"
                                )
                              );
                            } else {
                              setMessageTargetAudience([
                                ...messageTargetAudience,
                                "student",
                              ]);
                            }
                          }}
                        >
                          Students
                        </Button>
                        <Button
                          variant={
                            messageTargetAudience.includes("teacher")
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() => {
                            if (messageTargetAudience.includes("teacher")) {
                              setMessageTargetAudience(
                                messageTargetAudience.filter(
                                  (t) => t !== "teacher"
                                )
                              );
                            } else {
                              setMessageTargetAudience([
                                ...messageTargetAudience,
                                "teacher",
                              ]);
                            }
                          }}
                        >
                          Teachers
                        </Button>
                        <Button
                          variant={
                            messageTargetAudience.includes("admin")
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() => {
                            if (messageTargetAudience.includes("admin")) {
                              setMessageTargetAudience(
                                messageTargetAudience.filter(
                                  (t) => t !== "admin"
                                )
                              );
                            } else {
                              setMessageTargetAudience([
                                ...messageTargetAudience,
                                "admin",
                              ]);
                            }
                          }}
                        >
                          School Admins
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Select which user types will receive this message
                      </p>
                    </div>

                    {/* Student Message */}
                    {messageTargetAudience.includes("student") && (
                      <div className="border rounded-md p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">
                            Student Dashboard Message
                          </Label>
                        </div>

                        {/* School selection for student message */}
                        <div className="flex items-center space-x-2">
                          <Label className="text-xs whitespace-nowrap">
                            Target School:
                          </Label>
                          <Select
                            value={studentMessageSchool || "all"}
                            onValueChange={(value) =>
                              setStudentMessageSchool(
                                value === "all" ? null : value
                              )
                            }
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Select a school" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">
                                <div className="flex items-center">
                                  <Globe className="mr-2 h-3 w-3" />
                                  All Schools
                                </div>
                              </SelectItem>
                              {schools.map((school) => (
                                <SelectItem key={school.id} value={school.id}>
                                  {school.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <textarea
                          className="w-full min-h-[80px] p-2 border rounded-md text-sm"
                          value={customStudentMessage}
                          onChange={(e) =>
                            setCustomStudentMessage(e.target.value)
                          }
                          placeholder="Enter a message for student dashboards..."
                        />
                        {customStudentMessage && (
                          <div className="p-2 bg-muted/50 rounded-md text-xs border border-primary/20">
                            <div className="font-semibold mb-1">Preview:</div>
                            {customStudentMessage}
                            {studentMessageSchool && (
                              <div className="mt-1 text-xs text-muted-foreground">
                                Target:{" "}
                                {schools.find(
                                  (s) => s.id === studentMessageSchool
                                )?.name || "Unknown School"}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Teacher Message */}
                    {messageTargetAudience.includes("teacher") && (
                      <div className="border rounded-md p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">
                            Teacher Dashboard Message
                          </Label>
                        </div>

                        {/* School selection for teacher message */}
                        <div className="flex items-center space-x-2">
                          <Label className="text-xs whitespace-nowrap">
                            Target School:
                          </Label>
                          <Select
                            value={teacherMessageSchool || "all"}
                            onValueChange={(value) =>
                              setTeacherMessageSchool(
                                value === "all" ? null : value
                              )
                            }
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Select a school" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">
                                <div className="flex items-center">
                                  <Globe className="mr-2 h-3 w-3" />
                                  All Schools
                                </div>
                              </SelectItem>
                              {schools.map((school) => (
                                <SelectItem key={school.id} value={school.id}>
                                  {school.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <textarea
                          className="w-full min-h-[80px] p-2 border rounded-md text-sm"
                          value={customTeacherMessage}
                          onChange={(e) =>
                            setCustomTeacherMessage(e.target.value)
                          }
                          placeholder="Enter a message for teacher dashboards..."
                        />
                        {customTeacherMessage && (
                          <div className="p-2 bg-muted/50 rounded-md text-xs border border-primary/20">
                            <div className="font-semibold mb-1">Preview:</div>
                            {customTeacherMessage}
                            {teacherMessageSchool && (
                              <div className="mt-1 text-xs text-muted-foreground">
                                Target:{" "}
                                {schools.find(
                                  (s) => s.id === teacherMessageSchool
                                )?.name || "Unknown School"}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Admin Message */}
                    {messageTargetAudience.includes("admin") && (
                      <div className="border rounded-md p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">
                            School Admin Dashboard Message
                          </Label>
                        </div>

                        {/* School selection for admin message */}
                        <div className="flex items-center space-x-2">
                          <Label className="text-xs whitespace-nowrap">
                            Target School:
                          </Label>
                          <Select
                            value={adminMessageSchool || "all"}
                            onValueChange={(value) =>
                              setAdminMessageSchool(
                                value === "all" ? null : value
                              )
                            }
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Select a school" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">
                                <div className="flex items-center">
                                  <Globe className="mr-2 h-3 w-3" />
                                  All Schools
                                </div>
                              </SelectItem>
                              {schools.map((school) => (
                                <SelectItem key={school.id} value={school.id}>
                                  {school.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <textarea
                          className="w-full min-h-[80px] p-2 border rounded-md text-sm"
                          value={customAdminMessage}
                          onChange={(e) =>
                            setCustomAdminMessage(e.target.value)
                          }
                          placeholder="Enter a message for school admin dashboards..."
                        />
                        {customAdminMessage && (
                          <div className="p-2 bg-muted/50 rounded-md text-xs border border-primary/20">
                            <div className="font-semibold mb-1">Preview:</div>
                            {customAdminMessage}
                            {adminMessageSchool && (
                              <div className="mt-1 text-xs text-muted-foreground">
                                Target:{" "}
                                {schools.find(
                                  (s) => s.id === adminMessageSchool
                                )?.name || "Unknown School"}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}

                    <p className="text-xs text-muted-foreground mt-1">
                      These messages will be displayed on the respective user
                      dashboards. If both system admin and school admin set
                      messages, both will be shown to users. HTML is not
                      supported.
                    </p>
                  </div>
                </div>

                <CardFooter className="flex justify-end px-0 pt-4">
                  <Button onClick={saveCustomizationSettings} disabled={saving}>
                    {saving && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save Customization Settings
                  </Button>
                </CardFooter>
              </>
            )}
          </TabsContent>

          <TabsContent value="appearance" className="space-y-4 pt-4">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-36 w-full" />
              </div>
            ) : (
              <Suspense
                fallback={
                  <div className="space-y-4">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-36 w-full" />
                  </div>
                }
              >
                <ThemeCustomization
                  schools={schools}
                  selectedSchool={selectedSchool}
                  onSchoolSelect={onSchoolSelect}
                />
              </Suspense>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
