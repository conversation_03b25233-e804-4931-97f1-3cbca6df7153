import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import SecondaryNavigation from "./navigation/SecondaryNavigation";
import UsageChart from "./charts/UsageChart";
import AttendanceTrendChart from "./charts/AttendanceTrendChart";
import SystemPerformanceChart from "./charts/SystemPerformanceChart";
import {
  BarChart4,
  RefreshCw,
  FileText,
  Download,
  Calendar,
  TrendingUp,
  Activity,
  Users,
  PieChart,
  LineChart,
  Database,
  Cpu,
  HardDrive,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { fetchUsageStatistics } from "@/lib/api/system-admin";
import { generateReport } from "@/lib/api/reports";
import { supabase } from "@/lib/supabase";

export default function ReportsAnalytics() {
  const { toast } = useToast();
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState("usage");
  const [period, setPeriod] = useState(30);
  const [usageStats, setUsageStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [performanceType, setPerformanceType] = useState<
    "database" | "api" | "memory"
  >("database");
  const [reportType, setReportType] = useState("attendance");
  const [reportDateRange, setReportDateRange] = useState("30days");
  const [reportSchool, setReportSchool] = useState("all");
  const [reportFormat, setReportFormat] = useState("pdf");
  const [generatingReport, setGeneratingReport] = useState(false);
  const [schools, setSchools] = useState<any[]>([]);

  useEffect(() => {
    fetchUsageData();
    fetchSchools();
  }, [period]);

  // Fetch schools for the report filter
  const fetchSchools = async () => {
    try {
      const { data, error } = await supabase
        .from("schools")
        .select("id, name")
        .order("name");

      if (error) throw error;

      setSchools(data || []);
    } catch (error) {
      console.error("Error fetching schools:", error);
      toast({
        title: "Error",
        description: "Failed to fetch schools",
        variant: "destructive",
      });
    }
  };

  const fetchUsageData = async () => {
    try {
      setLoading(true);
      const stats = await fetchUsageStatistics(period);
      setUsageStats(stats);
    } catch (error) {
      console.error("Error fetching usage statistics:", error);
      toast({
        title: "Error",
        description: "Failed to fetch usage statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchUsageData();
    setRefreshing(false);
  };

  const handlePeriodChange = (value: string) => {
    const days = parseInt(value.replace("days", ""));
    setPeriod(days);
  };

  // Handle report generation
  const handleGenerateReport = async () => {
    try {
      setGeneratingReport(true);

      const success = await generateReport(
        reportType,
        reportDateRange,
        reportSchool,
        reportFormat
      );

      if (success) {
        toast({
          title: "Report Generated",
          description: `Your ${reportType} report has been generated and downloaded.`,
          variant: "success",
        });
      } else {
        toast({
          title: "Report Generation Failed",
          description: "Failed to generate the report. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error generating report:", error);
      toast({
        title: "Report Generation Error",
        description: "An error occurred while generating the report.",
        variant: "destructive",
      });
    } finally {
      setGeneratingReport(false);
    }
  };

  // Secondary navigation items
  const navItems = [
    {
      id: "usage",
      label: "Usage Statistics",
      icon: <Activity className="h-4 w-4" />,
    },
    {
      id: "attendance",
      label: "Attendance Trends",
      icon: <TrendingUp className="h-4 w-4" />,
    },
    {
      id: "performance",
      label: "System Performance",
      icon: <BarChart4 className="h-4 w-4" />,
    },
    {
      id: "reports",
      label: "Custom Reports",
      icon: <FileText className="h-4 w-4" />,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Reports & Analytics</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      <SecondaryNavigation
        items={navItems}
        activeItem={activeTab}
        onItemChange={setActiveTab}
        className="mt-4"
      >
        {{
          usage: (
            <div className="space-y-6">
              <motion.div
                className="flex items-center justify-between"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="space-y-1">
                  <h3 className="text-lg font-medium">
                    System Usage Statistics
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Overview of system usage across all schools
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="date-range" className="sr-only">
                    Date Range
                  </Label>
                  <Select
                    defaultValue={`${period}days`}
                    onValueChange={handlePeriodChange}
                  >
                    <SelectTrigger id="date-range" className="w-[180px]">
                      <Calendar className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">Last 7 days</SelectItem>
                      <SelectItem value="30days">Last 30 days</SelectItem>
                      <SelectItem value="90days">Last 90 days</SelectItem>
                      <SelectItem value="365days">Last year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </motion.div>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        Total Logins
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {loading
                          ? "..."
                          : usageStats?.loginCount.toLocaleString() || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        <span className="text-green-500">↑ 12%</span> vs
                        previous period
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        QR Scans
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {loading
                          ? "..."
                          : usageStats?.qrScanCount.toLocaleString() || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        <span className="text-green-500">↑ 8%</span> vs previous
                        period
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        Attendance Records
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {loading
                          ? "..."
                          : usageStats?.attendanceCount.toLocaleString() || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        <span className="text-green-500">↑ 15%</span> vs
                        previous period
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">
                        Excuse Submissions
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {loading
                          ? "..."
                          : usageStats?.excuseCount.toLocaleString() || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        <span className="text-red-500">↓ 3%</span> vs previous
                        period
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>

              {/* Usage Chart */}
              <UsageChart period={period} />
            </div>
          ),
          attendance: (
            <div className="space-y-6">
              <motion.div
                className="flex justify-between items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="space-y-1">
                  <h3 className="text-lg font-medium">Attendance Trends</h3>
                  <p className="text-sm text-muted-foreground">
                    Analyze attendance patterns across all schools
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="trend-date-range" className="sr-only">
                    Date Range
                  </Label>
                  <Select
                    defaultValue="14days"
                    onValueChange={(value) => {
                      const days = parseInt(value.replace("days", ""));
                      // This would update a local state for the attendance chart period
                    }}
                  >
                    <SelectTrigger id="trend-date-range" className="w-[180px]">
                      <Calendar className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">Last 7 days</SelectItem>
                      <SelectItem value="14days">Last 14 days</SelectItem>
                      <SelectItem value="30days">Last 30 days</SelectItem>
                      <SelectItem value="90days">Last 90 days</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </motion.div>

              <AttendanceTrendChart days={14} />
            </div>
          ),
          performance: (
            <div className="space-y-6">
              <motion.div
                className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="space-y-1">
                  <h3 className="text-base sm:text-lg font-medium">
                    System Performance Metrics
                  </h3>
                  <p className="text-xs sm:text-sm text-muted-foreground">
                    Monitor system performance and resource usage
                  </p>
                </div>
                <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 w-full sm:w-auto mt-2 sm:mt-0">
                  <Label htmlFor="performance-period" className="sr-only">
                    Date Range
                  </Label>
                  <Select
                    defaultValue={`${period}days`}
                    onValueChange={handlePeriodChange}
                  >
                    <SelectTrigger
                      id="performance-period"
                      className="w-full xs:w-[150px] sm:w-[180px] h-9 text-xs sm:text-sm"
                    >
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">Last 7 days</SelectItem>
                      <SelectItem value="30days">Last 30 days</SelectItem>
                      <SelectItem value="90days">Last 90 days</SelectItem>
                      <SelectItem value="365days">Last year</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={refreshing}
                    className="w-full xs:w-auto h-9 text-xs sm:text-sm"
                  >
                    <RefreshCw
                      className={`h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ${
                        refreshing ? "animate-spin" : ""
                      }`}
                    />
                    Refresh
                  </Button>
                </div>
              </motion.div>

              <motion.div
                className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    className={`cursor-pointer ${
                      performanceType === "database" ? "border-primary" : ""
                    }`}
                    onClick={() => setPerformanceType("database")}
                  >
                    <CardHeader className="pb-1 sm:pb-2 px-3 sm:px-6">
                      <CardTitle className="text-xs sm:text-sm font-medium flex items-center">
                        <Database className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-blue-500 flex-shrink-0" />
                        <span>Database Size</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6 py-2 sm:py-4">
                      <div className="text-xl sm:text-2xl font-bold">
                        {loading ? "..." : "750 MB"}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        <span className="text-green-500">↑ 5%</span> vs previous
                        period
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    className={`cursor-pointer ${
                      performanceType === "api" ? "border-primary" : ""
                    }`}
                    onClick={() => setPerformanceType("api")}
                  >
                    <CardHeader className="pb-1 sm:pb-2 px-3 sm:px-6">
                      <CardTitle className="text-xs sm:text-sm font-medium flex items-center">
                        <Activity className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-green-500 flex-shrink-0" />
                        <span>API Response Time</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6 py-2 sm:py-4">
                      <div className="text-xl sm:text-2xl font-bold">
                        {loading ? "..." : "120 ms"}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        <span className="text-red-500">↑ 2%</span> vs previous
                        period
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    className={`cursor-pointer ${
                      performanceType === "memory" ? "border-primary" : ""
                    }`}
                    onClick={() => setPerformanceType("memory")}
                  >
                    <CardHeader className="pb-1 sm:pb-2 px-3 sm:px-6">
                      <CardTitle className="text-xs sm:text-sm font-medium flex items-center">
                        <HardDrive className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-purple-500 flex-shrink-0" />
                        <span>Memory Usage</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="px-3 sm:px-6 py-2 sm:py-4">
                      <div className="text-xl sm:text-2xl font-bold">
                        {loading ? "..." : "4.2 GB"}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        <span className="text-green-500">↓ 3%</span> vs previous
                        period
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>

              <SystemPerformanceChart
                period={period}
                type={performanceType}
                title={
                  performanceType === "database"
                    ? "Database Size Over Time"
                    : performanceType === "api"
                    ? "API Response Time"
                    : "Memory Usage"
                }
                description={
                  performanceType === "database"
                    ? "Total database size in megabytes"
                    : performanceType === "api"
                    ? "Average API response time in milliseconds"
                    : "Server memory usage in gigabytes"
                }
              />
            </div>
          ),
          reports: (
            <div className="space-y-6">
              <motion.div
                className="flex justify-between items-center"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="space-y-1">
                  <h3 className="text-lg font-medium">
                    Custom Report Generation
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Create and download custom reports for your organization
                  </p>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Custom Report Generation</CardTitle>
                    <CardDescription>
                      Create and download custom reports
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="report-type">Report Type</Label>
                        <Select
                          defaultValue={reportType}
                          onValueChange={(value) => setReportType(value)}
                        >
                          <SelectTrigger id="report-type">
                            <SelectValue placeholder="Select report type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="attendance">
                              Attendance Report
                            </SelectItem>
                            <SelectItem value="users">
                              User Activity Report
                            </SelectItem>
                            <SelectItem value="schools">
                              School Statistics Report
                            </SelectItem>
                            <SelectItem value="security">
                              Security Audit Report
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="date-range">Date Range</Label>
                        <Select
                          defaultValue={reportDateRange}
                          onValueChange={(value) => setReportDateRange(value)}
                        >
                          <SelectTrigger id="date-range">
                            <SelectValue placeholder="Select date range" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="7days">Last 7 days</SelectItem>
                            <SelectItem value="30days">Last 30 days</SelectItem>
                            <SelectItem value="90days">Last 90 days</SelectItem>
                            <SelectItem value="365days">Last year</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="school-filter">School Filter</Label>
                        <Select
                          defaultValue={reportSchool}
                          onValueChange={(value) => setReportSchool(value)}
                        >
                          <SelectTrigger id="school-filter">
                            <SelectValue placeholder="Select school filter" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Schools</SelectItem>
                            {schools.map((school) => (
                              <SelectItem key={school.id} value={school.id}>
                                {school.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="format">Export Format</Label>
                        <Select
                          defaultValue={reportFormat}
                          onValueChange={(value) => setReportFormat(value)}
                        >
                          <SelectTrigger id="format">
                            <SelectValue placeholder="Select export format" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pdf">PDF Document</SelectItem>
                            <SelectItem value="csv">CSV Spreadsheet</SelectItem>
                            <SelectItem value="excel">
                              Excel Spreadsheet
                            </SelectItem>
                            <SelectItem value="json">JSON Data</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        className="ml-auto"
                        onClick={handleGenerateReport}
                        disabled={generatingReport}
                      >
                        {generatingReport ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Download className="h-4 w-4 mr-2" />
                            Generate Report
                          </>
                        )}
                      </Button>
                    </motion.div>
                  </CardFooter>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>Saved Reports</CardTitle>
                    <CardDescription>
                      Access your previously generated reports
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-4 text-muted-foreground">
                      No saved reports found
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          ),
        }}
      </SecondaryNavigation>
    </div>
  );
}
