/**
 * 🎉 Real-time Scan Feedback Component
 * Provides visual feedback for successful attendance scans
 */

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  <PERSON><PERSON>ircle,
  UserCheck,
  Clock,
  Users,
  Sparkles,
  Heart,
  Star,
  Zap
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";

export interface ScanEvent {
  id: string;
  studentName: string;
  studentId: string;
  timestamp: Date;
  status: "success" | "late" | "early";
  avatar?: string;
  grade?: string;
  className?: string;
}

interface ScanFeedbackProps {
  recentScans: ScanEvent[];
  totalScansToday: number;
  className?: string;
}

const successIcons = [CheckCircle, UserCheck, Sparkles, Heart, Star, Zap];
const getRandomIcon = () => successIcons[Math.floor(Math.random() * successIcons.length)];

const statusColors = {
  success: "text-green-300 bg-green-500/20 border-green-400/30",
  late: "text-orange-300 bg-orange-500/20 border-orange-400/30",
  early: "text-blue-300 bg-blue-500/20 border-blue-400/30",
};

// Utility function to format relative time with internationalization
const formatRelativeTime = (date: Date, t: any) => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return t("notifications.timeAgo.seconds", { count: diffInSeconds });
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return t("notifications.timeAgo.minutes", { count: minutes });
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return t("notifications.timeAgo.hours", { count: hours });
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return t("notifications.timeAgo.days", { count: days });
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return t("notifications.timeAgo.months", { count: months });
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return t("notifications.timeAgo.years", { count: years });
  }
};



export function ScanFeedback({ recentScans, totalScansToday, className }: ScanFeedbackProps) {
  const { t } = useTranslation();
  const [displayScans, setDisplayScans] = useState<ScanEvent[]>([]);
  const [celebrationMode, setCelebrationMode] = useState(false);

  const statusMessages = {
    success: t("admin.tablets.display.checkedInSuccessfully"),
    late: t("admin.tablets.display.lateArrival"),
    early: t("admin.tablets.display.earlyBird"),
  };



  // Update display scans when new scans arrive - Fixed logic
  useEffect(() => {
    // Always update displayScans to match recentScans (up to 5)
    const newScans = recentScans.slice(0, 5);
    setDisplayScans(newScans);

    // Trigger celebration for milestone scans
    if (totalScansToday > 0 && totalScansToday % 10 === 0) {
      setCelebrationMode(true);
      setTimeout(() => setCelebrationMode(false), 3000);
    }
  }, [recentScans, totalScansToday]); // Removed displayScans.length from dependencies

  return (
    <div className={cn("space-y-4", className)}>
      {/* Enhanced Scan Counter with Glassmorphism */}
      <motion.div
        className="text-center"
        animate={celebrationMode ? { scale: [1, 1.1, 1] } : {}}
        transition={{ duration: 0.5, repeat: celebrationMode ? 3 : 0 }}
      >
        <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/20 backdrop-blur-sm rounded-xl sm:rounded-2xl px-4 sm:px-6 lg:px-8 py-3 sm:py-4 shadow-2xl border border-white/30">
          <div className="p-1.5 sm:p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg sm:rounded-xl">
            <Users className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-white" />
          </div>
          <span className="text-xl sm:text-2xl lg:text-3xl font-black text-white">{totalScansToday}</span>
          <span className="text-white/90 font-semibold text-sm sm:text-base lg:text-lg">{t("admin.tablets.display.scansToday")}</span>
          {celebrationMode && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="text-2xl"
            >
              🎉
            </motion.div>
          )}
        </div>
      </motion.div>

      {/* Recent Scans List */}
      <div className="space-y-2 max-h-96 overflow-y-auto overflow-x-hidden">
        <AnimatePresence mode="popLayout">
          {displayScans.map((scan, index) => {
            const Icon = getRandomIcon();
            const isRecent = index === 0;
            
            return (
              <motion.div
                key={scan.id}
                initial={{ 
                  opacity: 0, 
                  y: -50, 
                  scale: 0.8,
                  rotateX: -90 
                }}
                animate={{ 
                  opacity: 1, 
                  y: 0, 
                  scale: 1,
                  rotateX: 0 
                }}
                exit={{ 
                  opacity: 0, 
                  y: 20, 
                  scale: 0.8,
                  transition: { duration: 0.2 }
                }}
                transition={{ 
                  duration: 0.6,
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 100,
                  damping: 15
                }}
                className={cn(
                  "relative overflow-hidden rounded-lg sm:rounded-xl border-2 p-3 sm:p-4 backdrop-blur-sm",
                  statusColors[scan.status],
                  isRecent && "ring-2 ring-blue-300 ring-opacity-50"
                )}
              >
                {/* Background Animation for Recent Scan */}
                {isRecent && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10"
                    initial={{ x: "-100%" }}
                    animate={{ x: "100%" }}
                    transition={{ 
                      duration: 2, 
                      repeat: Infinity, 
                      repeatType: "loop",
                      ease: "linear"
                    }}
                  />
                )}

                <div className="relative flex items-center gap-4">
                  {/* Animated Icon */}
                  <motion.div
                    className="flex-shrink-0"
                    animate={isRecent ? { 
                      rotate: [0, 10, -10, 0],
                      scale: [1, 1.1, 1]
                    } : {}}
                    transition={{ 
                      duration: 0.8,
                      repeat: isRecent ? 2 : 0
                    }}
                  >
                    <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center shadow-lg border border-white/50">
                      <Icon className="w-5 h-5 sm:w-6 sm:h-6" />
                    </div>
                  </motion.div>

                  {/* Student Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h3 className="font-bold text-base sm:text-lg truncate text-white">
                        {scan.studentName}
                      </h3>
                      {isRecent && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium"
                        >
                          {t("admin.tablets.display.new")}
                        </motion.div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs sm:text-sm text-white/80">
                      <span>{scan.studentId}</span>
                      {scan.grade && (
                        <>
                          <span>•</span>
                          <span className="hidden sm:inline">{scan.grade}</span>
                        </>
                      )}
                      {scan.className && (
                        <>
                          <span className="hidden sm:inline">•</span>
                          <span className="hidden sm:inline">{scan.className}</span>
                        </>
                      )}
                    </div>

                    <p className="text-xs sm:text-sm font-medium mt-1 text-white/90">
                      {statusMessages[scan.status]}
                    </p>
                  </div>

                  {/* Responsive Timestamp */}
                  <div className="flex-shrink-0 text-right">
                    <div className="flex items-center gap-1 text-xs sm:text-sm text-white/80">
                      <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                      <span className="hidden sm:inline">
                        {formatRelativeTime(scan.timestamp, t)}
                      </span>
                      <span className="sm:hidden">
                        {formatRelativeTime(scan.timestamp, t)}
                      </span>
                    </div>
                    <div className="text-xs text-white/60 mt-1">
                      {scan.timestamp.toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </div>
                  </div>
                </div>

                {/* Scan Success Ripple Effect */}
                {isRecent && (
                  <motion.div
                    className="absolute inset-0 border-2 border-green-400 rounded-xl"
                    initial={{ scale: 1, opacity: 0.8 }}
                    animate={{ scale: 1.05, opacity: 0 }}
                    transition={{ duration: 1.5, ease: "easeOut" }}
                  />
                )}
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Enhanced Empty State */}
      {displayScans.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-12"
        >
          <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30">
            <UserCheck className="w-10 h-10 text-white/60" />
          </div>
          <p className="text-white/80 font-semibold text-lg">{t("admin.tablets.display.waitingForScans", "Waiting for scans...")}</p>
          <p className="text-white/60 text-sm mt-2">
            {t("admin.tablets.display.studentAttendanceRealTime", "Student attendance will appear here in real-time")}
          </p>
        </motion.div>
      )}

      {/* Celebration Confetti */}
      {celebrationMode && (
        <motion.div
          className="fixed inset-0 pointer-events-none z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-yellow-400 rounded-full"
              initial={{
                x: Math.random() * window.innerWidth,
                y: -10,
                rotate: 0,
              }}
              animate={{
                y: window.innerHeight + 10,
                rotate: 360,
                x: Math.random() * window.innerWidth,
              }}
              transition={{
                duration: 3,
                delay: Math.random() * 2,
                ease: "easeOut",
              }}
            />
          ))}
        </motion.div>
      )}
    </div>
  );
}
