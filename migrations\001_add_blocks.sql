-- Create blocks table
CREATE TABLE IF NOT EXISTS blocks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW())
);

-- Insert default blocks
INSERT INTO blocks (name) VALUES 
('One'),
('Two'),
('Three'),
('Four');

-- Add block_id to rooms table
ALTER TABLE rooms 
ADD COLUMN IF NOT EXISTS block_id UUID REFERENCES blocks(id),
ADD COLUMN IF NOT EXISTS teacher_id UUID REFERENCES auth.users(id);

-- Update existing rooms to use block_id instead of building
UPDATE rooms
SET block_id = (
    SELECT id FROM blocks WHERE name = 'One'
)
WHERE block_id IS NULL;

-- Make block_id required
ALTER TABLE rooms 
ALTER COLUMN block_id SET NOT NULL; 