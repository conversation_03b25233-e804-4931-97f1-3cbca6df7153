import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Mail,
  MessageSquare,
  Calendar,
  CheckCircle,
  XCircle,
  RefreshCw,
  Clock,
} from "lucide-react";
import { format, parseISO } from "date-fns";

interface NotificationLog {
  id: string;
  student_id: string;
  excuse_id: string;
  notification_type: string;
  recipient: string;
  success: boolean;
  error_message?: string;
  created_at: string;
  studentName?: string;
}

export default function NotificationHistory() {
  const [logs, setLogs] = useState<NotificationLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<
    "all" | "success" | "failed"
  >("all");
  const [typeFilter, setTypeFilter] = useState<"all" | "email" | "sms">("all");
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();

  const fetchLogs = async () => {
    try {
      setLoading(true);

      const { data, error } = await supabase
        .from("notification_logs")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Fetch student names
      const studentIds = [
        ...new Set((data || []).map((log) => log.student_id)),
      ];
      const { data: students, error: studentsError } = await supabase
        .from("profiles")
        .select("id, name")
        .in("id", studentIds);

      if (studentsError) throw studentsError;

      // Map student names to logs
      const logsWithNames = (data || []).map((log) => {
        const student = students?.find((s) => s.id === log.student_id);
        return {
          ...log,
          studentName:
            student?.name || t("admin.parentNotifications.unknownStudent"),
        };
      });

      setLogs(logsWithNames);
    } catch (error: any) {
      console.error("Error fetching notification logs:", error);
      toast({
        title: t("admin.parentNotifications.error"),
        description: t("admin.parentNotifications.errorFetchingLogs", {
          message: error.message,
        }),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchLogs();
    setRefreshing(false);
  };

  // Filter logs based on search query and filters
  const filteredLogs = logs.filter((log) => {
    // Filter by search query
    const matchesSearch =
      log.studentName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.recipient.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by status
    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "success" && log.success) ||
      (statusFilter === "failed" && !log.success);

    // Filter by type
    const matchesType =
      typeFilter === "all" ||
      (typeFilter === "email" && log.notification_type === "email") ||
      (typeFilter === "sms" && log.notification_type === "sms");

    return matchesSearch && matchesStatus && matchesType;
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          {t("admin.parentNotifications.notificationHistoryTitle")}
        </h3>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw
            className={`mr-2 h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
          />
          {refreshing
            ? t("admin.parentNotifications.refreshing")
            : t("admin.parentNotifications.refresh")}
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex items-center space-x-2 flex-1">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t(
              "admin.parentNotifications.searchByStudentOrRecipient"
            )}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1"
          />
        </div>

        <div className="flex gap-2">
          <div className="w-40">
            <Select
              value={statusFilter}
              onValueChange={(value: any) => setStatusFilter(value)}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={t("admin.parentNotifications.status")}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("admin.parentNotifications.allStatuses")}
                </SelectItem>
                <SelectItem value="success">
                  {t("admin.parentNotifications.successful")}
                </SelectItem>
                <SelectItem value="failed">
                  {t("admin.parentNotifications.failed")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="w-40">
            <Select
              value={typeFilter}
              onValueChange={(value: any) => setTypeFilter(value)}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={t("admin.parentNotifications.type")}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("admin.parentNotifications.allTypes")}
                </SelectItem>
                <SelectItem value="email">
                  {t("admin.parentNotifications.email")}
                </SelectItem>
                <SelectItem value="sms">
                  {t("admin.parentNotifications.sms")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="space-y-2">
          {[1, 2, 3, 4, 5].map((i) => (
            <Skeleton key={i} className="h-12 w-full" />
          ))}
        </div>
      ) : filteredLogs.length === 0 ? (
        <div className="text-center py-8 border rounded-md bg-muted/20">
          <Clock className="mx-auto h-12 w-12 text-muted-foreground mb-2" />
          <h3 className="text-lg font-medium">
            {t("admin.parentNotifications.noNotificationLogs")}
          </h3>
          <p className="text-muted-foreground">
            {searchQuery || statusFilter !== "all" || typeFilter !== "all"
              ? t("admin.parentNotifications.noLogsMatchCriteria")
              : t("admin.parentNotifications.noLogsRecorded")}
          </p>
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  {t("admin.parentNotifications.dateAndTime")}
                </TableHead>
                <TableHead>{t("admin.parentNotifications.student")}</TableHead>
                <TableHead>
                  {t("admin.parentNotifications.recipient")}
                </TableHead>
                <TableHead>{t("admin.parentNotifications.type")}</TableHead>
                <TableHead>{t("admin.parentNotifications.status")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>
                        {format(parseISO(log.created_at), "MMM d, yyyy h:mm a")}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>{log.studentName}</TableCell>
                  <TableCell>{log.recipient}</TableCell>
                  <TableCell>
                    {log.notification_type === "email" ? (
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700 hover:bg-blue-50"
                      >
                        <Mail className="mr-1 h-3 w-3" />
                        {t("admin.parentNotifications.email")}
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="bg-purple-50 text-purple-700 hover:bg-purple-50"
                      >
                        <MessageSquare className="mr-1 h-3 w-3" />
                        {t("admin.parentNotifications.sms")}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {log.success ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="mr-1 h-4 w-4" />
                        {t("admin.parentNotifications.sent")}
                      </div>
                    ) : (
                      <div className="flex items-center text-red-600">
                        <XCircle className="mr-1 h-4 w-4" />
                        {t("admin.parentNotifications.failedStatus")}
                        {log.error_message && (
                          <span className="ml-1 text-xs text-muted-foreground">
                            ({log.error_message})
                          </span>
                        )}
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
