import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Mic, MicOff, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslation } from 'react-i18next';
import { ChatMessage } from '@/lib/services/ai-assistant-service';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import QuickActions from './QuickActions';
import AIIntroduction from './AIIntroduction';

interface ChatWindowProps {
  isOpen: boolean;
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  isTyping: boolean;
  onClose: () => void;
}

export default function ChatWindow({ 
  isOpen, 
  messages, 
  onSendMessage, 
  isTyping,
  onClose 
}: ChatWindowProps) {
  const [inputMessage, setInputMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { t, i18n } = useTranslation();

  // Initialize speech recognition with language support
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognitionInstance = new SpeechRecognition();

      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;

      // Set language based on current i18n language
      const currentLang = i18n.language || 'en';
      recognitionInstance.lang = currentLang === 'tr' ? 'tr-TR' : 'en-US';

      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInputMessage(transcript);
        setIsListening(false);
      };

      recognitionInstance.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
      };

      recognitionInstance.onend = () => {
        setIsListening(false);
      };

      setRecognition(recognitionInstance);
    }
  }, [i18n.language]); // Re-initialize when language changes

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 300);
    }
  }, [isOpen]);

  const handleSendMessage = () => {
    if (inputMessage.trim()) {
      onSendMessage(inputMessage.trim());
      setInputMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleVoiceInput = () => {
    if (!recognition) return;

    if (isListening) {
      recognition.stop();
      setIsListening(false);
    } else {
      recognition.start();
      setIsListening(true);
    }
  };

  // Text-to-Speech functionality
  const speakMessage = (text: string) => {
    if ('speechSynthesis' in window) {
      // Stop any ongoing speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);

      // Set language based on current i18n language
      const currentLang = i18n.language || 'en';
      utterance.lang = currentLang === 'tr' ? 'tr-TR' : 'en-US';

      // Configure speech settings
      utterance.rate = 0.9;
      utterance.pitch = 1;
      utterance.volume = 0.8;

      utterance.onstart = () => setIsSpeaking(true);
      utterance.onend = () => setIsSpeaking(false);
      utterance.onerror = () => setIsSpeaking(false);

      window.speechSynthesis.speak(utterance);
    }
  };

  const stopSpeaking = () => {
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
    }
  };

  const quickActionSuggestions = [
    { id: 'scan_qr', label: t('aiAssistant.quickActions.scanQR'), message: t('aiAssistant.quickActions.scanQR') },
    { id: 'check_attendance', label: t('aiAssistant.quickActions.checkAttendance'), message: t('aiAssistant.quickActions.checkAttendance') },
    { id: 'biometric_help', label: t('aiAssistant.quickActions.biometricHelp'), message: t('aiAssistant.quickActions.biometricHelp') },
    { id: 'app_tour', label: t('aiAssistant.quickActions.appTour'), message: t('aiAssistant.quickActions.appTour') }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 100, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 100, scale: 0.9 }}
          transition={{ type: "spring", damping: 25, stiffness: 300 }}
          className="fixed bottom-20 left-4 right-4 md:bottom-24 md:right-6 md:left-auto md:w-96 h-[min(70vh,calc(100vh-120px))] md:h-[min(600px,calc(100vh-120px))] max-h-[calc(100vh-120px)] z-40"
        >
          {/* Glassmorphism container */}
          <div className="relative w-full h-full bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#08194A]/90 via-[#0A1B4D]/85 to-[#0C1E50]/90" />
            
            {/* Animated background particles */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(20)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ 
                    x: Math.random() * 400,
                    y: Math.random() * 600,
                    opacity: 0
                  }}
                  animate={{ 
                    x: Math.random() * 400,
                    y: Math.random() * 600,
                    opacity: [0, 0.3, 0]
                  }}
                  transition={{ 
                    duration: Math.random() * 10 + 10,
                    repeat: Infinity,
                    delay: Math.random() * 5
                  }}
                  className="absolute w-1 h-1 bg-[#EE0D09]/50 rounded-full"
                />
              ))}
            </div>

            {/* Content */}
            <div className="relative z-10 flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-3 md:p-4 border-b border-white/10">
                <div className="flex items-center gap-2 md:gap-3 min-w-0 flex-1">
                  <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A] flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 md:w-5 md:h-5 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-white text-sm md:text-base truncate">{t('aiAssistant.title')}</h3>
                    <p className="text-xs text-gray-300 truncate">
                      {isTyping ? t('aiAssistant.typing') : t('aiAssistant.subtitle')}
                    </p>
                  </div>
                </div>

                <motion.div
                  animate={{ rotate: isTyping ? 360 : 0 }}
                  transition={{ duration: 2, repeat: isTyping ? Infinity : 0 }}
                  className="flex-shrink-0"
                >
                  <Sparkles className="w-4 h-4 md:w-5 md:h-5 text-[#EE0D09]" />
                </motion.div>
              </div>

              {/* Messages area */}
              <ScrollArea className="flex-1">
                <div className="space-y-4">
                  {/* AI Introduction if no messages */}
                  {messages.length === 0 && (
                    <AIIntroduction
                      onQuickAction={(message) => {
                        setInputMessage(message);
                        // Auto-send the message
                        onSendMessage(message);
                      }}
                    />
                  )}

                  {/* Message bubbles */}
                  {messages.length > 0 && (
                    <div className="p-3 md:p-4 space-y-4">
                      {messages.map((message) => (
                        <MessageBubble
                          key={message.id}
                          message={message}
                          onActionClick={(message) => {
                            setInputMessage(message);
                            // Auto-send the message
                            onSendMessage(message);
                          }}
                          onSpeakMessage={speakMessage}
                          onStopSpeaking={stopSpeaking}
                          isSpeaking={isSpeaking}
                        />
                      ))}
                    </div>
                  )}

                  {/* Typing indicator */}
                  {isTyping && (
                    <div className="p-3 md:p-4">
                      <TypingIndicator />
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Input area */}
              <div className="p-3 md:p-4 border-t border-white/10">
                <div className="flex items-center gap-2">
                  <div className="flex-1 relative">
                    <Input
                      ref={inputRef}
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder={t('aiAssistant.askAnything')}
                      className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 pr-10 md:pr-12 text-sm md:text-base focus:border-[#EE0D09]/50 focus:ring-[#EE0D09]/20"
                    />
                    
                    {/* Voice input button */}
                    <Button
                      onClick={toggleVoiceInput}
                      size="sm"
                      variant="ghost"
                      className={`absolute right-1 top-1/2 -translate-y-1/2 w-7 h-7 md:w-8 md:h-8 p-0 ${
                        isListening
                          ? 'text-[#EE0D09] bg-[#EE0D09]/20'
                          : 'text-gray-400 hover:text-white'
                      }`}
                      title={isListening ? t('aiAssistant.voiceInputActive') : t('aiAssistant.voiceInput')}
                    >
                      <motion.div
                        animate={isListening ? { scale: [1, 1.2, 1] } : {}}
                        transition={{ duration: 1, repeat: isListening ? Infinity : 0 }}
                      >
                        {isListening ? (
                          <MicOff className="w-3 h-3 md:w-4 md:h-4" />
                        ) : (
                          <Mic className="w-3 h-3 md:w-4 md:h-4" />
                        )}
                      </motion.div>
                    </Button>
                  </div>
                  
                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputMessage.trim() || isTyping}
                    size="sm"
                    className="w-9 h-9 md:w-10 md:h-10 p-0 bg-gradient-to-r from-[#EE0D09] to-[#FF1A1A] hover:from-[#D00B08] hover:to-[#E61717] disabled:opacity-50"
                  >
                    <Send className="w-3 h-3 md:w-4 md:h-4" />
                  </Button>
                </div>

                {/* Voice input indicator */}
                <AnimatePresence>
                  {isListening && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      className="mt-2 text-center"
                    >
                      <div className="flex items-center justify-center gap-2 text-[#EE0D09] text-sm">
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                          className="w-2 h-2 bg-[#EE0D09] rounded-full"
                        />
                        {t('aiAssistant.listening')}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
