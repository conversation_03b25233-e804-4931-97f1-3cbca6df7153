-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL REFERENCES profiles(id),
  title VARCHAR NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR NOT NULL,
  read BO<PERSON><PERSON><PERSON> DEFAULT false,
  timestamp TIMESTAMPTZ DEFAULT now(),
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_notifications_student_id ON public.notifications(student_id);
CREATE INDEX IF NOT EXISTS idx_notifications_timestamp ON public.notifications(timestamp);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);

-- Enable RLS
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Students can view their own notifications"
  ON public.notifications
  FOR SELECT
  TO authenticated
  USING (student_id::text = (
    SELECT id::text FROM profiles WHERE user_id::text = auth.uid()::text
  ));

CREATE POLICY "Teachers and admins can create notifications"
  ON public.notifications
  FOR INSERT
  TO authenticated
  WITH CHECK (EXISTS (
    SELECT 1 FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role IN ('teacher', 'admin')
  ));

-- Create function to automatically create notifications when attendance status changes
CREATE OR REPLACE FUNCTION create_attendance_notification()
RETURNS TRIGGER AS $$
DECLARE
  student_name text;
  teacher_name text;
  room_name text;
  teacher_id uuid;
BEGIN
  -- Get student name
  SELECT name INTO student_name
  FROM profiles
  WHERE id::text = NEW.student_id::text;

  -- Get teacher name and room name
  SELECT p.name, r.name, r.teacher_id 
  INTO teacher_name, room_name, teacher_id
  FROM rooms r
  JOIN profiles p ON p.id::text = r.teacher_id::text
  WHERE r.id::text = NEW.room_id::text;

  -- Create notification
  INSERT INTO notifications (
    student_id,
    title,
    message,
    type,
    metadata
  )
  VALUES (
    NEW.student_id,  -- Already a UUID from the attendance_records table
    CASE NEW.status
      WHEN 'present' THEN 'Marked Present'
      WHEN 'absent' THEN 'Marked Absent'
      WHEN 'late' THEN 'Marked Late'
      WHEN 'excused' THEN 'Marked Excused'
    END,
    CASE NEW.status
      WHEN 'present' THEN format('You were marked present in %s by %s', room_name, teacher_name)
      WHEN 'absent' THEN format('You were marked absent in %s. If this is incorrect, please contact %s', room_name, teacher_name)
      WHEN 'late' THEN format('You were marked late for %s by %s', room_name, teacher_name)
      WHEN 'excused' THEN format('Your absence in %s has been excused by %s', room_name, teacher_name)
    END,
    CASE NEW.status
      WHEN 'present' THEN 'attendance'
      WHEN 'absent' THEN 'absence'
      WHEN 'late' THEN 'late'
      WHEN 'excused' THEN 'excused'
    END,
    jsonb_build_object(
      'attendance_id', NEW.id::text,
      'room_id', NEW.room_id::text,
      'status', NEW.status,
      'verification_method', NEW.verification_method,
      'teacher_id', teacher_id::text
    )
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER attendance_notification_trigger
  AFTER INSERT OR UPDATE OF status
  ON attendance_records
  FOR EACH ROW
  EXECUTE FUNCTION create_attendance_notification();

-- Grant permissions
GRANT ALL ON public.notifications TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated; 