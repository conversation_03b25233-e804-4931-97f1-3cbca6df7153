import { useState, useEffect, useCallback } from "react";
import { supabase, safeRemoveChannels } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { AttendanceRecord } from "@/lib/types";
import { useTranslation } from "react-i18next";
import { useDataCache } from "./useDataCache";

export function useAttendanceHistory() {
  const { profile } = useAuth();
  const { t } = useTranslation();
  const userId = profile?.id || "guest";

  // Create a fetch function that we can pass to useDataCache
  const fetchAttendanceData = useCallback(async () => {
    if (!profile) return [];

    try {
      // For students: get their own attendance records
      // For teachers: get records related to their courses
      // For admins: get all records

      let query = supabase
        .from("attendance_records")
        .select(
          `
          id,
          student_id,
          room_id,
          timestamp,
          device_info,
          location,
          verification_method,
          status,
          rooms:room_id (name, building)
        `
        )
        .order("timestamp", { ascending: false });

      if (profile.role === "student") {
        query = query.eq("student_id", profile.id);
      } else if (profile.role === "teacher" && profile.id) {
        // Get attendance records for rooms associated with teacher's courses
        const { data: teacherCourses, error: coursesError } = await supabase
          .from("courses")
          .select("room_id")
          .eq("teacher_id", profile.id);

        if (coursesError) {
          throw coursesError;
        }

        if (teacherCourses && teacherCourses.length > 0) {
          const roomIds = teacherCourses.map((course) => course.room_id);
          query = query.in("room_id", roomIds);
        }
      }
      // No filter for admin - they see all records

      const { data, error: fetchError } = await query;

      if (fetchError) {
        throw fetchError;
      }

      // Fetch approved excuses for the student
      let excusesData: any[] = [];
      if (profile.role === "student") {
        const { data: excuses, error: excusesError } = await supabase
          .from("excuses")
          .select(
            `
            id,
            student_id,
            room_id,
            start_date,
            end_date,
            start_time,
            end_time,
            status,
            rooms:room_id (name, building)
          `
          )
          .eq("student_id", profile.id)
          .eq("status", "approved")
          .order("start_date", { ascending: false });

        if (excusesError) {
          throw excusesError;
        }

        if (excuses && excuses.length > 0) {
          excusesData = excuses;
        }
      }

      if (!data && excusesData.length === 0) {
        return [];
      }

      // If we got data, format it properly
      const formattedData = (data || []).map((record) => {
        // Handle the case where rooms might be null or undefined
        const roomData = record.rooms || {};

        // When room_id is "manual-attendance", set custom room name and building
        const isManualAttendance = record.room_id === "manual-attendance";

        // Extract values safely - handle special case for manual attendance
        const roomName = isManualAttendance
          ? t("attendance.export.manualByTeacher")
          : roomData && typeof roomData === "object" && "name" in roomData
          ? String(roomData.name)
          : t("attendance.export.unknownRoom");

        const buildingName = isManualAttendance
          ? t("attendance.export.manualByTeacher")
          : roomData && typeof roomData === "object" && "building" in roomData
          ? String(roomData.building)
          : t("attendance.export.unknownRoom");

        return {
          id: record.id,
          studentId: record.student_id,
          roomId: record.room_id,
          timestamp: record.timestamp,
          deviceInfo: record.device_info || "",
          location: record.location || { latitude: 0, longitude: 0 },
          verificationMethod: record.verification_method as
            | "biometric"
            | "pin"
            | "manual",
          status: record.status as "present" | "absent" | "late" | "excused",
          roomName: roomName,
          buildingName: buildingName,
        };
      });

      // Format excuses as attendance records
      const excuseRecords = excusesData.map((excuse) => {
        const roomData = excuse.rooms || {};

        // Create a timestamp from start_date and start_time
        const dateParts = excuse.start_date.split("-");
        const timeParts = excuse.start_time.split(":");
        const timestamp = new Date(
          parseInt(dateParts[0]),
          parseInt(dateParts[1]) - 1,
          parseInt(dateParts[2]),
          parseInt(timeParts[0]),
          parseInt(timeParts[1])
        ).toISOString();

        return {
          id: `excuse-${excuse.id}`,
          studentId: excuse.student_id,
          roomId: excuse.room_id,
          timestamp: timestamp,
          deviceInfo: "Excused absence",
          location: { latitude: 0, longitude: 0 },
          verificationMethod: "manual" as "biometric" | "pin" | "manual",
          status: "excused" as "present" | "absent" | "late" | "excused",
          roomName: roomData.name || t("attendance.export.unknownRoom"),
          buildingName: roomData.building || t("attendance.export.unknownRoom"),
        };
      });

      // Combine attendance records and excuses, and sort by timestamp
      const combinedRecords = [...formattedData, ...excuseRecords].sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      return combinedRecords;
    } catch (err: any) {
      console.error("Error fetching attendance records:", err);
      throw new Error(
        "Failed to load attendance records. Please try again later."
      );
    }
  }, [profile, t]);

  // Use our data cache hook
  const { data, loading, error, refetch } = useDataCache<AttendanceRecord[]>(
    fetchAttendanceData,
    {
      cacheKey: `attendance-history-${userId}`,
      cacheDuration: 5 * 60 * 1000, // 5 minutes
      initialFetch: true,
    }
  );

  useEffect(() => {
    if (!profile?.id) return;

    // Create unique channel names to avoid conflicts
    const attendanceChannelName = `attendance-history-${profile.id}-${Date.now()}`;
    const excusesChannelName = `excuses-history-${profile.id}-${Date.now()}`;

    // Set up a real-time subscription for new attendance records
    const attendanceChannel = supabase
      .channel(attendanceChannelName)
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "attendance_records",
          filter:
            profile?.role === "student"
              ? `student_id=eq.${profile?.id}`
              : profile?.school_id && profile?.accessLevel !== 3
              ? `school_id=eq.${profile?.school_id}`
              : undefined,
        },
        (payload) => {
          console.log("New attendance record in history:", payload);
          // Refresh the data when a new record is detected
          refetch();
        }
      )
      .subscribe();

    // Set up a real-time subscription for excuse changes
    const excusesChannel = supabase
      .channel(excusesChannelName)
      .on(
        "postgres_changes",
        {
          event: "*", // Listen for all events (INSERT, UPDATE, DELETE)
          schema: "public",
          table: "excuses",
          filter:
            profile?.role === "student"
              ? `student_id=eq.${profile?.id}`
              : profile?.school_id && profile?.accessLevel !== 3
              ? `student.school_id=eq.${profile?.school_id}`
              : undefined,
        },
        (payload) => {
          console.log("Excuse change detected:", payload);
          // Refresh the data when an excuse is changed
          refetch();
        }
      )
      .subscribe();

    // Clean up subscriptions
    return () => {
      safeRemoveChannels(attendanceChannel, excusesChannel);
    };
  }, [profile, refetch]);

  return {
    attendanceRecords: data || [],
    loading,
    error: error ? error : null,
  };
}
