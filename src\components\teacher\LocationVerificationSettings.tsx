import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loader2, MapPin, Building, Home } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";

interface LocationVerificationSettingsProps {
  roomId: string;
  blockId: string;
}

interface LocationSettings {
  globalEnabled: boolean;
  blockEnabled: boolean;
  roomEnabled: boolean;
  blockId: string;
  roomId: string;
  blockRadiusMeters: number;
  roomRadiusMeters: number;
}

export default function LocationVerificationSettings({
  roomId,
  blockId,
}: LocationVerificationSettingsProps) {
  const [settings, setSettings] = useState<LocationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();
  const { profile } = useAuth();

  // Fetch current settings
  useEffect(() => {
    const fetchSettings = async () => {
      if (!roomId) return;

      setLoading(true);
      try {
        // Get settings using our custom function
        const { data, error } = await supabase.rpc(
          "get_location_verification_settings",
          {
            room_uuid: roomId,
          }
        );

        if (error) {
          console.error("Error from RPC call:", error);

          // Fall back to default settings
          setSettings({
            globalEnabled: true,
            blockEnabled: true,
            roomEnabled: true,
            blockId: blockId,
            roomId: roomId,
            blockRadiusMeters: 100,
            roomRadiusMeters: 50,
          });

          // Only show toast for non-network errors
          if (!error.message.includes("Failed to fetch")) {
            toast({
              title: "Warning",
              description:
                "Using default settings. Some values may not be accurate.",
              variant: "default",
            });
          }
          return;
        }

        if (data && data.length > 0) {
          setSettings({
            globalEnabled: data[0].global_enabled,
            blockEnabled: data[0].block_enabled,
            roomEnabled: data[0].room_enabled,
            blockId: data[0].block_id,
            roomId: data[0].room_id,
            blockRadiusMeters: data[0].block_radius_meters,
            roomRadiusMeters: data[0].room_radius_meters,
          });
        } else {
          // No data returned, use defaults
          setSettings({
            globalEnabled: true,
            blockEnabled: true,
            roomEnabled: true,
            blockId: blockId,
            roomId: roomId,
            blockRadiusMeters: 100,
            roomRadiusMeters: 50,
          });
        }
      } catch (error) {
        console.error("Error fetching location verification settings:", error);

        // Set default settings
        setSettings({
          globalEnabled: true,
          blockEnabled: true,
          roomEnabled: true,
          blockId: blockId,
          roomId: roomId,
          blockRadiusMeters: 100,
          roomRadiusMeters: 50,
        });

        toast({
          title: "Error",
          description:
            "Failed to load location verification settings. Using defaults.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [roomId, blockId, toast]);

  // Save settings
  const saveSettings = async () => {
    if (!settings || !profile) return;

    setSaving(true);
    try {
      // Update global settings
      if (settings.blockId === null && settings.roomId === null) {
        const { error: globalError } = await supabase
          .from("location_verification_settings")
          .upsert(
            {
              global_enabled: settings.globalEnabled,
              block_id: null,
              room_id: null,
              updated_by: profile.id,
              updated_at: new Date().toISOString(),
            },
            { onConflict: "block_id,room_id" }
          );

        if (globalError) throw globalError;
      }

      // Update block settings
      if (settings.blockId && settings.roomId === null) {
        const { error: blockError } = await supabase
          .from("location_verification_settings")
          .upsert(
            {
              block_enabled: settings.blockEnabled,
              block_id: settings.blockId,
              room_id: null,
              updated_by: profile.id,
              updated_at: new Date().toISOString(),
            },
            { onConflict: "block_id,room_id" }
          );

        if (blockError) throw blockError;
      }

      // Update room settings
      if (settings.roomId) {
        const { error: roomError } = await supabase
          .from("location_verification_settings")
          .upsert(
            {
              room_enabled: settings.roomEnabled,
              block_id: settings.blockId,
              room_id: settings.roomId,
              updated_by: profile.id,
              updated_at: new Date().toISOString(),
            },
            { onConflict: "block_id,room_id" }
          );

        if (roomError) throw roomError;
      }

      toast({
        title: "Success",
        description: "Location verification settings saved successfully",
        variant: "default",
      });
    } catch (error) {
      console.error("Error saving location verification settings:", error);
      toast({
        title: "Error",
        description: "Failed to save location verification settings",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <LoadingSpinner message="Loading verification settings..." />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Location Verification Settings</CardTitle>
        <CardDescription>
          Configure location verification requirements for attendance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Global Setting */}
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-muted-foreground" />
            <Label htmlFor="global-location" className="text-base">
              Enable Location Verification
            </Label>
          </div>
          <Switch
            id="global-location"
            checked={settings?.globalEnabled}
            onCheckedChange={(checked) =>
              setSettings((prev) =>
                prev ? { ...prev, globalEnabled: checked } : null
              )
            }
            disabled={saving}
          />
        </div>
        <p className="text-sm text-muted-foreground pl-7">
          When disabled, no location verification will be required for any
          attendance
        </p>

        {/* Block Setting */}
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <Building className="h-5 w-5 text-muted-foreground" />
            <Label htmlFor="block-location" className="text-base">
              Block-level Verification
            </Label>
          </div>
          <Switch
            id="block-location"
            checked={settings?.blockEnabled}
            onCheckedChange={(checked) =>
              setSettings((prev) =>
                prev ? { ...prev, blockEnabled: checked } : null
              )
            }
            disabled={saving || !settings?.globalEnabled}
          />
        </div>
        <p className="text-sm text-muted-foreground pl-7">
          When enabled, students must be within {settings?.blockRadiusMeters}m
          of the block
        </p>

        {/* Room Setting */}
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <Home className="h-5 w-5 text-muted-foreground" />
            <Label htmlFor="room-location" className="text-base">
              Room-level Verification
            </Label>
          </div>
          <Switch
            id="room-location"
            checked={settings?.roomEnabled}
            onCheckedChange={(checked) =>
              setSettings((prev) =>
                prev ? { ...prev, roomEnabled: checked } : null
              )
            }
            disabled={
              saving || !settings?.globalEnabled || !settings?.blockEnabled
            }
          />
        </div>
        <p className="text-sm text-muted-foreground pl-7">
          When enabled, students must be within {settings?.roomRadiusMeters}m of
          the room
        </p>

        <Button
          onClick={saveSettings}
          disabled={saving || loading}
          className="w-full mt-4"
        >
          {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Save Settings
        </Button>
      </CardContent>
    </Card>
  );
}
