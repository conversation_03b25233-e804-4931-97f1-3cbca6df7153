import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useTranslation } from "react-i18next";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { User } from "@/lib/types";

interface UserMaintenanceDialogProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUserUpdated: () => void;
}

export default function UserMaintenanceDialog({
  user,
  open,
  onOpenChange,
  onUserUpdated,
}: UserMaintenanceDialogProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const isInMaintenanceMode = user?.maintenance_mode === true;
  const actionText = isInMaintenanceMode ? "disable" : "enable";
  const actionTitle = isInMaintenanceMode
    ? t("admin.userManagement.disableMaintenanceMode")
    : t("admin.userManagement.enableMaintenanceMode");

  const handleToggleMaintenance = async () => {
    if (!user) return;

    setLoading(true);
    try {
      console.log(
        `Toggling maintenance mode for user: ${user.name} (${user.id})`
      );

      // Update the user's maintenance_mode status
      let { error } = await supabase
        .from("profiles")
        .update({
          maintenance_mode: !isInMaintenanceMode,
        })
        .eq("id", user.id);

      // If there's an error with maintenance_mode column, it might not exist yet
      if (
        error &&
        error.code === "PGRST204" &&
        error.message.includes("maintenance_mode")
      ) {
        console.error("maintenance_mode column not found in profiles table");
        throw new Error(
          `Failed to ${actionText} maintenance mode: The maintenance_mode column does not exist in the profiles table. Please contact your system administrator.`
        );
      }

      // If there's still an error, it might be a permission issue
      if (error && error.code === "PGRST301") {
        console.log("Permission issue, trying with RPC function");

        // Try using an RPC function as a fallback
        const { error: rpcError } = await supabase.rpc(
          "update_user_maintenance",
          {
            user_id: user.id,
            maintenance_mode_val: !isInMaintenanceMode,
          }
        );

        if (rpcError) {
          console.error("RPC function error:", rpcError);
          error = rpcError;
        } else {
          error = null;
        }
      }

      if (error) {
        console.error("Error updating maintenance mode:", error);
        throw new Error(
          `Failed to ${actionText} maintenance mode: ${error.message}`
        );
      }

      console.log(
        `Maintenance mode ${
          isInMaintenanceMode ? "disabled" : "enabled"
        } successfully`
      );

      // Close the dialog
      onOpenChange(false);

      // Show success message
      toast({
        title: "Success",
        description: `Maintenance mode ${
          isInMaintenanceMode ? "disabled" : "enabled"
        } for ${user.name}.`,
        duration: 3000,
      });

      // Refresh the user list
      onUserUpdated();
    } catch (error: any) {
      console.error("Error toggling maintenance mode:", error);
      toast({
        title: "Error",
        description:
          error.message || `Failed to ${actionText} maintenance mode`,
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{actionTitle}</AlertDialogTitle>
          <AlertDialogDescription>
            {isInMaintenanceMode ?
              t("admin.userManagement.disableMaintenanceModeConfirm", { name: user?.name }) :
              t("admin.userManagement.enableMaintenanceModeConfirm", { name: user?.name })
            }
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleToggleMaintenance}
            className={
              isInMaintenanceMode
                ? "bg-primary"
                : "bg-amber-500 hover:bg-amber-600"
            }
            disabled={loading}
          >
            {loading ? "Processing..." : actionTitle}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
