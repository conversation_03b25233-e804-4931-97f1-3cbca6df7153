
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";

interface StatisticsProps {
  stats: {
    present: number;
    absent: number;
    late: number;
    excused: number;
    total: number;
  };
}

export function StatisticsCards({ stats }: StatisticsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card className="md:col-span-1">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Present</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {stats.present}
            <span className="text-sm text-muted-foreground font-normal ml-1">
              / {stats.total}
            </span>
          </div>
        </CardContent>
      </Card>
      <Card className="md:col-span-1">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Absent</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {stats.absent}
            <span className="text-sm text-muted-foreground font-normal ml-1">
              / {stats.total}
            </span>
          </div>
        </CardContent>
      </Card>
      <Card className="md:col-span-1">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Late</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">
            {stats.late}
            <span className="text-sm text-muted-foreground font-normal ml-1">
              / {stats.total}
            </span>
          </div>
        </CardContent>
      </Card>
      <Card className="md:col-span-1">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Excused</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {stats.excused}
            <span className="text-sm text-muted-foreground font-normal ml-1">
              / {stats.total}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
