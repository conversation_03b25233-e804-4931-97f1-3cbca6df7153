import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { excuseCleanupService } from "@/lib/services/excuse-cleanup-service";
import { 
  Clock, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  AlertTriangle,
  Info
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface CleanupSettings {
  enabled: boolean;
  check_interval_minutes: number;
  notify_students: boolean;
}

export default function ExcuseCleanupSettings() {
  const { t } = useTranslation();
  const { toast } = useToast();
  
  const [settings, setSettings] = useState<CleanupSettings>({
    enabled: true,
    check_interval_minutes: 60,
    notify_students: true
  });
  
  const [status, setStatus] = useState<{
    isRunning: boolean;
    lastCleanup?: string;
    totalProcessed?: number;
  }>({
    isRunning: false
  });
  
  const [expiredCount, setExpiredCount] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [checking, setChecking] = useState(false);
  const [running, setRunning] = useState(false);

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Get current status and settings
      const statusData = await excuseCleanupService.getCleanupStatus();
      setStatus({
        isRunning: statusData.isRunning,
        lastCleanup: statusData.lastCleanup,
        totalProcessed: statusData.totalProcessed
      });
      setSettings(statusData.settings);
      
      // Check for expired excuses
      const expiredData = await excuseCleanupService.checkExpiredExcuses();
      setExpiredCount(expiredData.count);
      
    } catch (error) {
      console.error("Error loading cleanup data:", error);
      toast({
        title: t("common.error"),
        description: t("admin.excuseCleanup.errorLoadingSettings"),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      
      const success = await excuseCleanupService.updateCleanupSettings(settings);
      
      if (success) {
        toast({
          title: t("admin.excuseCleanup.settingsSaved"),
          description: t("admin.excuseCleanup.settingsSavedMessage")
        });
        
        // Reload status
        await loadData();
      } else {
        throw new Error("Failed to save settings");
      }
      
    } catch (error) {
      console.error("Error saving settings:", error);
      toast({
        title: t("common.error"),
        description: t("admin.excuseCleanup.errorSavingSettings"),
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleRunCleanup = async () => {
    try {
      setRunning(true);
      
      const result = await excuseCleanupService.runCleanup();
      
      if (result.success) {
        toast({
          title: t("admin.excuseCleanup.cleanupCompleted"),
          description: t("admin.excuseCleanup.cleanupCompletedMessage", { count: result.processed_count })
        });
      } else {
        throw new Error(result.error || "Cleanup failed");
      }
      
      // Reload data to show updated status
      await loadData();
      
    } catch (error) {
      console.error("Error running cleanup:", error);
      toast({
        title: t("admin.excuseCleanup.cleanupFailed"),
        description: t("admin.excuseCleanup.cleanupFailedMessage"),
        variant: "destructive"
      });
    } finally {
      setRunning(false);
    }
  };

  const handleCheckExpired = async () => {
    try {
      setChecking(true);
      const expiredData = await excuseCleanupService.checkExpiredExcuses();
      setExpiredCount(expiredData.count);
      
      toast({
        title: t("admin.excuseCleanup.checkComplete"),
        description: t("admin.excuseCleanup.checkCompleteMessage", { count: expiredData.count })
      });
      
    } catch (error) {
      console.error("Error checking expired excuses:", error);
      toast({
        title: t("common.error"),
        description: t("admin.excuseCleanup.errorCheckingExpired"),
        variant: "destructive"
      });
    } finally {
      setChecking(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 w-1/3 bg-gray-200 animate-pulse rounded"></div>
        <div className="h-32 bg-gray-200 animate-pulse rounded"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            {t("admin.excuseCleanup.title")}
          </CardTitle>
          <CardDescription>
            {t("admin.excuseCleanup.description")}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Label>{t("admin.excuseCleanup.status")}:</Label>
              <Badge variant={status.isRunning ? "default" : "secondary"}>
                {status.isRunning ? (
                  <>
                    <CheckCircle className="h-3 w-3 mr-1" />
                    {t("admin.excuseCleanup.running")}
                  </>
                ) : (
                  <>
                    <XCircle className="h-3 w-3 mr-1" />
                    {t("admin.excuseCleanup.stopped")}
                  </>
                )}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Label>{t("admin.excuseCleanup.expiredExcuses")}:</Label>
              <Badge variant={expiredCount > 0 ? "destructive" : "outline"}>
                {expiredCount}
              </Badge>
            </div>

            {status.lastCleanup && (
              <div className="flex items-center gap-2">
                <Label>{t("admin.excuseCleanup.lastCleanup")}:</Label>
                <span className="text-sm text-muted-foreground">
                  {new Date(status.lastCleanup).toLocaleString()}
                </span>
              </div>
            )}
          </div>

          {/* Warning for expired excuses */}
          {expiredCount > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>{t("admin.excuseCleanup.expiredExcusesFound")}</AlertTitle>
              <AlertDescription>
                {t("admin.excuseCleanup.expiredExcusesFoundMessage", { count: expiredCount })}
              </AlertDescription>
            </Alert>
          )}

          {/* Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="cleanup-enabled">{t("admin.excuseCleanup.enableAutomaticCleanup")}</Label>
                <p className="text-sm text-muted-foreground">
                  {t("admin.excuseCleanup.enableAutomaticCleanupDescription")}
                </p>
              </div>
              <Switch
                id="cleanup-enabled"
                checked={settings.enabled}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, enabled: checked }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="check-interval">{t("admin.excuseCleanup.checkInterval")}</Label>
              <Input
                id="check-interval"
                type="number"
                min="5"
                max="1440"
                value={settings.check_interval_minutes}
                onChange={(e) =>
                  setSettings(prev => ({
                    ...prev,
                    check_interval_minutes: parseInt(e.target.value) || 60
                  }))
                }
                className="w-32"
              />
              <p className="text-sm text-muted-foreground">
                {t("admin.excuseCleanup.checkIntervalDescription")}
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="notify-students">{t("admin.excuseCleanup.notifyStudents")}</Label>
                <p className="text-sm text-muted-foreground">
                  {t("admin.excuseCleanup.notifyStudentsDescription")}
                </p>
              </div>
              <Switch
                id="notify-students"
                checked={settings.notify_students}
                onCheckedChange={(checked) =>
                  setSettings(prev => ({ ...prev, notify_students: checked }))
                }
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleSaveSettings} disabled={saving}>
              {saving ? t("common.saving") : t("admin.excuseCleanup.saveSettings")}
            </Button>

            <Button
              variant="outline"
              onClick={handleRunCleanup}
              disabled={running}
            >
              {running ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  {t("admin.excuseCleanup.running")}...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t("admin.excuseCleanup.runCleanupNow")}
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={handleCheckExpired}
              disabled={checking}
            >
              {checking ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  {t("admin.excuseCleanup.checking")}...
                </>
              ) : (
                <>
                  <Clock className="h-4 w-4 mr-2" />
                  {t("admin.excuseCleanup.checkExpired")}
                </>
              )}
            </Button>
          </div>

          {/* Information */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>{t("admin.excuseCleanup.howItWorks")}</AlertTitle>
            <AlertDescription className="space-y-2">
              <p>
                • <strong>{t("admin.excuseCleanup.automaticCleanup")}:</strong> {t("admin.excuseCleanup.automaticCleanupDescription")}
              </p>
              <p>
                • <strong>{t("admin.excuseCleanup.statusReversion")}:</strong> {t("admin.excuseCleanup.statusReversionDescription")}
              </p>
              <p>
                • <strong>{t("admin.excuseCleanup.recordCleanup")}:</strong> {t("admin.excuseCleanup.recordCleanupDescription")}
              </p>
              <p>
                • <strong>{t("admin.excuseCleanup.studentNotifications")}:</strong> {t("admin.excuseCleanup.studentNotificationsDescription")}
              </p>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}
