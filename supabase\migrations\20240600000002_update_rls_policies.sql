-- Update RLS policies to enforce school-based isolation

-- Define admin access levels:
-- 1: School admin (default) - can only access their school's data
-- 2: District admin - can access multiple schools (not implemented yet)
-- 3: System admin - can access all schools

-- Profiles table policies
DROP POLICY IF EXISTS "Users can manage their own profile" ON profiles;
DROP POLICY IF EXISTS "Teachers can view student profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can create profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update student profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can delete profiles" ON profiles;

-- Re-enable RLS on profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Users can manage their own profile
CREATE POLICY "Users can manage their own profile"
ON profiles
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Teachers can view student profiles in their school
CREATE POLICY "Teachers can view student profiles in their school"
ON profiles
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the profile being accessed belongs to a student in the same school
  role = 'student'
  AND
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can view profiles in their school
CREATE POLICY "School admins can view profiles in their school"
ON profiles
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the profile belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can create profiles in their school
CREATE POLICY "School admins can create profiles in their school"
ON profiles
FOR INSERT
TO authenticated
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the profile being created belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can update profiles in their school
CREATE POLICY "School admins can update profiles in their school"
ON profiles
FOR UPDATE
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the profile being updated belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the profile being updated belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can delete profiles in their school
CREATE POLICY "School admins can delete profiles in their school"
ON profiles
FOR DELETE
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the profile being deleted belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all profiles
CREATE POLICY "System admins can manage all profiles"
ON profiles
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);
