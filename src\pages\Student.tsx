import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import Navbar from "@/components/shared/Navbar";
import {
  Profile as StudentProfile,
  Notifications as StudentNotifications,
  Excuses as StudentExcuses,
} from "@/components/student";
import SocialMediaFeed from "@/components/student/SocialMediaFeed";
import EnhancedAttendanceHistory from "@/components/student/EnhancedAttendanceHistory";

import ProductionQRScanner from "@/components/student/ProductionQRScanner";
import EnhancedTabNavigation from "@/components/student/EnhancedTabNavigation";
import { Student as StudentType } from "@/lib/types";
import { useAuth } from "@/context/AuthContext";
import { supabase, safeRemoveChannel } from "@/lib/supabase";
import { motion, AnimatePresence } from "framer-motion";


import DashboardMessage from "@/components/shared/DashboardMessage";
import Footer from "@/components/shared/Footer";
import FeedbackForm from "@/components/shared/FeedbackForm";
import SimpleCarousel from "@/components/shared/SimpleCarousel";
import { audioNotificationService } from "@/lib/services/audio-notification-service";
import { pushNotificationService } from "@/lib/services/push-notification-service";

export default function Student() {
  const { profile, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const isProfileSetup =
    new URLSearchParams(location.search).get("setup") === "true";
  const [activeTab, setActiveTab] = useState<string>("scan");

  // Expose setActiveTab globally for direct access
  // @ts-ignore
  window.setStudentActiveTab = setActiveTab;
  const [unreadCount, setUnreadCount] = useState(0);
  const [lastProcessedNotificationId, setLastProcessedNotificationId] = useState<string | null>(null);

  // Initialize push notifications automatically on first visit
  useEffect(() => {
    const initializeNotifications = async () => {
      // Initialize push notifications service worker
      await pushNotificationService.initializeServiceWorker();

      // Automatically request push notification permission after a short delay
      const hasRequestedBefore = localStorage.getItem('pushPermissionRequested');
      if (!hasRequestedBefore) {
        // Wait 2 seconds to let the page load completely
        setTimeout(async () => {
          // Mark that we've requested permission to avoid repeated prompts
          localStorage.setItem('pushPermissionRequested', 'true');

          // Request permission automatically
          await pushNotificationService.requestNotificationPermission();
        }, 2000);
      }
    };

    // Initialize immediately on page load
    initializeNotifications();

    // Also enable audio notifications on first user interaction
    const handleFirstInteraction = () => {
      audioNotificationService.requestAudioPermission();
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('touchstart', handleFirstInteraction);
    };

    document.addEventListener('click', handleFirstInteraction);
    document.addEventListener('touchstart', handleFirstInteraction);

    return () => {
      document.removeEventListener('click', handleFirstInteraction);
      document.removeEventListener('touchstart', handleFirstInteraction);
    };
  }, []);

  // Helper function to check if student needs profile setup
  const needsProfileSetup = (studentProfile: StudentType) => {
    return (
      !studentProfile.course ||
      !studentProfile.block_id ||
      !studentProfile.room_id ||
      !studentProfile.pin ||
      !studentProfile.school_id
    );
  };

  // Fetch unread notifications count
  useEffect(() => {
    if (!profile?.id) return;

    const fetchUnreadCount = async () => {
      const { count, error } = await supabase
        .from("notifications")
        .select("*", { count: "exact", head: true })
        .eq("student_id", profile.id)
        .eq("read", false)
        .not("type", "in", "(distance_alert,attendance_alert,system_alert)");

      if (!error && count !== null) {
        setUnreadCount(count);

        // Store the viewed status in localStorage when the count is 0
        if (count === 0) {
          localStorage.setItem("notificationsViewed", "true");
        }
      }
    };

    // Check if the notifications tab is active
    if (activeTab === "notifications") {
      // If the user is viewing the notifications tab, mark as viewed
      localStorage.setItem("notificationsViewed", "true");
    }

    fetchUnreadCount();

    // Subscribe to notification changes
    const channel = supabase
      .channel("student-notification-count")
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "notifications",
          filter: `student_id=eq.${profile.id}`,
        },
        async (payload) => {
          console.log('Student notification subscription triggered:', payload);
          if (payload.eventType === "INSERT") {
            // Skip alert notifications that are meant for teachers/admins only
            const alertTypes = ["distance_alert", "attendance_alert", "system_alert"];
            if (alertTypes.includes(payload.new?.type)) {
              console.log(`Skipping ${payload.new?.type} notification for student`);
              return;
            }

            // Check for duplicate notifications using notification ID
            const notificationId = payload.new?.id;
            if (!notificationId) {
              console.warn('Notification received without ID, skipping');
              return;
            }

            // Check if we've already processed this notification
            const processedNotifications = JSON.parse(localStorage.getItem('processedNotifications') || '[]');
            if (processedNotifications.includes(notificationId)) {
              console.log('Duplicate notification detected, skipping:', notificationId);
              return;
            }

            // Add to processed notifications list (keep only last 100 to prevent memory issues)
            processedNotifications.push(notificationId);
            if (processedNotifications.length > 100) {
              processedNotifications.shift(); // Remove oldest
            }
            localStorage.setItem('processedNotifications', JSON.stringify(processedNotifications));

            // Update last processed notification ID for backward compatibility
            setLastProcessedNotificationId(notificationId);

            // New notification received, increment count and mark as unviewed
            setUnreadCount((prev) => prev + 1);
            localStorage.removeItem("notificationsViewed");

            // Handle new notification with both audio and push notification
            if (payload.new) {
              const notificationType = payload.new.type;
              const title = payload.new.title;
              const message = payload.new.message;
              let metadata = payload.new.metadata;

              // Parse metadata if it's a string
              if (typeof metadata === 'string') {
                try {
                  metadata = JSON.parse(metadata);
                } catch (e) {
                  console.warn('Failed to parse notification metadata:', e);
                  metadata = {};
                }
              }

              // Always trigger push notification (push notifications are always enabled)
              try {
                // Include notification ID in push notification for deduplication
                const enhancedMetadata = {
                  ...metadata,
                  notificationId: notificationId,
                  timestamp: payload.new.timestamp || new Date().toISOString()
                };

                await pushNotificationService.sendContextualNotification(
                  title,
                  message,
                  notificationType,
                  enhancedMetadata
                );
              } catch (error) {
                console.warn('Failed to trigger push notification:', error);

                // Fallback: Play audio directly if push notification fails
                playFallbackAudio();
              }

              async function playFallbackAudio() {
                // Fallback: Play audio directly if push notification fails or is disabled
                let soundType = 'default';
                let soundConfig = { repeat: 1, volume: 0.8 };

                // Determine if this is an attendance reminder
                const isReminder = metadata?.notification_type?.includes('reminder');

                if (notificationType === 'attendance' && isReminder) {
                  soundType = 'attendance_reminder';
                  soundConfig = { repeat: 3, volume: 0.9 };
                } else {
                  // Map other notification types to sounds
                  switch (notificationType) {
                    case 'attendance':
                      soundType = metadata?.status === 'present' ? 'marked_present' : 'success';
                      break;
                    case 'absence':
                      soundType = 'marked_absent';
                      break;
                    case 'late':
                      soundType = 'marked_late';
                      soundConfig = { repeat: 2, volume: 0.8 };
                      break;
                    case 'excused':
                      soundType = 'marked_excused';
                      break;
                    case 'system':
                      if (metadata?.excuse_status === 'approved') {
                        soundType = 'excuse_approved';
                      } else if (metadata?.excuse_status === 'rejected') {
                        soundType = 'excuse_rejected';
                      } else {
                        soundType = 'system_update';
                      }
                      break;
                  }
                }

                await audioNotificationService.playNotificationSound(soundType, soundConfig);
              }
            }
          } else if (
            payload.eventType === "UPDATE" ||
            payload.eventType === "DELETE"
          ) {
            // Notification updated or deleted, refresh count
            fetchUnreadCount();
          }
        }
      )
      .subscribe((status) => {
        if (status === 'TIMED_OUT') {
          // Set up polling fallback
          setupPollingFallback();
        } else if (status === 'CLOSED') {
          console.warn('❌ Real-time subscription closed');
          // Don't auto-reload, just log the issue
        }
      });

    // Polling fallback function
    const setupPollingFallback = () => {
      const pollInterval = setInterval(async () => {
        try {
          const { data: newNotifications, error } = await supabase
            .from('notifications')
            .select('*')
            .eq('student_id', profile.id)
            .eq('read', false)
            .not('type', 'in', '(distance_alert,attendance_alert,system_alert)')
            .order('timestamp', { ascending: false })
            .limit(1);

          if (error) {
            console.error('Polling error:', error);
            return;
          }

          if (newNotifications && newNotifications.length > 0) {
            const latestNotification = newNotifications[0];
            const lastChecked = localStorage.getItem('lastNotificationCheck');
            const notificationTime = new Date(latestNotification.timestamp).getTime();

            if (!lastChecked || notificationTime > parseInt(lastChecked)) {
              // Trigger push notification
              try {
                await pushNotificationService.sendContextualNotification(
                  latestNotification.title,
                  latestNotification.message,
                  latestNotification.type,
                  typeof latestNotification.metadata === 'string'
                    ? JSON.parse(latestNotification.metadata)
                    : latestNotification.metadata
                );

                // Update unread count
                setUnreadCount((prev) => prev + 1);
                localStorage.removeItem("notificationsViewed");
                localStorage.setItem('lastNotificationCheck', notificationTime.toString());

              } catch (error) {
                console.error('Failed to trigger push notification from polling:', error);
              }
            }
          }
        } catch (error) {
          console.error('Polling fallback error:', error);
        }
      }, 3000); // Poll every 3 seconds

      // Clean up polling when component unmounts
      return () => clearInterval(pollInterval);
    };

    return () => {
      safeRemoveChannel(channel);
    };
  }, [profile?.id, activeTab]);

  useEffect(() => {
    // If we're in setup mode or profile is incomplete, force the profile tab
    if (
      isProfileSetup ||
      (profile &&
        profile.role === "student" &&
        needsProfileSetup(profile as StudentType))
    ) {
      setActiveTab("profile");
    }
  }, [isProfileSetup, profile]);

  // Reset unread count when opening notifications tab
  useEffect(() => {
    if (activeTab === "notifications") {
      setUnreadCount(0);
    }
  }, [activeTab]);

  // If loading, show loading indicator
  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // If not a student, redirect to login
  if (!profile || profile.role !== "student") {
    navigate("/login");
    return null;
  }

  const studentProfile = profile as StudentType;

  // Check if this is a new student that needs to complete profile
  const requiresProfileSetup =
    isProfileSetup || needsProfileSetup(studentProfile);

  // Force redirect to profile setup if needed (for fresh signups/logins)
  useEffect(() => {
    if (requiresProfileSetup && !isProfileSetup) {
      navigate("/student?setup=true");
    }
  }, [requiresProfileSetup, isProfileSetup, navigate]);

  // Set active tab based on URL parameter
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const tabParam = params.get("tab");
    if (
      tabParam &&
      ["scan", "history", "excuses", "profile", "notifications"].includes(
        tabParam
      )
    ) {
      setActiveTab(tabParam);
    }
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      {requiresProfileSetup ? (
        <div className="container mx-auto py-6 px-4 flex-1">
          {/* Render the Profile component for setup - it has its own title and message */}
          <StudentProfile isSetupMode={true} />
        </div>
      ) : (
        <div className="flex-1">
          {/* Dashboard Carousel with title overlay - directly below navbar */}
          <SimpleCarousel userType="student" />

          {/* Display dashboard message below carousel */}
          <div className="container mx-auto px-4 mt-6">
            <DashboardMessage userType="student" />

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <EnhancedTabNavigation
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                unreadCount={unreadCount}
                requiresProfileSetup={requiresProfileSetup}
              />

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <TabsContent value="scan" className="mt-0">
                    <div className="space-y-6">
                      <ProductionQRScanner />
                      <SocialMediaFeed />
                    </div>
                  </TabsContent>

                  <TabsContent value="history" className="mt-0">
                    <EnhancedAttendanceHistory />
                  </TabsContent>

                  <TabsContent value="excuses" className="mt-0">
                    <StudentExcuses />
                  </TabsContent>

                  <TabsContent value="profile" className="mt-0">
                    <StudentProfile isSetupMode={requiresProfileSetup} />
                  </TabsContent>

                  <TabsContent value="notifications" className="mt-0">
                    <StudentNotifications />
                  </TabsContent>


                </motion.div>
              </AnimatePresence>
            </Tabs>
          </div>
        </div>
      )}
      {!requiresProfileSetup && <Footer />}
    </div>
  );
}
