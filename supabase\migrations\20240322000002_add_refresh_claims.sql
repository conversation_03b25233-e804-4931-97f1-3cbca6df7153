-- Create a function to refresh JWT claims for a user
CREATE OR REPLACE FUNCTION public.refresh_jwt_claims(target_user_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_role text;
BEGIN
  -- Get the user's role from profiles
  SELECT role INTO user_role
  FROM public.profiles
  WHERE user_id = target_user_id;
  
  -- Update the user's JW<PERSON> claims
  UPDATE auth.users
  SET raw_app_meta_data = jsonb_set(
    COALESCE(raw_app_meta_data, '{}'::jsonb),
    '{role}',
    to_jsonb(user_role)
  )
  WHERE id = target_user_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.refresh_jwt_claims(uuid) TO authenticated;

-- Create a function to refresh all users' JWT claims
CREATE OR REPLACE FUNCTION public.refresh_all_jwt_claims()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_record RECORD;
BEGIN
  FOR user_record IN 
    SELECT user_id, role 
    FROM public.profiles
  LOOP
    UPDATE auth.users
    SET raw_app_meta_data = jsonb_set(
      COALESCE(raw_app_meta_data, '{}'::jsonb),
      '{role}',
      to_jsonb(user_record.role)
    )
    WHERE id = user_record.user_id;
  END LOOP;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.refresh_all_jwt_claims() TO authenticated;

-- Refresh all existing users' JWT claims
SELECT public.refresh_all_jwt_claims(); 