import { toast as sonnerToast } from "sonner";
import { TFunction } from "i18next";

/**
 * Safely convert any value to a string
 * @param value The value to convert
 * @returns A string representation of the value
 */
export const safeString = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (typeof value === 'string') {
    return value;
  }
  
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value);
    } catch (e) {
      return '[Object]';
    }
  }
  
  return String(value);
};

/**
 * Safely translate a key, with fallback
 * @param t The i18next translation function
 * @param key The translation key
 * @param fallback Fallback string if translation fails
 * @returns The translated string or fallback
 */
export const safeTranslate = (
  t: TFunction,
  key: string,
  fallback: string
): string => {
  try {
    const translated = t(key);
    // If the translation returns the key itself, it means the translation is missing
    return translated === key ? fallback : translated;
  } catch (e) {
    return fallback;
  }
};

/**
 * A unified toast utility that uses Sonner toast with internationalization support
 */
export const toast = {
  /**
   * Show a success toast
   * @param title The toast title
   * @param options Toast options
   */
  success: (title: string, options?: any) => {
    return sonnerToast.success(safeString(title), {
      ...options,
      description: options?.description ? safeString(options.description) : undefined,
    });
  },

  /**
   * Show an error toast
   * @param title The toast title
   * @param options Toast options
   */
  error: (title: string, options?: any) => {
    return sonnerToast.error(safeString(title), {
      ...options,
      description: options?.description ? safeString(options.description) : undefined,
    });
  },

  /**
   * Show an info toast
   * @param title The toast title
   * @param options Toast options
   */
  info: (title: string, options?: any) => {
    return sonnerToast.info(safeString(title), {
      ...options,
      description: options?.description ? safeString(options.description) : undefined,
    });
  },

  /**
   * Show a warning toast
   * @param title The toast title
   * @param options Toast options
   */
  warning: (title: string, options?: any) => {
    return sonnerToast.warning(safeString(title), {
      ...options,
      description: options?.description ? safeString(options.description) : undefined,
    });
  },

  /**
   * Show a toast with a promise
   * @param promise The promise to track
   * @param options Toast options
   */
  promise: <T,>(promise: Promise<T>, options: any) => {
    const safeOptions = {
      ...options,
      loading: safeString(options.loading),
      success: (data: any) => safeString(typeof options.success === 'function' ? options.success(data) : options.success),
      error: (error: any) => safeString(typeof options.error === 'function' ? options.error(error) : options.error),
    };
    
    return sonnerToast.promise(promise, safeOptions);
  },

  /**
   * Show a toast with translation support
   * @param t The i18next translation function
   * @param titleKey The translation key for the title
   * @param descriptionKey The translation key for the description
   * @param options Additional toast options
   */
  translate: (
    t: TFunction,
    titleKey: string,
    descriptionKey?: string,
    options: any = {}
  ) => {
    const title = safeTranslate(t, titleKey, titleKey);
    const description = descriptionKey ? safeTranslate(t, descriptionKey, descriptionKey) : undefined;
    
    return sonnerToast({
      ...options,
      title,
      description,
    });
  },

  /**
   * Show a success toast with translation support
   * @param t The i18next translation function
   * @param titleKey The translation key for the title
   * @param descriptionKey The translation key for the description
   * @param options Additional toast options
   */
  translateSuccess: (
    t: TFunction,
    titleKey: string,
    descriptionKey?: string,
    options: any = {}
  ) => {
    const title = safeTranslate(t, titleKey, titleKey);
    const description = descriptionKey ? safeTranslate(t, descriptionKey, descriptionKey) : undefined;
    
    return sonnerToast.success(title, {
      ...options,
      description,
    });
  },

  /**
   * Show an error toast with translation support
   * @param t The i18next translation function
   * @param titleKey The translation key for the title
   * @param descriptionKey The translation key for the description
   * @param options Additional toast options
   */
  translateError: (
    t: TFunction,
    titleKey: string,
    descriptionKey?: string,
    options: any = {}
  ) => {
    const title = safeTranslate(t, titleKey, titleKey);
    const description = descriptionKey ? safeTranslate(t, descriptionKey, descriptionKey) : undefined;
    
    return sonnerToast.error(title, {
      ...options,
      description,
    });
  },
};
