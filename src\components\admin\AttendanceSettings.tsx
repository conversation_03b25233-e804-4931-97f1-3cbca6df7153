import { useState, useEffect } from "react";
import { useAttendanceSettings } from "@/hooks/useAttendanceSettings";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Clock, Save, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";

export default function AttendanceSettings() {
  const { settings, loading, updateSettings } = useAttendanceSettings();
  const { toast } = useToast();
  const { t } = useTranslation();

  const [startTime, setStartTime] = useState<string>("");
  const [endTime, setEndTime] = useState<string>("");
  const [isSaving, setIsSaving] = useState(false);

  // Initialize form when settings are loaded
  useEffect(() => {
    if (settings) {
      if (settings.recording_start_time) {
        setStartTime(settings.recording_start_time.substring(0, 5));
      }
      if (settings.recording_end_time) {
        setEndTime(settings.recording_end_time.substring(0, 5));
      }
    }
  }, [settings]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      await updateSettings({
        recording_start_time: startTime + ":00",
        recording_end_time: endTime + ":00",
      });

      toast({
        title: t("attendance.settingsSaved", "Settings Saved"),
        description: t("attendance.attendanceRecordingSettingsUpdated"),
      });
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-[250px] mb-2" />
          <Skeleton className="h-4 w-[350px]" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
        <CardFooter>
          <Skeleton className="h-10 w-[100px]" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>{t("attendance.attendanceRecordingSettings")}</CardTitle>
          <CardDescription>
            {t("attendance.controlAttendanceRecording")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                {t("attendance.recordingTimeRestrictions")}
              </AlertTitle>
              <AlertDescription>
                {t("attendance.attendanceTimeRestrictionsDescription")}
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startTime">
                  {t("attendance.recordingStartTime")}
                </Label>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-muted-foreground" />
                  <Input
                    id="startTime"
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="endTime">
                  {t("attendance.recordingEndTime")}
                </Label>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-muted-foreground" />
                  <Input
                    id="endTime"
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isSaving}>
              {isSaving ? (
                <>{t("attendance.saving")}</>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {t("attendance.saveSettings")}
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Alert className="bg-blue-50 text-blue-800 border-blue-200">
        <AlertDescription>
          {t("attendance.attendanceTimeRestrictionInfo")}
        </AlertDescription>
      </Alert>
    </div>
  );
}
