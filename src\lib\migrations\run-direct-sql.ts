import { supabase } from "@/lib/supabase";
import fs from 'fs';
import path from 'path';

/**
 * Run SQL scripts directly
 */
export const runDirectSQL = async () => {
  try {
    console.log("Running direct SQL scripts...");
    
    // Read the SQL files
    const checkTableExistsSQL = fs.readFileSync(
      path.join(process.cwd(), 'src/lib/migrations/create-check-table-exists.sql'),
      'utf8'
    );
    
    const footerSettingsSQL = fs.readFileSync(
      path.join(process.cwd(), 'src/lib/migrations/create-footer-settings.sql'),
      'utf8'
    );
    
    // Execute the SQL scripts
    const { error: checkTableExistsError } = await supabase.rpc('execute_sql', {
      sql: checkTableExistsSQL
    });
    
    if (checkTableExistsError) {
      console.error("Error creating check_table_exists function:", checkTableExistsError);
      return false;
    }
    
    const { error: footerSettingsError } = await supabase.rpc('execute_sql', {
      sql: footerSettingsSQL
    });
    
    if (footerSettingsError) {
      console.error("Error creating footer_settings table:", footerSettingsError);
      return false;
    }
    
    console.log("Direct SQL scripts executed successfully");
    return true;
  } catch (error) {
    console.error("Error running direct SQL scripts:", error);
    return false;
  }
};
