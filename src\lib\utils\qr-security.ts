import CryptoJS from "crypto-js";

// Secret key for QR code signing (in production, this should be from environment variables)
const QR_SECRET_KEY =
  process.env.NEXT_PUBLIC_QR_SECRET_KEY ||
  "default-secret-key-change-in-production";

// Configurable QR code expiry time (in seconds)
const QR_EXPIRY_SECONDS = (() => {
  const envValue = parseInt(process.env.NEXT_PUBLIC_QR_EXPIRY_SECONDS || "300");

  // Validate range: minimum 30 seconds, maximum 1800 seconds (30 minutes)
  if (envValue < 30) {
    console.warn(`QR expiry time too low (${envValue}s), using minimum 30s`);
    return 30;
  }
  if (envValue > 1800) {
    console.warn(`QR expiry time too high (${envValue}s), using maximum 1800s`);
    return 1800;
  }

  // QR Code expiry time configured
  return envValue;
})();

// Configurable rotating challenge interval (in seconds)
const CHALLENGE_ROTATION_INTERVAL = (() => {
  const envValue = parseInt(
    process.env.NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS || "30"
  );

  // Validate range: minimum 5 seconds, maximum 300 seconds (5 minutes)
  if (envValue < 5) {
    console.warn(
      `QR Challenge rotation interval too low (${envValue}s), using minimum 5s`
    );
    return 5;
  }
  if (envValue > 300) {
    console.warn(
      `QR Challenge rotation interval too high (${envValue}s), using maximum 300s`
    );
    return 300;
  }

  // QR Challenge rotation interval configured
  return envValue;
})();

// Grace period for challenge validation (allows previous challenge to be valid)
const CHALLENGE_GRACE_PERIOD_SLOTS = (() => {
  // Calculate default grace period based on QR expiry time
  const defaultSlots = Math.ceil(
    QR_EXPIRY_SECONDS / CHALLENGE_ROTATION_INTERVAL
  );

  const envValue = parseInt(
    process.env.NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS || defaultSlots.toString()
  );

  // Validate range: minimum 0 slots, maximum based on QR expiry
  const maxSlots =
    Math.ceil(QR_EXPIRY_SECONDS / CHALLENGE_ROTATION_INTERVAL) + 5;

  if (envValue < 0) {
    console.warn(
      `QR Challenge grace period too low (${envValue} slots), using minimum 0`
    );
    return 0;
  }
  if (envValue > maxSlots) {
    console.warn(
      `QR Challenge grace period too high (${envValue} slots), using maximum ${maxSlots}`
    );
    return maxSlots;
  }

  // QR Challenge grace period configured
  return envValue;
})();

// QR Security Configuration initialized

export interface QRCodePayload {
  room_id: string;
  session_id: string;
  timestamp: string;
  expires_at: string;
  school_id: string;
  block_id: string;
  nonce: string;
  challenge?: string; // Rotating challenge for screenshot prevention
}

export interface SignedQRData extends QRCodePayload {
  signature: string;
}

/**
 * Generate a cryptographically secure QR code payload
 */
export function generateSecureQRCode(
  roomId: string,
  schoolId: string,
  blockId: string,
  expirySeconds?: number
): SignedQRData {
  const now = new Date();
  const expiryTime = new Date(
    now.getTime() + (expirySeconds || QR_EXPIRY_SECONDS) * 1000
  );

  // Generate unique session ID and nonce
  const sessionId = crypto.randomUUID();
  const nonce = generateNonce();

  // Add rotating challenge to prevent screenshot attacks
  const rotatingChallenge = generateRotatingChallenge();

  const payload: QRCodePayload = {
    room_id: roomId,
    session_id: sessionId,
    timestamp: now.toISOString(),
    expires_at: expiryTime.toISOString(),
    school_id: schoolId,
    block_id: blockId,
    nonce: nonce,
    challenge: rotatingChallenge, // New field for screenshot prevention
  };

  // Create signature
  const signature = signPayload(payload);

  return {
    ...payload,
    signature,
  };
}

// SECURITY NOTE: Compact QR format functions removed
// All QR codes use full format to maintain maximum cryptographic security
// No data truncation, no UUID shortening, no signature truncation

// SECURITY: Compact format conversion functions removed
// These functions truncated critical security data and are not safe to use

// SECURITY: Compact QR verification removed - only full secure format supported

/**
 * Verify QR code signature and validity
 */
export function verifyQRCode(qrData: SignedQRData): {
  isValid: boolean;
  error?: string;
} {
  try {
    // Extract signature and payload
    const { signature, ...payload } = qrData;

    // Verify signature
    const expectedSignature = signPayload(payload);
    if (signature !== expectedSignature) {
      return {
        isValid: false,
        error: "Invalid signature - QR code may be tampered with",
      };
    }

    // Check expiry
    const expiryTime = new Date(payload.expires_at);
    if (expiryTime.getTime() <= Date.now()) {
      return {
        isValid: false,
        error: "QR code has expired",
      };
    }

    // Check timestamp is not too old (prevent replay attacks)
    const createdTime = new Date(payload.timestamp);
    const maxAge = 10 * 60 * 1000; // 10 minutes max age
    if (Date.now() - createdTime.getTime() > maxAge) {
      return {
        isValid: false,
        error: "QR code is too old",
      };
    }

    // Check timestamp is not in the future (prevent time manipulation)
    if (createdTime.getTime() > Date.now() + 60000) {
      // Allow 1 minute clock skew
      return {
        isValid: false,
        error: "QR code timestamp is in the future",
      };
    }

    // Verify rotating challenge (if present) to prevent screenshot attacks
    if (payload.challenge) {
      if (!verifyRotatingChallenge(payload.challenge)) {
        return {
          isValid: false,
          error:
            "QR code challenge verification failed - possible screenshot attack",
        };
      }
    }

    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: "Failed to verify QR code",
    };
  }
}

/**
 * Create HMAC signature for payload
 */
function signPayload(payload: QRCodePayload): string {
  // Create canonical string representation
  const canonicalString = [
    payload.room_id,
    payload.session_id,
    payload.timestamp,
    payload.expires_at,
    payload.school_id,
    payload.block_id,
    payload.nonce,
    payload.challenge || "", // Include challenge in signature
  ].join("|");

  // Create HMAC-SHA256 signature
  const signature = CryptoJS.HmacSHA256(canonicalString, QR_SECRET_KEY);
  return signature.toString(CryptoJS.enc.Hex);
}

/**
 * Generate cryptographically secure nonce
 */
function generateNonce(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
    ""
  );
}

/**
 * Generate rotating challenge based on current time
 * Changes every configurable interval to prevent screenshot attacks
 */
function generateRotatingChallenge(): string {
  // Get current time rounded to configurable intervals
  const now = Math.floor(Date.now() / 1000);
  const timeSlot = Math.floor(now / CHALLENGE_ROTATION_INTERVAL);

  // Create challenge based on time slot and secret
  const challengeData = `${timeSlot}:${QR_SECRET_KEY}`;
  return CryptoJS.SHA256(challengeData)
    .toString(CryptoJS.enc.Hex)
    .substring(0, 16);
}

/**
 * Verify rotating challenge
 */
function verifyRotatingChallenge(challenge: string): boolean {
  const now = Math.floor(Date.now() / 1000);

  // Check current and previous time slots based on configurable grace period
  for (let offset = 0; offset <= CHALLENGE_GRACE_PERIOD_SLOTS; offset++) {
    const timeSlot = Math.floor(now / CHALLENGE_ROTATION_INTERVAL) - offset;
    const expectedChallenge = CryptoJS.SHA256(`${timeSlot}:${QR_SECRET_KEY}`)
      .toString(CryptoJS.enc.Hex)
      .substring(0, 16);

    if (challenge === expectedChallenge) {
      return true;
    }
  }

  return false;
}

/**
 * Get current QR security configuration
 */
export function getQRSecurityConfig() {
  return {
    qrExpirySeconds: QR_EXPIRY_SECONDS,
    challengeRotationInterval: CHALLENGE_ROTATION_INTERVAL,
    challengeGracePeriodSlots: CHALLENGE_GRACE_PERIOD_SLOTS,
    totalGracePeriodSeconds:
      CHALLENGE_ROTATION_INTERVAL * CHALLENGE_GRACE_PERIOD_SLOTS,
    effectiveValidityWindow:
      CHALLENGE_ROTATION_INTERVAL * (CHALLENGE_GRACE_PERIOD_SLOTS + 1),
  };
}

/**
 * Get QR expiry time in seconds
 */
export function getQRExpirySeconds(): number {
  return QR_EXPIRY_SECONDS;
}

/**
 * Check if a QR code is the current valid one for a room
 * This prevents old QR codes from being used even if they haven't expired
 */
export async function isCurrentQRCode(
  roomId: string,
  qrSessionId: string,
  retryCount: number = 0
): Promise<boolean> {
  const maxRetries = 3;
  const retryDelay = 500; // 500ms delay between retries

  try {
    console.log(
      `Checking if QR session ${qrSessionId} is current for room ${roomId} (attempt ${
        retryCount + 1
      })`
    );

    // Import supabase dynamically to avoid circular dependencies
    const { supabase } = await import("@/lib/supabase");

    // Check database for current QR session
    const { data: room, error } = await supabase
      .from("rooms")
      .select("current_qr_code, qr_expiry")
      .eq("id", roomId)
      .single();

    if (error) {
      console.error("Error checking current QR code:", error);

      // Retry on database errors (network issues, etc.)
      if (retryCount < maxRetries) {
        console.log(`Retrying QR validation in ${retryDelay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
        return isCurrentQRCode(roomId, qrSessionId, retryCount + 1);
      }

      return false;
    }

    if (!room.current_qr_code || !room.qr_expiry) {
      console.log(`No current QR code found for room ${roomId}`);
      return false;
    }

    // Check if QR code has expired
    const expiryTime = new Date(room.qr_expiry);
    if (expiryTime.getTime() <= Date.now()) {
      console.log(`QR code for room ${roomId} has expired`);
      return false;
    }

    // Parse the current QR code to get its session ID
    try {
      const currentQRData = JSON.parse(room.current_qr_code);
      const isMatch = currentQRData.session_id === qrSessionId;

      console.log(`QR session comparison for room ${roomId}:`);
      console.log(`  Expected: ${qrSessionId}`);
      console.log(`  Current:  ${currentQRData.session_id}`);
      console.log(`  Match:    ${isMatch}`);

      return isMatch;
    } catch (parseError) {
      console.error("Error parsing current QR code:", parseError);
      return false;
    }
  } catch (error) {
    console.error("Error checking current QR code:", error);

    // Retry on unexpected errors
    if (retryCount < maxRetries) {
      console.log(`Retrying QR validation in ${retryDelay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, retryDelay));
      return isCurrentQRCode(roomId, qrSessionId, retryCount + 1);
    }

    return false;
  }
}

/**
 * Mark a QR session as the current one for a room
 * This invalidates any previous QR codes for the same room
 * Note: The actual database update happens in the QR generator component
 */
export function setCurrentQRSession(roomId: string, qrSessionId: string): void {
  // Keep local session storage for immediate feedback (optional)
  const currentSessionKey = `current_qr_session_${roomId}`;
  sessionStorage.setItem(currentSessionKey, qrSessionId);

  // Also store the timestamp when this session was created
  const timestampKey = `qr_session_timestamp_${roomId}`;
  sessionStorage.setItem(timestampKey, Date.now().toString());

  console.log(`Marked QR session ${qrSessionId} as current for room ${roomId}`);
}

/**
 * Enhanced QR verification that checks if it's the current valid QR
 */
export async function verifyCurrentQRCode(qrData: SignedQRData): Promise<{
  isValid: boolean;
  error?: string;
}> {
  // First, do standard verification
  const standardVerification = verifyQRCode(qrData);
  if (!standardVerification.isValid) {
    return standardVerification;
  }

  // For client-side fallback, be more lenient about "current QR" check
  // This prevents race condition issues during the transition period
  try {
    const isCurrent = await isCurrentQRCode(qrData.room_id, qrData.session_id);
    if (!isCurrent) {
      console.warn(
        "QR code may not be current, but allowing due to fallback mode"
      );
      // In fallback mode, we'll allow it if basic verification passed
      // This prevents the race condition issue
    }
  } catch (error) {
    console.warn(
      "Error checking current QR code, allowing due to fallback mode:",
      error
    );
    // If we can't check, allow it in fallback mode
  }

  return { isValid: true };
}

/**
 * Get human-readable security settings
 */
export function getQRSecurityInfo(): string {
  const config = getQRSecurityConfig();
  return `QR Challenge rotates every ${config.challengeRotationInterval}s, valid for ${config.effectiveValidityWindow}s total`;
}

/**
 * Encrypt QR code data (optional additional security layer)
 */
export function encryptQRData(data: SignedQRData): string {
  const jsonString = JSON.stringify(data);
  const encrypted = CryptoJS.AES.encrypt(jsonString, QR_SECRET_KEY);
  return encrypted.toString();
}

/**
 * Decrypt QR code data
 */
export function decryptQRData(encryptedData: string): SignedQRData | null {
  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, QR_SECRET_KEY);
    const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
    return JSON.parse(jsonString);
  } catch (error) {
    console.error("Failed to decrypt QR data:", error);
    return null;
  }
}

/**
 * Validate room assignment for student
 */
export function validateRoomAssignment(
  qrData: SignedQRData,
  studentRoomId: string | null,
  studentBlockId: string | null
): {
  isValid: boolean;
  level: "perfect" | "warning" | "error";
  message: string;
} {
  if (studentRoomId === qrData.room_id) {
    return {
      isValid: true,
      level: "perfect",
      message: "Perfect match - you are in your assigned room",
    };
  }

  if (studentBlockId === qrData.block_id) {
    return {
      isValid: true,
      level: "warning",
      message: "You are in the correct block but not your assigned room",
    };
  }

  return {
    isValid: false,
    level: "error",
    message: "You are not in your assigned block or room",
  };
}

/**
 * Check for replay attacks by tracking used session IDs
 */
export async function checkReplayAttack(
  sessionId: string,
  studentId: string
): Promise<boolean> {
  // In a real implementation, this would check a database or cache
  // to see if this session ID has already been used by this student

  // For now, we'll implement a simple in-memory cache
  // In production, use Redis or database storage

  const cacheKey = `qr_session_${sessionId}_${studentId}`;
  const cached = sessionStorage.getItem(cacheKey);

  if (cached) {
    return true; // Replay attack detected
  }

  // Mark session as used
  sessionStorage.setItem(cacheKey, Date.now().toString());

  // Clean up old entries (optional)
  cleanupOldSessions();

  return false;
}

/**
 * Clean up old session entries from cache
 */
function cleanupOldSessions(): void {
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  const now = Date.now();

  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && key.startsWith("qr_session_")) {
      const timestamp = parseInt(sessionStorage.getItem(key) || "0");
      if (now - timestamp > maxAge) {
        sessionStorage.removeItem(key);
      }
    }
  }
}

/**
 * Generate device fingerprint for additional security
 */
export function generateDeviceFingerprint(): string {
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (ctx) {
    ctx.textBaseline = "top";
    ctx.font = "14px Arial";
    ctx.fillText("Device fingerprint", 2, 2);
  }

  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + "x" + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL(),
    navigator.hardwareConcurrency || 0,
    navigator.deviceMemory || 0,
  ].join("|");

  return CryptoJS.SHA256(fingerprint).toString(CryptoJS.enc.Hex);
}

/**
 * Validate location if GPS coordinates are provided
 * Note: This function requires maxDistanceMeters to be explicitly provided
 * to ensure configured radius values are used instead of hardcoded defaults
 */
export function validateLocation(
  currentLat: number,
  currentLng: number,
  roomLat: number,
  roomLng: number,
  maxDistanceMeters: number
): {
  isValid: boolean;
  distance: number;
  message: string;
} {
  const distance = calculateDistance(currentLat, currentLng, roomLat, roomLng);

  const distanceDisplay = distance >= 1000
    ? `${(distance / 1000).toFixed(1)}km`
    : `${Math.round(distance)}m`;

  if (distance <= maxDistanceMeters) {
    return {
      isValid: true,
      distance,
      message: `Location verified (${distanceDisplay} from room)`,
    };
  }

  const excessDistance = Math.round(distance - maxDistanceMeters);
  return {
    isValid: false,
    distance,
    message: `Too far from room (${distanceDisplay} away, ${excessDistance}m outside ${maxDistanceMeters}m radius)`,
  };
}

/**
 * Calculate distance between two GPS coordinates using Haversine formula
 */
function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lng2 - lng1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}
