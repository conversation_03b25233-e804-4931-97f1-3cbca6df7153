-- Add system_admin_code to system_settings table
INSERT INTO system_settings (setting_name, setting_value)
VALUES ('system_admin_code', jsonb_build_object('code', 'INITIAL_SYSTEM_SETUP_CODE'))
ON CONFLICT (setting_name) 
DO NOTHING;

-- Create a function to update the system admin code
CREATE OR REPLACE FUNCTION update_system_admin_code(new_code TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Update the system admin code in the system_settings table
  UPDATE system_settings
  SET 
    setting_value = jsonb_build_object('code', new_code),
    updated_at = NOW()
  WHERE setting_name = 'system_admin_code';
  
  -- Return true if the update was successful
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to validate the system admin code
CREATE OR REPLACE FUNCTION validate_system_admin_code(code_to_validate TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  stored_code TEXT;
BEGIN
  -- Get the stored code from the system_settings table
  SELECT setting_value->>'code' INTO stored_code
  FROM system_settings
  WHERE setting_name = 'system_admin_code';
  
  -- Return true if the codes match
  RETURN stored_code = code_to_validate;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the functions to authenticated users
GRANT EXECUTE ON FUNCTION update_system_admin_code TO authenticated;
GRANT EXECUTE ON FUNCTION validate_system_admin_code TO authenticated;

-- Create RLS policy to allow only system admins to update the system admin code
CREATE POLICY "Only system admins can update system admin code" ON system_settings
FOR UPDATE
USING (
  setting_name = 'system_admin_code' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.user_id = auth.uid()
    AND profiles.role = 'admin'
    AND profiles.access_level = 3
  )
)
WITH CHECK (
  setting_name = 'system_admin_code' AND
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.user_id = auth.uid()
    AND profiles.role = 'admin'
    AND profiles.access_level = 3
  )
);
