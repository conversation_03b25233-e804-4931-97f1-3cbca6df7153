import { ReactNode } from "react";

interface StatsCardProps {
  title: string;
  value: number;
  total: number;
  icon: ReactNode;
  description: string;
}

export function StatsCard({ title, value, total, icon, description }: StatsCardProps) {
  const percentage = total > 0 ? Math.round((value / total) * 100) : 0;

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
      <div className="flex items-center justify-between space-x-4">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-primary/10 rounded-full">
            {icon}
          </div>
          <div>
            <p className="text-sm font-medium leading-none">{title}</p>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold">{value}</div>
          <p className="text-xs text-muted-foreground">
            {percentage}% ({value}/{total})
          </p>
        </div>
      </div>
    </div>
  );
} 