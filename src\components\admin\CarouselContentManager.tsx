import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { useTranslation } from "react-i18next";
import {
  Loader2,
  Plus,
  Trash2,
  Edit,
  Image,
  Video,
  Eye,
  EyeOff,
  ArrowUp,
  ArrowDown,
  Calendar,
} from "lucide-react";
import { format } from "date-fns";
import { tr, enUS } from "date-fns/locale";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface CarouselItem {
  id: string;
  title: string;
  description: string | null;
  media_url: string;
  media_type: "image" | "video";
  active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
  start_date: string | null;
  end_date: string | null;
  target_audience: string[];
}

export default function CarouselContentManager() {
  const { toast } = useToast();
  const { profile } = useAuth();
  const { currentSchool } = useSchool();
  const { t, i18n } = useTranslation();

  // Get the appropriate locale for date formatting
  const getDateLocale = () => {
    return i18n.language === 'tr' ? tr : enUS;
  };
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [carouselItems, setCarouselItems] = useState<CarouselItem[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingItem, setEditingItem] = useState<CarouselItem | null>(null);
  const [fileToUpload, setFileToUpload] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string>("");

  // Form state
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [mediaType, setMediaType] = useState<"image" | "video">("image");
  const [active, setActive] = useState(true);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [targetAudience, setTargetAudience] = useState<string[]>(["student", "teacher"]);

  // Load carousel items
  useEffect(() => {
    if (currentSchool?.id) {
      loadCarouselItems();
    }
  }, [currentSchool]);

  const loadCarouselItems = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("carousel_content")
        .select("*")
        .eq("school_id", currentSchool.id)
        .order("display_order", { ascending: true });

      if (error) {
        throw error;
      }

      setCarouselItems(data || []);
    } catch (error: any) {
      console.error("Error loading carousel items:", error.message);
      toast({
        title: t("common.error"),
        description: t("admin.carousel.errorLoading"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    const fileType = file.type.split("/")[0];
    if (fileType !== "image" && fileType !== "video") {
      toast({
        title: t("common.error"),
        description: t("admin.carousel.invalidFileType"),
        variant: "destructive",
      });
      return;
    }

    setFileToUpload(file);
    setMediaType(fileType as "image" | "video");

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setFilePreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Reset form
  const resetForm = () => {
    setTitle("");
    setDescription("");
    setMediaType("image");
    setActive(true);
    setStartDate(undefined);
    setEndDate(undefined);
    setTargetAudience(["student", "teacher"]);
    setFileToUpload(null);
    setFilePreview("");
    setEditingItem(null);
  };

  // Open edit dialog
  const handleEdit = (item: CarouselItem) => {
    setEditingItem(item);
    setTitle(item.title);
    setDescription(item.description || "");
    setMediaType(item.media_type);
    setActive(item.active);
    setStartDate(item.start_date ? new Date(item.start_date) : undefined);
    setEndDate(item.end_date ? new Date(item.end_date) : undefined);
    setTargetAudience(item.target_audience);
    setFilePreview(item.media_url);
    setShowAddDialog(true);
  };

  // Handle audience checkbox change
  const handleAudienceChange = (audience: string, checked: boolean) => {
    if (checked) {
      setTargetAudience([...targetAudience, audience]);
    } else {
      setTargetAudience(targetAudience.filter((a) => a !== audience));
    }
  };

  // Save carousel item
  const handleSave = async () => {
    if (!title) {
      toast({
        title: t("common.error"),
        description: t("admin.carousel.titleRequired"),
        variant: "destructive",
      });
      return;
    }

    if (!fileToUpload && !editingItem) {
      toast({
        title: t("common.error"),
        description: t("admin.carousel.mediaRequired"),
        variant: "destructive",
      });
      return;
    }

    if (targetAudience.length === 0) {
      toast({
        title: t("common.error"),
        description: t("admin.carousel.audienceRequired"),
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      let mediaUrl = editingItem?.media_url || "";

      // Upload file if there's a new one
      if (fileToUpload) {
        const fileExt = fileToUpload.name.split(".").pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `carousel/${currentSchool.id}/${fileName}`;

        const { error: uploadError, data: uploadData } = await supabase.storage
          .from("media")
          .upload(filePath, fileToUpload);

        if (uploadError) {
          throw uploadError;
        }

        // Get public URL
        const { data: urlData } = supabase.storage.from("media").getPublicUrl(filePath);
        mediaUrl = urlData.publicUrl;
      }

      const itemData = {
        school_id: currentSchool.id,
        title,
        description: description || null,
        media_url: mediaUrl,
        media_type: mediaType,
        active,
        start_date: startDate ? startDate.toISOString() : null,
        end_date: endDate ? endDate.toISOString() : null,
        target_audience: targetAudience,
        created_by: profile?.id,
        updated_at: new Date().toISOString(),
      };

      if (editingItem) {
        // Update existing item
        const { error } = await supabase
          .from("carousel_content")
          .update(itemData)
          .eq("id", editingItem.id);

        if (error) throw error;

        toast({
          title: t("common.success"),
          description: t("admin.carousel.itemUpdated"),
        });
      } else {
        // Add new item with the next display order
        const nextOrder = carouselItems.length > 0 
          ? Math.max(...carouselItems.map(item => item.display_order)) + 1 
          : 0;
        
        const { error } = await supabase
          .from("carousel_content")
          .insert({
            ...itemData,
            display_order: nextOrder,
            created_at: new Date().toISOString(),
          });

        if (error) throw error;

        toast({
          title: t("common.success"),
          description: t("admin.carousel.itemAdded"),
        });
      }

      // Reload items and reset form
      await loadCarouselItems();
      resetForm();
      setShowAddDialog(false);
    } catch (error: any) {
      console.error("Error saving carousel item:", error.message);
      toast({
        title: t("common.error"),
        description: t("admin.carousel.errorSaving"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Delete carousel item
  const handleDelete = async (id: string) => {
    if (!confirm(t("admin.carousel.confirmDelete"))) return;

    try {
      const { error } = await supabase
        .from("carousel_content")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: t("common.success"),
        description: t("admin.carousel.itemDeleted"),
      });

      await loadCarouselItems();
    } catch (error: any) {
      console.error("Error deleting carousel item:", error.message);
      toast({
        title: t("common.error"),
        description: t("admin.carousel.errorDeleting"),
        variant: "destructive",
      });
    }
  };

  // Change display order
  const handleReorder = async (id: string, direction: "up" | "down") => {
    const currentIndex = carouselItems.findIndex(item => item.id === id);
    if (currentIndex === -1) return;
    
    const newIndex = direction === "up" 
      ? Math.max(0, currentIndex - 1)
      : Math.min(carouselItems.length - 1, currentIndex + 1);
    
    if (newIndex === currentIndex) return;
    
    const updatedItems = [...carouselItems];
    const [movedItem] = updatedItems.splice(currentIndex, 1);
    updatedItems.splice(newIndex, 0, movedItem);
    
    // Update display_order for all items
    setCarouselItems(updatedItems);
    
    try {
      // Update the moved item's display order
      const { error } = await supabase
        .from("carousel_content")
        .update({ display_order: newIndex })
        .eq("id", id);
      
      if (error) throw error;
      
      // Update other affected items
      for (let i = 0; i < updatedItems.length; i++) {
        if (updatedItems[i].id !== id && updatedItems[i].display_order !== i) {
          await supabase
            .from("carousel_content")
            .update({ display_order: i })
            .eq("id", updatedItems[i].id);
        }
      }
      
      await loadCarouselItems();
    } catch (error: any) {
      console.error("Error reordering carousel items:", error.message);
      toast({
        title: t("common.error"),
        description: t("admin.carousel.errorReordering"),
        variant: "destructive",
      });
    }
  };

  // Toggle item active status
  const toggleActive = async (id: string, currentActive: boolean) => {
    try {
      const { error } = await supabase
        .from("carousel_content")
        .update({ active: !currentActive })
        .eq("id", id);

      if (error) throw error;

      await loadCarouselItems();
    } catch (error: any) {
      console.error("Error toggling carousel item status:", error.message);
      toast({
        title: t("common.error"),
        description: t("admin.carousel.errorToggling"),
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.carousel.title")}</CardTitle>
        <CardDescription>
          {t("admin.carousel.description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">{t("admin.carousel.items")}</h3>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            {t("admin.carousel.addItem")}
          </Button>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : carouselItems.length === 0 ? (
          <div className="text-center py-8 border rounded-md bg-muted/20">
            <p className="text-muted-foreground">
              {t("admin.carousel.noItems")}
            </p>
          </div>
        ) : (
          <div className="border rounded-md overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">{t("admin.carousel.order")}</TableHead>
                  <TableHead>{t("admin.carousel.title")}</TableHead>
                  <TableHead>{t("admin.carousel.type")}</TableHead>
                  <TableHead>{t("admin.carousel.audience")}</TableHead>
                  <TableHead>{t("admin.carousel.dateRange")}</TableHead>
                  <TableHead>{t("admin.carousel.status")}</TableHead>
                  <TableHead className="text-right">{t("common.actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {carouselItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="font-medium">
                      <div className="flex flex-col gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleReorder(item.id, "up")}
                          disabled={item.display_order === 0}
                        >
                          <ArrowUp className="h-4 w-4" />
                        </Button>
                        <span className="text-center">{item.display_order + 1}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleReorder(item.id, "down")}
                          disabled={item.display_order === carouselItems.length - 1}
                        >
                          <ArrowDown className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{item.title}</div>
                      {item.description && (
                        <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                          {item.description}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {item.media_type === "image" ? (
                        <Badge variant="outline" className="bg-blue-50">
                          <Image className="h-3 w-3 mr-1" />
                          {t("admin.carousel.image")}
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-red-50">
                          <Video className="h-3 w-3 mr-1" />
                          {t("admin.carousel.video")}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1 flex-wrap">
                        {item.target_audience.includes("student") && (
                          <Badge variant="secondary" className="text-xs">
                            {t("admin.carousel.students")}
                          </Badge>
                        )}
                        {item.target_audience.includes("teacher") && (
                          <Badge variant="secondary" className="text-xs">
                            {t("admin.carousel.teachers")}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {item.start_date || item.end_date ? (
                        <div className="text-xs">
                          {item.start_date && (
                            <div>
                              {t("admin.carousel.from")}: {format(new Date(item.start_date), "MMM d, yyyy")}
                            </div>
                          )}
                          {item.end_date && (
                            <div>
                              {t("admin.carousel.to")}: {format(new Date(item.end_date), "MMM d, yyyy")}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-xs text-muted-foreground">
                          {t("admin.carousel.always")}
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleActive(item.id, item.active)}
                      >
                        {item.active ? (
                          <Badge variant="outline" className="bg-green-50 border-green-200">
                            <Eye className="h-3 w-3 mr-1 text-green-600" />
                            {t("admin.carousel.active")}
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-gray-50 border-gray-200">
                            <EyeOff className="h-3 w-3 mr-1 text-gray-600" />
                            {t("admin.carousel.inactive")}
                          </Badge>
                        )}
                      </Button>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEdit(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(item.id)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Add/Edit Dialog */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>
                {editingItem
                  ? t("admin.carousel.editItem")
                  : t("admin.carousel.addItem")}
              </DialogTitle>
              <DialogDescription>
                {t("admin.carousel.itemFormDescription")}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="title">{t("admin.carousel.itemTitle")}</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder={t("admin.carousel.itemTitlePlaceholder")}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">{t("admin.carousel.itemDescription")}</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder={t("admin.carousel.itemDescriptionPlaceholder")}
                  rows={3}
                />
              </div>

              <div className="grid gap-2">
                <Label>{t("admin.carousel.mediaUpload")}</Label>
                <div className="flex items-center gap-4">
                  <Input
                    type="file"
                    accept="image/*,video/*"
                    onChange={handleFileChange}
                    className="flex-1"
                  />
                  <div className="flex items-center gap-2">
                    <Label htmlFor="media-type" className="text-sm whitespace-nowrap">
                      {t("admin.carousel.mediaType")}:
                    </Label>
                    <Select
                      value={mediaType}
                      onValueChange={(value) => setMediaType(value as "image" | "video")}
                      disabled={!!fileToUpload}
                    >
                      <SelectTrigger id="media-type" className="w-[120px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="image">{t("admin.carousel.image")}</SelectItem>
                        <SelectItem value="video">{t("admin.carousel.video")}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {filePreview && (
                  <div className="mt-2 border rounded-md p-2 bg-muted/20">
                    {mediaType === "image" ? (
                      <img
                        src={filePreview}
                        alt="Preview"
                        className="max-h-[200px] mx-auto object-contain rounded-md"
                      />
                    ) : (
                      <video
                        src={filePreview}
                        controls
                        className="max-h-[200px] w-full rounded-md"
                      />
                    )}
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label>{t("admin.carousel.startDate")}</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start text-left font-normal",
                          !startDate && "text-muted-foreground"
                        )}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        {startDate ? (
                          format(startDate, "PPP", { locale: getDateLocale() })
                        ) : (
                          <span>{t("admin.carousel.pickDate")}</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={startDate}
                        onSelect={setStartDate}
                        locale={getDateLocale()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="grid gap-2">
                  <Label>{t("admin.carousel.endDate")}</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start text-left font-normal",
                          !endDate && "text-muted-foreground"
                        )}
                      >
                        <Calendar className="mr-2 h-4 w-4" />
                        {endDate ? (
                          format(endDate, "PPP", { locale: getDateLocale() })
                        ) : (
                          <span>{t("admin.carousel.pickDate")}</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={endDate}
                        onSelect={setEndDate}
                        locale={getDateLocale()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="grid gap-2">
                <Label>{t("admin.carousel.targetAudience")}</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="students"
                      checked={targetAudience.includes("student")}
                      onCheckedChange={(checked) =>
                        handleAudienceChange("student", checked as boolean)
                      }
                    />
                    <Label htmlFor="students" className="text-sm">
                      {t("admin.carousel.students")}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="teachers"
                      checked={targetAudience.includes("teacher")}
                      onCheckedChange={(checked) =>
                        handleAudienceChange("teacher", checked as boolean)
                      }
                    />
                    <Label htmlFor="teachers" className="text-sm">
                      {t("admin.carousel.teachers")}
                    </Label>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={active}
                  onCheckedChange={setActive}
                />
                <Label htmlFor="active">{t("admin.carousel.activeItem")}</Label>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  resetForm();
                  setShowAddDialog(false);
                }}
              >
                {t("common.cancel")}
              </Button>
              <Button onClick={handleSave} disabled={saving}>
                {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {editingItem ? t("common.update") : t("common.save")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
