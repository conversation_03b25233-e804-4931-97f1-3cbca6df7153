import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "./use-toast";

export interface AttendanceSettings {
  id: string;
  recording_start_time: string;
  recording_end_time: string;
  created_at: string;
  updated_at: string;
}

export function useAttendanceSettings() {
  const [settings, setSettings] = useState<AttendanceSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { profile } = useAuth();
  const { toast } = useToast();

  const fetchSettings = async () => {
    if (!profile?.school_id) return;

    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from("attendance_settings")
        .select("*");

      // Filter by school_id for non-system admins
      if (profile.accessLevel !== 3) {
        query = query.eq("school_id", profile.school_id);
      }

      const { data, error } = await query
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== "PGRST116") {
        throw error;
      }

      if (data) {
        setSettings(data);
      }
    } catch (err: any) {
      console.error("Error fetching attendance settings:", err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<AttendanceSettings>) => {
    try {
      if (!profile || profile.role !== "admin") {
        throw new Error("Only admins can update attendance settings");
      }

      // If we have existing settings, update them
      if (settings?.id) {
        const { data, error } = await supabase
          .from("attendance_settings")
          .update({
            ...newSettings,
            updated_at: new Date().toISOString(),
          })
          .eq("id", settings.id)
          .select();

        if (error) throw error;

        setSettings(data[0]);
        toast({
          title: "Settings Updated",
          description: "Attendance recording time settings have been updated.",
        });

        return data[0];
      } else {
        // If no settings exist, create new ones with school_id
        const { data, error } = await supabase
          .from("attendance_settings")
          .insert({
            ...newSettings,
            school_id: profile.school_id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select();

        if (error) throw error;

        setSettings(data[0]);
        toast({
          title: "Settings Created",
          description: "Attendance recording time settings have been created.",
        });

        return data[0];
      }
    } catch (err: any) {
      console.error("Error updating attendance settings:", err);
      toast({
        title: "Error",
        description: `Failed to update settings: ${err.message}`,
        variant: "destructive",
      });
      throw err;
    }
  };

  // Check if current time is within allowed recording hours
  const isWithinRecordingHours = () => {
    if (
      !settings ||
      !settings.recording_start_time ||
      !settings.recording_end_time
    ) {
      return true; // If no settings or incomplete settings, allow recording
    }

    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();

    // Convert settings times to hours and minutes
    const startTimeParts = settings.recording_start_time.split(":");
    const endTimeParts = settings.recording_end_time.split(":");

    const startHours = parseInt(startTimeParts[0], 10);
    const startMinutes = parseInt(startTimeParts[1], 10);

    const endHours = parseInt(endTimeParts[0], 10);
    const endMinutes = parseInt(endTimeParts[1], 10);

    // Convert all to minutes for easier comparison
    const currentTotalMinutes = currentHours * 60 + currentMinutes;
    const startTotalMinutes = startHours * 60 + startMinutes;
    const endTotalMinutes = endHours * 60 + endMinutes;

    return (
      currentTotalMinutes >= startTotalMinutes &&
      currentTotalMinutes <= endTotalMinutes
    );
  };

  useEffect(() => {
    fetchSettings();
  }, [profile?.school_id, profile?.accessLevel]);

  return {
    settings,
    loading,
    error,
    fetchSettings,
    updateSettings,
    isWithinRecordingHours,
  };
}
