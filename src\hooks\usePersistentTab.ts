import { useState, useEffect } from 'react';

/**
 * A hook to persist tab state across page navigations
 * @param defaultTab The default tab to use if no tab is stored
 * @param storageKey The key to use for localStorage
 * @returns [activeTab, setActiveTab] - The active tab and a function to set it
 */
export function usePersistentTab(defaultTab: string, storageKey: string): [string, (tab: string) => void] {
  // Initialize state from localStorage or use default
  const [activeTab, setActiveTabState] = useState<string>(() => {
    try {
      // Try to get the tab from localStorage
      const storedTab = localStorage.getItem(storageKey);
      return storedTab || defaultTab;
    } catch (error) {
      // If localStorage is not available, use default
      console.error('Error accessing localStorage:', error);
      return defaultTab;
    }
  });

  // Update the active tab and persist to localStorage
  const setActiveTab = (tab: string) => {
    setActiveTabState(tab);
    try {
      localStorage.setItem(storageKey, tab);
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  };

  // Sync with localStorage changes from other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === storageKey && e.newValue) {
        setActiveTabState(e.newValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [storageKey]);

  return [activeTab, setActiveTab];
}
