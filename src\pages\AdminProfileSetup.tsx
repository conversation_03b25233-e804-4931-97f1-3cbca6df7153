import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import AdminProfile from "@/components/admin/AdminProfile";

export default function AdminProfileSetup() {
  const { profile, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // If not loading and either not logged in or not an admin, redirect to login
    if (!loading && (!profile || profile.role !== "admin")) {
      navigate("/login");
      return;
    }

    // If admin profile is already complete and not explicitly in setup mode, redirect to dashboard
    if (
      !loading &&
      profile &&
      profile.role === "admin" &&
      profile.position &&
      profile.school &&
      !location.search.includes("setup=true")
    ) {
      navigate("/admin");
    }
  }, [profile, loading, navigate, location]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8">
            Campus Guardian Admin Setup
          </h1>
          <AdminProfile isSetupMode={true} />
        </div>
      </div>
    </div>
  );
}
