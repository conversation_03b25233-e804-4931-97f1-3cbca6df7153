import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { User } from "@/lib/types";
import { supabase } from "@/lib/supabase";
import { useTranslation } from "react-i18next";

interface UserEditFormProps {
  user: User;
  open: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

export default function UserEditForm({ user, open, onClose, onUpdate }: UserEditFormProps) {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: user.name,
    studentId: user.studentId || "",
    course: user.course || "",
    blockName: user.blockName || "",
    roomNumber: user.roomNumber || "",
  });

  // Reset form data when user changes
  useEffect(() => {
    setFormData({
      name: user.name,
      studentId: user.studentId || "",
      course: user.course || "",
      blockName: user.blockName || "",
      roomNumber: user.roomNumber || "",
    });
  }, [user]);

  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      console.log('Starting user update with data:', formData);
      console.log('Current user:', user);

      // First update auth user if name changed
      if (formData.name !== user.name) {
        console.log('Updating auth user name...');
        const { data: authData, error: authError } = await supabase.auth.updateUser({
          data: { full_name: formData.name }
        });

        if (authError) {
          console.error('Auth update error:', authError);
          throw authError;
        }
        console.log('Auth user name updated successfully:', authData);
      }

      // Prepare profile update data
      const profileData = {
        name: formData.name.trim(),
        student_id: user.role === 'student' ? (formData.studentId || '').trim() : null,
        course: user.role === 'student' ? (formData.course || '').trim() : null,
        block_name: user.role === 'student' ? (formData.blockName || '').trim() : null,
        room_number: user.role === 'student' ? (formData.roomNumber || '').trim() : null,
        updated_at: new Date().toISOString(),
      };

      console.log('Updating profile with data:', profileData);
      console.log('Using user_id:', user.id);

      // Update profile
      const { data: updateResult, error: profileError } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('user_id', user.id);

      if (profileError) {
        console.error('Profile update error:', profileError);
        throw profileError;
      }

      console.log('Profile update response:', updateResult);

      // Verify the update by fetching the latest data
      const { data: verifyData, error: verifyError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (verifyError) {
        console.error('Error verifying update:', verifyError);
      } else {
        console.log('Verified updated profile:', verifyData);
        // Update local state if needed
        if (typeof onUpdate === 'function') {
          onUpdate();
        }
      }

      toast({
        title: t("admin.userEditForm.success"),
        description: t("admin.userEditForm.successMessage"),
      });

      onClose();
    } catch (error: any) {
      console.error('Error in update process:', error);
      toast({
        title: t("admin.userEditForm.error"),
        description: error.message || t("admin.userEditForm.errorMessage"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>{t("admin.userEditForm.title")}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t("admin.userEditForm.name")}</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>

          {user.role === "student" && (
            <>
              <div className="space-y-2">
                <Label htmlFor="studentId">{t("admin.userEditForm.studentId")}</Label>
                <Input
                  id="studentId"
                  value={formData.studentId}
                  onChange={(e) => setFormData({ ...formData, studentId: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="course">{t("admin.userEditForm.course")}</Label>
                <Input
                  id="course"
                  value={formData.course}
                  onChange={(e) => setFormData({ ...formData, course: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="blockName">{t("admin.userEditForm.blockCorridor")}</Label>
                <Input
                  id="blockName"
                  value={formData.blockName}
                  onChange={(e) => setFormData({ ...formData, blockName: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="roomNumber">{t("admin.userEditForm.roomNumber")}</Label>
                <Input
                  id="roomNumber"
                  value={formData.roomNumber}
                  onChange={(e) => setFormData({ ...formData, roomNumber: e.target.value })}
                />
              </div>
            </>
          )}

          <div className="flex justify-end gap-2">
            <Button variant="outline" type="button" onClick={onClose}>
              {t("admin.userEditForm.cancel")}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? t("admin.userEditForm.saving") : t("admin.userEditForm.saveChanges")}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
} 