-- Add block_id and room_id to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS block_id UUID REFERENCES blocks(id),
ADD COLUMN IF NOT EXISTS room_id UUID REFERENCES rooms(id);

-- Update existing student profiles to use block_id and room_id based on their block_name and room_number
UPDATE profiles p
SET 
  block_id = b.id,
  room_id = r.id
FROM 
  blocks b,
  rooms r
WHERE 
  p.role = 'student' 
  AND p.block_id IS NULL 
  AND p.room_id IS NULL
  AND b.name = COALESCE(
    NULLIF(REGEXP_REPLACE(p.block_name, '[^0-9]', '', 'g'), ''),
    '1'
  )
  AND r.block_id = b.id
  AND r.name = p.room_number; 