import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts";
import { supabase } from "@/lib/supabase";
import { Progress } from "@/components/ui/progress";

interface SystemPerformanceChartProps {
  period?: number;
  title?: string;
  description?: string;
  type?: "database" | "api" | "memory";
}

export default function SystemPerformanceChart({
  period = 30,
  title = "System Performance",
  description = "Performance metrics over time",
  type = "database",
}: SystemPerformanceChartProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [currentValue, setCurrentValue] = useState<number>(0);
  const [maxValue, setMaxValue] = useState<number>(100);

  useEffect(() => {
    fetchData();
  }, [period, type]);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Generate simulated performance data based on type
      // In a real implementation, this would fetch from a monitoring API
      const performanceData = generatePerformanceData(period, type);

      setData(performanceData);

      // Set current value to the latest data point
      if (performanceData.length > 0) {
        const latestPoint = performanceData[performanceData.length - 1];
        setCurrentValue(latestPoint.value);

        // Set max value based on type
        switch (type) {
          case "database":
            setMaxValue(1000); // 1000 MB
            break;
          case "api":
            setMaxValue(500); // 500 ms
            break;
          case "memory":
            setMaxValue(8192); // 8 GB
            break;
          default:
            setMaxValue(100);
        }
      }

      setError(null);
    } catch (err) {
      console.error(`Error fetching ${type} performance data:`, err);
      setError(`Failed to load ${type} performance data`);
      toast({
        title: "Error",
        description: `Failed to load ${type} performance data`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Generate simulated performance data
  // In a real implementation, this would be replaced with actual data from a monitoring API
  const generatePerformanceData = (days: number, type: string) => {
    const data = [];
    const now = new Date();

    for (let i = days; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);

      let value = 0;
      let trend = Math.sin(i / 5) * 10; // Create a sine wave pattern

      switch (type) {
        case "database":
          // Simulate database size growth (300-800 MB)
          value = 300 + (500 * (days - i)) / days + trend;
          break;
        case "api":
          // Simulate API response time (50-200 ms)
          value = 50 + Math.random() * 150 + trend;
          break;
        case "memory":
          // Simulate memory usage (2-6 GB)
          value = 2048 + (4096 * (days - i)) / days + trend * 100;
          break;
        default:
          value = 50 + Math.random() * 50;
      }

      data.push({
        date: date.toISOString().split("T")[0],
        value: Math.round(value),
      });
    }

    return data;
  };

  // Format the current value based on type
  const formatCurrentValue = () => {
    switch (type) {
      case "database":
        return `${currentValue.toLocaleString()} MB`;
      case "api":
        return `${currentValue.toLocaleString()} ms`;
      case "memory":
        return `${(currentValue / 1024).toFixed(1)} GB`;
      default:
        return currentValue.toLocaleString();
    }
  };

  // Format the chart label based on type
  const getChartLabel = () => {
    switch (type) {
      case "database":
        return "Database Size";
      case "api":
        return "API Response Time";
      case "memory":
        return "Memory Usage";
      default:
        return "Value";
    }
  };

  // Get chart color based on type
  const getChartColor = () => {
    switch (type) {
      case "database":
        return "#3b82f6"; // Blue
      case "api":
        return "#10b981"; // Green
      case "memory":
        return "#8b5cf6"; // Purple
      default:
        return "#f59e0b"; // Amber
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center text-muted-foreground">
                <p>{error}</p>
                <button
                  onClick={fetchData}
                  className="mt-4 text-sm text-primary hover:underline"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{getChartLabel()}</span>
                  <span className="text-sm font-medium">
                    {formatCurrentValue()}
                  </span>
                </div>
                <Progress
                  value={(currentValue / maxValue) * 100}
                  className="h-2"
                />
              </div>

              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={data}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 0,
                      bottom: 0,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) => {
                        const date = new Date(value);
                        return date.getDate().toString();
                      }}
                    />
                    <YAxis />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "#ffffff",
                        borderColor: "#e2e8f0",
                        borderRadius: "6px",
                        color: "#1e293b",
                      }}
                      formatter={(value: number) => {
                        switch (type) {
                          case "database":
                            return [
                              `${value.toLocaleString()} MB`,
                              "Database Size",
                            ];
                          case "api":
                            return [
                              `${value.toLocaleString()} ms`,
                              "Response Time",
                            ];
                          case "memory":
                            return [
                              `${(value / 1024).toFixed(1)} GB`,
                              "Memory Usage",
                            ];
                          default:
                            return [value.toLocaleString(), "Value"];
                        }
                      }}
                      labelFormatter={(label) => {
                        const date = new Date(label);
                        return date.toLocaleDateString();
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="value"
                      name={getChartLabel()}
                      stroke={getChartColor()}
                      fill={getChartColor()}
                      fillOpacity={0.2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
