-- Create enum for notification types
CREATE TYPE notification_type AS ENUM ('distance_alert', 'system_alert', 'attendance_alert');

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type notification_type NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  student_id UUID REFERENCES profiles(user_id),
  student_location POINT,  -- Store latitude and longitude
  distance_meters DOUBLE PRECISION,
  room_number TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  read_at TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}'::jsonb
);

-- <PERSON><PERSON> function to check distance and create alerts
CREATE OR REPLACE FUNCTION handle_distance_alert(
  student_id UUID,
  student_lat DOUBLE PRECISION,
  student_lng DOUBLE PRECISION,
  room_lat DOUBLE PRECISION,
  room_lng DOUBLE PRECISION,
  max_distance_meters DOUBLE PRECISION
)
RETURNS TABLE (
  is_within_radius BOOLEAN,
  distance DOUBLE PRECISION,
  alert_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  calculated_distance DOUBLE PRECISION;
  student_name TEXT;
  student_room TEXT;
  alert_id UUID;
BEGIN
  -- Calculate distance using PostGIS
  calculated_distance := ST_DistanceSphere(
    ST_MakePoint(student_lng, student_lat),
    ST_MakePoint(room_lng, room_lat)
  );

  -- Get student details
  SELECT name, room_number INTO student_name, student_room
  FROM profiles
  WHERE user_id = student_id;

  -- If outside radius, create alerts
  IF calculated_distance > max_distance_meters THEN
    -- Create notification record
    INSERT INTO notifications (
      type,
      title,
      message,
      student_id,
      student_location,
      distance_meters,
      room_number,
      metadata
    )
    VALUES (
      'distance_alert',
      'Distance Alert: Student Too Far',
      format('Student %s is attempting to verify attendance from %s meters away from room %s',
             student_name,
             ROUND(calculated_distance::numeric, 2)::text,
             student_room),
      student_id,
      ST_MakePoint(student_lng, student_lat),
      calculated_distance,
      student_room,
      jsonb_build_object(
        'student_name', student_name,
        'latitude', student_lat,
        'longitude', student_lng,
        'room_number', student_room,
        'max_allowed_distance', max_distance_meters
      )
    )
    RETURNING id INTO alert_id;

    RETURN QUERY
    SELECT 
      FALSE as is_within_radius,
      calculated_distance as distance,
      alert_id;
  ELSE
    RETURN QUERY
    SELECT 
      TRUE as is_within_radius,
      calculated_distance as distance,
      NULL::UUID as alert_id;
  END IF;
END;
$$;

-- Create policy for notifications
CREATE POLICY "Enable read access for notifications"
ON notifications FOR SELECT
TO authenticated
USING (
  -- Allow access if user is admin/teacher or if it's their own notification
  (SELECT role FROM profiles WHERE user_id = auth.uid()) IN ('admin', 'teacher')
  OR
  student_id = auth.uid()
);

-- Enable RLS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY; 