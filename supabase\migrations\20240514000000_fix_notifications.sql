-- Ensure the notification_type enum exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'notification_type') THEN
        CREATE TYPE notification_type AS ENUM (
            'distance_alert',
            'system_alert',
            'attendance_alert',
            'attendance',
            'absence',
            'late',
            'excused',
            'system'
        );
    END IF;
END$$;

-- Recreate the notifications table if it doesn't have the correct structure
DO $$
BEGIN
    -- Check if the notifications table exists with the correct structure
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'student_id'
    ) THEN
        -- Drop the table if it exists but doesn't have the correct structure
        DROP TABLE IF EXISTS notifications;
        
        -- Create the notifications table
        CREATE TABLE notifications (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
            teacher_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
            school_id UUID REFERENCES schools(id) ON DELETE CASCADE,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            type notification_type NOT NULL,
            read BOOLEAN NOT NULL DEFAULT FALSE,
            timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            metadata JSONB,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ
        );
        
        -- Create indexes
        CREATE INDEX notifications_student_id_idx ON notifications(student_id);
        CREATE INDEX notifications_teacher_id_idx ON notifications(teacher_id);
        CREATE INDEX notifications_school_id_idx ON notifications(school_id);
        CREATE INDEX notifications_read_idx ON notifications(read);
        CREATE INDEX notifications_timestamp_idx ON notifications(timestamp);
        
        -- Set up RLS policies
        ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
        
        -- Students can view their own notifications
        CREATE POLICY "Students can view their own notifications"
        ON notifications FOR SELECT
        USING (auth.uid()::text = student_id::text);
        
        -- Students can update their own notifications (to mark as read)
        CREATE POLICY "Students can update their own notifications"
        ON notifications FOR UPDATE
        USING (auth.uid()::text = student_id::text)
        WITH CHECK (auth.uid()::text = student_id::text);
        
        -- Teachers can view notifications they created
        CREATE POLICY "Teachers can view notifications they created"
        ON notifications FOR SELECT
        USING (auth.uid()::text = teacher_id::text);
        
        -- Teachers can create notifications
        CREATE POLICY "Teachers can create notifications"
        ON notifications FOR INSERT
        WITH CHECK (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role IN ('teacher', 'admin')
            )
        );
        
        -- Admins can view all notifications in their school
        CREATE POLICY "Admins can view all notifications in their school"
        ON notifications FOR SELECT
        USING (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role = 'admin' AND
                (
                    school_id IS NULL OR
                    school_id = (SELECT school_id FROM profiles WHERE id = auth.uid())
                )
            )
        );
        
        -- System admins can view all notifications
        CREATE POLICY "System admins can view all notifications"
        ON notifications FOR SELECT
        USING (
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role = 'admin' AND access_level >= 2
            )
        );
    END IF;
END$$;
