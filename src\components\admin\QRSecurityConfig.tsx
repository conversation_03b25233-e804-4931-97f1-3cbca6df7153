import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getQRSecurityConfig, getQRSecurityInfo } from "@/lib/utils/qr-security";
import { Shield, Clock, CheckCircle, AlertTriangle } from "lucide-react";
import { useTranslation } from "react-i18next";

export default function QRSecurityConfig() {
  const { t } = useTranslation();
  const config = getQRSecurityConfig();
  const securityInfo = getQRSecurityInfo();

  // Determine security level based on configuration
  const getSecurityLevel = () => {
    const { challengeRotationInterval, challengeGracePeriodSlots } = config;
    const totalWindow = challengeRotationInterval * (challengeGracePeriodSlots + 1);

    if (totalWindow <= 30) {
      return { level: "Maximum", color: "destructive", icon: Shield };
    } else if (totalWindow <= 60) {
      return { level: "High", color: "default", icon: CheckCircle };
    } else if (totalWindow <= 120) {
      return { level: "Medium", color: "secondary", icon: Clock };
    } else {
      return { level: "Low", color: "outline", icon: AlertTriangle };
    }
  };

  const securityLevel = getSecurityLevel();
  const SecurityIcon = securityLevel.icon;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          QR Security Configuration
        </CardTitle>
        <CardDescription>
          Current rotating challenge and anti-screenshot protection settings
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Security Level */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Security Level:</span>
          <Badge variant={securityLevel.color as any} className="flex items-center gap-1">
            <SecurityIcon className="h-3 w-3" />
            {securityLevel.level}
          </Badge>
        </div>

        {/* Configuration Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Challenge Rotation:</span>
              <span className="text-sm font-mono">{config.challengeRotationInterval}s</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Grace Period:</span>
              <span className="text-sm font-mono">{config.challengeGracePeriodSlots} slots</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Grace Duration:</span>
              <span className="text-sm font-mono">{config.totalGracePeriodSeconds}s</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Total Valid Window:</span>
              <span className="text-sm font-mono font-semibold">{config.effectiveValidityWindow}s</span>
            </div>
          </div>
        </div>

        {/* Security Summary */}
        <div className="p-3 bg-muted rounded-lg">
          <p className="text-sm text-muted-foreground">
            <Clock className="h-4 w-4 inline mr-1" />
            {securityInfo}
          </p>
        </div>

        {/* Security Implications */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Security Implications:</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• Screenshots remain valid for up to {config.effectiveValidityWindow} seconds</li>
            <li>• QR codes refresh every {config.challengeRotationInterval} seconds</li>
            <li>• {config.challengeGracePeriodSlots === 0 ? "No" : config.challengeGracePeriodSlots} previous challenge{config.challengeGracePeriodSlots !== 1 ? "s" : ""} accepted</li>
            <li>• Network delays up to {config.totalGracePeriodSeconds}s are tolerated</li>
          </ul>
        </div>

        {/* Configuration Source */}
        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground">
            Configuration loaded from environment variables. 
            Modify <code className="bg-muted px-1 rounded">NEXT_PUBLIC_QR_CHALLENGE_*</code> settings to adjust.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
