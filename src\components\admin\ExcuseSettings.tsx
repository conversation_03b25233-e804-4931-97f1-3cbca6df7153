import { useState, useEffect } from "react";
import { useExcuseSettings } from "@/hooks/useExcuseSettings";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Clock, Save, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";

export default function ExcuseSettings() {
  const { settings, loading, updateSettings } = useExcuseSettings();
  const { toast } = useToast();
  const { t } = useTranslation();

  const [startTime, setStartTime] = useState<string>("");
  const [endTime, setEndTime] = useState<string>("");
  const [maxDaysInAdvance, setMaxDaysInAdvance] = useState<number>(30);
  const [maxExcuseDuration, setMaxExcuseDuration] = useState<number>(14);
  const [isSaving, setIsSaving] = useState(false);

  // Initialize form when settings are loaded
  useEffect(() => {
    if (settings) {
      if (settings.submission_start_time) {
        setStartTime(settings.submission_start_time.substring(0, 5));
      }
      if (settings.submission_end_time) {
        setEndTime(settings.submission_end_time.substring(0, 5));
      }
      if (settings.max_days_in_advance) {
        setMaxDaysInAdvance(settings.max_days_in_advance);
      }
      if (settings.max_excuse_duration_days) {
        setMaxExcuseDuration(settings.max_excuse_duration_days);
      }
    }
  }, [settings]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      await updateSettings({
        submission_start_time: startTime + ":00",
        submission_end_time: endTime + ":00",
        max_days_in_advance: maxDaysInAdvance,
        max_excuse_duration_days: maxExcuseDuration,
      });

      toast({
        title: t("admin.settings.settingsSaved"),
        description: t(
          "admin.settings.excuseSubmissionSettingsUpdated",
          "Excuse submission settings have been updated successfully."
        ),
      });
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-[250px] mb-2" />
          <Skeleton className="h-4 w-[350px]" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>
        <CardFooter>
          <Skeleton className="h-10 w-[100px]" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.settings.excuseSubmissionSettings")}</CardTitle>
        <CardDescription>
          {t("admin.settings.controlExcuseSubmission")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>
              {t("admin.settings.submissionTimeRestrictions")}
            </AlertTitle>
            <AlertDescription>
              {t("admin.settings.submissionTimeRestrictionsDescription")}
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime">
                {t("admin.settings.submissionStartTime")}
              </Label>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-muted-foreground" />
                <Input
                  id="startTime"
                  type="time"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="endTime">
                {t("admin.settings.submissionEndTime")}
              </Label>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-muted-foreground" />
                <Input
                  id="endTime"
                  type="time"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                  required
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="maxDaysInAdvance">
                {t("admin.settings.maxDaysInAdvance")}
              </Label>
              <Input
                id="maxDaysInAdvance"
                type="number"
                min="1"
                max="365"
                value={maxDaysInAdvance}
                onChange={(e) => setMaxDaysInAdvance(parseInt(e.target.value))}
                required
              />
              <p className="text-sm text-muted-foreground">
                {t("admin.settings.maxDaysInAdvanceDescription")}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxExcuseDuration">
                {t("admin.settings.maxExcuseDuration")}
              </Label>
              <Input
                id="maxExcuseDuration"
                type="number"
                min="1"
                max="365"
                value={maxExcuseDuration}
                onChange={(e) => setMaxExcuseDuration(parseInt(e.target.value))}
                required
              />
              <p className="text-sm text-muted-foreground">
                {t("admin.settings.maxExcuseDurationDescription")}
              </p>
            </div>
          </div>

          <Button type="submit" className="w-full" disabled={isSaving}>
            {isSaving ? (
              <>{t("admin.settings.saving")}</>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                {t("admin.settings.saveSettings")}
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
