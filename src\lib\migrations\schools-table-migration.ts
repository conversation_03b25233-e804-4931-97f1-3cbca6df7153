import { supabase } from "@/lib/supabase";

/**
 * Ensures the schools table has all required columns
 * @returns Promise that resolves when the migration is complete
 */
export const ensureSchoolsTableColumns = async (): Promise<boolean> => {
  try {
    console.log("Checking schools table columns...");

    // Check if invitation_expiry column exists
    const { error: columnCheckError } = await supabase.rpc("execute_sql", {
      sql: `
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'schools'
        AND column_name = 'invitation_expiry';
      `,
    });

    if (columnCheckError) {
      console.error("Error checking schools table columns:", columnCheckError);
      return false;
    }

    // Add invitation_expiry column if it doesn't exist
    const { error: addColumnError } = await supabase.rpc("execute_sql", {
      sql: `
        DO $$
        BEGIN
          -- Check if invitation_expiry column exists
          IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'schools'
            AND column_name = 'invitation_expiry'
          ) THEN
            -- Add invitation_expiry column
            ALTER TABLE schools ADD COLUMN invitation_expiry TIMESTAMP WITH TIME ZONE;
            RAISE NOTICE 'Added invitation_expiry column to schools table';
          ELSE
            RAISE NOTICE 'invitation_expiry column already exists in schools table';
          END IF;

          -- Check if maintenance_mode column exists
          IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'schools'
            AND column_name = 'maintenance_mode'
          ) THEN
            -- Add maintenance_mode column
            ALTER TABLE schools ADD COLUMN maintenance_mode BOOLEAN DEFAULT FALSE;
            RAISE NOTICE 'Added maintenance_mode column to schools table';
          ELSE
            RAISE NOTICE 'maintenance_mode column already exists in schools table';
          END IF;

          -- Check if maintenance_message column exists
          IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'schools'
            AND column_name = 'maintenance_message'
          ) THEN
            -- Add maintenance_message column
            ALTER TABLE schools ADD COLUMN maintenance_message TEXT;
            RAISE NOTICE 'Added maintenance_message column to schools table';
          ELSE
            RAISE NOTICE 'maintenance_message column already exists in schools table';
          END IF;

          -- Check if maintenance_estimated_time column exists
          IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'schools'
            AND column_name = 'maintenance_estimated_time'
          ) THEN
            -- Add maintenance_estimated_time column
            ALTER TABLE schools ADD COLUMN maintenance_estimated_time TEXT;
            RAISE NOTICE 'Added maintenance_estimated_time column to schools table';
          ELSE
            RAISE NOTICE 'maintenance_estimated_time column already exists in schools table';
          END IF;
        END $$;
      `,
    });

    if (addColumnError) {
      console.error("Error adding columns to schools table:", addColumnError);
      return false;
    }

    console.log("Schools table columns updated successfully.");
    return true;
  } catch (error) {
    console.error("Error in schools table migration:", error);
    return false;
  }
};
