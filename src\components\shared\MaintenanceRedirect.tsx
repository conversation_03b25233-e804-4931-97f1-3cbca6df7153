import { useEffect, useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { supabase } from "@/lib/supabase";
import MaintenancePage from "./MaintenancePage";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useTranslation } from "react-i18next";
import { getUIText } from "@/config/branding";

/**
 * Component that checks if the current school is in maintenance mode
 * and redirects to the maintenance page if it is
 */
export default function MaintenanceRedirect({
  children,
}: {
  children: React.ReactNode;
}) {
  const { profile } = useAuth();
  const { currentSchool } = useSchool();
  const { i18n } = useTranslation();
  const [isInMaintenance, setIsInMaintenance] = useState(false);
  const [maintenanceMessage, setMaintenanceMessage] = useState("");
  const [maintenanceTime, setMaintenanceTime] = useState("a few hours");
  const [isSystemAdmin, setIsSystemAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkMaintenanceMode = async () => {
      // Always set loading to false when done
      setLoading(true);

      try {
        // Check if the user is a system admin first
        if (profile) {
          try {
            const { data: adminData, error: adminError } = await supabase
              .from("profiles")
              .select("role")
              .eq("id", profile.id)
              .single();

            if (!adminError && adminData && adminData.role === "system_admin") {
              setIsSystemAdmin(true);
              return; // System admins bypass maintenance mode
            }
          } catch (adminError) {
            console.error("Error checking admin status:", adminError);
            // Continue with maintenance check even if admin check fails
          }
        }

        // If no current school or no school ID, skip maintenance check
        if (!currentSchool || !currentSchool.id) {
          return;
        }

        // Try to get maintenance info without using single()
        const { data, error } = await supabase
          .from("schools")
          .select(
            "maintenance_mode, maintenance_message, maintenance_estimated_time"
          )
          .eq("id", currentSchool.id);

        if (error) {
          console.error("Error checking maintenance mode:", error);
          return;
        }

        // If we got results, use the first one
        if (data && data.length > 0) {
          const schoolData = data[0];

          if (schoolData.maintenance_mode) {
            setIsInMaintenance(true);
            setMaintenanceMessage(schoolData.maintenance_message || "");
            setMaintenanceTime(
              schoolData.maintenance_estimated_time || "a few hours"
            );
          }
        }
      } catch (error) {
        console.error("Error in maintenance check:", error);
      } finally {
        setLoading(false);
      }
    };

    checkMaintenanceMode();
  }, [currentSchool, profile]);

  if (loading) {
    // Get loading message based on current language
    const currentLang = i18n.language || "en";
    const uiText = getUIText(currentLang);

    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner message={uiText.LOADING} size="lg" />
      </div>
    );
  }

  if (isInMaintenance && !isSystemAdmin) {
    return (
      <MaintenancePage
        schoolName={currentSchool?.name || "your school"}
        message={maintenanceMessage}
        estimatedTime={maintenanceTime}
      />
    );
  }

  return <>{children}</>;
}
