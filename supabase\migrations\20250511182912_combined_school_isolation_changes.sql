-- COMBINED MIGRATION FOR SCHOOL ISOLATION
-- This migration adds school_id to all relevant tables and updates RLS policies
-- to ensure proper school isolation in the multi-school app

-- =============================================
-- BLOCKS TABLE
-- =============================================

-- Add school_id to blocks table
ALTER TABLE blocks ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id);

-- Update existing blocks to use the default school
DO $$
DECLARE
  default_school_id UUID;
BEGIN
  -- Get the default school ID
  SELECT id INTO default_school_id FROM public.schools LIMIT 1;

  -- Update blocks table
  UPDATE public.blocks
  SET school_id = default_school_id
  WHERE school_id IS NULL;
END $$;

-- Add RLS policies for blocks table
ALTER TABLE blocks ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Teachers can view blocks in their school" ON blocks;
DROP POLICY IF EXISTS "School admins can manage blocks in their school" ON blocks;
DROP POLICY IF EXISTS "System admins can manage all blocks" ON blocks;
DROP POLICY IF EXISTS "Students can view blocks in their school" ON blocks;

-- Teachers can view blocks in their school
CREATE POLICY "Teachers can view blocks in their school"
ON blocks
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the block belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage blocks in their school
CREATE POLICY "School admins can manage blocks in their school"
ON blocks
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the block belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the block belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all blocks
CREATE POLICY "System admins can manage all blocks"
ON blocks
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Students can view blocks in their school
CREATE POLICY "Students can view blocks in their school"
ON blocks
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
  )
  AND
  -- Check if the block belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- =============================================
-- ROOMS TABLE
-- =============================================

-- Add school_id to rooms table if it doesn't exist
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id);

-- Update existing rooms to use the school_id from their teacher's profile
UPDATE rooms r
SET school_id = (
  SELECT school_id
  FROM profiles p
  WHERE p.id = r.teacher_id
)
WHERE r.school_id IS NULL;

-- Enable RLS on rooms table
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Teachers can view rooms in their school" ON rooms;
DROP POLICY IF EXISTS "Teachers can manage their own rooms" ON rooms;
DROP POLICY IF EXISTS "School admins can manage rooms in their school" ON rooms;
DROP POLICY IF EXISTS "System admins can manage all rooms" ON rooms;
DROP POLICY IF EXISTS "Students can view rooms in their school" ON rooms;

-- Teachers can view all rooms in their school
CREATE POLICY "Teachers can view rooms in their school"
ON rooms
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Teachers can manage their own rooms
CREATE POLICY "Teachers can manage their own rooms"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
    AND teacher.id = teacher_id
  )
)
WITH CHECK (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
    AND teacher.id = teacher_id
  )
  AND
  -- Ensure school_id matches the teacher's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage all rooms in their school
CREATE POLICY "School admins can manage rooms in their school"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Ensure school_id matches the admin's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all rooms
CREATE POLICY "System admins can manage all rooms"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Students can view rooms in their school
CREATE POLICY "Students can view rooms in their school"
ON rooms
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- =============================================
-- ATTENDANCE RECORDS TABLE
-- =============================================

-- Add school_id to attendance_records table if it doesn't exist
ALTER TABLE attendance_records ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id);

-- Update existing attendance records to use the school_id from the student's profile
UPDATE attendance_records ar
SET school_id = (
  SELECT school_id
  FROM profiles p
  WHERE p.id = ar.student_id
)
WHERE ar.school_id IS NULL;

-- Enable RLS on attendance_records table
ALTER TABLE attendance_records ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Students can view their own attendance records" ON attendance_records;
DROP POLICY IF EXISTS "Students can insert their own attendance records" ON attendance_records;
DROP POLICY IF EXISTS "Teachers can view attendance records in their school" ON attendance_records;
DROP POLICY IF EXISTS "Teachers can manage attendance records in their school" ON attendance_records;
DROP POLICY IF EXISTS "School admins can manage attendance records in their school" ON attendance_records;
DROP POLICY IF EXISTS "System admins can manage all attendance records" ON attendance_records;

-- Students can view their own attendance records
CREATE POLICY "Students can view their own attendance records"
ON attendance_records
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
    AND student.id = student_id
  )
);

-- Students can insert their own attendance records
CREATE POLICY "Students can insert their own attendance records"
ON attendance_records
FOR INSERT
TO authenticated
WITH CHECK (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
    AND student.id = student_id
  )
  AND
  -- Ensure school_id matches the student's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Teachers can view attendance records in their school
CREATE POLICY "Teachers can view attendance records in their school"
ON attendance_records
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the attendance record belongs to a student in the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Teachers can manage attendance records in their school
CREATE POLICY "Teachers can manage attendance records in their school"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the attendance record belongs to a student in the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Ensure school_id matches the teacher's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage attendance records in their school
CREATE POLICY "School admins can manage attendance records in their school"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the attendance record belongs to a student in the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Ensure school_id matches the admin's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all attendance records
CREATE POLICY "System admins can manage all attendance records"
ON attendance_records
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- =============================================
-- SETTINGS TABLE
-- =============================================

-- Add school_id to settings table if it doesn't exist
ALTER TABLE settings ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id);

-- Update existing settings to use the default school
DO $$
DECLARE
  default_school_id UUID;
BEGIN
  -- Get the default school ID
  SELECT id INTO default_school_id FROM public.schools LIMIT 1;

  -- Update settings table
  UPDATE public.settings
  SET school_id = default_school_id
  WHERE school_id IS NULL;
END $$;

-- Enable RLS on settings table
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Teachers can view settings in their school" ON settings;
DROP POLICY IF EXISTS "School admins can manage settings in their school" ON settings;
DROP POLICY IF EXISTS "System admins can manage all settings" ON settings;
DROP POLICY IF EXISTS "Students can view settings in their school" ON settings;

-- Teachers can view settings in their school
CREATE POLICY "Teachers can view settings in their school"
ON settings
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the setting belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage settings in their school
CREATE POLICY "School admins can manage settings in their school"
ON settings
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the setting belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Ensure school_id matches the admin's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all settings
CREATE POLICY "System admins can manage all settings"
ON settings
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Students can view settings in their school
CREATE POLICY "Students can view settings in their school"
ON settings
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
  )
  AND
  -- Check if the setting belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Create default settings for each school if they don't exist
INSERT INTO settings (key, value, description, school_id)
SELECT
  'default_radius',
  '100',
  'Default radius for geolocation verification (in meters)',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'default_radius' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_location_verification',
  'true',
  'Enable geolocation verification for attendance',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_location_verification' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_wifi_verification',
  'false',
  'Enable WiFi verification for attendance',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_wifi_verification' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_biometric_verification',
  'false',
  'Enable biometric verification for attendance',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_biometric_verification' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_pin_verification',
  'true',
  'Enable PIN verification for attendance',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_pin_verification' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'attendance_time_start',
  '08:00',
  'Start time for attendance recording',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'attendance_time_start' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'attendance_time_end',
  '17:00',
  'End time for attendance recording',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'attendance_time_end' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'excuse_time_start',
  '08:00',
  'Start time for excuse submissions',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'excuse_time_start' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'excuse_time_end',
  '17:00',
  'End time for excuse submissions',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'excuse_time_end' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'enable_parent_notifications',
  'true',
  'Enable parent notifications for excuses',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'enable_parent_notifications' AND school_id = s.id
  );

INSERT INTO settings (key, value, description, school_id)
SELECT
  'parent_notification_method',
  'email',
  'Method for parent notifications (email or sms)',
  s.id
FROM
  schools s
WHERE
  NOT EXISTS (
    SELECT 1
    FROM settings
    WHERE key = 'parent_notification_method' AND school_id = s.id
  );