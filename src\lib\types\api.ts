/**
 * API and Service Types
 * Type definitions for API responses, requests, and service interfaces
 */

export interface APIResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
  status: number;
  timestamp?: string;
}

export interface PaginatedResponse<T = any> extends APIResponse<T[]> {
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface QueryParams {
  page?: number;
  pageSize?: number;
  sort?: string;
  order?: "asc" | "desc";
  search?: string;
  filters?: Record<string, any>;
  school_id?: string;
}

export interface DatabaseError {
  code: string;
  message: string;
  details?: string;
  hint?: string;
}

export interface SupabaseResponse<T = any> {
  data: T | null;
  error: DatabaseError | null;
  count?: number;
  status: number;
  statusText: string;
}

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
  schoolId?: string;
}

export interface NotificationPayload {
  title: string;
  message: string;
  type: "info" | "warning" | "error" | "success";
  userId?: string;
  schoolId?: string;
  metadata?: Record<string, any>;
}

export interface EmailPayload {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  from?: string;
  replyTo?: string;
  attachments?: {
    filename: string;
    content: string | Buffer;
    contentType: string;
  }[];
}

export interface SMSPayload {
  to: string;
  message: string;
  from?: string;
}

export interface PushNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  data?: Record<string, any>;
  actions?: {
    action: string;
    title: string;
    icon?: string;
  }[];
}

export interface FileUpload {
  file: File;
  path: string;
  bucket?: string;
  options?: {
    cacheControl?: string;
    contentType?: string;
    upsert?: boolean;
  };
}

export interface FileDownload {
  path: string;
  bucket?: string;
  options?: {
    download?: boolean;
    transform?: {
      width?: number;
      height?: number;
      quality?: number;
      format?: "webp" | "jpeg" | "png";
    };
  };
}

export interface AuditLog {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  school_id?: string;
  timestamp: string;
}

export interface SystemHealth {
  status: "healthy" | "degraded" | "down";
  services: {
    database: "up" | "down";
    storage: "up" | "down";
    auth: "up" | "down";
    notifications: "up" | "down";
  };
  metrics: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    activeUsers: number;
  };
  timestamp: string;
}

export interface BackupInfo {
  id: string;
  type: "full" | "incremental";
  size: number;
  status: "pending" | "running" | "completed" | "failed";
  created_at: string;
  completed_at?: string;
  error?: string;
}

export interface MigrationInfo {
  id: string;
  name: string;
  version: string;
  status: "pending" | "running" | "completed" | "failed" | "rolled_back";
  applied_at?: string;
  rolled_back_at?: string;
  error?: string;
}
