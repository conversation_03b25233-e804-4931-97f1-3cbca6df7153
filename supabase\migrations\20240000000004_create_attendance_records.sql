-- Create attendance_records table
CREATE TABLE IF NOT EXISTS public.attendance_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL,
  room_id UUID NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT now(),
  device_info TEXT,
  verification_method VARCHAR NOT NULL,
  status VARCHAR NOT NULL,
  location JSONB CHECK (
    jsonb_typeof(location) = 'object' AND
    location ? 'type' AND
    location->>'type' = 'Point' AND
    location ? 'coordinates' AND
    jsonb_typeof(location->'coordinates') = 'array' AND
    jsonb_array_length(location->'coordinates') = 2
  ),
  created_at TIMESTAMPTZ DEFAULT now(),
  FOREIGN KEY (room_id) REFERENCES rooms(id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_attendance_student_id ON public.attendance_records(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_room_id ON public.attendance_records(room_id);
CREATE INDEX IF NOT EXISTS idx_attendance_timestamp ON public.attendance_records(timestamp);

-- Enable RLS
ALTER TABLE public.attendance_records ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows all authenticated users to insert and select
CREATE POLICY "Allow authenticated users full access" ON public.attendance_records
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Grant permissions
GRANT ALL ON public.attendance_records TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated; 