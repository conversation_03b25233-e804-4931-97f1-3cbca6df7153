import { toast as sonnerToast } from "sonner";
import { safeString } from "@/lib/utils/safe-toast";

/**
 * A safe wrapper for the toast function that ensures all toast data is properly rendered
 */
const toast = (options: any) => {
  const safeOptions = {
    ...options,
    title: safeString(options.title),
    description: safeString(options.description),
  };
  
  return sonnerToast(safeOptions);
};

// Re-export all the toast methods with safe wrappers
const success = (title: any, options?: any) => {
  return sonnerToast.success(safeString(title), {
    ...options,
    description: options?.description ? safeString(options.description) : undefined,
  });
};

const error = (title: any, options?: any) => {
  return sonnerToast.error(safeString(title), {
    ...options,
    description: options?.description ? safeString(options.description) : undefined,
  });
};

const info = (title: any, options?: any) => {
  return sonnerToast.info(safeString(title), {
    ...options,
    description: options?.description ? safeString(options.description) : undefined,
  });
};

const warning = (title: any, options?: any) => {
  return sonnerToast.warning(safeString(title), {
    ...options,
    description: options?.description ? safeString(options.description) : undefined,
  });
};

const promise = <T,>(
  promise: Promise<T>,
  options: any
) => {
  const safeOptions = {
    ...options,
    loading: safeString(options.loading),
    success: (data: any) => safeString(typeof options.success === 'function' ? options.success(data) : options.success),
    error: (error: any) => safeString(typeof options.error === 'function' ? options.error(error) : options.error),
  };
  
  return sonnerToast.promise(promise, safeOptions);
};

// Add all the methods to the toast function
toast.success = success;
toast.error = error;
toast.info = info;
toast.warning = warning;
toast.promise = promise;

// Re-export the toast function
export { toast };
