import { supabase } from "@/lib/supabase";

/**
 * Migration to create the feedback_submissions table and footer_settings table
 * @returns Promise that resolves to true if migration was successful
 */
export const createFeedbackSystem = async (): Promise<boolean> => {
  try {
    console.log("Running feedback system migration...");

    // Create the feedback_submissions table
    const { error: feedbackTableError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create feedback_submissions table
        CREATE TABLE IF NOT EXISTS public.feedback_submissions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          user_role VARCHAR,
          name VARCHAR,
          email VARCHAR,
          phone VARCHAR,
          message TEXT NOT NULL,
          school_id UUID REFERENCES schools(id) ON DELETE CASCADE,
          status VARCHAR DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'archived')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Add indexes for better performance
        CREATE INDEX IF NOT EXISTS feedback_submissions_user_id_idx ON public.feedback_submissions(user_id);
        CREATE INDEX IF NOT EXISTS feedback_submissions_school_id_idx ON public.feedback_submissions(school_id);
        CREATE INDEX IF NOT EXISTS feedback_submissions_status_idx ON public.feedback_submissions(status);
      `,
    });

    if (feedbackTableError) {
      console.error(
        "Error creating feedback_submissions table:",
        feedbackTableError
      );
      return false;
    }

    // Create the footer_settings table
    const { error: footerSettingsError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create footer_settings table
        CREATE TABLE IF NOT EXISTS public.footer_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          developer_name VARCHAR DEFAULT 'Attendance Tracking System Team',
          developer_website VARCHAR DEFAULT 'https://attendancetracking.edu',
          contact_email VARCHAR DEFAULT '<EMAIL>',
          app_tagline VARCHAR DEFAULT 'Streamlining attendance management for educational institutions',
          github_url VARCHAR,
          linkedin_url VARCHAR,
          twitter_url VARCHAR,
          facebook_url VARCHAR,
          instagram_url VARCHAR,
          youtube_url VARCHAR,
          whatsapp_url VARCHAR,
          show_github BOOLEAN DEFAULT true,
          show_linkedin BOOLEAN DEFAULT true,
          show_twitter BOOLEAN DEFAULT true,
          show_facebook BOOLEAN DEFAULT true,
          show_instagram BOOLEAN DEFAULT true,
          show_youtube BOOLEAN DEFAULT true,
          show_whatsapp BOOLEAN DEFAULT true,
          show_developer_info BOOLEAN DEFAULT true,
          show_copyright BOOLEAN DEFAULT true,
          show_app_info BOOLEAN DEFAULT true,
          show_contact BOOLEAN DEFAULT true,
          show_legal BOOLEAN DEFAULT true,
          custom_copyright_text VARCHAR,
          privacy_policy_url VARCHAR,
          terms_of_service_url VARCHAR,
          cookie_policy_url VARCHAR,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Insert default footer settings
        INSERT INTO public.footer_settings (
          developer_name,
          developer_website,
          contact_email,
          app_tagline,
          show_github,
          show_linkedin,
          show_twitter,
          show_facebook,
          show_instagram,
          show_youtube,
          show_whatsapp,
          show_developer_info,
          show_copyright,
          show_app_info,
          show_contact,
          show_legal
        )
        VALUES (
          'Attendance Tracking System Team',
          'https://attendancetracking.edu',
          '<EMAIL>',
          'Streamlining attendance management for educational institutions',
          true,
          true,
          true,
          true,
          true,
          true,
          true,
          true,
          true,
          true,
          true,
          true
        )
        ON CONFLICT (id) DO NOTHING;
      `,
    });

    if (footerSettingsError) {
      console.error(
        "Error creating footer_settings table:",
        footerSettingsError
      );
      return false;
    }

    console.log("Feedback system migration completed successfully.");
    return true;
  } catch (error) {
    console.error("Error in feedback system migration:", error);
    return false;
  }
};
