import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ExternalLink,
  Instagram,
  Twitter,
  Facebook,
  Youtube,
  Globe,
  Info,
  AlertCircle,
  CheckCircle,
  Copy,
} from "lucide-react";
import { useState } from "react";
import { toast } from "@/lib/utils/toast";
import { useTranslation } from "react-i18next";

export default function SocialMediaGuide() {
  const { t } = useTranslation();
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    setCopiedCode(label);
    toast.success(
      t("admin.socialMedia.copied"),
      {
        description: t("admin.socialMedia.codeCopied", { label })
      }
    );
    setTimeout(() => setCopiedCode(null), 2000);
  };

  const platforms = [
    {
      name: "Facebook",
      icon: <Facebook className="w-5 h-5" />,
      color: "bg-blue-600",
      difficulty: t("admin.socialMedia.guide.difficulty.easy"),
      realTime: true,
      description: t("admin.socialMedia.guide.platformDescriptions.facebook"),
      steps: [
        t("admin.socialMedia.embedInstructions.facebook.step1") + " Facebook Page Plugin Generator",
        t("admin.socialMedia.embedInstructions.facebook.step2"),
        t("admin.socialMedia.embedInstructions.facebook.step3"),
        t("admin.socialMedia.embedInstructions.facebook.step4"),
        "Paste it in the Social Media Settings"
      ],
      url: "https://developers.facebook.com/docs/plugins/page-plugin",
      sampleCode: `<div class="fb-page"
  data-href="https://www.facebook.com/yourschool"
  data-tabs="timeline"
  data-width="500"
  data-height="600"
  data-small-header="false"
  data-adapt-container-width="true"
  data-hide-cover="false"
  data-show-facepile="true">
  <blockquote cite="https://www.facebook.com/yourschool"
    class="fb-xfbml-parse-ignore">
    <a href="https://www.facebook.com/yourschool">Your School</a>
  </blockquote>
</div>

<!-- Add this script once in your page -->
<script async defer crossorigin="anonymous"
  src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v18.0">
</script>`
    },
    {
      name: "Twitter",
      icon: <Twitter className="w-5 h-5" />,
      color: "bg-blue-500",
      difficulty: t("admin.socialMedia.guide.difficulty.easy"),
      realTime: true,
      description: t("admin.socialMedia.guide.platformDescriptions.twitter"),
      steps: [
        t("admin.socialMedia.embedInstructions.twitter.step1") + " tool",
        t("admin.socialMedia.embedInstructions.twitter.step2"),
        t("admin.socialMedia.embedInstructions.twitter.step3"),
        "Customize appearance options",
        t("admin.socialMedia.embedInstructions.twitter.step4")
      ],
      url: "https://publish.twitter.com/",
      sampleCode: `<a class="twitter-timeline"
  data-width="500"
  data-height="600"
  data-theme="light"
  href="https://twitter.com/yourschool?ref_src=twsrc%5Etfw">
  Tweets by yourschool
</a>
<script async src="https://platform.twitter.com/widgets.js" charset="utf-8">
</script>`
    },
    {
      name: "Instagram",
      icon: <Instagram className="w-5 h-5" />,
      color: "bg-gradient-to-r from-purple-500 to-pink-500",
      difficulty: t("admin.socialMedia.guide.difficulty.medium"),
      realTime: false,
      description: t("admin.socialMedia.guide.platformDescriptions.instagram"),
      steps: [
        t("admin.socialMedia.embedInstructions.instagram.step1"),
        t("admin.socialMedia.embedInstructions.instagram.step2"),
        t("admin.socialMedia.embedInstructions.instagram.step3"),
        t("admin.socialMedia.embedInstructions.instagram.step4"),
        t("admin.socialMedia.embedInstructions.instagram.note")
      ],
      url: "https://www.instagram.com/",
      sampleCode: `<blockquote class="instagram-media"
  data-instgrm-permalink="https://www.instagram.com/p/POST_ID/"
  data-instgrm-version="14">
  <!-- Instagram post content -->
</blockquote>
<script async src="//www.instagram.com/embed.js"></script>`
    },
    {
      name: "YouTube",
      icon: <Youtube className="w-5 h-5" />,
      color: "bg-red-500",
      difficulty: t("admin.socialMedia.guide.difficulty.superEasy"),
      realTime: true,
      description: t("admin.socialMedia.guide.platformDescriptions.youtube"),
      steps: [
        t("admin.socialMedia.embedInstructions.youtube.step1"),
        t("admin.socialMedia.embedInstructions.youtube.step2"),
        t("admin.socialMedia.embedInstructions.youtube.step3"),
        "We create 6 separate video embeds with index parameters",
        t("admin.socialMedia.embedInstructions.youtube.note")
      ],
      url: "https://www.youtube.com/",
      sampleCode: `✨ RESPONSIVE VIDEO PLAYLIST EMBEDS ✨

Enter your Channel ID in the settings:
• Find it: YouTube Studio → Settings → Channel → Advanced
• Format: UCxxxxxxxxxxxxxxxxxx (24 characters)
• Example: UClbIlkP6078-DapTSPwYy7Q

We automatically create:
- Beautiful grid layout with 6 separate video embeds
- Perfect aspect ratios for all devices (responsive design)
- Each video uses index parameter for proper display
- Students can watch each video individually
- Template-based system like email notifications!

Video 1: <iframe src="https://www.youtube.com/embed?listType=playlist&list=UU{CHANNEL_ID}&index=1">
Video 2: <iframe src="https://www.youtube.com/embed?listType=playlist&list=UU{CHANNEL_ID}&index=2">
Video 3: <iframe src="https://www.youtube.com/embed?listType=playlist&list=UU{CHANNEL_ID}&index=3">
...and so on for 6 videos total!`
    }
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
            <Info className="w-5 h-5 flex-shrink-0" />
            <span>{t("admin.socialMedia.guide.title")}</span>
          </CardTitle>
          <p className="text-xs sm:text-sm text-muted-foreground">
            {t("admin.socialMedia.guide.subtitle")}
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 sm:gap-6">
            {platforms.map((platform) => (
              <Card key={platform.name} className="border-l-4" style={{ borderLeftColor: platform.color.includes('gradient') ? '#8B5CF6' : undefined }}>
                <CardHeader className="pb-3 sm:pb-4">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      <div className={`p-2 rounded-lg text-white flex-shrink-0 ${platform.color}`}>
                        {platform.icon}
                      </div>
                      <div className="min-w-0 flex-1">
                        <h3 className="font-semibold text-sm sm:text-base">{platform.name}</h3>
                        <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">{platform.description}</p>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                      <Badge variant={platform.difficulty === t("admin.socialMedia.guide.difficulty.easy") ? "default" : "secondary"} className="text-xs">
                        {platform.difficulty}
                      </Badge>
                      {platform.realTime ? (
                        <Badge variant="default" className="bg-green-500 text-xs">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          <span className="hidden sm:inline">{t("admin.socialMedia.guide.badges.realTime")}</span>
                          <span className="sm:hidden">Real-time</span>
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          <span className="hidden sm:inline">{t("admin.socialMedia.guide.badges.individualPosts")}</span>
                          <span className="sm:hidden">Individual</span>
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4 pt-0">
                  <div>
                    <h4 className="font-medium mb-2 text-sm sm:text-base">{t("admin.socialMedia.guide.steps")}</h4>
                    <ol className="list-decimal list-inside space-y-1 text-xs sm:text-sm text-muted-foreground">
                      {platform.steps.map((step, index) => (
                        <li key={index} className="leading-relaxed">{step}</li>
                      ))}
                    </ol>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
                    <Button variant="outline" size="sm" asChild className="w-full sm:w-auto text-xs sm:text-sm">
                      <a href={platform.url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-4 h-4 mr-2 flex-shrink-0" />
                        <span className="truncate">
                          {t("admin.socialMedia.guide.buttons.openTool", { platform: platform.name })}
                        </span>
                      </a>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(platform.sampleCode, platform.name)}
                      className="w-full sm:w-auto text-xs sm:text-sm"
                    >
                      <Copy className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="truncate">
                        {copiedCode === platform.name ? t("admin.socialMedia.guide.buttons.copied") : t("admin.socialMedia.guide.buttons.copySampleCode")}
                      </span>
                    </Button>
                  </div>

                  <details className="text-xs sm:text-sm">
                    <summary className="cursor-pointer font-medium text-muted-foreground hover:text-foreground py-2">
                      {t("admin.socialMedia.guide.buttons.viewSampleCode")}
                    </summary>
                    <pre className="mt-2 p-2 sm:p-3 bg-muted rounded-md overflow-x-auto text-xs max-h-48 overflow-y-auto">
                      <code className="whitespace-pre-wrap break-all sm:break-normal">{platform.sampleCode}</code>
                    </pre>
                  </details>
                </CardContent>
              </Card>
            ))}
          </div>

          <Separator className="my-6" />

          <div className="space-y-4">
            <h3 className="font-semibold flex items-center space-x-2 text-sm sm:text-base">
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span>{t("admin.socialMedia.guide.importantNotes")}</span>
            </h3>
            <div className="grid gap-3 text-xs sm:text-sm text-muted-foreground">
              <div className="flex items-start space-x-2 p-2 rounded-md bg-green-50 dark:bg-green-950/20">
                <CheckCircle className="w-4 h-4 mt-0.5 text-green-500 flex-shrink-0" />
                <p className="leading-relaxed">{t("admin.socialMedia.guide.notes.facebookTwitter")}</p>
              </div>
              <div className="flex items-start space-x-2 p-2 rounded-md bg-yellow-50 dark:bg-yellow-950/20">
                <AlertCircle className="w-4 h-4 mt-0.5 text-yellow-500 flex-shrink-0" />
                <p className="leading-relaxed">{t("admin.socialMedia.guide.notes.instagram")}</p>
              </div>
              <div className="flex items-start space-x-2 p-2 rounded-md bg-green-50 dark:bg-green-950/20">
                <CheckCircle className="w-4 h-4 mt-0.5 text-green-500 flex-shrink-0" />
                <p className="leading-relaxed">{t("admin.socialMedia.guide.notes.youtube")}</p>
              </div>
              <div className="flex items-start space-x-2 p-2 rounded-md bg-blue-50 dark:bg-blue-950/20">
                <Info className="w-4 h-4 mt-0.5 text-blue-500 flex-shrink-0" />
                <p className="leading-relaxed">{t("admin.socialMedia.guide.notes.demoMode")}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
