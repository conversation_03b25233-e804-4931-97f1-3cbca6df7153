import React, { createContext, useContext, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { useAuth } from "./AuthContext";
import { toast } from "@/lib/utils/toast";

type LanguageCode = "en" | "tr";

interface LanguageContextType {
  currentLanguage: LanguageCode;
  changeLanguage: (language: LanguageCode) => Promise<void>;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { i18n } = useTranslation();
  const { profile } = useAuth();
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>("en");
  const [isLoading, setIsLoading] = useState(false);

  // Initialize language from profile or localStorage
  useEffect(() => {
    const initializeLanguage = async () => {
      // Get the language from profile, localStorage, or default to English
      let targetLanguage: LanguageCode = "en";

      // If user is logged in, use their preferred language from profile
      if (profile?.preferred_language) {
        targetLanguage = profile.preferred_language as LanguageCode;
      } else {
        // Otherwise use the language from localStorage or browser
        const detectedLang =
          localStorage.getItem("i18nextLng") || i18n.language;
        targetLanguage = detectedLang === "tr" ? "tr" : "en";
      }

      // Update state
      setCurrentLanguage(targetLanguage);

      // Store in localStorage
      localStorage.setItem("i18nextLng", targetLanguage);

      // Change language in i18next
      await i18n.changeLanguage(targetLanguage);

      // Force a re-render of all components
      document.documentElement.lang = targetLanguage;
    };

    initializeLanguage();
  }, [profile, i18n]);

  // Change language function
  const changeLanguage = async (language: LanguageCode) => {
    setIsLoading(true);
    try {
      // Update state
      setCurrentLanguage(language);

      // Store in localStorage
      localStorage.setItem("i18nextLng", language);

      // Force reload all translations
      await i18n.changeLanguage(language);

      // Force a re-render of all components
      document.documentElement.lang = language;

      // Show success notification for language change
      const languageNames = {
        en: { en: "English", tr: "İngilizce" },
        tr: { en: "Turkish", tr: "Türkçe" }
      };

      const currentLangName = languageNames[language as keyof typeof languageNames]?.[language as keyof typeof languageNames[typeof language]] || language;

      if (language === 'tr') {
        toast.success("Başarılı", {
          description: `Dil tercihiniz ${currentLangName} olarak güncellendi`,
          duration: 3000,
        });
      } else {
        toast.success("Success", {
          description: `Language preference updated to ${currentLangName}`,
          duration: 3000,
        });
      }

      // If user is logged in, update their profile silently
      if (profile?.id) {
        const { error } = await supabase
          .from("profiles")
          .update({ preferred_language: language })
          .eq("id", profile.id);

        if (error) {
          console.error("Error updating language preference:", error);
          // Show error notification
          if (language === 'tr') {
            toast.error("Hata", {
              description: "Dil tercihi kaydedilirken bir hata oluştu",
              duration: 4000,
            });
          } else {
            toast.error("Error", {
              description: "Failed to save language preference",
              duration: 4000,
            });
          }
        }
      }
    } catch (error) {
      console.error("Error changing language:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LanguageContext.Provider
      value={{ currentLanguage, changeLanguage, isLoading }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};
