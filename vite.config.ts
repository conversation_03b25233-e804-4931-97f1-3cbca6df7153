import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import mkcert from "vite-plugin-mkcert";
import fs from "fs";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), "");

  // Check if custom certificates exist
  const certPath = path.resolve(__dirname, "certs/localhost.pem");
  const keyPath = path.resolve(__dirname, "certs/localhost-key.pem");
  const hasCustomCerts = fs.existsSync(certPath) && fs.existsSync(keyPath);

  return {
    server: {
      host: "0.0.0.0", // Listen on all network interfaces
      port: 5173, // Use standard Vite port
      strictPort: false, // Allow fallback ports
      https: hasCustomCerts
        ? {
            cert: fs.readFileSync(certPath),
            key: fs.readFileSync(keyPath),
          }
        : true, // Use mkcert plugin if no custom certs
      proxy: {
        // Add proxy configuration if needed
      },
    },
    preview: {
      host: "0.0.0.0",
      port: 8081,
      strictPort: true,
    },
    plugins: [
      react(),
      mkcert(), // Add local HTTPS certificates
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    // Expose environment variables to your app
    define: {
      "process.env": env,
    },
  };
});
