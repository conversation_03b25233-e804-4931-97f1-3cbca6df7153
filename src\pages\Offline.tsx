import { useState, useEffect } from "react";
import { WifiOff, <PERSON>fresh<PERSON><PERSON>, ArrowLeft, AlertCircle, Wifi } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { getBranding } from "@/config/branding";
import { motion } from "framer-motion";

export default function Offline() {
  const { t, i18n } = useTranslation();
  const branding = getBranding(i18n.language);
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    // Set document title
    document.title = `${t("offline.title")} | ${branding.APP_NAME}`;
  }, [t, branding.APP_NAME]);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      // Redirect to home page when back online
      window.location.href = "/";
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  const handleRetry = () => {
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    // Simulate checking connection
    setTimeout(() => {
      if (navigator.onLine) {
        window.location.reload();
      } else {
        setIsRetrying(false);
      }
    }, 2000);
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 flex items-center justify-center p-4 sm:p-6 lg:p-8">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="w-full max-w-lg"
      >
        <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-md overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-amber-600 to-orange-600 text-white p-6 sm:p-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
              className="flex flex-col items-center space-y-4"
            >
              <div className="relative">
                <div className="h-16 w-16 sm:h-20 sm:w-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <WifiOff className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
                </div>
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                  className="absolute -top-1 -right-1 h-5 w-5 sm:h-6 sm:w-6 bg-red-400 rounded-full flex items-center justify-center shadow-lg"
                >
                  <AlertCircle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-red-800" />
                </motion.div>
              </div>

              <div className="text-center space-y-2">
                <CardTitle className="text-xl sm:text-2xl font-bold text-white">
                  {t("offline.title")}
                </CardTitle>
                <CardDescription className="text-amber-100 text-sm sm:text-base opacity-90">
                  {t("offline.description")}
                </CardDescription>
              </div>
            </motion.div>
          </CardHeader>
          <CardContent className="p-6 sm:p-8 space-y-6">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="space-y-4"
            >
              <div className="bg-gradient-to-r from-amber-50 to-orange-50 p-4 rounded-xl border border-amber-200">
                <p className="font-semibold text-amber-800 text-sm sm:text-base mb-3">
                  {t("offline.troubleshootingTips")}
                </p>
                <ul className="list-disc pl-5 space-y-2 text-amber-700 text-xs sm:text-sm">
                  <li>{t("offline.checkConnection")}</li>
                  <li>{t("offline.tryDifferentNetwork")}</li>
                  <li>{t("offline.restartRouter")}</li>
                  <li>{t("offline.checkOtherApps")}</li>
                </ul>
              </div>

              {retryCount > 2 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                  className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200"
                >
                  <div className="flex items-start gap-3">
                    <Wifi className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <p className="text-blue-800 text-xs sm:text-sm">
                      {t("offline.persistentIssues")}
                    </p>
                  </div>
                </motion.div>
              )}
            </motion.div>
          </CardContent>
          <CardFooter className="p-6 sm:p-8 pt-0 space-y-4">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="w-full space-y-3"
            >
              <div className="flex items-center justify-center gap-2 mb-4">
                <motion.div
                  animate={{ opacity: [0.3, 1, 0.3] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="h-2 w-2 bg-amber-500 rounded-full"
                />
                <motion.div
                  animate={{ opacity: [0.3, 1, 0.3] }}
                  transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
                  className="h-2 w-2 bg-orange-500 rounded-full"
                />
                <motion.div
                  animate={{ opacity: [0.3, 1, 0.3] }}
                  transition={{ duration: 2, repeat: Infinity, delay: 0.6 }}
                  className="h-2 w-2 bg-red-500 rounded-full"
                />
              </div>

              <Button
                className="w-full bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg transition-all duration-200 transform hover:scale-105"
                onClick={handleRetry}
                disabled={isRetrying}
                size="lg"
              >
                {isRetrying ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    {t("offline.checkingConnection")}
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    {t("offline.tryAgain")}
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 transition-all duration-200"
                onClick={handleGoBack}
                size="lg"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("offline.goBack")}
              </Button>
            </motion.div>
          </CardFooter>
        </Card>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.9 }}
          className="text-center mt-6"
        >
          <p className="text-gray-500 text-xs sm:text-sm bg-white/60 backdrop-blur-sm rounded-lg px-4 py-2 inline-block">
            {t("offline.retryCount")}: {retryCount} • {t("offline.connectionStatus")}: {isOnline ? t("offline.online") : t("offline.offline")}
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
}
