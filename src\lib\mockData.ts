import { AttendanceRecord, Course, FraudCase, Room, Student, Teacher, Admin, User } from "./types";

export const mockStudents: Student[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'student',
    email: '<EMAIL>',
    studentId: 'S10001',
    course: 'Computer Science',
    biometricRegistered: true,
    photoUrl: 'https://i.pravatar.cc/150?img=1'
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'student',
    email: '<EMAIL>',
    studentId: 'S10002',
    course: 'Biology',
    biometricRegistered: false,
    photoUrl: 'https://i.pravatar.cc/150?img=5'
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'student',
    email: '<EMAIL>',
    studentId: 'S10003',
    course: 'Physics',
    biometricRegistered: true,
    photoUrl: 'https://i.pravatar.cc/150?img=3'
  },
  {
    id: '4',
    name: '<PERSON>',
    role: 'student',
    email: '<EMAIL>',
    studentId: 'S10004',
    course: 'Mathematics',
    biometricRegistered: true,
    photoUrl: 'https://i.pravatar.cc/150?img=8'
  },
  {
    id: '5',
    name: 'Omar Patel',
    role: 'student',
    email: '<EMAIL>',
    studentId: 'S10005',
    course: 'Computer Science',
    biometricRegistered: false,
    photoUrl: 'https://i.pravatar.cc/150?img=9'
  }
];

export const mockTeachers: Teacher[] = [
  {
    id: '101',
    name: 'Dr. Sarah Matthews',
    role: 'teacher',
    email: '<EMAIL>',
    teacherId: 'T2001',
    department: 'Computer Science',
    photoUrl: 'https://i.pravatar.cc/150?img=20'
  },
  {
    id: '102',
    name: 'Prof. Michael Brown',
    role: 'teacher',
    email: '<EMAIL>',
    teacherId: 'T2002',
    department: 'Biology',
    photoUrl: 'https://i.pravatar.cc/150?img=11'
  }
];

export const mockAdmins: Admin[] = [
  {
    id: '201',
    name: 'Admin Jane Doe',
    role: 'admin',
    email: '<EMAIL>',
    adminId: 'A3001',
    accessLevel: 1,
    photoUrl: 'https://i.pravatar.cc/150?img=13'
  }
];

export const mockRooms: Room[] = [
  {
    id: '301',
    name: 'CS Lab 101',
    building: 'Technology Building',
    floor: 1,
    capacity: 30,
    currentQrCode: 'mock-qr-code-data-1',
    qrExpiry: new Date(Date.now() + 5 * 60000).toISOString() // 5 minutes from now
  },
  {
    id: '302',
    name: 'Biology Lab 201',
    building: 'Science Building',
    floor: 2,
    capacity: 25,
    currentQrCode: 'mock-qr-code-data-2',
    qrExpiry: new Date(Date.now() + 5 * 60000).toISOString()
  },
  {
    id: '303',
    name: 'Lecture Hall 301',
    building: 'Main Building',
    floor: 3,
    capacity: 100,
    currentQrCode: 'mock-qr-code-data-3',
    qrExpiry: new Date(Date.now() + 5 * 60000).toISOString()
  }
];

export const mockAttendanceRecords: AttendanceRecord[] = [
  {
    id: '1001',
    studentId: '1',
    roomId: '301',
    timestamp: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    deviceInfo: 'iPhone 13, iOS 15.4',
    location: {
      latitude: 37.7749,
      longitude: -122.4194
    },
    verificationMethod: 'biometric',
    status: 'present'
  },
  {
    id: '1002',
    studentId: '2',
    roomId: '302',
    timestamp: new Date(Date.now() - 86400000).toISOString(),
    deviceInfo: 'Samsung Galaxy S21, Android 12',
    location: {
      latitude: 37.7748,
      longitude: -122.4193
    },
    verificationMethod: 'pin',
    status: 'present'
  },
  {
    id: '1003',
    studentId: '3',
    roomId: '303',
    timestamp: new Date(Date.now() - 86400000).toISOString(),
    deviceInfo: 'Google Pixel 6, Android 12',
    location: {
      latitude: 37.7747,
      longitude: -122.4192
    },
    verificationMethod: 'biometric',
    status: 'late'
  },
  {
    id: '1004',
    studentId: '1',
    roomId: '301',
    timestamp: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    deviceInfo: 'iPhone 13, iOS 15.4',
    location: {
      latitude: 37.7749,
      longitude: -122.4194
    },
    verificationMethod: 'biometric',
    status: 'present'
  },
  {
    id: '1005',
    studentId: '4',
    roomId: '302',
    timestamp: new Date(Date.now() - 172800000).toISOString(),
    deviceInfo: 'iPad Pro, iOS 15.3',
    location: {
      latitude: 37.7746,
      longitude: -122.4191
    },
    verificationMethod: 'manual',
    status: 'excused'
  }
];

export const mockCourses: Course[] = [
  {
    id: '401',
    name: 'Advanced Programming',
    teacherId: '101',
    roomId: '301',
    schedule: [
      {
        day: 'monday',
        startTime: '09:00',
        endTime: '10:30'
      },
      {
        day: 'wednesday',
        startTime: '09:00',
        endTime: '10:30'
      }
    ]
  },
  {
    id: '402',
    name: 'Cell Biology',
    teacherId: '102',
    roomId: '302',
    schedule: [
      {
        day: 'tuesday',
        startTime: '11:00',
        endTime: '12:30'
      },
      {
        day: 'thursday',
        startTime: '11:00',
        endTime: '12:30'
      }
    ]
  }
];

export const mockFraudCases: FraudCase[] = [
  {
    id: '501',
    studentId: '5',
    attendanceId: '1006',
    timestamp: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
    evidenceType: 'location_mismatch',
    status: 'pending',
    notes: 'Location reported was 2km away from classroom'
  },
  {
    id: '502',
    studentId: '2',
    attendanceId: '1007',
    timestamp: new Date(Date.now() - 129600000).toISOString(), // 36 hours ago
    evidenceType: 'multiple_scans',
    status: 'reviewing',
    notes: '3 scan attempts within 2 minutes from different device IDs'
  }
];

// Mock login function
export const mockLogin = (email: string, password: string): User | null => {
  // This is just a mock, in a real app this would verify against a database
  const allUsers: User[] = [
    ...mockStudents,
    ...mockTeachers,
    ...mockAdmins,
  ];
  
  const user = allUsers.find(user => user.email === email);
  
  // In a real app, you would hash and compare passwords
  // For this mock, we just check if the password is "password123"
  if (user && password === "password123") {
    return user;
  }
  
  return null;
};
