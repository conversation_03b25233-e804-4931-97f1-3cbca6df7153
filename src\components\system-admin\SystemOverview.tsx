import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { motion } from "framer-motion";
import {
  Building2,
  Users,
  UserCheck,
  AlertTriangle,
  RefreshCw,
  Shield,
  Server,
  Activity,
  Clock,
  BarChart4,
  TrendingUp,
} from "lucide-react";
import { format } from "date-fns";
import { fetchRecentAuditLogs } from "@/lib/api/system-admin";

interface SystemStats {
  schoolCount: number;
  userCount: number;
  activeUserCount: number;
  alertCount: number;
  systemHealth: "healthy" | "warning" | "critical";
  lastUpdated: Date;
}

interface SystemOverviewProps {
  stats?: SystemStats | null;
}

export default function SystemOverview({
  stats: initialStats,
}: SystemOverviewProps) {
  const { toast } = useToast();
  const { profile } = useAuth();
  const [stats, setStats] = useState<SystemStats | null>(initialStats || null);
  const [loading, setLoading] = useState(!initialStats);
  const [recentLogs, setRecentLogs] = useState<any[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (!initialStats) {
      fetchSystemStats();
    }
    fetchRecentLogs();
  }, [initialStats]);

  const fetchSystemStats = async () => {
    setLoading(true);
    try {
      // Fetch school count
      const { data: schoolData, error: schoolError } = await supabase
        .from("schools")
        .select("id", { count: "exact" });

      if (schoolError) throw schoolError;

      // Fetch user count
      const { data: userData, error: userError } = await supabase
        .from("profiles")
        .select("id", { count: "exact" });

      if (userError) throw userError;

      // Fetch active user count (users who logged in within the last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: activeUserData, error: activeUserError } = await supabase
        .from("profiles")
        .select("id", { count: "exact" })
        .gt("last_login", thirtyDaysAgo.toISOString());

      if (activeUserError) throw activeUserError;

      // Fetch alert count
      const { data: alertData, error: alertError } = await supabase
        .from("notifications")
        .select("id", { count: "exact" })
        .eq("type", "system")
        .eq("read", false);

      if (alertError) throw alertError;

      // Set system stats
      setStats({
        schoolCount: schoolData.length,
        userCount: userData.length,
        activeUserCount: activeUserData.length,
        alertCount: alertData.length,
        systemHealth: "healthy", // Default to healthy
        lastUpdated: new Date(),
      });
    } catch (error) {
      console.error("Error fetching system stats:", error);
      toast({
        title: "Error",
        description: "Failed to fetch system statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchRecentLogs = async () => {
    try {
      const logs = await fetchRecentAuditLogs(5);
      setRecentLogs(logs);
    } catch (error) {
      console.error("Error fetching recent logs:", error);
      toast({
        title: "Error",
        description: "Failed to fetch recent activity logs",
        variant: "destructive",
      });
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([fetchSystemStats(), fetchRecentLogs()]);
    setRefreshing(false);
  };

  return (
    <div className="space-y-6">
      <motion.div
        className="flex justify-between items-center"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <h2 className="text-2xl font-bold">System Overview</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </motion.div>

      {/* System Health Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <Server className="h-5 w-5" />
              System Health
            </CardTitle>
            <CardDescription>
              Current status of the Campus Guardian system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                <Activity className="h-3.5 w-3.5 mr-1" />
                {stats?.systemHealth === "warning"
                  ? "Warning"
                  : stats?.systemHealth === "critical"
                  ? "Critical"
                  : "Healthy"}
              </Badge>
              <span className="text-sm text-muted-foreground">
                Last checked:{" "}
                {stats
                  ? format(stats.lastUpdated, "MMM d, yyyy h:mm a")
                  : "Loading..."}
              </span>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Key Metrics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
      >
        {/* Schools Card */}
        <motion.div whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Schools</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold">
                  {stats?.schoolCount || 0}
                  <Building2 className="h-4 w-4 inline ml-2 text-muted-foreground" />
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Total Users Card */}
        <motion.div whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold">
                  {stats?.userCount || 0}
                  <Users className="h-4 w-4 inline ml-2 text-muted-foreground" />
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Active Users Card */}
        <motion.div whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                Active Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold">
                  {stats?.activeUserCount || 0}
                  <UserCheck className="h-4 w-4 inline ml-2 text-muted-foreground" />
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* System Alerts Card */}
        <motion.div whileHover={{ scale: 1.02 }} transition={{ duration: 0.2 }}>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                System Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-20" />
              ) : (
                <div className="text-2xl font-bold">
                  {stats?.alertCount || 0}
                  <AlertTriangle className="h-4 w-4 inline ml-2 text-muted-foreground" />
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest system events and user actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-2">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-12 w-full" />
                ))}
              </div>
            ) : recentLogs.length > 0 ? (
              <div className="space-y-2">
                {recentLogs.map((log, index) => (
                  <motion.div
                    key={log.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileHover={{
                      scale: 1.01,
                      backgroundColor: "rgba(var(--card-foreground-rgb), 0.02)",
                    }}
                    className="flex items-center justify-between p-2 rounded-md border"
                  >
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">
                          {log.action_type} - {log.entity_type}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {log.users?.email || "System"}
                          {log.schools ? ` - ${log.schools.name}` : ""}
                        </p>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {format(new Date(log.created_at), "MMM d, h:mm a")}
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                No recent activity found
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button variant="outline" size="sm">
              View All Logs
            </Button>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
}
