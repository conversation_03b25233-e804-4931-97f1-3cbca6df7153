-- Drop existing policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;

-- Create a function to check admin status without recursion
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  -- Get the role directly from the JWT claim
  SELECT COALESCE((auth.jwt() ->> 'role') = 'admin', false);
$$;

-- Create policies using the new function
CREATE POLICY "Enable read access for users"
ON profiles FOR SELECT
TO authenticated
USING (
  -- Allow if it's the user's own profile OR if they're an admin
  auth.uid() = user_id OR 
  is_admin()
);

CREATE POLICY "Enable update access for users"
ON profiles FOR UPDATE
TO authenticated
USING (
  -- Allow if it's the user's own profile OR if they're an admin
  auth.uid() = user_id OR 
  is_admin()
)
WITH CHECK (
  -- Same condition for the check phase
  auth.uid() = user_id OR 
  is_admin()
);

-- Ensure RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY; 