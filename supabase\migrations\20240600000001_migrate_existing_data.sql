-- This migration script will create a default school and associate existing data with it

-- Create a default school if none exists
INSERT INTO public.schools (id, name, invitation_code)
SELECT 
  gen_random_uuid(),
  'Default School',
  'DEFAULT' || substr(md5(random()::text), 1, 8)
WHERE NOT EXISTS (
  SELECT 1 FROM public.schools
);

-- Get the ID of the default school
DO $$
DECLARE
  default_school_id UUID;
BEGIN
  -- Get the default school ID
  SELECT id INTO default_school_id FROM public.schools LIMIT 1;
  
  -- Update profiles table
  UPDATE public.profiles
  SET school_id = default_school_id
  WHERE school_id IS NULL;
  
  -- Update rooms table
  UPDATE public.rooms
  SET school_id = default_school_id
  WHERE school_id IS NULL;
  
  -- Update attendance_records table
  UPDATE public.attendance_records
  SET school_id = default_school_id
  WHERE school_id IS NULL;
  
  -- Update notifications table if it exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') THEN
    UPDATE public.notifications
    SET school_id = default_school_id
    WHERE school_id IS NULL;
  END IF;
  
  -- Update excuses table if it exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'excuses') THEN
    UPDATE public.excuses
    SET school_id = default_school_id
    WHERE school_id IS NULL;
  END IF;
  
  -- Update parent_contacts table if it exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'parent_contacts') THEN
    UPDATE public.parent_contacts
    SET school_id = default_school_id
    WHERE school_id IS NULL;
  END IF;
  
  -- Update room_locations table if it exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'room_locations') THEN
    UPDATE public.room_locations
    SET school_id = default_school_id
    WHERE school_id IS NULL;
  END IF;
  
  -- Update system_settings table if it exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_settings') THEN
    UPDATE public.system_settings
    SET school_id = default_school_id
    WHERE school_id IS NULL;
  END IF;
END $$;
