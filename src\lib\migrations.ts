import { supabase } from "./supabase";

/**
 * Execute SQL directly in Supabase
 */
export async function executeSql(sql: string): Promise<boolean> {
  try {
    // First check if the exec_sql function exists
    const { data: functionExists, error: functionError } = await supabase.rpc(
      "exec_sql",
      { sql: "SELECT 1" }
    );

    if (functionError) {
      // Function doesn't exist, create it
      const createFunctionSQL = `
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$;
      `;

      // Create the function using a direct query
      const { error: createError } = await supabase.rpc("exec_sql", {
        sql: createFunctionSQL,
      });

      if (createError) {
        // If we can't create the function, try a direct query
        const { error: directError } = await supabase
          .from("_exec_sql")
          .select("*")
          .limit(1);

        if (directError) {
          console.error("Error creating exec_sql function:", directError);
          return false;
        }
      }
    }

    // Now execute the SQL
    const { error } = await supabase.rpc("exec_sql", { sql });

    if (error) {
      console.error("Error executing SQL:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error in executeSql:", error);
    return false;
  }
}

/**
 * Creates the block_locations table if it doesn't exist
 */
export async function createBlockLocationsTable(): Promise<boolean> {
  try {
    // Check if the block_locations table exists by trying to query it
    const { data: existingTable, error: tableCheckError } = await supabase
      .from("block_locations")
      .select("id")
      .limit(1);

    // If we get a specific error about the table not existing, we need to create it
    if (tableCheckError && tableCheckError.code === "PGRST204") {
      console.log("Block locations table doesn't exist yet");

      // We can't create tables directly from the client, so we'll need to do this
      // through the Supabase dashboard or migrations
      console.log(
        "Please create the block_locations table through Supabase dashboard"
      );

      // For now, we'll just return true and let the app continue
      return true;
    }

    // If we can query the table, it exists
    console.log("Block locations table exists");
    return true;

    // Create function to get block location by room_id
    const createGetBlockLocationSQL = `
      CREATE OR REPLACE FUNCTION get_block_location_by_room(room_id UUID)
      RETURNS TABLE (
        latitude DECIMAL,
        longitude DECIMAL,
        radius_meters INTEGER
      )
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        RETURN QUERY
        SELECT
          bl.latitude,
          bl.longitude,
          bl.radius_meters
        FROM
          rooms r
          JOIN blocks b ON r.block_id = b.id
          JOIN block_locations bl ON b.id = bl.block_id
        WHERE
          r.id = room_id;
      END;
      $$;
    `;

    const { error: getBlockLocationError } = await supabase.rpc("exec_sql", {
      sql: createGetBlockLocationSQL,
    });

    if (getBlockLocationError) {
      console.error(
        "Error creating get_block_location_by_room function:",
        getBlockLocationError
      );
      return false;
    }

    // Create function to verify attendance location using block location
    const createVerifyLocationSQL = `
      CREATE OR REPLACE FUNCTION verify_attendance_location_by_block(
          student_lat DECIMAL,
          student_lon DECIMAL,
          room_id UUID
      ) RETURNS BOOLEAN AS $$
      DECLARE
          block_location RECORD;
          distance DECIMAL;
      BEGIN
          -- Get block location for the room
          SELECT * INTO block_location FROM get_block_location_by_room(room_id);

          IF block_location IS NULL THEN
              -- If no block location is set, fall back to room location
              SELECT * INTO block_location
              FROM room_locations
              WHERE room_locations.room_id = room_id;

              IF block_location IS NULL THEN
                  RETURN TRUE; -- If no location set for room or block, allow attendance
              END IF;
          END IF;

          -- Calculate distance
          distance := calculate_distance(
              student_lat,
              student_lon,
              block_location.latitude,
              block_location.longitude
          );

          -- Return true if within radius
          RETURN distance <= block_location.radius_meters;
      END;
      $$ LANGUAGE plpgsql;
    `;

    const { error: verifyLocationError } = await supabase.rpc("exec_sql", {
      sql: createVerifyLocationSQL,
    });

    if (verifyLocationError) {
      console.error(
        "Error creating verify_attendance_location_by_block function:",
        verifyLocationError
      );
      return false;
    }

    console.log(
      "Successfully created block_locations table and related functions"
    );
    return true;
  } catch (error) {
    console.error("Error in createBlockLocationsTable:", error);
    return false;
  }
}
