import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Mail, MessageSquare, AlertTriangle, CheckCircle2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useTranslation } from "react-i18next";

interface Settings {
  notifications_enabled: boolean;
  default_method: "email" | "sms" | "both" | "none";
  email_template_new: string;
  email_template_approved: string;
  email_template_rejected: string;
  sms_template_new: string;
  sms_template_approved: string;
  sms_template_rejected: string;
  // Template variables
  school_name: string;
  contact_email: string;
  school_policy: string;
}

// Get localized default settings based on current language
const getDefaultSettings = (language: string): Settings => {
  if (language === 'tr') {
    return {
      notifications_enabled: true,
      default_method: "email",
      email_template_new:
        "Sayın Veli/Vasi,\n\nÇocuğunuz {{studentName}}'in {{schoolName}} okulundan devamsızlık talebi gönderdiğini bildirmek isteriz.\n\nTalep Detayları:\n- Başlangıç Tarihi: {{startDate}}\n- Bitiş Tarihi: {{endDate}}\n- Sebep: {{reason}}\n\nBu talep şu anda okul yönetiminin onayını beklemektedir. Talep incelendikten sonra bilgilendirileceksiniz.\n\nSorularınız için lütfen {{contactEmail}} adresinden bizimle iletişime geçin.\n\n{{schoolPolicy}}\n\nTeşekkürler,\n{{schoolName}} Devam Takip Sistemi",
      email_template_approved:
        "Sayın Veli/Vasi,\n\nÇocuğunuzun devamsızlık talebi ONAYLANDI.\n\nTalep Detayları:\n- Öğrenci: {{studentName}}\n- Başlangıç Tarihi: {{startDate}}\n- Bitiş Tarihi: {{endDate}}\n- Sebep: {{reason}}\n\nSorularınız varsa, lütfen {{contactEmail}} adresinden bizimle iletişime geçin.\n\nTeşekkürler,\n{{schoolName}}",
      email_template_rejected:
        "Sayın Veli/Vasi,\n\nÇocuğunuzun devamsızlık talebi REDDEDİLDİ.\n\nTalep Detayları:\n- Öğrenci: {{studentName}}\n- Başlangıç Tarihi: {{startDate}}\n- Bitiş Tarihi: {{endDate}}\n- Sebep: {{reason}}\n\nSorularınız varsa, lütfen {{contactEmail}} adresinden bizimle iletişime geçin.\n\nTeşekkürler,\n{{schoolName}}",
      sms_template_new:
        "BİLDİRİM: {{studentName}} {{startDate}} - {{endDate}} tarihleri için devamsızlık talebinde bulundu. Sebep: {{reason}}. Sorular için {{contactEmail}}",
      sms_template_approved:
        "ONAYLANDI: {{studentName}}'in {{startDate}} - {{endDate}} devamsızlık talebi {{schoolName}} tarafından onaylandı.",
      sms_template_rejected:
        "REDDEDİLDİ: {{studentName}}'in {{startDate}} - {{endDate}} devamsızlık talebi reddedildi. İletişim: {{contactEmail}}",
      // Template variables
      school_name: "Okul Adı",
      contact_email: "<EMAIL>",
      school_policy: "Devam politikası bilgileri için lütfen okul idaresi ile iletişime geçin.",
    };
  }

  // English default
  return {
    notifications_enabled: true,
    default_method: "email",
    email_template_new:
      "Dear Parent/Guardian,\n\nThis is to inform you that your child, {{studentName}}, has submitted a request for absence from {{schoolName}}.\n\nRequest Details:\n- Start Date: {{startDate}}\n- End Date: {{endDate}}\n- Reason: {{reason}}\n\nThis request is currently pending approval from school administration. You will be notified once the request has been reviewed.\n\nFor questions, please contact us at {{contactEmail}}.\n\n{{schoolPolicy}}\n\nThank you,\n{{schoolName}} Attendance System",
    email_template_approved:
      "Dear Parent/Guardian,\n\nThis is to inform you that your child's absence request has been APPROVED.\n\nRequest Details:\n- Student: {{studentName}}\n- Start Date: {{startDate}}\n- End Date: {{endDate}}\n- Reason: {{reason}}\n\nIf you have any questions, please contact us at {{contactEmail}}.\n\nThank you,\n{{schoolName}}",
    email_template_rejected:
      "Dear Parent/Guardian,\n\nThis is to inform you that your child's absence request has been REJECTED.\n\nRequest Details:\n- Student: {{studentName}}\n- Start Date: {{startDate}}\n- End Date: {{endDate}}\n- Reason: {{reason}}\n\nIf you have any questions, please contact us at {{contactEmail}}.\n\nThank you,\n{{schoolName}}",
    sms_template_new:
      "NOTICE: {{studentName}} has requested absence from {{startDate}} to {{endDate}}. Reason: {{reason}}. Contact {{contactEmail}} for questions.",
    sms_template_approved:
      "APPROVED: {{studentName}}'s absence request ({{startDate}} to {{endDate}}) has been approved by {{schoolName}}.",
    sms_template_rejected:
      "REJECTED: {{studentName}}'s absence request ({{startDate}} to {{endDate}}) has been rejected. Contact {{contactEmail}}.",
    // Template variables
    school_name: "Your School Name",
    contact_email: "<EMAIL>",
    school_policy: "Please contact the school office for attendance policy information.",
  };
};

export default function ParentNotificationSettings() {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  const [settings, setSettings] = useState<Settings>(getDefaultSettings(i18n.language));
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [templateType, setTemplateType] = useState<
    "new" | "approved" | "rejected"
  >("new");

  // Fetch settings on component mount
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);

        // Fetch settings from the database
        const { data, error } = await supabase
          .from("system_settings")
          .select("setting_value")
          .eq("setting_name", "parent_notification_settings")
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
          throw error;
        }

        if (data) {
          const savedSettings = JSON.parse(data.setting_value);
          setSettings(savedSettings);
        } else {
          // Use default settings if none exist
          setSettings(getDefaultSettings(i18n.language));
        }
      } catch (error) {
        console.error("Error fetching notification settings:", error);
        toast({
          title: t("common.error"),
          description: t("admin.settings.failedToLoadNotificationSettings"),
          variant: "destructive",
        });
        // Use default settings on error
        setSettings(getDefaultSettings(i18n.language));
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [toast, i18n.language]);

  // Update template defaults when language changes - always use current language templates
  useEffect(() => {
    // Skip if settings are still loading
    if (loading) return;

    const defaultSettings = getDefaultSettings(i18n.language);
    setSettings(prevSettings => ({
      ...prevSettings,
      // Always update templates to match the current language
      email_template_new: defaultSettings.email_template_new,
      email_template_approved: defaultSettings.email_template_approved,
      email_template_rejected: defaultSettings.email_template_rejected,
      sms_template_new: defaultSettings.sms_template_new,
      sms_template_approved: defaultSettings.sms_template_approved,
      sms_template_rejected: defaultSettings.sms_template_rejected,
    }));
  }, [i18n.language, loading]);

  // Handle toggle changes
  const handleToggleChange = (field: keyof Settings, value: boolean) => {
    setSettings((prev) => ({ ...prev, [field]: value }));
  };

  // Handle radio changes
  const handleRadioChange = (field: keyof Settings, value: string) => {
    setSettings((prev) => ({ ...prev, [field]: value }));
  };

  // Handle text input changes
  const handleTextChange = (field: keyof Settings, value: string) => {
    setSettings((prev) => ({ ...prev, [field]: value }));
  };

  // Save settings
  const saveSettings = async () => {
    try {
      setSaving(true);

      // Check if settings already exist
      const { data: existingData } = await supabase
        .from("system_settings")
        .select("id")
        .eq("setting_name", "parent_notification_settings")
        .single();

      const settingsValue = JSON.stringify(settings);

      if (existingData?.id) {
        // Update existing settings
        const { error } = await supabase
          .from("system_settings")
          .update({
            setting_value: settingsValue,
            updated_at: new Date().toISOString(),
          })
          .eq("setting_name", "parent_notification_settings");

        if (error) throw error;
      } else {
        // Insert new settings
        const { error } = await supabase
          .from("system_settings")
          .insert({
            setting_name: "parent_notification_settings",
            setting_value: settingsValue,
            updated_at: new Date().toISOString(),
          });

        if (error) throw error;
      }

      toast({
        title: t("admin.settings.settingsSaved"),
        description: t("admin.settings.parentNotificationSettingsUpdated"),
      });
    } catch (error) {
      console.error("Error saving notification settings:", error);
      toast({
        title: t("common.error"),
        description: t("admin.settings.failedToSaveNotificationSettings"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Reset settings to default (localized)
  const resetSettings = () => {
    if (window.confirm(t("admin.settings.confirmResetNotificationSettings"))) {
      setSettings(getDefaultSettings(i18n.language));
      toast({
        title: t("admin.settings.settingsReset"),
        description: t("admin.settings.parentNotificationSettingsReset"),
      });
    }
  };

  // Update templates to current language
  const updateTemplatesToCurrentLanguage = () => {
    const defaultSettings = getDefaultSettings(i18n.language);
    setSettings(prevSettings => ({
      ...prevSettings,
      email_template_new: defaultSettings.email_template_new,
      email_template_approved: defaultSettings.email_template_approved,
      email_template_rejected: defaultSettings.email_template_rejected,
      sms_template_new: defaultSettings.sms_template_new,
      sms_template_approved: defaultSettings.sms_template_approved,
      sms_template_rejected: defaultSettings.sms_template_rejected,
    }));
    toast({
      title: t("admin.settings.templatesUpdated"),
      description: t("admin.settings.templatesUpdatedToCurrentLanguage"),
    });
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 w-1/3 bg-gray-200 animate-pulse rounded"></div>
        <div className="h-24 bg-gray-200 animate-pulse rounded"></div>
        <div className="h-24 bg-gray-200 animate-pulse rounded"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 gap-1">
          <TabsTrigger value="general" className="text-xs sm:text-sm px-2 sm:px-3">
            {t("admin.settings.generalSettingsTab")}
          </TabsTrigger>
          <TabsTrigger value="variables" className="text-xs sm:text-sm px-2 sm:px-3">
            {t("admin.settings.templateVariablesTab")}
          </TabsTrigger>
          <TabsTrigger value="email" className="text-xs sm:text-sm px-2 sm:px-3">
            {t("admin.settings.emailTemplatesTab")}
          </TabsTrigger>
          <TabsTrigger value="sms" className="text-xs sm:text-sm px-2 sm:px-3">
            {t("admin.settings.smsTemplatesTab")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {t("admin.settings.notificationPreferences")}
              </CardTitle>
              <CardDescription>
                {t("admin.settings.configureGlobalParentNotifications")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notifications_enabled">
                    {t("admin.settings.enableParentNotifications")}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {t("admin.settings.whenEnabledParentsNotified")}
                  </p>
                </div>
                <Switch
                  id="notifications_enabled"
                  checked={settings.notifications_enabled}
                  onCheckedChange={(checked) =>
                    handleToggleChange("notifications_enabled", checked)
                  }
                />
              </div>

              <Separator />

              <div className="space-y-3">
                <Label>{t("admin.settings.defaultNotificationMethod")}</Label>
                <RadioGroup
                  value={settings.default_method}
                  onValueChange={(value) =>
                    handleRadioChange(
                      "default_method",
                      value as "email" | "sms" | "both" | "none"
                    )
                  }
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="email" id="method-email" />
                    <Label htmlFor="method-email" className="flex items-center">
                      <Mail className="mr-2 h-4 w-4" />
                      {t("admin.settings.emailOnly")}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="sms" id="method-sms" />
                    <Label htmlFor="method-sms" className="flex items-center">
                      <MessageSquare className="mr-2 h-4 w-4" />
                      {t("admin.settings.smsOnly")}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="both" id="method-both" />
                    <Label htmlFor="method-both">
                      {t("admin.settings.bothEmailAndSMS")}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="none" id="method-none" />
                    <Label htmlFor="method-none">
                      {t("admin.settings.noNotificationsDisabled")}
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <Alert className="bg-blue-50 border-blue-200">
                <AlertTriangle className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-800">
                  {t("admin.settings.importantNote")}
                </AlertTitle>
                <AlertDescription className="text-blue-700">
                  {t("admin.settings.globalDefaultSettingsInfo")}
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="variables" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {t("admin.settings.templateVariablesConfiguration")}
              </CardTitle>
              <CardDescription>
                {t("admin.settings.templateVariablesConfigurationDescription")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="school_name">{t("admin.settings.schoolName")}</Label>
                <Input
                  id="school_name"
                  value={settings.school_name}
                  onChange={(e) => handleTextChange("school_name", e.target.value)}
                  placeholder={t("admin.settings.schoolNamePlaceholder")}
                />
                <p className="text-xs text-muted-foreground">
                  {t("admin.settings.schoolNameHelp")}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="contact_email">{t("admin.settings.contactEmail")}</Label>
                <Input
                  id="contact_email"
                  type="email"
                  value={settings.contact_email}
                  onChange={(e) => handleTextChange("contact_email", e.target.value)}
                  placeholder={t("admin.settings.contactEmailPlaceholder")}
                />
                <p className="text-xs text-muted-foreground">
                  {t("admin.settings.contactEmailHelp")}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="school_policy">{t("admin.settings.schoolAttendancePolicy")}</Label>
                <textarea
                  id="school_policy"
                  className="w-full min-h-[100px] p-2 border rounded-md"
                  value={settings.school_policy}
                  onChange={(e) => handleTextChange("school_policy", e.target.value)}
                  placeholder={t("admin.settings.schoolAttendancePolicyPlaceholder")}
                />
                <p className="text-xs text-muted-foreground">
                  {t("admin.settings.schoolAttendancePolicyHelp")}
                </p>
              </div>

              <Alert className="bg-blue-50 border-blue-200">
                <CheckCircle2 className="h-4 w-4 text-blue-600" />
                <AlertTitle className="text-blue-800">
                  {t("admin.settings.templateVariables")}
                </AlertTitle>
                <AlertDescription className="text-blue-700">
                  {t("admin.settings.templateVariablesInfo")}
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {t("admin.settings.emailNotificationTemplates")}
              </CardTitle>
              <CardDescription>
                {t("admin.settings.customizeEmailTemplates")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label className="text-sm font-medium">{t("admin.settings.templateType")}</Label>
                <div className="flex flex-col sm:flex-row gap-2 sm:space-x-2 sm:gap-0">
                  <Button
                    variant={templateType === "new" ? "default" : "outline"}
                    onClick={() => setTemplateType("new")}
                    size="sm"
                    className="w-full sm:w-auto text-xs sm:text-sm"
                  >
                    {t("admin.settings.newRequest")}
                  </Button>
                  <Button
                    variant={
                      templateType === "approved" ? "default" : "outline"
                    }
                    onClick={() => setTemplateType("approved")}
                    size="sm"
                    className="w-full sm:w-auto text-xs sm:text-sm"
                  >
                    {t("admin.settings.approved")}
                  </Button>
                  <Button
                    variant={
                      templateType === "rejected" ? "default" : "outline"
                    }
                    onClick={() => setTemplateType("rejected")}
                    size="sm"
                    className="w-full sm:w-auto text-xs sm:text-sm"
                  >
                    {t("admin.settings.rejected")}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">{t("admin.settings.emailTemplate")}</Label>
                <div className="text-xs text-muted-foreground mb-2 break-words">
                  {t("admin.settings.availableVariables")}:{" "}
                  <span className="font-mono text-xs">
                    {`{{studentName}}, {{startDate}}, {{endDate}}, {{reason}}, {{schoolName}}, {{contactEmail}}, {{schoolPolicy}}`}
                  </span>
                </div>
                <textarea
                  className="w-full min-h-[150px] sm:min-h-[200px] p-2 sm:p-3 border rounded-md text-sm resize-y"
                  value={
                    templateType === "new"
                      ? settings.email_template_new
                      : templateType === "approved"
                      ? settings.email_template_approved
                      : settings.email_template_rejected
                  }
                  onChange={(e) =>
                    handleTextChange(
                      templateType === "new"
                        ? "email_template_new"
                        : templateType === "approved"
                        ? "email_template_approved"
                        : "email_template_rejected",
                      e.target.value
                    )
                  }
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sms" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {t("admin.settings.smsNotificationTemplates")}
              </CardTitle>
              <CardDescription>
                {t("admin.settings.customizeSmsTemplates")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label className="text-sm font-medium">{t("admin.settings.templateType")}</Label>
                <div className="flex flex-col sm:flex-row gap-2 sm:space-x-2 sm:gap-0">
                  <Button
                    variant={templateType === "new" ? "default" : "outline"}
                    onClick={() => setTemplateType("new")}
                    size="sm"
                    className="w-full sm:w-auto text-xs sm:text-sm"
                  >
                    {t("admin.settings.newRequest")}
                  </Button>
                  <Button
                    variant={
                      templateType === "approved" ? "default" : "outline"
                    }
                    onClick={() => setTemplateType("approved")}
                    size="sm"
                    className="w-full sm:w-auto text-xs sm:text-sm"
                  >
                    {t("admin.settings.approved")}
                  </Button>
                  <Button
                    variant={
                      templateType === "rejected" ? "default" : "outline"
                    }
                    onClick={() => setTemplateType("rejected")}
                    size="sm"
                    className="w-full sm:w-auto text-xs sm:text-sm"
                  >
                    {t("admin.settings.rejected")}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">{t("admin.settings.smsTemplate")}</Label>
                <div className="text-xs text-muted-foreground mb-2 break-words">
                  {t("admin.settings.availableVariables")}:{" "}
                  <span className="font-mono text-xs">
                    {`{{studentName}}, {{startDate}}, {{endDate}}, {{reason}}, {{schoolName}}, {{contactEmail}}, {{schoolPolicy}}`}
                  </span>
                </div>
                <div className="p-3 sm:p-4 border rounded-md bg-gray-50">
                  <p className="text-xs sm:text-sm text-muted-foreground mb-2">
                    {t("admin.settings.keepSmsMessagesConcise")}
                  </p>
                  <textarea
                    className="w-full min-h-[80px] sm:min-h-[100px] p-2 border rounded-md text-sm resize-y"
                    value={
                      templateType === "new"
                        ? settings.sms_template_new
                        : templateType === "approved"
                        ? settings.sms_template_approved
                        : settings.sms_template_rejected
                    }
                    onChange={(e) =>
                      handleTextChange(
                        templateType === "new"
                          ? "sms_template_new"
                          : templateType === "approved"
                          ? "sms_template_approved"
                          : "sms_template_rejected",
                        e.target.value
                      )
                    }
                  />
                  <div className="mt-2 text-right text-xs sm:text-sm">
                    <span
                      className={
                        (templateType === "new"
                          ? settings.sms_template_new.length
                          : templateType === "approved"
                          ? settings.sms_template_approved.length
                          : settings.sms_template_rejected.length) > 160
                          ? "text-red-500 font-medium"
                          : "text-green-600"
                      }
                    >
                      {templateType === "new"
                        ? settings.sms_template_new.length
                        : templateType === "approved"
                        ? settings.sms_template_approved.length
                        : settings.sms_template_rejected.length}{" "}
                      / {t("admin.settings.characters", { count: 160 })}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0 pt-4 border-t">
        <Button variant="outline" onClick={resetSettings} className="w-full sm:w-auto text-sm">
          {t("admin.settings.resetToDefault")}
        </Button>
        <Button onClick={saveSettings} disabled={saving} className="w-full sm:w-auto text-sm">
          {saving
            ? t("admin.settings.saving")
            : t("admin.settings.saveSettings")}
        </Button>
      </div>
    </div>
  );
}
