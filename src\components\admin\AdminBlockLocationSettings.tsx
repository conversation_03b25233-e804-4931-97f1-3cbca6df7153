import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast as sonnerToast } from "sonner";
import { Loader2, MapPin, AlertCircle, Save } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { createBlockLocationsTable } from "@/lib/migrations.new";
import { useTranslation } from "react-i18next";

interface BlockLocationSettingsProps {
  blockId: string;
  blockName: string;
}

export function AdminBlockLocationSettings({
  blockId,
  blockName,
}: BlockLocationSettingsProps) {
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [savingRadius, setSavingRadius] = useState(false);
  const [locationPermission, setLocationPermission] =
    useState<PermissionState | null>(null);
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
    radius_meters: number;
  } | null>(null);
  const [localRadius, setLocalRadius] = useState<number | null>(null);
  const [localLatitude, setLocalLatitude] = useState<number | null>(null);
  const [localLongitude, setLocalLongitude] = useState<number | null>(null);
  const [hasUnsavedLocation, setHasUnsavedLocation] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    checkLocationPermission();
    ensureTableExists();
    fetchBlockLocation();
  }, [blockId]);

  const checkLocationPermission = async () => {
    if (!navigator.permissions) {
      console.log("Permissions API not supported");
      return;
    }

    try {
      const permission = await navigator.permissions.query({
        name: "geolocation" as PermissionName,
      });
      setLocationPermission(permission.state);

      permission.onchange = () => {
        setLocationPermission(permission.state);
      };
    } catch (error) {
      console.error("Error checking location permission:", error);
    }
  };

  const getCurrentLocation = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by this browser."));
        return;
      }

      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      });
    });
  };

  const ensureTableExists = async (): Promise<boolean> => {
    try {
      // Check if the table exists by trying to select from it
      const { error } = await supabase
        .from("block_locations")
        .select("*")
        .limit(1);

      if (error) {
        if (error.code === "PGRST204") {
          // Table doesn't exist, create it
          console.log("Creating block_locations table...");
          await createBlockLocationsTable();
          return true;
        } else {
          throw error;
        }
      }

      return true;
    } catch (error) {
      console.error("Error ensuring block locations table exists:", error);
      return false;
    }
  };

  const fetchBlockLocation = async () => {
    if (!blockId) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("block_locations")
        .select("*")
        .eq("block_id", blockId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // No location set yet, this is fine
          setLocation(null);
        } else if (error.code === "PGRST204") {
          // Table doesn't exist yet
          console.warn("Block locations table does not exist");
          setLocation(null);
        } else {
          throw error;
        }
      } else if (data) {
        const locationData = {
          latitude: Number(data.latitude),
          longitude: Number(data.longitude),
          radius_meters: data.radius_meters,
        };
        setLocation(locationData);
        setLocalRadius(data.radius_meters);
        setLocalLatitude(Number(data.latitude));
        setLocalLongitude(Number(data.longitude));
        setHasUnsavedLocation(false);
      }
    } catch (error) {
      console.error("Error fetching block location:", error);
      sonnerToast.error(t("common.error"), {
        description: "Failed to fetch block location. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGetCurrentLocation = async () => {
    try {
      setUpdating(true);
      // Getting current location
      const position = await getCurrentLocation();

      // Set the coordinates in local state for editing (don't save yet)
      setLocalLatitude(position.coords.latitude);
      setLocalLongitude(position.coords.longitude);
      setHasUnsavedLocation(true);

      sonnerToast.success(t("admin.settings.locationSettings.locationRetrieved"), {
        description: t("admin.settings.locationSettings.locationRetrievedDescription"),
      });

      // Update permission state after successful location access
      checkLocationPermission();
    } catch (error) {
      console.error("Error getting location:", error);
      let errorMessage = "Failed to get current location";

      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage =
              "Please allow location access in your browser settings to get current location";
            setLocationPermission("denied");
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage =
              "Location information is unavailable. Please try again";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out. Please try again";
            break;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      sonnerToast.error("Error", {
        description: errorMessage,
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleSaveLocation = async () => {
    if (localLatitude === null || localLongitude === null) return;

    try {
      setUpdating(true);

      // Check if table exists first
      // Verifying table and permissions
      const tableExists = await ensureTableExists();
      if (!tableExists) {
        throw new Error("Block locations table is not properly configured");
      }

      const locationData = {
        block_id: blockId,
        latitude: localLatitude,
        longitude: localLongitude,
        radius_meters: location?.radius_meters || 50,
      };

      console.log("Saving location data:", locationData);

      // Upsert the location data
      const { data: savedLocation, error } = await supabase
        .from("block_locations")
        .upsert(locationData, {
          onConflict: "block_id",
        })
        .select()
        .single();

      if (error) {
        console.error("Error saving location:", error);
        throw new Error("Failed to save location. Please try again.");
      }

      if (!savedLocation) {
        console.error("Location not found after saving");
        throw new Error("Location was not saved correctly. Please try again.");
      }

      // Location saved successfully

      const newRadius = location?.radius_meters || 50;
      setLocation({
        latitude: localLatitude,
        longitude: localLongitude,
        radius_meters: newRadius,
      });
      setLocalRadius(newRadius);
      setHasUnsavedLocation(false);

      sonnerToast.success(t("admin.settings.locationSettings.locationUpdated"), {
        description: t("admin.settings.locationSettings.blockLocationUpdatedMessage", {
          blockName,
        }),
      });
    } catch (error) {
      console.error("Error saving location:", error);
      let errorMessage = "Failed to save block location";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      sonnerToast.error("Error", {
        description: errorMessage,
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleSaveRadius = async () => {
    if (localRadius === null) return;

    try {
      setSavingRadius(true);

      console.log("Saving radius:", {
        blockId,
        localRadius,
        currentRadius: location?.radius_meters
      });

      const { data, error } = await supabase
        .from("block_locations")
        .update({ radius_meters: localRadius })
        .eq("block_id", blockId)
        .select();

      if (error) throw error;

      console.log("Update result:", data);

      // Update location state, creating it if it doesn't exist
      if (location) {
        setLocation({
          ...location,
          radius_meters: localRadius,
        });
      } else {
        // If no location exists yet, we can't update radius without coordinates
        sonnerToast.error(t("common.error"), {
          description: t("admin.settings.locationSettings.setLocationFirst"),
        });
        return;
      }

      sonnerToast.success(t("admin.settings.locationSettings.radiusUpdated"), {
        description: t("admin.settings.locationSettings.blockRadiusUpdatedMessage", {
          blockName,
        }),
      });
    } catch (error) {
      console.error("Error updating radius:", error);
      sonnerToast.error(t("common.error"), {
        description: t("admin.settings.locationSettings.errorUpdatingRadius"),
      });
    } finally {
      setSavingRadius(false);
    }
  };

  // Check if radius has been changed
  const hasRadiusChanged = location && localRadius !== null && localRadius !== location.radius_meters;

  // Check if location coordinates have been changed
  const hasLocationChanged = (localLatitude !== null && localLongitude !== null) &&
    (!location || localLatitude !== location.latitude || localLongitude !== location.longitude);

  // Show location fields if we have coordinates (either from existing location or newly retrieved)
  const showLocationFields = location || (localLatitude !== null && localLongitude !== null);

  const renderLocationPermissionMessage = () => {
    if (locationPermission === "denied") {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t("admin.settings.locationPermissionDenied")}
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.settings.blockLocationSettings")}</CardTitle>
        <CardDescription>
          {t("admin.settings.blockLocationDescription", { blockName })}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {renderLocationPermissionMessage()}

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={handleGetCurrentLocation}
              disabled={updating || locationPermission === "denied"}
              className="flex items-center gap-2"
            >
              {updating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <MapPin className="h-4 w-4" />
              )}
              {location
                ? t("admin.settings.updateCurrentLocation")
                : t("admin.settings.setCurrentLocation")}
            </Button>
          </div>

          {showLocationFields && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>{t("admin.settings.latitude")}</Label>
                  <Input
                    type="number"
                    step="any"
                    value={localLatitude ?? (location?.latitude || "")}
                    onChange={(e) => {
                      setLocalLatitude(Number(e.target.value));
                      setHasUnsavedLocation(true);
                    }}
                    placeholder="Enter latitude"
                  />
                </div>
                <div className="space-y-2">
                  <Label>{t("admin.settings.longitude")}</Label>
                  <Input
                    type="number"
                    step="any"
                    value={localLongitude ?? (location?.longitude || "")}
                    onChange={(e) => {
                      setLocalLongitude(Number(e.target.value));
                      setHasUnsavedLocation(true);
                    }}
                    placeholder="Enter longitude"
                  />
                </div>
              </div>

              {(hasLocationChanged || hasUnsavedLocation) && (
                <div className="flex gap-2">
                  <Button
                    onClick={handleSaveLocation}
                    disabled={updating || localLatitude === null || localLongitude === null}
                    className="flex items-center gap-2"
                  >
                    {updating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {t("admin.settings.locationSettings.saveLocation")}
                  </Button>
                  {hasUnsavedLocation && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        setLocalLatitude(location?.latitude || null);
                        setLocalLongitude(location?.longitude || null);
                        setHasUnsavedLocation(false);
                      }}
                    >
                      {t("common.cancel")}
                    </Button>
                  )}
                </div>
              )}
              <div className="space-y-2">
                <Label>{t("admin.settings.attendanceRadius")}</Label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    value={localRadius ?? (location?.radius_meters || 50)}
                    onChange={(e) => setLocalRadius(Number(e.target.value))}
                    min={10}
                    max={1000}
                    className="flex-1"
                  />
                  {hasRadiusChanged && (
                    <Button
                      onClick={handleSaveRadius}
                      disabled={savingRadius}
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      {savingRadius ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                      {t("common.save")}
                    </Button>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.studentsWithinRadius", { blockName })}
                </p>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
