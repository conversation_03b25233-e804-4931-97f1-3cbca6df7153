-- First, ensure the profiles table has the proper unique constraint
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_user_id_key;
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_user_id_unique;
ALTER TABLE profiles ADD CONSTRAINT profiles_user_id_unique UNIQUE (user_id);

-- Now we can safely recreate the notifications table
DROP TABLE IF EXISTS notifications CASCADE;
DROP TYPE IF EXISTS notification_type CASCADE;

-- Create notification type enum
CREATE TYPE notification_type AS ENUM (
  'distance_alert',
  'system_alert',
  'attendance_alert',
  'attendance',
  'absence',
  'late',
  'excused',
  'system'
);

-- Create notifications table with all required columns
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type notification_type NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  student_id UUID,
  teacher_id UUID,
  student_location POINT,
  distance_meters DOUBLE PRECISION,
  room_number TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  teacher_read_at TIMESTAMPTZ,
  admin_read_at TIMESTAMPTZ,
  read BOOLEAN DEFAULT false,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Now add the foreign key constraints
ALTER TABLE notifications 
ADD CONSTRAINT notifications_student_id_fkey 
FOREIGN KEY (student_id) REFERENCES profiles(user_id);

ALTER TABLE notifications 
ADD CONSTRAINT notifications_teacher_id_fkey 
FOREIGN KEY (teacher_id) REFERENCES profiles(user_id);

-- Create indexes for better query performance
CREATE INDEX idx_notifications_student_id ON notifications(student_id);
CREATE INDEX idx_notifications_teacher_id ON notifications(teacher_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_notifications_timestamp ON notifications(timestamp);

-- Enable RLS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Enable notifications access for users"
ON notifications FOR ALL
TO authenticated
USING (
  -- Students can view their own notifications
  student_id = auth.uid()
  OR
  -- Teachers can view notifications for their students
  teacher_id = auth.uid()
  OR
  -- Admins can view all notifications
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
)
WITH CHECK (
  -- Students can't modify notifications
  (student_id = auth.uid() AND FALSE)
  OR
  -- Teachers can only update their read status
  (teacher_id = auth.uid() AND (
    (TG_OP = 'UPDATE' AND NEW.teacher_read_at IS DISTINCT FROM OLD.teacher_read_at)
    OR
    TG_OP = 'DELETE'
  ))
  OR
  -- Admins can do anything
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Grant permissions
GRANT ALL ON notifications TO authenticated; 