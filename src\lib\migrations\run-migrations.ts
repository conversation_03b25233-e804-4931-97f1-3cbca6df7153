import { ensureExcusesTableExists } from "./excuses-table-migration";
import { ensureSchoolsTableColumns } from "./schools-table-migration";
import { createSystemSchoolSettingsOverridesTable } from "./system-school-settings-overrides-migration";
import { runSystemAdminCodeMigration } from "./system-admin-code-migration";
import { createDatabaseCleanupSettings } from "./database-cleanup-migration";
import { createFeedbackSystem } from "./feedback-system-migration";
import { createTableExistsFunction } from "./table-exists-function-migration";

/**
 * Run all necessary database migrations
 * @returns Promise that resolves when all migrations are complete
 */
export const runMigrations = async (): Promise<boolean> => {
  try {


    // Run schools table migration first
    const schoolsTableMigrated = await ensureSchoolsTableColumns();

    // Then run excuses table migration
    const excusesTableMigrated = await ensureExcusesTableExists();

    // Run system school settings overrides migration
    const systemOverridesMigrated =
      await createSystemSchoolSettingsOverridesTable();

    // Run system admin code migration
    const systemAdminCodeMigrated = await runSystemAdminCodeMigration();

    // Run database cleanup settings migration
    const databaseCleanupSettingsMigrated =
      await createDatabaseCleanupSettings();

    // Run table exists function migration
    const tableExistsFunctionMigrated = await createTableExistsFunction();

    // Run feedback system migration
    const feedbackSystemMigrated = await createFeedbackSystem();

    // Add more migrations here as needed

    console.log("Database migrations completed.");
    return (
      schoolsTableMigrated &&
      excusesTableMigrated &&
      systemOverridesMigrated &&
      systemAdminCodeMigrated &&
      databaseCleanupSettingsMigrated &&
      tableExistsFunctionMigrated &&
      feedbackSystemMigrated
    );
  } catch (error) {
    console.error("Error running migrations:", error);
    return false;
  }
};
