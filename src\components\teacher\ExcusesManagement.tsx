import { useState, useEffect } from "react";
import { useExcuses } from "@/hooks/useExcuses";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { format } from "date-fns";
import { enUS, tr } from "date-fns/locale";
import { calculateDaysBetween, formatDuration } from "@/lib/date-utils";
import { useToast } from "@/hooks/use-toast";
import { Excuse } from "@/lib/types";
import { useTranslation } from "react-i18next";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Calendar,
  Search,
  Filter,
  User,
  DoorClosed,
  X,
  Check,
  Trash2,
  Download,
  ChevronDown,
  Globe,
  FileImage,
  FileSpreadsheet,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function ExcusesManagement() {
  const { t } = useTranslation();
  const { profile } = useAuth();
  const { toast } = useToast();

  // Create the getStatusBadge function with translation
  const getStatusBadge = createGetStatusBadge(t);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedExcuse, setSelectedExcuse] = useState<Excuse | null>(null);
  const [teacherNotes, setTeacherNotes] = useState("");
  const [rooms, setRooms] = useState<{ id: string; name: string }[]>([]);
  const [selectedRoom, setSelectedRoom] = useState<string>("all");
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  // Fetch excuses using our custom hook
  const {
    excuses,
    loading,
    isUpdating,
    error,
    fetchExcuses,
    updateExcuseStatus,
    deleteExcuse,
  } = useExcuses({
    role: "teacher",
    roomId: selectedRoom !== "all" ? selectedRoom : undefined,
    status: statusFilter !== "all" ? (statusFilter as any) : undefined,
  });

  // Fetch rooms assigned to this teacher from their school only
  useEffect(() => {
    const fetchTeacherRooms = async () => {
      if (!profile?.id || !profile?.school_id) return;

      try {
        let query = supabase
          .from("rooms")
          .select("id, name")
          .eq("teacher_id", profile.id);

        // Filter by school_id for non-system admins
        if (profile.accessLevel !== 3) {
          query = query.eq("school_id", profile.school_id);
        }

        const { data, error } = await query;

        if (error) throw error;
        setRooms(data || []);
      } catch (err) {
        console.error("Error fetching teacher rooms:", err);
      }
    };

    fetchTeacherRooms();
  }, [profile?.id, profile?.school_id, profile?.accessLevel]);

  // Filter excuses based on search query
  const filteredExcuses = excuses.filter((excuse) => {
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    return (
      excuse.studentName?.toLowerCase().includes(searchLower) ||
      excuse.roomName?.toLowerCase().includes(searchLower) ||
      excuse.reason.toLowerCase().includes(searchLower)
    );
  });

  // Handle excuse approval
  const handleApproveExcuse = async () => {
    if (!selectedExcuse) return;

    try {
      await updateExcuseStatus(selectedExcuse.id, "approved", teacherNotes);
      setSelectedExcuse(null);
      setTeacherNotes("");
    } catch (error) {
      console.error("Error approving excuse:", error);
    }
  };

  // Handle excuse rejection
  const handleRejectExcuse = async () => {
    if (!selectedExcuse) return;

    try {
      await updateExcuseStatus(selectedExcuse.id, "rejected", teacherNotes);
      setSelectedExcuse(null);
      setTeacherNotes("");
    } catch (error) {
      console.error("Error rejecting excuse:", error);
    }
  };

  // Handle excuse deletion
  const handleDeleteExcuse = async (excuseId: string) => {
    try {
      await deleteExcuse(excuseId);
      setConfirmDelete(null);
      toast({
        title: t("teacher.excuses.excuseDeleted"),
        description: t("common.success"),
      });
    } catch (error) {
      console.error("Error deleting excuse:", error);
      toast({
        title: t("common.error"),
        description: t("common.error"),
        variant: "destructive",
      });
    }
  };

  // Export functionality
  const showExportSuccess = (format: string) => {
    toast({
      title: t("teacher.excuses.exportComplete"),
      description: t("teacher.excuses.excusesExported", {
        count: filteredExcuses.length,
        format: format,
      }),
    });
  };

  // Export excuses to CSV
  const exportToCSV = () => {
    const headers = [
      t("teacher.excuses.student"),
      t("common.room"),
      t("teacher.excuses.date"),
      t("teacher.excuses.duration"),
      t("common.time"),
      t("teacher.excuses.reason"),
      t("common.status"),
      t("teacher.excuses.notes"),
    ];

    const rows = filteredExcuses.map((excuse) => [
      excuse.studentName || t("teacher.excuses.unknownStudent"),
      excuse.roomName || t("teacher.excuses.unknownRoom"),
      excuse.start_date === excuse.end_date
        ? format(new Date(excuse.start_date), "yyyy-MM-dd")
        : `${format(new Date(excuse.start_date), "yyyy-MM-dd")} - ${format(
            new Date(excuse.end_date),
            "yyyy-MM-dd"
          )}`,
      formatDuration(
        calculateDaysBetween(excuse.start_date, excuse.end_date)
      ),
      `${excuse.start_time} - ${excuse.end_time}`,
      excuse.reason,
      t(`teacher.excuses.${excuse.status}`),
      excuse.notes || t("teacher.excuses.notApplicable"),
    ]);

    const csvContent = [
      headers.join(","),
      ...rows.map((row) =>
        row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(",")
      ),
    ].join("\n");

    downloadFile(
      csvContent,
      `${t("teacher.excuses.excusesReport")}_${format(
        new Date(),
        "yyyy-MM-dd"
      )}.csv`,
      "text/csv"
    );
    showExportSuccess("CSV");
  };

  // Export excuses to PDF
  const exportToPDF = () => {
    const htmlContent = generateHTMLContent(true);

    // Create a new window for PDF generation
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(htmlContent);
      printWindow.document.close();

      // Wait for content to load then print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };
    }

    showExportSuccess("PDF");
  };

  // Export excuses to HTML
  const exportToHTML = () => {
    const htmlContent = generateHTMLContent(false);
    downloadFile(
      htmlContent,
      `${t("teacher.excuses.excusesReport")}_${format(
        new Date(),
        "yyyy-MM-dd"
      )}.html`,
      "text/html"
    );
    showExportSuccess("HTML");
  };

  // Generate beautiful HTML content (matching admin design)
  const generateHTMLContent = (forPrint = false) => {
    const locale = t("common.locale") === "tr" ? tr : enUS;
    const currentDate = format(new Date(), "d MMMM yyyy", { locale });
    const totalExcuses = filteredExcuses.length;
    const pendingCount = filteredExcuses.filter(
      (e) => e.status === "pending"
    ).length;
    const approvedCount = filteredExcuses.filter(
      (e) => e.status === "approved"
    ).length;
    const rejectedCount = filteredExcuses.filter(
      (e) => e.status === "rejected"
    ).length;

    return `<!DOCTYPE html>
<html lang="${t("common.locale") === "tr" ? "tr" : "en"}" dir="${t("common.direction") === "rtl" ? "rtl" : "ltr"}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${t("teacher.excuses.excusesReport")} - ${currentDate}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.2;
            color: #1a202c;
            background: ${forPrint ? '#fff' : 'linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%)'};
            min-height: 100vh;
            padding: ${forPrint ? '0' : '10px'};
            font-size: 14px;
            animation: ${forPrint ? 'none' : 'gradientShift 15s ease infinite'};
        }

        @keyframes gradientShift {
            0% { background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%); }
            25% { background: linear-gradient(135deg, #4ecdc4 0%, #45b7d1 25%, #96ceb4 50%, #ffeaa7 75%, #ff6b6b 100%); }
            50% { background: linear-gradient(135deg, #45b7d1 0%, #96ceb4 25%, #ffeaa7 50%, #ff6b6b 75%, #4ecdc4 100%); }
            75% { background: linear-gradient(135deg, #96ceb4 0%, #ffeaa7 25%, #ff6b6b 50%, #4ecdc4 75%, #45b7d1 100%); }
            100% { background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: ${forPrint ? '0' : '20px'};
            box-shadow: ${forPrint ? 'none' : '0 25px 50px rgba(0,0,0,0.15)'};
            overflow: hidden;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: ${forPrint ? '8px 20px' : '16px 30px'};
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: ${forPrint ? 'none' : 'float 20s ease-in-out infinite'};
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            font-size: ${forPrint ? '1.4rem' : '1.8rem'};
            font-weight: 800;
            margin-bottom: ${forPrint ? '2px' : '4px'};
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            letter-spacing: -0.5px;
        }

        .header p {
            font-size: ${forPrint ? '0.75rem' : '0.9rem'};
            opacity: 0.95;
            margin-bottom: ${forPrint ? '1px' : '3px'};
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: ${forPrint ? '6px' : '12px'};
            padding: ${forPrint ? '8px 15px' : '16px 25px'};
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
        }

        .stat-card {
            background: white;
            padding: ${forPrint ? '8px 12px' : '14px 18px'};
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: none;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--accent-color);
        }

        .stat-card:hover {
            transform: ${forPrint ? 'none' : 'translateY(-3px)'};
        }

        .stat-card.total { --accent-color: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); }
        .stat-card.pending { --accent-color: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); }
        .stat-card.approved { --accent-color: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .stat-card.rejected { --accent-color: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }

        .stat-number {
            font-size: ${forPrint ? '1.3rem' : '1.8rem'};
            font-weight: 800;
            margin-bottom: ${forPrint ? '1px' : '3px'};
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-size: ${forPrint ? '0.65rem' : '0.75rem'};
        }

        .content {
            padding: ${forPrint ? '12px 15px' : '24px 30px'};
        }

        .table-container {
            overflow-x: auto;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            border: 1px solid #e2e8f0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: ${forPrint ? '8px 6px' : '14px 12px'};
            text-align: left;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-size: ${forPrint ? '0.7rem' : '0.8rem'};
            position: relative;
        }

        th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
        }

        td {
            padding: ${forPrint ? '8px 6px' : '12px 12px'};
            border-bottom: 1px solid #f1f5f9;
            vertical-align: top;
            font-size: ${forPrint ? '0.8rem' : '0.9rem'};
            font-weight: 500;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: ${forPrint ? 'inherit' : 'linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%)'};
            transform: ${forPrint ? 'none' : 'scale(1.01)'};
            transition: all 0.2s ease;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: ${forPrint ? '4px 8px' : '6px 12px'};
            border-radius: 25px;
            font-size: ${forPrint ? '0.7rem' : '0.8rem'};
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .status-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: ${forPrint ? 'none' : 'shimmer 2s infinite'};
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .status-pending {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }

        .status-approved {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status-rejected {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .reason-cell {
            max-width: 300px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .footer {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: ${forPrint ? '8px 15px' : '16px 25px'};
            text-align: center;
            color: #64748b;
            border-top: 2px solid #e2e8f0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .footer p {
            margin-bottom: ${forPrint ? '2px' : '4px'};
            font-size: ${forPrint ? '0.75rem' : '0.85rem'};
            font-weight: 500;
        }

        .export-info {
            font-size: 0.9rem;
            font-style: italic;
        }

        @media print {
            body { background: white !important; padding: 0 !important; }
            .container { box-shadow: none !important; border-radius: 0 !important; }
            .stat-card:hover { transform: none !important; }
            tr:hover { background: inherit !important; }
        }

        @media (max-width: 768px) {
            .header {
                padding: 12px 20px;
            }
            .header h1 {
                font-size: 1.5rem;
                margin-bottom: 3px;
            }
            .header p {
                font-size: 0.8rem;
                margin-bottom: 3px;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                padding: 15px;
            }
            .stat-card {
                padding: 12px 15px;
            }
            .stat-number {
                font-size: 1.5rem;
            }
            .stat-label {
                font-size: 0.75rem;
            }
            .content { padding: 20px; }
            th, td { padding: 12px 8px; font-size: 0.9rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>📋 ${t("teacher.excuses.excusesReport")}</h1>
                <p>${t("teacher.excuses.comprehensiveReport")}</p>
                <p><strong>${t("teacher.excuses.generatedOn")}:</strong> ${currentDate}</p>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-number">${totalExcuses}</div>
                <div class="stat-label">${t("teacher.excuses.totalExcuses")}</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number">${pendingCount}</div>
                <div class="stat-label">${t("teacher.excuses.pending")}</div>
            </div>
            <div class="stat-card approved">
                <div class="stat-number">${approvedCount}</div>
                <div class="stat-label">${t("teacher.excuses.approved")}</div>
            </div>
            <div class="stat-card rejected">
                <div class="stat-number">${rejectedCount}</div>
                <div class="stat-label">${t("teacher.excuses.rejected")}</div>
            </div>
        </div>

        <div class="content">
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>📅 ${t("teacher.excuses.dateRange")}</th>
                            <th>👤 ${t("teacher.excuses.student")}</th>
                            <th>🏫 ${t("common.room")}</th>
                            <th>📝 ${t("teacher.excuses.reason")}</th>
                            <th>⏰ ${t("common.time")}</th>
                            <th>📊 ${t("teacher.excuses.status")}</th>
                            <th>📝 ${t("teacher.excuses.notes")}</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredExcuses.map(excuse => `
                            <tr>
                                <td>
                                    <strong>${excuse.start_date === excuse.end_date
                                        ? format(new Date(excuse.start_date), "d MMM yyyy", { locale })
                                        : `${format(new Date(excuse.start_date), "d MMM", { locale })} - ${format(new Date(excuse.end_date), "d MMM yyyy", { locale })}`
                                    }</strong>
                                </td>
                                <td><strong>${excuse.studentName || t("teacher.excuses.unknownStudent")}</strong></td>
                                <td>${excuse.roomName || t("teacher.excuses.unknownRoom")}</td>
                                <td class="reason-cell">${excuse.reason}</td>
                                <td>${excuse.start_time && excuse.end_time ? `${excuse.start_time} - ${excuse.end_time}` : t("teacher.excuses.notSpecified")}</td>
                                <td>
                                    <span class="status-badge status-${excuse.status}">
                                        ${excuse.status === 'pending' ? t("teacher.excuses.pending") :
                                          excuse.status === 'approved' ? t("teacher.excuses.approved") :
                                          excuse.status === 'rejected' ? t("teacher.excuses.rejected") : excuse.status}
                                    </span>
                                </td>
                                <td>${excuse.notes || t("teacher.excuses.notApplicable")}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            <p><strong>${t("app.name")}</strong></p>
            <p class="export-info">${t("teacher.excuses.reportContains", { count: totalExcuses, date: currentDate })}</p>
        </div>
    </div>
</body>
</html>`;
  };

  // Helper function to download files
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: `${mimeType};charset=utf-8;` });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Group excuses by status
  const pendingExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "pending"
  );
  const approvedExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "approved"
  );
  const rejectedExcuses = filteredExcuses.filter(
    (excuse) => excuse.status === "rejected"
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("teacher.excuses.excusesManagement")}</CardTitle>
        <CardDescription>
          {t("teacher.excuses.manageStudentExcuses")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t("common.search") + "..."}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 w-full"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1 h-7 w-7"
                onClick={() => setSearchQuery("")}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Select value={selectedRoom} onValueChange={setSelectedRoom}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder={t("common.filter") + "..."} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("common.all")} {t("common.rooms")}
                </SelectItem>
                {rooms.map((room) => (
                  <SelectItem key={room.id} value={room.id}>
                    {t("common.room")} {room.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder={t("common.filter") + "..."} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("common.all")}</SelectItem>
                <SelectItem value="pending">
                  {t("teacher.excuses.pending")}
                </SelectItem>
                <SelectItem value="approved">
                  {t("teacher.excuses.approved")}
                </SelectItem>
                <SelectItem value="rejected">
                  {t("teacher.excuses.rejected")}
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Export Button */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full sm:w-auto flex items-center justify-center gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 border-blue-200 text-blue-700 hover:text-blue-800 transition-all duration-200 text-sm"
                >
                  <Download className="h-4 w-4 flex-shrink-0" />
                  <span className="hidden sm:inline">{t("teacher.excuses.export")}</span>
                  <span className="sm:hidden">Export</span>
                  <ChevronDown className="h-4 w-4 ml-1 flex-shrink-0" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={exportToHTML}
                  className="flex items-center gap-2 cursor-pointer hover:bg-blue-50 focus:bg-blue-50"
                >
                  <Globe className="h-4 w-4 text-blue-600" />
                  <span>{t("teacher.excuses.exportAsHTML")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={exportToPDF}
                  className="flex items-center gap-2 cursor-pointer hover:bg-red-50 focus:bg-red-50"
                >
                  <FileImage className="h-4 w-4 text-red-600" />
                  <span>{t("teacher.excuses.exportAsPDF")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={exportToCSV}
                  className="flex items-center gap-2 cursor-pointer hover:bg-green-50 focus:bg-green-50"
                >
                  <FileSpreadsheet className="h-4 w-4 text-green-600" />
                  <span>{t("teacher.excuses.exportAsCSV")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <Tabs defaultValue="pending">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="pending" className="relative">
              {t("teacher.excuses.pending")}
              {pendingExcuses.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {pendingExcuses.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="approved">
              {t("teacher.excuses.approved")}
            </TabsTrigger>
            <TabsTrigger value="rejected">
              {t("teacher.excuses.rejected")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending">
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <LoadingSpinner message={t("loading.excuses")} />
              </div>
            ) : pendingExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <Clock className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("teacher.excuses.noPendingExcuses")}
                description={t("teacher.excuses.noExcusesMessage")}
              />
            ) : (
              <div className="space-y-4">
                {pendingExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes("");
                    }}
                    onDelete={() => setConfirmDelete(excuse.id)}
                    t={t}
                    getStatusBadge={getStatusBadge}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="approved">
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <LoadingSpinner message={t("loading.excuses")} />
              </div>
            ) : approvedExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <CheckCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("teacher.excuses.noApprovedExcuses")}
                description={t("teacher.excuses.noExcusesMessage")}
              />
            ) : (
              <div className="space-y-4">
                {approvedExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes(excuse.notes || "");
                    }}
                    t={t}
                    getStatusBadge={getStatusBadge}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="rejected">
            {loading ? (
              <div className="flex justify-center items-center h-32">
                <LoadingSpinner message={t("loading.excuses")} />
              </div>
            ) : rejectedExcuses.length === 0 ? (
              <EmptyState
                icon={
                  <XCircle className="w-12 h-12 text-muted-foreground opacity-30" />
                }
                title={t("teacher.excuses.noRejectedExcuses")}
                description={t("teacher.excuses.noExcusesMessage")}
              />
            ) : (
              <div className="space-y-4">
                {rejectedExcuses.map((excuse) => (
                  <ExcuseCard
                    key={excuse.id}
                    excuse={excuse}
                    onClick={() => {
                      setSelectedExcuse(excuse);
                      setTeacherNotes(excuse.notes || "");
                    }}
                    t={t}
                    getStatusBadge={getStatusBadge}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Excuse Details Dialog */}
      <Dialog
        open={!!selectedExcuse}
        onOpenChange={(open) => !open && setSelectedExcuse(null)}
      >
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{t("teacher.excuses.excuseDetails")}</DialogTitle>
            <DialogDescription>
              {t("teacher.excuses.reviewExcuse")}
            </DialogDescription>
          </DialogHeader>

          {selectedExcuse && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("teacher.excuses.student")}
                  </p>
                  <p className="font-medium">{selectedExcuse.studentName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("common.room")}
                  </p>
                  <p className="font-medium">
                    {t("common.room")} {selectedExcuse.roomName}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("teacher.excuses.date")}
                  </p>
                  <p>
                    {format(
                      new Date(selectedExcuse.start_date),
                      "MMMM d, yyyy"
                    )}
                    {selectedExcuse.start_date !== selectedExcuse.end_date &&
                      ` - ${format(
                        new Date(selectedExcuse.end_date),
                        "MMMM d, yyyy"
                      )}`}
                  </p>
                  <p className="text-xs text-primary mt-1">
                    {t("teacher.excuses.duration")}:{" "}
                    {formatDuration(
                      calculateDaysBetween(
                        selectedExcuse.start_date,
                        selectedExcuse.end_date
                      )
                    )}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {t("common.time")}
                  </p>
                  <p>
                    {selectedExcuse.start_time} - {selectedExcuse.end_time}
                  </p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  {t("teacher.excuses.reason")}
                </p>
                <div className="p-3 bg-muted rounded-md text-sm">
                  {selectedExcuse.reason}
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  {t("common.status")}
                </p>
                <div className="flex items-center">
                  {getStatusBadge(selectedExcuse.status)}
                  {selectedExcuse.status !== "pending" && (
                    <p className="text-sm ml-2">
                      by {selectedExcuse.teacherName || "Unknown Teacher"}
                    </p>
                  )}
                </div>
              </div>

              {selectedExcuse.status === "pending" ? (
                <>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      {t("teacher.excuses.notes")}
                    </p>
                    <Textarea
                      placeholder={t("teacher.excuses.enterNotes")}
                      value={teacherNotes}
                      onChange={(e) => setTeacherNotes(e.target.value)}
                      className="resize-none"
                    />
                  </div>

                  <DialogFooter className="flex justify-between">
                    <Button
                      variant="destructive"
                      onClick={handleRejectExcuse}
                      disabled={isUpdating}
                      className="flex items-center gap-1"
                    >
                      {isUpdating ? (
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                      ) : (
                        <X className="w-4 h-4" />
                      )}
                      {isUpdating ? t("common.processing") : t("teacher.excuses.reject")}
                    </Button>
                    <Button
                      onClick={handleApproveExcuse}
                      disabled={isUpdating}
                      className="flex items-center gap-1"
                    >
                      {isUpdating ? (
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                      ) : (
                        <Check className="w-4 h-4" />
                      )}
                      {isUpdating ? t("common.processing") : t("teacher.excuses.approve")}
                    </Button>
                  </DialogFooter>
                </>
              ) : (
                selectedExcuse.notes && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      {t("teacher.excuses.notes")}
                    </p>
                    <div className="p-3 bg-muted rounded-md text-sm">
                      {selectedExcuse.notes}
                    </div>
                  </div>
                )
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!confirmDelete}
        onOpenChange={(open) => !open && setConfirmDelete(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("common.confirmDelete")}</DialogTitle>
            <DialogDescription>
              {t("teacher.excuses.confirmDeleteExcuse")}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDelete(null)}>
              {t("common.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={() => confirmDelete && handleDeleteExcuse(confirmDelete)}
            >
              {t("common.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

// Helper components
interface ExcuseCardProps {
  excuse: Excuse;
  onClick?: () => void;
  onDelete?: () => void;
  t: (key: string) => string;
  getStatusBadge: (status: string) => JSX.Element | null;
}

function ExcuseCard({ excuse, onClick, onDelete, t, getStatusBadge }: ExcuseCardProps) {
  const handleClick = (e: React.MouseEvent) => {
    // If we're clicking the delete button, don't trigger the card click
    if ((e.target as HTMLElement).closest(".delete-button")) {
      e.stopPropagation();
      return;
    }
    onClick?.();
  };

  return (
    <div
      className="border rounded-lg p-4 hover:border-primary/50 transition-colors cursor-pointer"
      onClick={handleClick}
    >
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4 text-muted-foreground" />
            <h3 className="font-medium">
              {excuse.studentName || "Unknown Student"}
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <DoorClosed className="w-4 h-4 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              {t("common.room")} {excuse.roomName || t("common.unknown")}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(excuse.status)}
          {excuse.status === "pending" && onDelete && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-destructive delete-button"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1.5 text-muted-foreground">
            <Calendar className="w-3.5 h-3.5" />
            <span>
              {format(new Date(excuse.start_date), "MMM dd, yyyy")}
              {excuse.start_date !== excuse.end_date &&
                ` - ${format(new Date(excuse.end_date), "MMM dd, yyyy")}`}
            </span>
          </div>
          {excuse.start_date && excuse.end_date && (
            <div className="text-xs text-primary ml-5">
              Duration:{" "}
              {formatDuration(
                calculateDaysBetween(excuse.start_date, excuse.end_date)
              )}
            </div>
          )}
        </div>
        <div className="flex items-center gap-1.5 text-muted-foreground">
          <Clock className="w-3.5 h-3.5" />
          <span>
            {excuse.start_time} - {excuse.end_time}
          </span>
        </div>
      </div>

      <div className="mt-2 flex items-start gap-1.5">
        <FileText className="w-3.5 h-3.5 text-muted-foreground mt-0.5" />
        <p className="text-sm text-muted-foreground line-clamp-1">
          {excuse.reason}
        </p>
      </div>
    </div>
  );
}

function createGetStatusBadge(t: (key: string) => string) {
  return function getStatusBadge(status: string) {
    switch (status) {
      case "pending":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-50 text-yellow-700 border-yellow-200 flex items-center gap-1"
          >
            <Clock className="w-3 h-3" />
            <span>{t("teacher.excuses.pending")}</span>
          </Badge>
        );
      case "approved":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
          >
            <CheckCircle className="w-3 h-3" />
            <span>{t("teacher.excuses.approved")}</span>
          </Badge>
        );
      case "rejected":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"
          >
            <XCircle className="w-3 h-3" />
            <span>{t("teacher.excuses.rejected")}</span>
          </Badge>
        );
      default:
        return null;
    }
  };
}

function EmptyState({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
}) {
  return (
    <div className="text-center py-8">
      <div className="mx-auto mb-4">{icon}</div>
      <h3 className="text-lg font-medium mb-1">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
}

// Default export
export default ExcusesManagement;
