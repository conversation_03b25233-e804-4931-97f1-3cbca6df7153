import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * This component forces a re-render when the language changes
 * to ensure all components update their translations
 */
export const LanguageUpdater: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { i18n } = useTranslation();
  const [, setLanguage] = useState(i18n.language);

  useEffect(() => {
    // Force re-render when language changes
    const handleLanguageChange = (lng: string) => {
      console.log(`LanguageUpdater: Language changed to ${lng}, forcing re-render`);
      setLanguage(lng);
    };

    // Add event listener
    i18n.on('languageChanged', handleLanguageChange);

    // Clean up
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n]);

  return <>{children}</>;
};
