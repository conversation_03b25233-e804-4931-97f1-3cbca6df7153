-- Add QR secret key column to schools table for multi-tenant security
ALTER TABLE schools 
ADD COLUMN IF NOT EXISTS qr_secret_key TEXT;

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_schools_qr_secret_key ON schools(qr_secret_key);

-- Add comment explaining the column
COMMENT ON COLUMN schools.qr_secret_key IS 'School-specific secret key for QR code signing and validation';

-- Update RLS policies to ensure only authorized users can access secret keys
-- Only system admins (access_level = 3) can view secret keys
CREATE POLICY "Only system admins can view QR secret keys" ON schools
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.access_level = 3
    )
  );

-- Ensure existing schools get a secret key generated when they first use QR codes
-- This will be handled by the edge function when needed
