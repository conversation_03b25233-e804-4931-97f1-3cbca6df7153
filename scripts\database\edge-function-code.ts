// 🚀 QR Security Edge Function - Copy this entire code to Supabase Dashboard
// Dashboard URL: https://supabase.com/dashboard/project/wclwxrilybnzkhvqzbmy/functions

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import {
  createHash,
  createHmac,
} from "https://deno.land/std@0.168.0/node/crypto.ts";

// Environment variables with fallbacks and validation
const SUPABASE_URL = Deno.env.get("SUPABASE_URL");
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

// Validate environment variables
if (!SUPABASE_URL) {
  console.error("Missing SUPABASE_URL environment variable");
}
if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error("Missing SUPABASE_SERVICE_ROLE_KEY environment variable");
}

// Server-side secret keys (different per school for multi-tenancy)
const QR_SECRET_KEYS = new Map<string, string>();

// Initialize Supabase client with service role (with validation)
const supabase =
  SUPABASE_URL && SUPABASE_SERVICE_ROLE_KEY
    ? createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
    : null;

interface QRCodePayload {
  room_id: string;
  session_id: string;
  timestamp: string;
  expires_at: string;
  school_id: string;
  block_id: string;
  nonce: string;
  challenge: string;
}

interface SignedQRData extends QRCodePayload {
  signature: string;
}

interface GenerateQRRequest {
  room_id: string;
  school_id: string;
  block_id: string;
  expiry_seconds?: number;
  user_id: string;
}

interface ValidateQRRequest {
  qr_data: SignedQRData;
  student_id: string;
}

// Get or generate secret key for school
async function getSchoolSecretKey(schoolId: string): Promise<string> {
  if (QR_SECRET_KEYS.has(schoolId)) {
    return QR_SECRET_KEYS.get(schoolId)!;
  }

  if (!supabase) {
    throw new Error("Database connection not available");
  }

  // Check if school has existing secret in database
  const { data: school, error } = await supabase
    .from("schools")
    .select("qr_secret_key")
    .eq("id", schoolId)
    .single();

  if (error || !school?.qr_secret_key) {
    // Generate new secret key for school
    const newSecret = generateSecretKey();

    // Store in database
    await supabase
      .from("schools")
      .update({ qr_secret_key: newSecret })
      .eq("id", schoolId);

    QR_SECRET_KEYS.set(schoolId, newSecret);
    return newSecret;
  }

  QR_SECRET_KEYS.set(schoolId, school.qr_secret_key);
  return school.qr_secret_key;
}

// Generate cryptographically secure secret key
function generateSecretKey(): string {
  const array = new Uint8Array(32); // 256-bit key
  crypto.getRandomValues(array);
  return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
    ""
  );
}

// Generate cryptographically secure nonce
function generateNonce(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
    ""
  );
}

// Generate rotating challenge based on server time
function generateRotatingChallenge(secretKey: string): string {
  const now = Math.floor(Date.now() / 1000);
  const timeSlot = Math.floor(now / 30); // 30-second intervals

  const challengeData = `${timeSlot}:${secretKey}`;
  return createHash("sha256")
    .update(challengeData)
    .digest("hex")
    .substring(0, 16);
}

// Verify rotating challenge
function verifyRotatingChallenge(
  challenge: string,
  secretKey: string
): boolean {
  const now = Math.floor(Date.now() / 1000);

  // Check current and previous 10 time slots (5 minutes grace period)
  for (let offset = 0; offset <= 10; offset++) {
    const timeSlot = Math.floor(now / 30) - offset;
    const expectedChallenge = createHash("sha256")
      .update(`${timeSlot}:${secretKey}`)
      .digest("hex")
      .substring(0, 16);

    if (challenge === expectedChallenge) {
      return true;
    }
  }

  return false;
}

// Create HMAC signature for payload
function signPayload(payload: QRCodePayload, secretKey: string): string {
  const canonicalString = [
    payload.room_id,
    payload.session_id,
    payload.timestamp,
    payload.expires_at,
    payload.school_id,
    payload.block_id,
    payload.nonce,
    payload.challenge,
  ].join("|");

  return createHmac("sha256", secretKey).update(canonicalString).digest("hex");
}

// Generate secure QR code on server
async function generateSecureQRCode(
  request: GenerateQRRequest
): Promise<SignedQRData> {
  const now = new Date();
  const expirySeconds = request.expiry_seconds || 400; // Default 400 seconds
  const expiryTime = new Date(now.getTime() + expirySeconds * 1000);

  // Get school-specific secret key
  const secretKey = await getSchoolSecretKey(request.school_id);

  // Generate unique session ID and nonce
  const sessionId = crypto.randomUUID();
  const nonce = generateNonce();
  const challenge = generateRotatingChallenge(secretKey);

  const payload: QRCodePayload = {
    room_id: request.room_id,
    session_id: sessionId,
    timestamp: now.toISOString(),
    expires_at: expiryTime.toISOString(),
    school_id: request.school_id,
    block_id: request.block_id,
    nonce: nonce,
    challenge: challenge,
  };

  // Create signature
  const signature = signPayload(payload, secretKey);

  const signedData: SignedQRData = {
    ...payload,
    signature,
  };

  // Store in qr_sessions table for tracking
  await supabase.from("qr_sessions").insert({
    room_id: request.room_id,
    session_id: sessionId,
    qr_data: JSON.stringify(signedData),
    expires_at: expiryTime.toISOString(),
    school_id: request.school_id,
    block_id: request.block_id,
    generated_by: request.user_id,
  });

  // Update room with current QR code
  await supabase
    .from("rooms")
    .update({
      current_qr_code: JSON.stringify(signedData),
      qr_expiry: expiryTime.toISOString(),
    })
    .eq("id", request.room_id);

  return signedData;
}

// Validate QR code on server
async function validateQRCode(request: ValidateQRRequest): Promise<{
  isValid: boolean;
  error?: string;
  sessionId?: string;
}> {
  const qrData = request.qr_data;

  try {
    // Get school-specific secret key
    const secretKey = await getSchoolSecretKey(qrData.school_id);

    // Verify signature
    const { signature, ...payload } = qrData;
    const expectedSignature = signPayload(payload, secretKey);

    if (signature !== expectedSignature) {
      return {
        isValid: false,
        error: "Invalid signature - QR code may be tampered with",
      };
    }

    // Check expiry using server time
    const expiryTime = new Date(payload.expires_at);
    if (expiryTime.getTime() <= Date.now()) {
      return {
        isValid: false,
        error: "QR code has expired",
      };
    }

    // Check timestamp is not too old (prevent replay attacks)
    const createdTime = new Date(payload.timestamp);
    const maxAge = 10 * 60 * 1000; // 10 minutes max age
    if (Date.now() - createdTime.getTime() > maxAge) {
      return {
        isValid: false,
        error: "QR code is too old",
      };
    }

    // Check timestamp is not in the future (prevent time manipulation)
    if (createdTime.getTime() > Date.now() + 60000) {
      return {
        isValid: false,
        error: "QR code timestamp is in the future",
      };
    }

    // Verify rotating challenge
    if (!verifyRotatingChallenge(payload.challenge, secretKey)) {
      return {
        isValid: false,
        error:
          "QR code challenge verification failed - possible screenshot attack",
      };
    }

    // Check if this is the current QR for the room
    const { data: room, error: roomError } = await supabase
      .from("rooms")
      .select("current_qr_code, qr_expiry")
      .eq("id", payload.room_id)
      .single();

    if (roomError || !room.current_qr_code) {
      return {
        isValid: false,
        error: "No current QR code found for room",
      };
    }

    try {
      const currentQRData = JSON.parse(room.current_qr_code);
      if (currentQRData.session_id !== payload.session_id) {
        return {
          isValid: false,
          error: "This QR code has been replaced by a newer one",
        };
      }
    } catch (parseError) {
      return {
        isValid: false,
        error: "Error parsing current QR code",
      };
    }

    // Check for replay attacks using database
    const { data: existingUsage, error: usageError } = await supabase
      .from("attendance_records")
      .select("id")
      .eq("qr_session_id", payload.session_id)
      .eq("student_id", request.student_id)
      .limit(1);

    if (usageError) {
      console.error("Error checking replay attack:", usageError);
    } else if (existingUsage && existingUsage.length > 0) {
      return {
        isValid: false,
        error: "This QR code has already been used",
      };
    }

    return {
      isValid: true,
      sessionId: payload.session_id,
    };
  } catch (error) {
    console.error("QR validation error:", error);
    return {
      isValid: false,
      error: "Failed to verify QR code",
    };
  }
}

serve(async (req) => {
  // Enhanced CORS headers for all requests
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers":
      "authorization, x-client-info, apikey, content-type, x-requested-with",
    "Access-Control-Max-Age": "86400", // 24 hours
  };

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      status: 200,
      headers: corsHeaders,
    });
  }

  // Check if environment variables are available
  if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY || !supabase) {
    console.error("Missing required environment variables");
    return new Response(
      JSON.stringify({
        error: "Server configuration error - missing environment variables",
        details: {
          hasUrl: !!SUPABASE_URL,
          hasServiceKey: !!SUPABASE_SERVICE_ROLE_KEY,
          hasSupabase: !!supabase,
        },
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      }
    );
  }

  try {
    // Validate request method
    if (req.method !== "POST") {
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }

    // Validate authentication
    const authHeader = req.headers.get("authorization");
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: "Missing authorization header" }),
        {
          status: 401,
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders,
          },
        }
      );
    }

    // Parse request body
    let requestData;
    try {
      requestData = await req.json();
    } catch (parseError) {
      return new Response(
        JSON.stringify({ error: "Invalid JSON in request body" }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders,
          },
        }
      );
    }

    const { action, ...data } = requestData;

    // Validate action
    if (!action) {
      return new Response(
        JSON.stringify({ error: "Missing action parameter" }),
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders,
          },
        }
      );
    }

    console.log(`QR Security Function: Processing ${action} action`);

    switch (action) {
      case "generate":
        // Validate generate request
        const generateReq = data as GenerateQRRequest;
        if (
          !generateReq.room_id ||
          !generateReq.school_id ||
          !generateReq.block_id ||
          !generateReq.user_id
        ) {
          return new Response(
            JSON.stringify({
              error:
                "Missing required fields: room_id, school_id, block_id, user_id",
            }),
            {
              status: 400,
              headers: {
                "Content-Type": "application/json",
                ...corsHeaders,
              },
            }
          );
        }

        console.log(
          `Generating QR code for room ${generateReq.room_id} by user ${generateReq.user_id}`
        );
        const qrData = await generateSecureQRCode(generateReq);
        console.log(
          `Successfully generated QR code with session ${qrData.session_id}`
        );

        return new Response(JSON.stringify({ success: true, data: qrData }), {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders,
          },
        });

      case "validate":
        // Validate validate request
        const validateReq = data as ValidateQRRequest;
        if (!validateReq.qr_data || !validateReq.student_id) {
          return new Response(
            JSON.stringify({
              error: "Missing required fields: qr_data, student_id",
            }),
            {
              status: 400,
              headers: {
                "Content-Type": "application/json",
                ...corsHeaders,
              },
            }
          );
        }

        console.log(
          `Validating QR code for student ${validateReq.student_id}, session ${validateReq.qr_data.session_id}`
        );
        const validation = await validateQRCode(validateReq);
        console.log(
          `QR validation result: ${
            validation.isValid ? "VALID" : "INVALID"
          } - ${validation.error || "Success"}`
        );

        return new Response(
          JSON.stringify({ success: true, data: validation }),
          {
            status: 200,
            headers: {
              "Content-Type": "application/json",
              ...corsHeaders,
            },
          }
        );

      default:
        return new Response(JSON.stringify({ error: "Invalid action" }), {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders,
          },
        });
    }
  } catch (error) {
    console.error("QR Security Function Error:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  }
});
