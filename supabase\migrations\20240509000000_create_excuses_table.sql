-- Create excuses table
CREATE TABLE IF NOT EXISTS public.excuses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  student_id UUID NOT NULL,
  room_id UUID NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  reason TEXT NOT NULL,
  status VARCHAR NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  teacher_id UUID,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  notes TEXT,
  CONSTRAINT excuses_student_id_fkey FOREIGN KEY (student_id) REFERENCES profiles(id),
  CONSTRAINT excuses_room_id_fkey FOREIGN KEY (room_id) REFERENCES rooms(id),
  CONSTRAINT excuses_teacher_id_fkey FOREIGN KEY (teacher_id) REFERENCES profiles(id)
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_excuses_student_id ON public.excuses(student_id);
CREATE INDEX IF NOT EXISTS idx_excuses_room_id ON public.excuses(room_id);
CREATE INDEX IF NOT EXISTS idx_excuses_date ON public.excuses(date);
CREATE INDEX IF NOT EXISTS idx_excuses_status ON public.excuses(status);

-- Enable RLS
ALTER TABLE public.excuses ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Students can view their own excuses
CREATE POLICY "Students can view their own excuses" ON public.excuses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = student_id);

-- Students can insert their own excuses
CREATE POLICY "Students can insert their own excuses" ON public.excuses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = student_id AND status = 'pending');

-- Students can update their own pending excuses
CREATE POLICY "Students can update their own pending excuses" ON public.excuses
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = student_id AND status = 'pending')
  WITH CHECK (auth.uid() = student_id AND status = 'pending');

-- Teachers can view excuses for their rooms
CREATE POLICY "Teachers can view excuses for their rooms" ON public.excuses
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT teacher_id FROM rooms WHERE id = room_id
    )
  );

-- Teachers can update excuses for their rooms
CREATE POLICY "Teachers can update excuses for their rooms" ON public.excuses
  FOR UPDATE
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT teacher_id FROM rooms WHERE id = room_id
    )
  )
  WITH CHECK (
    auth.uid() IN (
      SELECT teacher_id FROM rooms WHERE id = room_id
    )
  );

-- Admins can view all excuses
CREATE POLICY "Admins can view all excuses" ON public.excuses
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

-- Admins can update all excuses
CREATE POLICY "Admins can update all excuses" ON public.excuses
  FOR UPDATE
  TO authenticated
  USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  )
  WITH CHECK (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

-- Grant permissions
GRANT ALL ON public.excuses TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_excuses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_excuses_updated_at
BEFORE UPDATE ON public.excuses
FOR EACH ROW
EXECUTE FUNCTION update_excuses_updated_at();

-- Add trigger to create notification when excuse status changes
CREATE OR REPLACE FUNCTION create_excuse_notification()
RETURNS TRIGGER AS $$
DECLARE
  student_name TEXT;
  teacher_name TEXT;
  room_name TEXT;
  notification_title TEXT;
  notification_message TEXT;
  notification_type notification_type;
BEGIN
  -- Get student name
  SELECT name INTO student_name FROM profiles WHERE id = NEW.student_id;

  -- Get room name
  SELECT name INTO room_name FROM rooms WHERE id = NEW.room_id;

  -- If status changed from pending to approved or rejected
  IF OLD.status = 'pending' AND (NEW.status = 'approved' OR NEW.status = 'rejected') THEN
    -- Get teacher name
    SELECT name INTO teacher_name FROM profiles WHERE id = NEW.teacher_id;

    -- Create notification for student
    IF NEW.status = 'approved' THEN
      notification_title := 'Excuse Approved';
      notification_message := 'Your excuse for ' || room_name || ' on ' || NEW.date || ' has been approved by ' || teacher_name;
      notification_type := 'excused';
    ELSE
      notification_title := 'Excuse Rejected';
      notification_message := 'Your excuse for ' || room_name || ' on ' || NEW.date || ' has been rejected by ' || teacher_name;
      notification_type := 'absence';
    END IF;

    INSERT INTO notifications (
      type,
      title,
      message,
      student_id,
      teacher_id,
      room_number,
      metadata
    ) VALUES (
      notification_type,
      notification_title,
      notification_message,
      NEW.student_id,
      NEW.teacher_id,
      room_name,
      jsonb_build_object(
        'excuse_id', NEW.id,
        'date', NEW.date,
        'status', NEW.status,
        'notes', NEW.notes
      )
    );
  END IF;

  -- If a new excuse is created, notify teacher
  IF TG_OP = 'INSERT' THEN
    -- Get teacher ID for the room
    SELECT teacher_id INTO NEW.teacher_id FROM rooms WHERE id = NEW.room_id;

    -- Create notification for teacher
    INSERT INTO notifications (
      type,
      title,
      message,
      student_id,
      teacher_id,
      room_number,
      metadata
    ) VALUES (
      'system',
      'New Excuse Request',
      student_name || ' has requested an excuse for ' || room_name || ' on ' || NEW.date,
      NEW.student_id,
      NEW.teacher_id,
      room_name,
      jsonb_build_object(
        'excuse_id', NEW.id,
        'date', NEW.date,
        'reason', NEW.reason
      )
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER excuse_notification_trigger
AFTER INSERT OR UPDATE OF status ON public.excuses
FOR EACH ROW
EXECUTE FUNCTION create_excuse_notification();
