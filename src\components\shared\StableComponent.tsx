import React, { memo, useEffect, useRef } from 'react';
import { useTabVisibility } from '@/hooks/useTabVisibility';

interface StableComponentProps {
  children: React.ReactNode;
  componentKey: string;
  preventReloadOnTabSwitch?: boolean;
}

// Component that maintains stability across tab switches
const StableComponent = memo(({ 
  children, 
  componentKey, 
  preventReloadOnTabSwitch = true 
}: StableComponentProps) => {
  const isVisible = useTabVisibility();
  const hasInitialized = useRef(false);
  const contentRef = useRef<React.ReactNode>(null);

  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      contentRef.current = children;
    }
  }, []);

  // Update content only when visible or if we haven't initialized yet
  useEffect(() => {
    if (isVisible || !hasInitialized.current) {
      contentRef.current = children;
    }
  }, [children, isVisible]);

  // If we want to prevent reload on tab switch, use cached content
  if (preventReloadOnTabSwitch && hasInitialized.current && contentRef.current) {
    return <>{contentRef.current}</>;
  }

  return <>{children}</>;
});

StableComponent.displayName = 'StableComponent';

export default StableComponent;
