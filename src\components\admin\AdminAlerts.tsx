import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { AlertCircle, Check, X, Bell } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useTranslation } from "react-i18next";

interface Alert {
  id: string;
  student_id: string;
  room_id: string;
  timestamp: string;
  distance_meters: number;
  student_location: {
    latitude: number;
    longitude: number;
  };
  room_location: {
    latitude: number;
    longitude: number;
  };
  status: "pending" | "reviewed" | "dismissed";
  student: {
    full_name: string;
    email: string;
  };
  room: {
    name: string;
    teacher: {
      full_name: string;
      email: string;
    };
  };
}

interface AlertResponse {
  id: string;
  student_id: string;
  room_id: string;
  timestamp: string;
  distance_meters: number;
  student_location: {
    latitude: number;
    longitude: number;
  };
  room_location: {
    latitude: number;
    longitude: number;
  };
  status: "pending" | "reviewed" | "dismissed";
  student_info: {
    full_name: string;
    email: string;
  };
  room_info: {
    name: string;
    teacher_info: {
      full_name: string;
      email: string;
    };
  };
}

// Utility function to format relative time with internationalization
const formatRelativeTime = (date: Date, t: any) => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return t("notifications.timeAgo.seconds", { count: diffInSeconds });
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return t("notifications.timeAgo.minutes", { count: minutes });
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return t("notifications.timeAgo.hours", { count: hours });
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400);
    return t("notifications.timeAgo.days", { count: days });
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return t("notifications.timeAgo.months", { count: months });
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return t("notifications.timeAgo.years", { count: years });
  }
};

export default function AdminAlerts() {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("pending");
  const { toast } = useToast();
  const { profile } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    fetchAlerts();
    // Set up real-time subscription
    const subscription = supabase
      .channel("admin_alerts")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "attendance_alerts",
        },
        () => {
          fetchAlerts();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [activeTab]);

  const fetchAlerts = async () => {
    try {
      setLoading(true);

      // First get the alerts
      const { data: alertsData, error: alertsError } = await supabase
        .from("attendance_alerts")
        .select(
          `
          id,
          student_id,
          room_id,
          timestamp,
          distance_meters,
          student_location,
          room_location,
          status
        `
        )
        .eq("status", activeTab)
        .order("timestamp", { ascending: false });

      if (alertsError) {
        console.error("Error fetching alerts:", alertsError);
        throw new Error(alertsError.message);
      }

      if (!alertsData || alertsData.length === 0) {
        setAlerts([]);
        return;
      }

      // Get student profiles
      const { data: studentProfiles, error: studentError } = await supabase
        .from("profiles")
        .select("user_id, full_name, email")
        .in(
          "user_id",
          alertsData.map((alert) => alert.student_id)
        );

      if (studentError) throw studentError;

      // Get room details with teacher profiles
      const { data: roomDetails, error: roomError } = await supabase
        .from("rooms")
        .select(
          `
          id,
          name,
          teacher:profiles!teacher_id (
            full_name,
            email
          )
        `
        )
        .in(
          "id",
          alertsData.map((alert) => alert.room_id)
        );

      if (roomError) throw roomError;

      // Transform the data
      const transformedData = alertsData.map((alert) => {
        const studentProfile = studentProfiles?.find(
          (p) => p.user_id === alert.student_id
        );
        const roomDetail = roomDetails?.find((r) => r.id === alert.room_id);

        return {
          id: alert.id,
          student_id: alert.student_id,
          room_id: alert.room_id,
          timestamp: alert.timestamp,
          distance_meters: alert.distance_meters,
          student_location: alert.student_location,
          room_location: alert.room_location,
          status: alert.status,
          student: {
            full_name: studentProfile?.full_name || "Unknown",
            email: studentProfile?.email || "Unknown",
          },
          room: {
            name: roomDetail?.name || "Unknown Room",
            teacher: {
              full_name: roomDetail?.teacher?.[0]?.full_name || "Unknown",
              email: roomDetail?.teacher?.[0]?.email || "Unknown",
            },
          },
        } as Alert;
      });

      setAlerts(transformedData);
    } catch (error) {
      console.error("Error fetching alerts:", error);
      let errorMessage = "Failed to fetch alerts. ";

      if (error instanceof Error) {
        if (error.message.includes("permission denied")) {
          errorMessage += "You do not have permission to view alerts.";
        } else if (error.message.includes("network")) {
          errorMessage += "Network error. Please check your connection.";
        } else if (error.message.includes("timeout")) {
          errorMessage += "Request timed out. Please try again.";
        } else {
          errorMessage += error.message;
        }
      }

      toast({
        title: t("admin.alerts.error"),
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAlertAction = async (
    alertId: string,
    action: "reviewed" | "dismissed"
  ) => {
    try {
      const { error } = await supabase
        .from("attendance_alerts")
        .update({
          status: action,
          reviewed_by: profile?.id,
          reviewed_at: new Date().toISOString(),
        })
        .eq("id", alertId);

      if (error) {
        console.error("Database error:", error);
        throw new Error(error.message);
      }

      toast({
        title: t("admin.alerts.success"),
        description:
          action === "reviewed"
            ? t("admin.alerts.alertApproved")
            : t("admin.alerts.alertDismissed"),
        variant: "default",
      });

      fetchAlerts();
    } catch (error) {
      console.error("Error updating alert:", error);
      let errorMessage = "Failed to update alert status. ";

      if (error instanceof Error) {
        if (error.message.includes("permission denied")) {
          errorMessage += "You do not have permission to update alerts.";
        } else if (error.message.includes("network")) {
          errorMessage += "Network error. Please check your connection.";
        } else {
          errorMessage += "Please try again later.";
        }
      }

      toast({
        title: t("admin.alerts.error"),
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const getGoogleMapsUrl = (location: {
    latitude: number;
    longitude: number;
  }) => {
    return `https://www.google.com/maps?q=${location.latitude},${location.longitude}`;
  };

  const getStatusCount = (status: string) => {
    return alerts.filter((alert) => alert.status === status).length;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5 text-primary" />
          {t("admin.alerts.title")}
        </CardTitle>
        <CardDescription>{t("admin.alerts.description")}</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pending" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="pending" className="flex items-center gap-2">
              {t("admin.alerts.pending")}
              {getStatusCount("pending") > 0 && (
                <span className="bg-yellow-500 text-white text-xs px-2 py-0.5 rounded-full">
                  {getStatusCount("pending")}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="reviewed" className="flex items-center gap-2">
              {t("admin.alerts.resolved")}
              {getStatusCount("reviewed") > 0 && (
                <span className="bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full">
                  {getStatusCount("reviewed")}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="dismissed" className="flex items-center gap-2">
              {t("admin.alerts.dismissed")}
              {getStatusCount("dismissed") > 0 && (
                <span className="bg-gray-500 text-white text-xs px-2 py-0.5 rounded-full">
                  {getStatusCount("dismissed")}
                </span>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab}>
            {loading ? (
              <div className="text-center py-4">
                {t("admin.alerts.loadingAlerts")}
              </div>
            ) : alerts.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                {t("admin.alerts.noAlerts", { status: activeTab })}
              </div>
            ) : (
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <Card key={alert.id} className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">
                            {alert.student.full_name}
                          </h4>
                          <span className="text-sm text-muted-foreground">
                            ({alert.student.email})
                          </span>
                        </div>
                        <p className="text-sm mt-2">
                          {t("admin.alerts.attemptedToMarkAttendance")}{" "}
                          <strong>{alert.room.name}</strong>
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {t("admin.alerts.teacher")}:{" "}
                          {alert.room.teacher.full_name}
                        </p>
                        <p className="text-sm text-yellow-600">
                          {t("admin.alerts.distanceFromRoom")}:{" "}
                          {Math.round(alert.distance_meters)}{" "}
                          {t("admin.alerts.meters")}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {formatRelativeTime(new Date(alert.timestamp), t)}
                        </p>
                        <div className="mt-2 space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              window.open(
                                getGoogleMapsUrl(alert.student_location),
                                "_blank"
                              )
                            }
                          >
                            {t("admin.alerts.viewStudentLocation")}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              window.open(
                                getGoogleMapsUrl(alert.room_location),
                                "_blank"
                              )
                            }
                          >
                            {t("admin.alerts.viewRoomLocation")}
                          </Button>
                        </div>
                      </div>
                      {activeTab === "pending" && (
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleAlertAction(alert.id, "reviewed")
                            }
                          >
                            <Check className="h-4 w-4 mr-1" />
                            {t("admin.alerts.approve")}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleAlertAction(alert.id, "dismissed")
                            }
                          >
                            <X className="h-4 w-4 mr-1" />
                            {t("admin.alerts.dismiss")}
                          </Button>
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
