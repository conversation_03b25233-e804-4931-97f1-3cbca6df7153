-- Update the get_public_login_message function to handle school-specific messages
CREATE OR REPLACE FUNCTION public.get_public_login_message()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  message TEXT;
  school_id UUID;
  school_param TEXT;
BEGIN
  -- Get the school parameter from the request if available
  school_param := current_setting('request.headers', true)::json->>'x-school-id';
  
  -- Try to convert the school parameter to UUID if it exists
  BEGIN
    IF school_param IS NOT NULL AND school_param != '' THEN
      school_id := school_param::UUID;
    END IF;
  EXCEPTION WHEN OTHERS THEN
    -- If conversion fails, set school_id to NULL
    school_id := NULL;
  END;
  
  -- First check for school-specific override if school_id is provided
  IF school_id IS NOT NULL THEN
    -- Check for a school-specific override in system_settings_overrides
    SELECT setting_value->>'value' INTO message
    FROM system_settings_overrides
    WHERE setting_name = 'custom_login_message'
    AND school_id = school_id
    AND override_enabled = true
    LIMIT 1;
    
    -- If found, return it
    IF message IS NOT NULL AND trim(message) != '' THEN
      RETURN message;
    END IF;
    
    -- Check for a school-specific setting in school_settings
    SELECT custom_login_message INTO message
    FROM school_settings
    WHERE school_id = school_id
    LIMIT 1;
    
    -- If found, return it
    IF message IS NOT NULL AND trim(message) != '' THEN
      RETURN message;
    END IF;
  END IF;
  
  -- If no school-specific message or no school_id provided, check for global override
  SELECT setting_value->>'value' INTO message
  FROM system_settings_overrides
  WHERE setting_name = 'custom_login_message'
  AND applies_to_all = true
  AND override_enabled = true
  LIMIT 1;
  
  -- Return the message (will be NULL if none found)
  RETURN message;
END;
$$;
