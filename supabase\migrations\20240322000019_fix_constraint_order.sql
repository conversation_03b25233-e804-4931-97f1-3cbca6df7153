-- First drop the notifications table and its dependencies
DROP TABLE IF EXISTS notifications CASCAD<PERSON>;
DROP TYPE IF EXISTS notification_type CASCADE;
DROP TYPE IF EXISTS alert_category CASCADE;

-- Now we can safely modify the profiles table constraints
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_user_id_key;
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_user_id_unique;
ALTER TABLE profiles ADD CONSTRAINT profiles_user_id_unique UNIQUE (user_id);

-- Create notification type enum
CREATE TYPE notification_type AS ENUM (
  'distance_alert',
  'system_alert',
  'attendance_alert',
  'attendance',
  'absence',
  'late',
  'excused',
  'system'
);

-- Create alert category enum
CREATE TYPE alert_category AS ENUM (
  'fraud_detection',  -- For admin view
  'distance_alert',   -- For teacher view
  'general'          -- For other alerts
);

-- Create notifications table with all required columns
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type notification_type NOT NULL,
  category alert_category DEFAULT 'general',
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  student_id UUID,
  teacher_id UUID,
  student_location POINT,
  distance_meters DOUBLE PRECISION,
  room_number TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  teacher_read_at TIMESTAMPTZ,
  admin_read_at TIMESTAMPTZ,
  read BOOLEAN DEFAULT false,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb,
  severity TEXT DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'investigating', 'resolved', 'dismissed')),
  resolution_notes TEXT,
  resolved_by UUID,
  resolved_at TIMESTAMPTZ
);

-- Now add the foreign key constraints
ALTER TABLE notifications 
ADD CONSTRAINT notifications_student_id_fkey 
FOREIGN KEY (student_id) REFERENCES profiles(user_id);

ALTER TABLE notifications 
ADD CONSTRAINT notifications_teacher_id_fkey 
FOREIGN KEY (teacher_id) REFERENCES profiles(user_id);

ALTER TABLE notifications 
ADD CONSTRAINT notifications_resolved_by_fkey 
FOREIGN KEY (resolved_by) REFERENCES profiles(user_id);

-- Create indexes for better query performance
CREATE INDEX idx_notifications_student_id ON notifications(student_id);
CREATE INDEX idx_notifications_teacher_id ON notifications(teacher_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_category ON notifications(category);
CREATE INDEX idx_notifications_read ON notifications(read);
CREATE INDEX idx_notifications_timestamp ON notifications(timestamp);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_severity ON notifications(severity);

-- Enable RLS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Create separate policies for different operations

-- Read access policy
CREATE POLICY "Enable notifications read access"
ON notifications FOR SELECT
TO authenticated
USING (
  -- Students can view their own notifications
  student_id = auth.uid()
  OR
  -- Teachers can view notifications for their students (except fraud_detection category)
  (teacher_id = auth.uid() AND category != 'fraud_detection')
  OR
  -- Admins can view all notifications
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Create separate update policies for different roles

-- Admin update policy
CREATE POLICY "Enable admin notifications update"
ON notifications FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Teacher update policy (only for read status and non-fraud alerts)
CREATE POLICY "Enable teacher notifications update"
ON notifications FOR UPDATE
TO authenticated
USING (
  teacher_id = auth.uid() AND category != 'fraud_detection'
)
WITH CHECK (
  teacher_id = auth.uid() AND
  category != 'fraud_detection' AND
  (
    -- Only allow updating teacher_read_at and status
    (teacher_read_at IS NOT NULL AND read = true) OR
    (status IN ('resolved', 'dismissed') AND resolved_by = auth.uid())
  )
);

-- Delete access policy
CREATE POLICY "Enable notifications delete access"
ON notifications FOR DELETE
TO authenticated
USING (
  -- Teachers can delete their own non-fraud notifications
  (teacher_id = auth.uid() AND category != 'fraud_detection')
  OR
  -- Admins can delete any notification
  EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  )
);

-- Create function to handle distance alerts
CREATE OR REPLACE FUNCTION handle_distance_alert(
  student_id UUID,
  student_lat DOUBLE PRECISION,
  student_lng DOUBLE PRECISION,
  room_lat DOUBLE PRECISION,
  room_lng DOUBLE PRECISION,
  max_distance_meters DOUBLE PRECISION,
  room_number TEXT
)
RETURNS TABLE (
  is_within_radius BOOLEAN,
  distance DOUBLE PRECISION,
  alert_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  calculated_distance DOUBLE PRECISION;
  student_name TEXT;
  teacher_id UUID;
  alert_id UUID;
  alert_severity TEXT;
  alert_category alert_category;
BEGIN
  -- Calculate distance using PostGIS
  calculated_distance := ST_DistanceSphere(
    ST_MakePoint(student_lng, student_lat),
    ST_MakePoint(room_lng, room_lat)
  );

  -- Get student details
  SELECT name INTO student_name
  FROM profiles
  WHERE id = student_id;

  -- Get teacher ID from room number
  SELECT teacher_id INTO teacher_id
  FROM rooms
  WHERE room_number = $7;

  -- Determine alert severity and category based on distance
  IF calculated_distance > (max_distance_meters * 2) THEN
    alert_severity := 'high';
    alert_category := 'fraud_detection';
  ELSIF calculated_distance > (max_distance_meters * 1.5) THEN
    alert_severity := 'medium';
    alert_category := 'distance_alert';
  ELSE
    alert_severity := 'low';
    alert_category := 'distance_alert';
  END IF;

  -- Create alert
  INSERT INTO notifications (
    type,
    category,
    title,
    message,
    student_id,
    teacher_id,
    student_location,
    distance_meters,
    room_number,
    metadata,
    severity,
    status
  )
  VALUES (
    'distance_alert',
    alert_category,
    CASE 
      WHEN alert_severity = 'high' THEN 'Potential Attendance Fraud Detected'
      ELSE 'Distance Violation Alert'
    END,
    CASE 
      WHEN alert_severity = 'high' THEN 
        format('Student %s attempted to mark attendance from %s meters away (over 2x limit). This has been flagged for fraud investigation.', 
               student_name, round(calculated_distance::numeric, 2))
      ELSE 
        format('Student %s attempted to mark attendance from %s meters away from room %s.', 
               student_name, round(calculated_distance::numeric, 2), room_number)
    END,
    student_id,
    teacher_id,
    ST_MakePoint(student_lat, student_lng),
    calculated_distance,
    room_number,
    jsonb_build_object(
      'student_name', student_name,
      'room_number', room_number,
      'exceeded_by_meters', calculated_distance - max_distance_meters,
      'max_allowed_distance', max_distance_meters,
      'student_location', jsonb_build_object(
        'type', 'Point',
        'coordinates', array[student_lng, student_lat]
      ),
      'room_location', jsonb_build_object(
        'type', 'Point',
        'coordinates', array[room_lng, room_lat]
      )
    ),
    alert_severity,
    'pending'
  )
  RETURNING id INTO alert_id;

  RETURN QUERY
  SELECT 
    calculated_distance <= max_distance_meters,
    calculated_distance,
    alert_id;
END;
$$;

-- Grant permissions
GRANT ALL ON notifications TO authenticated;

-- Update attendance_records table to properly handle GeoJSON location data
ALTER TABLE attendance_records
DROP CONSTRAINT IF EXISTS attendance_records_location_check;

ALTER TABLE attendance_records
ADD CONSTRAINT attendance_records_location_check CHECK (
  jsonb_typeof(location) = 'object' AND
  location ? 'type' AND
  location->>'type' = 'Point' AND
  location ? 'coordinates' AND
  jsonb_typeof(location->'coordinates') = 'array' AND
  jsonb_array_length(location->'coordinates') = 2
); 