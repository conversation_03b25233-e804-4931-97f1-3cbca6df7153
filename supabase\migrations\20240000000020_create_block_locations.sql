-- Create block_locations table if it doesn't exist
CREATE OR REPLACE FUNCTION create_block_locations_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the table already exists
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'block_locations') THEN
    -- Create the block_locations table
    CREATE TABLE public.block_locations (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      block_id UUID NOT NULL REFERENCES public.blocks(id) ON DELETE CASCADE,
      latitude DECIMAL(10, 8) NOT NULL,
      longitude DECIMAL(11, 8) NOT NULL,
      radius_meters INTEGER NOT NULL DEFAULT 50,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
      UNIQUE(block_id)
    );

    -- Add RLS policies
    ALTER TABLE public.block_locations ENABLE ROW LEVEL SECURITY;

    -- Teachers can read block_locations
    CREATE POLICY "Teachers can read block_locations"
      ON public.block_locations
      FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid()
          AND profiles.role = 'teacher'
        )
      );

    -- Teachers can insert/update block_locations
    CREATE POLICY "Teachers can insert/update block_locations"
      ON public.block_locations
      FOR INSERT
      TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid()
          AND profiles.role = 'teacher'
        )
      );

    CREATE POLICY "Teachers can update block_locations"
      ON public.block_locations
      FOR UPDATE
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid()
          AND profiles.role = 'teacher'
        )
      );

    -- Students can read block_locations
    CREATE POLICY "Students can read block_locations"
      ON public.block_locations
      FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE profiles.id = auth.uid()
          AND profiles.role = 'student'
        )
      );

    -- Add triggers for updated_at
    CREATE TRIGGER set_block_locations_updated_at
      BEFORE UPDATE ON public.block_locations
      FOR EACH ROW
      EXECUTE FUNCTION public.set_current_timestamp_updated_at();

    -- Grant permissions
    GRANT ALL ON public.block_locations TO authenticated;
    GRANT USAGE ON SCHEMA public TO authenticated;

    RAISE NOTICE 'Created block_locations table';
  ELSE
    RAISE NOTICE 'block_locations table already exists';
  END IF;
END;
$$;

-- Execute the function to create the table
SELECT create_block_locations_table();

-- Create function to get block location by room_id
CREATE OR REPLACE FUNCTION get_block_location_by_room(room_id UUID)
RETURNS TABLE (
  latitude DECIMAL,
  longitude DECIMAL,
  radius_meters INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    bl.latitude,
    bl.longitude,
    bl.radius_meters
  FROM 
    rooms r
    JOIN blocks b ON r.block_id = b.id
    JOIN block_locations bl ON b.id = bl.block_id
  WHERE 
    r.id = room_id;
END;
$$;

-- Create function to verify attendance location using block location
CREATE OR REPLACE FUNCTION verify_attendance_location_by_block(
    student_lat DECIMAL,
    student_lon DECIMAL,
    room_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    block_location RECORD;
    distance DECIMAL;
BEGIN
    -- Get block location for the room
    SELECT * INTO block_location FROM get_block_location_by_room(room_id);
    
    IF block_location IS NULL THEN
        -- If no block location is set, fall back to room location
        SELECT * INTO block_location
        FROM room_locations
        WHERE room_locations.room_id = room_id;
        
        IF block_location IS NULL THEN
            RETURN TRUE; -- If no location set for room or block, allow attendance
        END IF;
    END IF;
    
    -- Calculate distance
    distance := calculate_distance(
        student_lat,
        student_lon,
        block_location.latitude,
        block_location.longitude
    );
    
    -- Return true if within radius
    RETURN distance <= block_location.radius_meters;
END;
$$ LANGUAGE plpgsql;
