import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import EnhancedFraudDetection from "@/components/admin/EnhancedFraudDetection";
import AdminAlerts from "@/components/admin/AdminAlerts";

export default function AdminDashboard() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Admin Dashboard</h1>

      <Tabs defaultValue="alerts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="fraud">Fraud Detection</TabsTrigger>
        </TabsList>

        <TabsContent value="alerts">
          <AdminAlerts />
        </TabsContent>

        <TabsContent value="fraud">
          <EnhancedFraudDetection />
        </TabsContent>
      </Tabs>
    </div>
  );
}
