import { supabase } from "@/lib/supabase";

export const createParentContactsTable = async (): Promise<boolean> => {
  try {
    // Check if the table already exists
    const { data: existingTable, error: checkError } = await supabase
      .from("parent_contacts")
      .select("id")
      .limit(1);

    // If we can query the table, it exists
    if (!checkError) {
      return true;
    }

    // Create the parent_contacts table
    const { error: createError } = await supabase.rpc("execute_sql", {
      sql: `
        CREATE TABLE IF NOT EXISTS parent_contacts (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          parent_name TEXT NOT NULL,
          email TEXT,
          phone TEXT,
          notification_method TEXT NOT NULL DEFAULT 'email',
          notifications_enabled BOOLEAN NOT NULL DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          CONSTRAINT notification_method_check CHECK (notification_method IN ('email', 'sms', 'both', 'none')),
          CONSTRAINT contact_info_check CHECK (email IS NOT NULL OR phone IS NOT NULL)
        );

        CREATE INDEX IF NOT EXISTS parent_contacts_student_id_idx ON parent_contacts(student_id);
      `,
    });

    if (createError) {
      console.error("Error creating parent_contacts table:", createError);
      return false;
    }

    // Create the notification_logs table
    const { error: logsError } = await supabase.rpc("execute_sql", {
      sql: `
        CREATE TABLE IF NOT EXISTS notification_logs (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          student_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          excuse_id UUID NOT NULL,
          notification_type TEXT NOT NULL,
          recipient TEXT NOT NULL,
          success BOOLEAN NOT NULL DEFAULT true,
          error_message TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        CREATE INDEX IF NOT EXISTS notification_logs_student_id_idx ON notification_logs(student_id);
        CREATE INDEX IF NOT EXISTS notification_logs_excuse_id_idx ON notification_logs(excuse_id);
      `,
    });

    if (logsError) {
      console.error("Error creating notification_logs table:", logsError);
      return false;
    }

    // Add RLS policies for parent_contacts table
    const { error: parentRlsError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Enable RLS on parent_contacts table
        ALTER TABLE parent_contacts ENABLE ROW LEVEL SECURITY;

        -- Create policies for parent_contacts table
        DROP POLICY IF EXISTS "Students can view their own parent contacts" ON parent_contacts;
        CREATE POLICY "Students can view their own parent contacts"
          ON parent_contacts FOR SELECT
          USING (student_id IN (
            SELECT id FROM profiles WHERE user_id = auth.uid()
          ));

        DROP POLICY IF EXISTS "Teachers can view parent contacts for their students" ON parent_contacts;
        CREATE POLICY "Teachers can view parent contacts for their students"
          ON parent_contacts FOR SELECT
          USING (EXISTS (
            SELECT 1 FROM profiles
            WHERE user_id = auth.uid() AND role = 'teacher'
          ));

        DROP POLICY IF EXISTS "Admins can manage all parent contacts" ON parent_contacts;
        CREATE POLICY "Admins can manage all parent contacts"
          ON parent_contacts FOR ALL
          USING (EXISTS (
            SELECT 1 FROM profiles
            WHERE user_id = auth.uid() AND role = 'admin'
          ));
      `,
    });

    if (parentRlsError) {
      console.error(
        "Error setting up RLS for parent_contacts:",
        parentRlsError
      );
      // Continue anyway, as the table was created
    }

    // Add RLS policies for notification_logs table
    const { error: logsRlsError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Enable RLS on notification_logs table
        ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;

        -- Create policies for notification_logs table
        DROP POLICY IF EXISTS "Admins can view all notification logs" ON notification_logs;
        CREATE POLICY "Admins can view all notification logs"
          ON notification_logs FOR SELECT
          USING (EXISTS (
            SELECT 1 FROM profiles
            WHERE user_id = auth.uid() AND role = 'admin'
          ));

        DROP POLICY IF EXISTS "Teachers can view notification logs" ON notification_logs;
        CREATE POLICY "Teachers can view notification logs"
          ON notification_logs FOR SELECT
          USING (EXISTS (
            SELECT 1 FROM profiles
            WHERE user_id = auth.uid() AND role = 'teacher'
          ));
      `,
    });

    if (logsRlsError) {
      console.error(
        "Error setting up RLS for notification_logs:",
        logsRlsError
      );
      // Continue anyway, as the table was created
    }

    return true;
  } catch (error) {
    console.error("Error in parent contacts migration:", error);
    return false;
  }
};
