import { supabase } from "@/lib/supabase";

/**
 * Migration to create a function that checks if a table exists
 * @returns Promise that resolves to true if migration was successful
 */
export const createTableExistsFunction = async (): Promise<boolean> => {
  try {
    console.log("Running table exists function migration...");

    // Create the function to check if a table exists
    const { error } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create function to check if a table exists
        CREATE OR REPLACE FUNCTION check_table_exists(table_name TEXT)
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        AS $$
        DECLARE
          table_exists BOOLEAN;
        BEGIN
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public'
            AND table_name = $1
          ) INTO table_exists;
          
          RETURN table_exists;
        END;
        $$;
      `,
    });

    if (error) {
      console.error("Error creating table_exists function:", error);
      return false;
    }

    console.log("Table exists function migration completed successfully.");
    return true;
  } catch (error) {
    console.error("Error in table exists function migration:", error);
    return false;
  }
};
