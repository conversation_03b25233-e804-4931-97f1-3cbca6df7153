-- CRITICAL FIX: Re-enable R<PERSON> and proper school isolation for profiles table
-- This migration fixes the critical security vulnerability where <PERSON><PERSON> was disabled

-- Drop all existing policies on profiles table
DROP POLICY IF EXISTS "Users can manage their own profile" ON profiles;
DROP POLICY IF EXISTS "Teachers can view student profiles in their school" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all profiles" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can create profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update any profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can delete profiles" ON profiles;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Allow reading profiles for alerts" ON profiles;
DROP POLICY IF EXISTS "Enable read access for users" ON profiles;
DROP POLICY IF EXISTS "Enable update access for users" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can update all profiles" ON profiles;

-- RE-ENABLE RLS on profiles table (CRITICAL SECURITY FIX)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create school-isolated policies for profiles table

-- 1. Users can manage their own profile
CREATE POLICY "Users can manage their own profile"
ON profiles
FOR ALL
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- 2. School admins can view/manage profiles in their school only
CREATE POLICY "School admins can manage profiles in their school"
ON profiles
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin (access_level 1 or 2)
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level IN (1, 2)
    AND admin.school_id = profiles.school_id
  )
)
WITH CHECK (
  -- Check if the current user is a school admin in the same school
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level IN (1, 2)
    AND admin.school_id = profiles.school_id
  )
);

-- 3. System admins can manage all profiles (access_level 3)
CREATE POLICY "System admins can manage all profiles"
ON profiles
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- 4. Teachers can view student profiles in their school
CREATE POLICY "Teachers can view student profiles in their school"
ON profiles
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the profile being accessed belongs to a student in the same school
  role = 'student'
  AND
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- 5. Students can view other student profiles in their school (for collaboration)
CREATE POLICY "Students can view other students in their school"
ON profiles
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
  )
  AND
  -- Check if the profile being accessed belongs to another student in the same school
  role = 'student'
  AND
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Update existing profiles to ensure they have proper school_id
-- This is critical for the RLS policies to work correctly
UPDATE profiles 
SET school_id = (
  SELECT id FROM schools 
  WHERE schools.name = 'Default School' 
  LIMIT 1
)
WHERE school_id IS NULL;

-- If no default school exists, create one
INSERT INTO schools (id, name, is_active)
SELECT 
  gen_random_uuid(),
  'Default School',
  true
WHERE NOT EXISTS (SELECT 1 FROM schools WHERE name = 'Default School');

-- Update profiles without school_id to use the default school
UPDATE profiles 
SET school_id = (
  SELECT id FROM schools 
  WHERE schools.name = 'Default School' 
  LIMIT 1
)
WHERE school_id IS NULL;

-- Add NOT NULL constraint to school_id after ensuring all profiles have it
ALTER TABLE profiles 
ALTER COLUMN school_id SET NOT NULL;

-- Create index for better performance on school-based queries
CREATE INDEX IF NOT EXISTS idx_profiles_school_id ON profiles(school_id);
CREATE INDEX IF NOT EXISTS idx_profiles_role_school_id ON profiles(role, school_id);

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON profiles TO authenticated;

-- Add comment explaining the security model
COMMENT ON TABLE profiles IS 'User profiles with school-based isolation. RLS policies ensure users can only access profiles within their school (except system admins).';
