import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import {
  CheckCircle2,
  Circle,
  ArrowRight,
  ArrowLeft,
  School,
  Settings,
  Users,
  MapPin,
  Palette,
  Upload,
  Loader2,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import {
  AuditActionType,
  AuditEntityType,
  logAuditEvent,
} from "@/lib/utils/audit-logger";

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  isCompleted: boolean;
}

export default function SchoolOnboarding() {
  const { toast } = useToast();
  const { profile } = useAuth();
  const { currentSchool, refreshSchool } = useSchool();

  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isCompleting, setIsCompleting] = useState(false);
  const [progress, setProgress] = useState(0);

  // School information
  const [schoolName, setSchoolName] = useState("");
  const [address, setAddress] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [zip, setZip] = useState("");
  const [country, setCountry] = useState("");
  const [phone, setPhone] = useState("");
  const [email, setEmail] = useState("");
  const [website, setWebsite] = useState("");

  // Branding
  const [primaryColor, setPrimaryColor] = useState("#4f46e5");
  const [secondaryColor, setSecondaryColor] = useState("#f97316");
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState("");

  // Settings
  const [emailNotificationsEnabled, setEmailNotificationsEnabled] =
    useState(true);
  const [locationVerificationEnabled, setLocationVerificationEnabled] =
    useState(true);
  const [biometricVerificationEnabled, setBiometricVerificationEnabled] =
    useState(false);

  // Steps tracking
  const [steps, setSteps] = useState<OnboardingStep[]>([
    {
      id: "school-info",
      title: "School Information",
      description: "Basic details about your school",
      icon: <School className="h-5 w-5" />,
      isCompleted: false,
    },
    {
      id: "branding",
      title: "School Branding",
      description: "Customize your school's appearance",
      icon: <Palette className="h-5 w-5" />,
      isCompleted: false,
    },
    {
      id: "settings",
      title: "System Settings",
      description: "Configure attendance settings",
      icon: <Settings className="h-5 w-5" />,
      isCompleted: false,
    },
    {
      id: "location",
      title: "Location Setup",
      description: "Set up your school's location",
      icon: <MapPin className="h-5 w-5" />,
      isCompleted: false,
    },
    {
      id: "users",
      title: "Add Users",
      description: "Invite teachers and students",
      icon: <Users className="h-5 w-5" />,
      isCompleted: false,
    },
  ]);

  useEffect(() => {
    if (currentSchool) {
      // Load school information
      setSchoolName(currentSchool.name || "");
      setAddress(currentSchool.address || "");
      setCity(currentSchool.city || "");
      setState(currentSchool.state || "");
      setZip(currentSchool.zip || "");
      setCountry(currentSchool.country || "");
      setPhone(currentSchool.phone || "");
      setEmail(currentSchool.email || "");
      setWebsite(currentSchool.website || "");

      // Load branding
      setPrimaryColor(currentSchool.primaryColor || "#4f46e5");
      setSecondaryColor(currentSchool.secondaryColor || "#f97316");

      // Check onboarding progress
      checkOnboardingProgress();
    }
  }, [currentSchool]);

  useEffect(() => {
    // Calculate progress percentage
    const completedSteps = steps.filter((step) => step.isCompleted).length;
    const progressPercentage = (completedSteps / steps.length) * 100;
    setProgress(progressPercentage);
  }, [steps]);

  const checkOnboardingProgress = async () => {
    if (!currentSchool?.id) return;

    try {
      // Check if school info is complete
      const hasSchoolInfo = Boolean(
        currentSchool.name && currentSchool.address && currentSchool.city
      );

      // Check if branding is complete
      const hasBranding = Boolean(
        currentSchool.primaryColor && currentSchool.secondaryColor
      );

      // Check if settings exist
      const { data: settingsData } = await supabase
        .from("school_settings")
        .select("*")
        .eq("school_id", currentSchool.id)
        .single();

      const hasSettings = Boolean(settingsData);

      // Check if locations exist
      const { data: locationsData, count: locationsCount } = await supabase
        .from("room_locations")
        .select("*", { count: "exact" })
        .eq("school_id", currentSchool.id)
        .limit(1);

      const hasLocations = Boolean(locationsCount && locationsCount > 0);

      // Check if users exist
      const { count: usersCount } = await supabase
        .from("profiles")
        .select("*", { count: "exact" })
        .eq("school_id", currentSchool.id)
        .neq("user_id", profile?.id) // Exclude current user
        .limit(1);

      const hasUsers = Boolean(usersCount && usersCount > 0);

      // Update steps
      setSteps((prev) =>
        prev.map((step, index) => {
          if (index === 0) return { ...step, isCompleted: hasSchoolInfo };
          if (index === 1) return { ...step, isCompleted: hasBranding };
          if (index === 2) return { ...step, isCompleted: hasSettings };
          if (index === 3) return { ...step, isCompleted: hasLocations };
          if (index === 4) return { ...step, isCompleted: hasUsers };
          return step;
        })
      );

      // Set current step to first incomplete step
      const firstIncompleteIndex = [
        hasSchoolInfo,
        hasBranding,
        hasSettings,
        hasLocations,
        hasUsers,
      ].findIndex((complete) => !complete);

      if (firstIncompleteIndex !== -1) {
        setCurrentStepIndex(firstIncompleteIndex);
      }
    } catch (error) {
      console.error("Error checking onboarding progress:", error);
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setLogoFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setLogoPreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const uploadLogo = async (): Promise<string | null> => {
    if (!logoFile || !currentSchool?.id) return null;

    try {
      // Create a unique file path
      const fileExt = logoFile.name.split(".").pop();
      const filePath = `school-logos/${
        currentSchool.id
      }/${Date.now()}.${fileExt}`;

      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from("public")
        .upload(filePath, logoFile);

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data } = supabase.storage.from("public").getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error("Error uploading logo:", error);
      return null;
    }
  };

  const saveSchoolInfo = async () => {
    if (!currentSchool?.id) return false;

    try {
      const { error } = await supabase
        .from("schools")
        .update({
          name: schoolName,
          address,
          city,
          state,
          zip,
          country,
          phone,
          email,
          website,
          updated_at: new Date().toISOString(),
        })
        .eq("id", currentSchool.id);

      if (error) throw error;

      // Log audit event
      await logAuditEvent({
        school_id: currentSchool.id,
        user_id: profile?.id,
        action_type: AuditActionType.SCHOOL_UPDATED,
        entity_type: AuditEntityType.SCHOOL,
        entity_id: currentSchool.id,
        details: { step: "school-info", completed: true },
      });

      return true;
    } catch (error) {
      console.error("Error saving school info:", error);
      return false;
    }
  };

  const saveBranding = async () => {
    if (!currentSchool?.id) return false;

    try {
      // Upload logo if provided
      let logoUrl = null;
      if (logoFile) {
        logoUrl = await uploadLogo();
      }

      // Update school record
      const { error } = await supabase
        .from("schools")
        .update({
          primary_color: primaryColor,
          secondary_color: secondaryColor,
          logo_url: logoUrl || currentSchool.logoUrl,
          updated_at: new Date().toISOString(),
        })
        .eq("id", currentSchool.id);

      if (error) throw error;

      // Log audit event
      await logAuditEvent({
        school_id: currentSchool.id,
        user_id: profile?.id,
        action_type: AuditActionType.SCHOOL_UPDATED,
        entity_type: AuditEntityType.SCHOOL,
        entity_id: currentSchool.id,
        details: { step: "branding", completed: true },
      });

      return true;
    } catch (error) {
      console.error("Error saving branding:", error);
      return false;
    }
  };

  const saveSettings = async () => {
    if (!currentSchool?.id) return false;

    try {
      // Upsert school settings
      const { error } = await supabase.from("school_settings").upsert({
        school_id: currentSchool.id,
        email_notifications_enabled: emailNotificationsEnabled,
        require_location_verification: locationVerificationEnabled,
        require_biometric_verification: biometricVerificationEnabled,
        updated_at: new Date().toISOString(),
      });

      if (error) throw error;

      // Log audit event
      await logAuditEvent({
        school_id: currentSchool.id,
        user_id: profile?.id,
        action_type: AuditActionType.SCHOOL_SETTINGS_UPDATED,
        entity_type: AuditEntityType.SETTINGS,
        entity_id: currentSchool.id,
        details: { step: "settings", completed: true },
      });

      return true;
    } catch (error) {
      console.error("Error saving settings:", error);
      return false;
    }
  };

  const handleNext = async () => {
    const currentStep = steps[currentStepIndex];

    // Save current step data
    let success = false;

    if (currentStep.id === "school-info") {
      success = await saveSchoolInfo();
    } else if (currentStep.id === "branding") {
      success = await saveBranding();
    } else if (currentStep.id === "settings") {
      success = await saveSettings();
    } else {
      // For steps that don't require saving data
      success = true;
    }

    if (success) {
      // Mark current step as completed
      setSteps((prev) =>
        prev.map((step, i) =>
          i === currentStepIndex ? { ...step, isCompleted: true } : step
        )
      );

      // Move to next step
      if (currentStepIndex < steps.length - 1) {
        setCurrentStepIndex(currentStepIndex + 1);
      }

      // Refresh school data
      refreshSchool();
    } else {
      toast({
        title: "Error",
        description: "Failed to save data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const handleComplete = async () => {
    setIsCompleting(true);

    try {
      // Mark all steps as completed
      setSteps((prev) => prev.map((step) => ({ ...step, isCompleted: true })));

      // Log completion event
      if (currentSchool?.id) {
        await logAuditEvent({
          school_id: currentSchool.id,
          user_id: profile?.id,
          action_type: AuditActionType.SCHOOL_UPDATED,
          entity_type: AuditEntityType.SCHOOL,
          entity_id: currentSchool.id,
          details: { onboarding_completed: true },
        });
      }

      toast({
        title: "Onboarding Complete",
        description: "Your school has been successfully set up!",
      });

      // Refresh school data
      refreshSchool();
    } catch (error) {
      console.error("Error completing onboarding:", error);
      toast({
        title: "Error",
        description: "Failed to complete onboarding. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCompleting(false);
    }
  };

  const currentStep = steps[currentStepIndex];

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>School Setup</CardTitle>
        <CardDescription>
          Complete these steps to set up your school in Attendance Tracking
          System
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          <Progress value={progress} className="h-2" />

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-1 space-y-4">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={`flex items-center p-3 rounded-md cursor-pointer ${
                    index === currentStepIndex
                      ? "bg-primary/10 border border-primary/30"
                      : "hover:bg-muted"
                  }`}
                  onClick={() => setCurrentStepIndex(index)}
                >
                  <div className="mr-3">
                    {step.isCompleted ? (
                      <CheckCircle2 className="h-5 w-5 text-primary" />
                    ) : (
                      <Circle className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{step.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {step.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="md:col-span-3 p-4 border rounded-md">
              {currentStep.id === "school-info" && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">School Information</h3>
                  <p className="text-muted-foreground">
                    Enter the basic details about your school
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="schoolName">School Name*</Label>
                      <Input
                        id="schoolName"
                        value={schoolName}
                        onChange={(e) => setSchoolName(e.target.value)}
                        placeholder="Enter school name"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        value={address}
                        onChange={(e) => setAddress(e.target.value)}
                        placeholder="Street address"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={city}
                        onChange={(e) => setCity(e.target.value)}
                        placeholder="City"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="state">State/Province</Label>
                      <Input
                        id="state"
                        value={state}
                        onChange={(e) => setState(e.target.value)}
                        placeholder="State or province"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="zip">Postal Code</Label>
                      <Input
                        id="zip"
                        value={zip}
                        onChange={(e) => setZip(e.target.value)}
                        placeholder="Postal code"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        value={country}
                        onChange={(e) => setCountry(e.target.value)}
                        placeholder="Country"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        placeholder="Phone number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="School email"
                      />
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        value={website}
                        onChange={(e) => setWebsite(e.target.value)}
                        placeholder="School website URL"
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep.id === "branding" && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">School Branding</h3>
                  <p className="text-muted-foreground">
                    Customize your school's appearance in the application
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="primaryColor">Primary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="primaryColor"
                            type="color"
                            value={primaryColor}
                            onChange={(e) => setPrimaryColor(e.target.value)}
                            className="w-12 h-10 p-1"
                          />
                          <Input
                            value={primaryColor}
                            onChange={(e) => setPrimaryColor(e.target.value)}
                            className="flex-1"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="secondaryColor">Secondary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            id="secondaryColor"
                            type="color"
                            value={secondaryColor}
                            onChange={(e) => setSecondaryColor(e.target.value)}
                            className="w-12 h-10 p-1"
                          />
                          <Input
                            value={secondaryColor}
                            onChange={(e) => setSecondaryColor(e.target.value)}
                            className="flex-1"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="logo">School Logo</Label>
                      <div className="flex flex-col items-center p-4 border-2 border-dashed rounded-md">
                        {logoPreview ? (
                          <div className="relative w-full h-32 mb-4">
                            <img
                              src={logoPreview}
                              alt="School logo preview"
                              className="w-full h-full object-contain"
                            />
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center h-32 w-full mb-4 bg-muted/20">
                            <Palette className="h-10 w-10 text-muted-foreground mb-2" />
                            <p className="text-sm text-muted-foreground">
                              No logo uploaded
                            </p>
                          </div>
                        )}

                        <Input
                          id="logo"
                          type="file"
                          accept="image/*"
                          onChange={handleLogoChange}
                          className="hidden"
                        />
                        <Label
                          htmlFor="logo"
                          className="cursor-pointer inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Choose Logo
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentStep.id === "settings" && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">System Settings</h3>
                  <p className="text-muted-foreground">
                    Configure how attendance and notifications work for your
                    school
                  </p>

                  <div className="space-y-6">
                    <div className="flex items-center justify-between space-x-2">
                      <div className="space-y-0.5">
                        <Label htmlFor="emailNotifications">
                          Email Notifications
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Enable email notifications for students and parents
                        </p>
                      </div>
                      <input
                        type="checkbox"
                        id="emailNotifications"
                        checked={emailNotificationsEnabled}
                        onChange={(e) =>
                          setEmailNotificationsEnabled(e.target.checked)
                        }
                        className="h-4 w-4"
                      />
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <div className="space-y-0.5">
                        <Label htmlFor="locationVerification">
                          Location Verification
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Require location verification for attendance
                        </p>
                      </div>
                      <input
                        type="checkbox"
                        id="locationVerification"
                        checked={locationVerificationEnabled}
                        onChange={(e) =>
                          setLocationVerificationEnabled(e.target.checked)
                        }
                        className="h-4 w-4"
                      />
                    </div>

                    <div className="flex items-center justify-between space-x-2">
                      <div className="space-y-0.5">
                        <Label htmlFor="biometricVerification">
                          Biometric Verification
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Require biometric verification for attendance
                        </p>
                      </div>
                      <input
                        type="checkbox"
                        id="biometricVerification"
                        checked={biometricVerificationEnabled}
                        onChange={(e) =>
                          setBiometricVerificationEnabled(e.target.checked)
                        }
                        className="h-4 w-4"
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep.id === "location" && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Location Setup</h3>
                  <p className="text-muted-foreground">
                    Set up your school's physical locations for attendance
                    tracking
                  </p>

                  <div className="p-6 border rounded-md bg-muted/20 text-center">
                    <MapPin className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <h4 className="text-lg font-medium mb-2">
                      Set Up Locations
                    </h4>
                    <p className="mb-4">
                      You'll need to set up blocks and rooms to track attendance
                      by location.
                    </p>
                    <Button>Go to Location Settings</Button>
                  </div>
                </div>
              )}

              {currentStep.id === "users" && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Add Users</h3>
                  <p className="text-muted-foreground">
                    Invite teachers and students to join your school
                  </p>

                  <div className="p-6 border rounded-md bg-muted/20 text-center">
                    <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <h4 className="text-lg font-medium mb-2">Invite Users</h4>
                    <p className="mb-4">
                      You'll need to add teachers and students to start tracking
                      attendance.
                    </p>
                    <Button>Go to User Management</Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStepIndex === 0}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        {currentStepIndex === steps.length - 1 ? (
          <Button onClick={handleComplete} disabled={isCompleting}>
            {isCompleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Completing...
              </>
            ) : (
              <>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Complete Setup
              </>
            )}
          </Button>
        ) : (
          <Button onClick={handleNext}>
            Next
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
