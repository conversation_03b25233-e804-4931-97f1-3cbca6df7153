import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  getDatabaseCleanupSettings,
  updateDatabaseCleanupSettings,
  triggerDatabaseCleanup,
  getCleanupStatistics,
  DatabaseCleanupSettings,
  CleanupResults,
} from '@/lib/services/database-cleanup-service';
import {
  getCleanupSystemHealth,
  triggerManualCleanup,
} from '@/lib/services/cleanup-scheduler';
import {
  Trash2,
  Database,
  Clock,
  Bar<PERSON>hart3,
  Refresh<PERSON><PERSON>,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Activity,
  Settings,
  Zap,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { formatDistanceToNow } from 'date-fns';
import { enUS, tr } from 'date-fns/locale';

interface SystemHealth {
  scheduler_running: boolean;
  last_check: string | null;
  next_scheduled_cleanup: string | null;
  system_status: 'healthy' | 'warning' | 'error';
  issues: string[];
}

export default function ImprovedDatabaseCleanup() {
  const { t, i18n } = useTranslation();
  const { toast } = useToast();
  
  const [settings, setSettings] = useState<DatabaseCleanupSettings | null>(null);
  const [statistics, setStatistics] = useState<any>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [cleanupRunning, setCleanupRunning] = useState(false);
  const [lastCleanupResults, setLastCleanupResults] = useState<CleanupResults | null>(null);

  const getDateLocale = () => {
    return i18n.language === 'tr' ? tr : enUS;
  };

  // Fetch all data
  const fetchData = async () => {
    setLoading(true);
    try {
      const [settingsData, statsData, healthData] = await Promise.all([
        getDatabaseCleanupSettings(),
        getCleanupStatistics(),
        getCleanupSystemHealth(),
      ]);

      setSettings(settingsData);
      setStatistics(statsData);
      setSystemHealth(healthData);
    } catch (error) {
      console.error('Error fetching cleanup data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load cleanup data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Trigger manual cleanup
  const handleManualCleanup = async () => {
    setCleanupRunning(true);
    try {
      const success = await triggerManualCleanup();
      
      if (success) {
        toast({
          title: 'Cleanup Completed',
          description: 'Database cleanup completed successfully',
        });
        
        // Refresh data
        await fetchData();
      } else {
        toast({
          title: 'Cleanup Failed',
          description: 'Database cleanup failed or was skipped',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error during manual cleanup:', error);
      toast({
        title: 'Error',
        description: 'Failed to trigger cleanup',
        variant: 'destructive',
      });
    } finally {
      setCleanupRunning(false);
    }
  };

  // Format time ago
  const formatTimeAgo = (timestamp: string | null) => {
    if (!timestamp) return 'Never';
    
    return formatDistanceToNow(new Date(timestamp), {
      addSuffix: true,
      locale: getDateLocale(),
    });
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  // Table data for current statistics
  const tableData = [
    { name: 'Notifications', count: statistics?.notifications || 0, key: 'notifications' },
    { name: 'Attendance Records', count: statistics?.attendance_records || 0, key: 'attendance_records' },
    { name: 'Audit Logs', count: statistics?.audit_logs || 0, key: 'audit_logs' },
    { name: 'Excuses', count: statistics?.excuses || 0, key: 'excuses' },
    { name: 'Location Alerts', count: statistics?.location_alerts || 0, key: 'location_alerts' },
    { name: 'Biometric Credentials', count: statistics?.biometric_credentials || 0, key: 'biometric_credentials' },
    { name: 'Feedback Submissions', count: statistics?.feedback_submissions || 0, key: 'feedback_submissions' },
    { name: 'System Logs', count: statistics?.system_logs || 0, key: 'system_logs' },
    { name: 'User Activity Logs', count: statistics?.user_activity_logs || 0, key: 'user_activity_logs' },
    { name: 'QR Sessions', count: statistics?.qr_sessions || 0, key: 'qr_sessions' },
  ];

  useEffect(() => {
    fetchData();
    
    // Set up periodic refresh
    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Database Cleanup</h2>
            <p className="text-muted-foreground">Loading cleanup system...</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Database Cleanup System</h2>
          <p className="text-muted-foreground">
            Comprehensive database maintenance and cleanup management
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={fetchData}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={handleManualCleanup}
            disabled={cleanupRunning}
            variant="destructive"
          >
            {cleanupRunning ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Run Cleanup
              </>
            )}
          </Button>
        </div>
      </div>

      {/* System Health Status */}
      {systemHealth && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(systemHealth.system_status)}
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <p className="text-sm font-medium">Overall Status</p>
                <Badge 
                  variant={systemHealth.system_status === 'healthy' ? 'default' : 'destructive'}
                  className="capitalize"
                >
                  {systemHealth.system_status}
                </Badge>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Scheduler</p>
                <Badge variant={systemHealth.scheduler_running ? 'default' : 'secondary'}>
                  {systemHealth.scheduler_running ? 'Running' : 'Stopped'}
                </Badge>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium">Next Cleanup</p>
                <p className="text-sm text-muted-foreground">
                  {formatTimeAgo(systemHealth.next_scheduled_cleanup)}
                </p>
              </div>
            </div>
            
            {systemHealth.issues.length > 0 && (
              <Alert className="mt-4" variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Issues detected:</strong>
                  <ul className="mt-2 list-disc list-inside">
                    {systemHealth.issues.map((issue, index) => (
                      <li key={index}>{issue}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Current Database Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Current Database Statistics
          </CardTitle>
          <CardDescription>
            Current record counts across all tables
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Table</TableHead>
                <TableHead className="text-right">Record Count</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tableData.map((item) => (
                <TableRow key={item.key}>
                  <TableCell className="font-medium">{item.name}</TableCell>
                  <TableCell className="text-right">
                    {item.count.toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
