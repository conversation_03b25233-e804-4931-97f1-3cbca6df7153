import React from 'react';
import { toast as sonnerToast } from 'sonner';
import { toast as uiToast } from '@/components/ui/use-toast';
import { TFunction } from 'i18next';

/**
 * Safely convert any value to a string
 * @param value The value to convert
 * @returns A string representation of the value
 */
export const safeString = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (typeof value === 'string') {
    return value;
  }
  
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value);
    } catch (e) {
      return '[Object]';
    }
  }
  
  return String(value);
};

/**
 * Safely translate a key using i18next
 * @param t The i18next translation function
 * @param key The translation key
 * @param fallback Fallback string if translation fails
 * @returns A string that is safe to use
 */
export const safeTranslate = (t: TFunction, key: string, fallback: string): string => {
  try {
    const translation = t(key);
    return typeof translation === 'string' ? translation : fallback;
  } catch (error) {
    console.error(`Translation error for key "${key}":`, error);
    return fallback;
  }
};

/**
 * Safe toast function that ensures all values are strings
 * @param options Toast options
 */
export const safeToast = (options: any) => {
  const safeOptions = {
    ...options,
    title: safeString(options.title),
    description: safeString(options.description),
  };
  
  sonnerToast(safeOptions);
};

/**
 * Safe toast function with translation support
 * @param t The i18next translation function
 * @param titleKey The translation key for the title
 * @param descriptionKey The translation key for the description
 * @param titleFallback Fallback string for the title
 * @param descriptionFallback Fallback string for the description
 * @param options Additional toast options
 */
export const safeTranslatedToast = (
  t: TFunction,
  titleKey: string,
  descriptionKey: string,
  titleFallback: string,
  descriptionFallback: string,
  options: any = {}
) => {
  const title = safeTranslate(t, titleKey, titleFallback);
  const description = safeTranslate(t, descriptionKey, descriptionFallback);
  
  safeToast({
    ...options,
    title,
    description,
  });
};

/**
 * Safe success toast with translation support
 */
export const safeSuccessToast = (
  t: TFunction,
  titleKey: string,
  descriptionKey: string,
  titleFallback: string = 'Success',
  descriptionFallback: string = 'Operation completed successfully'
) => {
  safeTranslatedToast(t, titleKey, descriptionKey, titleFallback, descriptionFallback, {
    type: 'success',
  });
};

/**
 * Safe error toast with translation support
 */
export const safeErrorToast = (
  t: TFunction,
  titleKey: string,
  descriptionKey: string,
  titleFallback: string = 'Error',
  descriptionFallback: string = 'An error occurred'
) => {
  safeTranslatedToast(t, titleKey, descriptionKey, titleFallback, descriptionFallback, {
    type: 'error',
  });
};

// Export a safe version of the UI toast
export const safeShadcnToast = (options: any) => {
  const safeOptions = {
    ...options,
    title: safeString(options.title),
    description: safeString(options.description),
  };
  
  return uiToast(safeOptions);
};
