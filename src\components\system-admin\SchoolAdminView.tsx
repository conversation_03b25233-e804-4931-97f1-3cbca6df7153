import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { TabProvider } from "@/context/TabContext";
import { School } from "@/lib/types";
import { motion, AnimatePresence } from "framer-motion";
import { getSchoolById } from "@/lib/utils/school-context";
import {
  ArrowLeft,
  Bell,
  Users,
  Settings,
  FileText,
  UserPlus,
  User,
  Building2,
  Shield,
  School as SchoolIcon,
} from "lucide-react";

// Import admin components
import QRGenerator from "@/components/admin/QRGenerator";
import UserManagement from "@/components/admin/UserManagement";
import AdminAlerts from "@/components/admin/AdminAlerts";
import EnhancedFraudDetection from "@/components/admin/EnhancedFraudDetection";
import AdminStudentDirectory from "@/components/admin/AdminStudentDirectory";
import AdminSettings from "@/components/admin/AdminSettings";
import AdminExcusesManagement from "@/components/admin/AdminExcusesManagement";
import AdminParentContacts from "@/pages/AdminParentContacts";
import SchoolManagement from "@/components/admin/SchoolManagement";
import SchoolContext from "@/components/admin/SchoolContext";
import SchoolSettings from "@/components/admin/SchoolSettings";
import AuditLogs from "@/components/admin/AuditLogs";
import AdminProfile from "@/components/admin/AdminProfile";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface SchoolAdminViewProps {
  schoolId: string;
  onBack: () => void;
}

export default function SchoolAdminView({
  schoolId,
  onBack,
}: SchoolAdminViewProps) {
  const { profile } = useAuth();
  const { currentSchool, switchSchool, loading: schoolLoading } = useSchool();
  const [loading, setLoading] = useState(true);
  const [school, setSchool] = useState<School | null>(null);
  const [activeTab, setActiveTab] = useState("users");
  const { toast } = useToast();
  const navigate = useNavigate();

  // Load the school and switch context
  useEffect(() => {
    const loadSchool = async () => {
      setLoading(true);
      try {
        // Get school details
        const schoolData = await getSchoolById(schoolId);
        if (!schoolData) {
          toast({
            title: "Error",
            description: "School not found",
            variant: "destructive",
          });
          onBack();
          return;
        }

        setSchool(schoolData);

        // Switch school context
        await switchSchool(schoolId);
      } catch (error) {
        console.error("Error loading school:", error);
        toast({
          title: "Error",
          description: "Failed to load school",
          variant: "destructive",
        });
        onBack();
      } finally {
        setLoading(false);
      }
    };

    loadSchool();

    // Cleanup - reset school context when component unmounts
    return () => {
      // We don't reset the school context here because it might cause flickering
      // The parent component should handle this when navigating away
    };
  }, [schoolId]);

  // If loading, show loading indicator
  if (loading || schoolLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <LoadingSpinner message="Loading school administration..." size="lg" />
      </div>
    );
  }

  // If school not found or not loaded, show error
  if (!school || !currentSchool) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center">
        <p className="text-lg text-red-500 mb-4">Failed to load school</p>
        <Button onClick={onBack} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to School Management
        </Button>
      </div>
    );
  }

  return (
    <TabProvider>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Button onClick={onBack} variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to System Admin
            </Button>

            <div>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <SchoolIcon className="h-6 w-6 text-primary" />
                {school.name}
                <Badge
                  variant="outline"
                  className="ml-2 bg-blue-100 text-blue-800 border-blue-200"
                >
                  System Admin View
                </Badge>
              </h1>
              <p className="text-sm text-muted-foreground">
                You are viewing this school as a system administrator
              </p>
            </div>
          </div>
        </div>

        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Shield className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="font-medium text-blue-700">
                  System Administrator Mode
                </p>
                <p className="text-sm text-blue-600">
                  You are viewing this school with system administrator
                  privileges. Any changes you make will affect this school.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex flex-wrap mb-6 gap-2">
            <TabsList className="flex-wrap">
              {/* Primary tabs - always visible */}
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Users
              </TabsTrigger>
              <TabsTrigger value="students" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Students
              </TabsTrigger>
              <TabsTrigger value="qrcodes" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                QR Codes
              </TabsTrigger>
              <TabsTrigger value="excuses" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Excuses
              </TabsTrigger>
              <TabsTrigger value="fraud" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Fraud Detection
              </TabsTrigger>
              <TabsTrigger value="alerts" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Alerts
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </TabsTrigger>
              <TabsTrigger
                value="school-settings"
                className="flex items-center gap-2"
              >
                <Building2 className="h-4 w-4" />
                School Settings
              </TabsTrigger>
              <TabsTrigger
                value="audit-logs"
                className="flex items-center gap-2"
              >
                <Shield className="h-4 w-4" />
                Audit Logs
              </TabsTrigger>
            </TabsList>
          </div>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              <TabsContent value="users" className="mt-0">
                <div className="grid gap-6">
                  <SchoolContext />
                  <UserManagement />
                </div>
              </TabsContent>

              <TabsContent value="students" className="mt-0">
                <AdminStudentDirectory />
              </TabsContent>

              <TabsContent value="qrcodes" className="mt-0">
                <QRGenerator />
              </TabsContent>

              <TabsContent value="fraud" className="mt-0">
                <EnhancedFraudDetection />
              </TabsContent>

              <TabsContent value="excuses" className="mt-0">
                <AdminExcusesManagement />
              </TabsContent>

              <TabsContent value="alerts" className="mt-0">
                <AdminAlerts />
              </TabsContent>

              <TabsContent value="settings" className="mt-0">
                <AdminSettings />
              </TabsContent>

              <TabsContent value="school-settings" className="mt-0">
                <div className="grid gap-6">
                  <SchoolContext />
                  <SchoolSettings />
                </div>
              </TabsContent>

              <TabsContent value="audit-logs" className="mt-0">
                <div className="grid gap-6">
                  <SchoolContext />
                  <AuditLogs />
                </div>
              </TabsContent>
            </motion.div>
          </AnimatePresence>
        </Tabs>
      </div>
    </TabProvider>
  );
}
