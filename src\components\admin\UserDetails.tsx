import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User } from "@/lib/types";
import { UserCircle2, Mail, BookOpen, Building2, MapPin, Key, Calendar, Pencil } from "lucide-react";
import UserEditForm from "./UserEditForm";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "react-i18next";

interface UserDetailsProps {
  user: User;
  open: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

export default function UserDetails({ user, open, onClose, onUpdate }: UserDetailsProps) {
  const [showEditForm, setShowEditForm] = useState(false);
  const { session } = useAuth();
  const { t } = useTranslation();
  const isAdmin = session?.user?.user_metadata?.role === "admin";

  const renderStudentDetails = () => (
    <>
      <div className="flex items-center gap-2 text-muted-foreground">
        <BookOpen className="h-4 w-4" />
        <span>{t("admin.userDetails.course")}: {user.course || t("admin.userDetails.notSet")}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <Building2 className="h-4 w-4" />
        <span>{t("admin.userDetails.block")}: {user.blockName || t("admin.userDetails.notSet")}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <MapPin className="h-4 w-4" />
        <span>{t("admin.userDetails.room")}: {user.roomNumber || t("admin.userDetails.notSet")}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <Key className="h-4 w-4" />
        <span>{t("admin.userDetails.studentId")}: {user.studentId || t("admin.userDetails.notSet")}</span>
      </div>
    </>
  );

  const renderTeacherDetails = () => (
    <>
      <div className="flex items-center gap-2 text-muted-foreground">
        <Building2 className="h-4 w-4" />
        <span>{t("admin.userDetails.department")}: {user.department || t("admin.userDetails.notSet")}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <BookOpen className="h-4 w-4" />
        <span>{t("admin.userDetails.subject")}: {user.subject || t("admin.userDetails.notSet")}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <Key className="h-4 w-4" />
        <span>{t("admin.userDetails.teacherId")}: {user.teacherId || t("admin.userDetails.notSet")}</span>
      </div>
      <div className="flex items-center gap-2 text-muted-foreground">
        <MapPin className="h-4 w-4" />
        <span>{t("admin.userDetails.position")}: {user.position || t("admin.userDetails.notSet")}</span>
      </div>
    </>
  );

  const handleEdit = () => {
    setShowEditForm(true);
  };

  const handleEditClose = () => {
    setShowEditForm(false);
  };

  const handleUpdate = () => {
    onUpdate();
    handleEditClose();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex justify-between items-center">
              <span>{t("admin.userDetails.title")}</span>
              {isAdmin && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={handleEdit}
                >
                  <Pencil className="h-4 w-4" />
                  {t("admin.userDetails.edit")}
                </Button>
              )}
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex flex-col items-center gap-4 mb-6">
            <Avatar className="w-24 h-24">
              <AvatarImage src={user.photoUrl} alt={user.name} />
              <AvatarFallback>{user.name.charAt(0).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div className="text-center">
              <h2 className="text-xl font-semibold">{user.name}</h2>
              <p className="text-muted-foreground capitalize">{t(`common.roles.${user.role}`)}</p>
            </div>
          </div>

          <div className="grid gap-6">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <UserCircle2 className="h-4 w-4" />
                    <span>{t("admin.userDetails.name")}: {user.name}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Mail className="h-4 w-4" />
                    <span>{t("admin.userDetails.email")}: {user.email}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>{t("admin.userDetails.joined")}: {new Date(user.created_at || "").toLocaleDateString()}</span>
                  </div>
                  {user.role === "student" && renderStudentDetails()}
                  {user.role === "teacher" && renderTeacherDetails()}
                </div>
              </CardContent>
            </Card>
          </div>
        </DialogContent>
      </Dialog>

      {showEditForm && (
        <UserEditForm
          user={user}
          open={showEditForm}
          onClose={handleEditClose}
          onUpdate={handleUpdate}
        />
      )}
    </>
  );
} 