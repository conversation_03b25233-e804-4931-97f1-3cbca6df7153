import { supabase } from "@/lib/supabase";

/**
 * Creates the school_settings table and audit_logs table
 */
export const createSchoolSettingsTables = async (): Promise<void> => {
  try {
    // Create school_settings and audit_logs tables

    // Create school_settings table
    const { error: settingsError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create school_settings table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.school_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
          email_notifications_enabled BOOLEAN DEFAULT true,
          sms_notifications_enabled BOOLEAN DEFAULT false,
          invitation_expiry TIMESTAMP WITH TIME ZONE,
          theme_css TEXT,
          custom_login_message TEXT,
          require_location_verification BOOLEAN DEFAULT true,
          require_biometric_verification BOOLEAN DEFAULT false,
          allow_pin_verification BOOLEAN DEFAULT true,
          allow_wifi_verification BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          UNIQUE(school_id)
        );

        -- Create index for faster lookups
        CREATE INDEX IF NOT EXISTS idx_school_settings_school_id ON public.school_settings(school_id);
      `,
    });

    if (settingsError) {
      console.error("Error creating school_settings table:", settingsError);
      throw settingsError;
    }

    // Create audit_logs table
    const { error: auditError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Create audit_logs table for tracking important actions
        CREATE TABLE IF NOT EXISTS public.audit_logs (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
          user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
          action_type VARCHAR NOT NULL,
          entity_type VARCHAR NOT NULL,
          entity_id UUID,
          details JSONB,
          ip_address VARCHAR,
          user_agent VARCHAR,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Create indexes for faster queries
        CREATE INDEX IF NOT EXISTS idx_audit_logs_school_id ON public.audit_logs(school_id);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_action_type ON public.audit_logs(action_type);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);
      `,
    });

    if (auditError) {
      console.error("Error creating audit_logs table:", auditError);
      throw auditError;
    }

    // Set up RLS policies
    const { error: rlsError } = await supabase.rpc("execute_sql", {
      sql: `
        -- Enable RLS on school_settings table
        ALTER TABLE public.school_settings ENABLE ROW LEVEL SECURITY;

        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "School admins can manage their school settings" ON public.school_settings;

        -- School admins can manage their school settings
        CREATE POLICY "School admins can manage their school settings"
        ON public.school_settings
        FOR ALL
        TO authenticated
        USING (
          -- Check if the current user is a school admin
          EXISTS (
            SELECT 1 FROM profiles admin
            WHERE admin.user_id = auth.uid()
            AND admin.role = 'admin'
            AND admin.access_level = 1
          )
          AND
          -- Check if the settings belong to the same school
          school_id = (
            SELECT school_id FROM profiles
            WHERE user_id = auth.uid()
          )
        )
        WITH CHECK (
          -- Check if the current user is a school admin
          EXISTS (
            SELECT 1 FROM profiles admin
            WHERE admin.user_id = auth.uid()
            AND admin.role = 'admin'
            AND admin.access_level = 1
          )
          AND
          -- Check if the settings belong to the same school
          school_id = (
            SELECT school_id FROM profiles
            WHERE user_id = auth.uid()
          )
        );

        -- Drop existing policy if it exists
        DROP POLICY IF EXISTS "System admins can manage all school settings" ON public.school_settings;

        -- System admins can manage all school settings
        CREATE POLICY "System admins can manage all school settings"
        ON public.school_settings
        FOR ALL
        TO authenticated
        USING (
          -- Check if the current user is a system admin
          EXISTS (
            SELECT 1 FROM profiles admin
            WHERE admin.user_id = auth.uid()
            AND admin.role = 'admin'
            AND admin.access_level = 3
          )
        )
        WITH CHECK (
          -- Check if the current user is a system admin
          EXISTS (
            SELECT 1 FROM profiles admin
            WHERE admin.user_id = auth.uid()
            AND admin.role = 'admin'
            AND admin.access_level = 3
          )
        );

        -- Enable RLS on audit_logs table
        ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

        -- Drop existing policy if it exists
        DROP POLICY IF EXISTS "School admins can view audit logs for their school" ON public.audit_logs;

        -- School admins can view audit logs for their school
        CREATE POLICY "School admins can view audit logs for their school"
        ON public.audit_logs
        FOR SELECT
        TO authenticated
        USING (
          -- Check if the current user is a school admin
          EXISTS (
            SELECT 1 FROM profiles admin
            WHERE admin.user_id = auth.uid()
            AND admin.role = 'admin'
            AND admin.access_level = 1
          )
          AND
          -- Check if the logs belong to the same school
          school_id = (
            SELECT school_id FROM profiles
            WHERE user_id = auth.uid()
          )
        );

        -- Drop existing policy if it exists
        DROP POLICY IF EXISTS "System admins can view all audit logs" ON public.audit_logs;

        -- System admins can view all audit logs
        CREATE POLICY "System admins can view all audit logs"
        ON public.audit_logs
        FOR SELECT
        TO authenticated
        USING (
          -- Check if the current user is a system admin
          EXISTS (
            SELECT 1 FROM profiles admin
            WHERE admin.user_id = auth.uid()
            AND admin.role = 'admin'
            AND admin.access_level = 3
          )
        );

        -- Drop existing policy if it exists
        DROP POLICY IF EXISTS "System can insert audit logs" ON public.audit_logs;

        -- Only the system can insert audit logs
        CREATE POLICY "System can insert audit logs"
        ON public.audit_logs
        FOR INSERT
        TO authenticated
        WITH CHECK (true);

        -- Grant necessary permissions
        GRANT ALL ON public.school_settings TO authenticated;
        GRANT ALL ON public.audit_logs TO authenticated;
      `,
    });

    if (rlsError) {
      console.error("Error setting up RLS policies:", rlsError);
      throw rlsError;
    }

    // School settings and audit logs tables created successfully
  } catch (error) {
    console.error("Error in createSchoolSettingsTables:", error);
    throw error;
  }
};
