-- Add custom dashboard message columns to school_settings table
ALTER TABLE school_settings 
ADD COLUMN IF NOT EXISTS custom_student_message TEXT,
ADD COLUMN IF NOT EXISTS custom_teacher_message TEXT,
ADD COLUMN IF NOT EXISTS custom_admin_message TEXT;

-- Create system settings overrides for these new settings
INSERT INTO system_settings_overrides (setting_name, setting_value, applies_to_all, override_enabled, created_at, updated_at)
VALUES 
('custom_student_message', '{"value": ""}', true, false, NOW(), NOW()),
('custom_teacher_message', '{"value": ""}', true, false, NOW(), NOW()),
('custom_admin_message', '{"value": ""}', true, false, NOW(), NOW())
ON CONFLICT (setting_name, school_id) 
DO NOTHING;
