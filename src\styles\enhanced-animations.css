/* Enhanced Animations and Micro-interactions */

/* Page Transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 300ms, transform 300ms;
}

/* Tab Transitions */
.tab-transition-enter {
  opacity: 0;
  transform: translateX(10px);
}

.tab-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 250ms, transform 250ms;
}

.tab-transition-exit {
  opacity: 1;
}

.tab-transition-exit-active {
  opacity: 0;
  transition: opacity 200ms;
}

/* Enhanced Tab Animations */
@keyframes tabFadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab hover effect */
[data-state="inactive"][role="tab"]:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Active tab animation */
[data-state="active"][role="tab"] {
  animation: tabFadeIn 0.3s ease forwards;
}

/* Tab content animation */
[role="tabpanel"] {
  animation: tabFadeIn 0.4s ease forwards;
}

/* Enhanced Hover Effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.hover-glow {
  transition: box-shadow 0.2s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 8px rgba(var(--primary), 0.4);
}

.hover-glow-accent:hover {
  box-shadow: 0 0 8px rgba(var(--accent), 0.4);
}

/* Button Animations */
.btn-scale {
  transition: transform 0.15s ease;
}

.btn-scale:active {
  transform: scale(0.97);
}

/* QR Scanner Animations */
@keyframes scan-line {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

.qr-scanner-frame {
  position: relative;
  width: 280px;
  height: 280px;
  border: 2px solid hsl(var(--primary));
  border-radius: 16px;
  box-shadow: 0 0 0 4px rgba(var(--primary), 0.2);
  overflow: hidden;
}

.qr-scanner-line {
  position: absolute;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    hsl(var(--primary)),
    transparent
  );
  box-shadow: 0 0 8px hsl(var(--primary));
  animation: scan-line 2s linear infinite;
}

.qr-scanner-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border-color: hsl(var(--primary));
  border-width: 2px;
}

.qr-scanner-corner-top-left {
  top: -2px;
  left: -2px;
  border-top: 4px solid;
  border-left: 4px solid;
  border-top-left-radius: 8px;
}

.qr-scanner-corner-top-right {
  top: -2px;
  right: -2px;
  border-top: 4px solid;
  border-right: 4px solid;
  border-top-right-radius: 8px;
}

.qr-scanner-corner-bottom-left {
  bottom: -2px;
  left: -2px;
  border-bottom: 4px solid;
  border-left: 4px solid;
  border-bottom-left-radius: 8px;
}

.qr-scanner-corner-bottom-right {
  bottom: -2px;
  right: -2px;
  border-bottom: 4px solid;
  border-right: 4px solid;
  border-bottom-right-radius: 8px;
}

/* Success Animation */
@keyframes success-checkmark {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-checkmark {
  animation: success-checkmark 0.5s ease-out forwards;
}

@keyframes success-circle {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-circle {
  animation: success-circle 0.4s ease-out forwards;
}

/* Error Animation */
@keyframes error-shake {
  0%,
  100% {
    transform: translateX(0);
  }
  20%,
  60% {
    transform: translateX(-5px);
  }
  40%,
  80% {
    transform: translateX(5px);
  }
}

.error-shake {
  animation: error-shake 0.5s ease-in-out;
}

/* Loading Skeleton Animation */
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  border-radius: var(--radius);
}

/* Attendance Status Animations */
@keyframes status-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.status-indicator-animated {
  animation: status-pulse 2s ease-in-out infinite;
}

/* Timeline Animation */
@keyframes timeline-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.timeline-item {
  animation: timeline-appear 0.5s ease-out forwards;
  animation-delay: calc(var(--index) * 0.1s);
  opacity: 0;
}

/* Reduced Motion */
@media (prefers-reduced-motion) {
  .page-transition-enter-active,
  .page-transition-exit-active,
  .tab-transition-enter-active,
  .tab-transition-exit-active {
    transition: opacity 100ms;
    transform: none;
  }

  .qr-scanner-line,
  .status-indicator-animated,
  .timeline-item,
  .skeleton {
    animation: none;
  }

  .hover-lift:hover {
    transform: none;
  }

  .btn-scale:active {
    transform: none;
  }
}
