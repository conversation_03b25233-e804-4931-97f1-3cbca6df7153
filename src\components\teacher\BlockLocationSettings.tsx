import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { toast as sonnerToast } from "sonner";
import { Loader2, MapPin, AlertCircle, Save } from "lucide-react";
import { supabase } from "@/lib/supabase";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Block } from "@/lib/types";
import { createBlockLocationsTable } from "@/lib/migrations.new";
import { useTranslation } from "react-i18next";

interface BlockLocationSettingsProps {
  blockId: string;
  blockName: string;
}

export function BlockLocationSettings({
  blockId,
  blockName,
}: BlockLocationSettingsProps) {
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [savingRadius, setSavingRadius] = useState(false);
  const [locationPermission, setLocationPermission] =
    useState<PermissionState | null>(null);
  const [location, setLocation] = useState<{
    latitude: number;
    longitude: number;
    radius_meters: number;
  } | null>(null);
  const [localRadius, setLocalRadius] = useState<number | null>(null);
  const [localLatitude, setLocalLatitude] = useState<number | null>(null);
  const [localLongitude, setLocalLongitude] = useState<number | null>(null);
  const [hasUnsavedLocation, setHasUnsavedLocation] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    checkLocationPermission();
    ensureTableExists();
    fetchBlockLocation();
  }, [blockId]);

  const checkLocationPermission = async () => {
    try {
      // Check if the Permissions API is supported
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({
          name: "geolocation",
        });
        setLocationPermission(permission.state);

        // Listen for permission changes
        permission.addEventListener("change", () => {
          setLocationPermission(permission.state);
        });
      } else {
        // Fallback for browsers that don't support the Permissions API
        setLocationPermission("prompt");
      }
    } catch (error) {
      console.error("Error checking location permission:", error);
      setLocationPermission("prompt");
    }
  };

  const getCurrentLocation = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by your browser"));
        return;
      }

      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      });
    });
  };

  const ensureTableExists = async (): Promise<boolean> => {
    try {
      // Check if block_locations table exists
      const { data, error } = await supabase
        .from("block_locations")
        .select("id")
        .limit(1);

      if (error && error.code === "PGRST204") {
        // Table doesn't exist, create it
        console.log("Block locations table does not exist, creating it...");
        const result = await createBlockLocationsTable();
        return result;
      } else if (error) {
        console.error("Error checking block_locations table:", error);
        // Try to create the table anyway
        console.log("Attempting to create block_locations table...");
        const result = await createBlockLocationsTable();
        return result;
      }

      return true;
    } catch (error) {
      console.error("Error ensuring table exists:", error);
      return false;
    }
  };

  const fetchBlockLocation = async () => {
    if (!blockId) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("block_locations")
        .select("*")
        .eq("block_id", blockId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // No location set yet, this is fine
          setLocation(null);
        } else if (error.code === "PGRST204") {
          // Table doesn't exist yet
          console.warn("Block locations table does not exist");
          setLocation(null);
        } else {
          throw error;
        }
      } else if (data) {
        const locationData = {
          latitude: Number(data.latitude),
          longitude: Number(data.longitude),
          radius_meters: data.radius_meters,
        };
        setLocation(locationData);
        setLocalRadius(data.radius_meters);
        setLocalLatitude(Number(data.latitude));
        setLocalLongitude(Number(data.longitude));
        setHasUnsavedLocation(false);
      }
    } catch (error) {
      console.error("Error fetching block location:", error);
      toast({
        title: "Error",
        description: "Failed to fetch block location. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGetCurrentLocation = async () => {
    try {
      setUpdating(true);
      console.log("Getting current location...");
      const position = await getCurrentLocation();
      console.log("Current position:", position.coords);

      // Set the coordinates in local state for editing (don't save yet)
      setLocalLatitude(position.coords.latitude);
      setLocalLongitude(position.coords.longitude);
      setHasUnsavedLocation(true);

      sonnerToast.success(t("teacher.settings.locationRetrieved"), {
        description: t("teacher.settings.locationRetrievedDescription"),
      });

      // Update permission state after successful location access
      checkLocationPermission();
    } catch (error) {
      console.error("Error getting location:", error);
      let errorMessage = "Failed to get current location";

      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage =
              "Please allow location access in your browser settings to get current location";
            setLocationPermission("denied");
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage =
              "Location information is unavailable. Please try again";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out. Please try again";
            break;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      sonnerToast.error(t("common.error"), {
        description: errorMessage,
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleSaveLocation = async () => {
    if (localLatitude === null || localLongitude === null) return;

    try {
      setUpdating(true);

      // Check if table exists first
      // Verifying table and permissions
      const tableExists = await ensureTableExists();
      if (!tableExists) {
        throw new Error("Block locations table is not properly configured");
      }

      const locationData = {
        block_id: blockId,
        latitude: localLatitude,
        longitude: localLongitude,
        radius_meters: location?.radius_meters || 50,
      };

      // Saving location data

      // Upsert the location data
      const { data: savedLocation, error } = await supabase
        .from("block_locations")
        .upsert(locationData, {
          onConflict: "block_id",
        })
        .select()
        .single();

      if (error) {
        console.error("Error saving location:", error);
        throw new Error("Failed to save location. Please try again.");
      }

      if (!savedLocation) {
        console.error("Location not found after saving");
        throw new Error("Location was not saved correctly. Please try again.");
      }

      // Location saved successfully

      const newRadius = location?.radius_meters || 50;
      setLocation({
        latitude: localLatitude,
        longitude: localLongitude,
        radius_meters: newRadius,
      });
      setLocalRadius(newRadius);
      setHasUnsavedLocation(false);

      sonnerToast.success(t("teacher.settings.locationUpdated"), {
        description: t("teacher.settings.blockLocationUpdatedMessage", {
          blockName,
        }),
      });
    } catch (error) {
      console.error("Error saving location:", error);
      let errorMessage = "Failed to save block location";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      sonnerToast.error(t("common.error"), {
        description: errorMessage,
      });
    } finally {
      setUpdating(false);
    }
  };

  const handleSaveRadius = async () => {
    if (!location || localRadius === null) return;

    try {
      setSavingRadius(true);

      console.log("Saving radius:", {
        blockId,
        localRadius,
        currentRadius: location?.radius_meters
      });

      const { data, error } = await supabase
        .from("block_locations")
        .update({ radius_meters: localRadius })
        .eq("block_id", blockId)
        .select();

      if (error) throw error;

      console.log("Update result:", data);

      setLocation({
        ...location,
        radius_meters: localRadius,
      });

      sonnerToast.success(t("teacher.settings.radiusUpdated"), {
        description: t("teacher.settings.blockRadiusUpdatedMessage", {
          blockName,
        }),
      });
    } catch (error) {
      console.error("Error updating radius:", error);
      sonnerToast.error(t("common.error"), {
        description: t("teacher.settings.errorUpdatingRadius"),
      });
    } finally {
      setSavingRadius(false);
    }
  };

  // Check if radius has been changed
  const hasRadiusChanged = location && localRadius !== null && localRadius !== location.radius_meters;

  // Check if location coordinates have been changed
  const hasLocationChanged = (localLatitude !== null && localLongitude !== null) &&
    (!location || localLatitude !== location.latitude || localLongitude !== location.longitude);

  // Show location fields if we have coordinates (either from existing location or newly retrieved)
  const showLocationFields = location || (localLatitude !== null && localLongitude !== null);

  const renderLocationPermissionMessage = () => {
    if (locationPermission === "denied") {
      return (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t("teacher.settings.locationPermissionDenied")}
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("teacher.settings.blockLocationSettings")}</CardTitle>
        <CardDescription>
          {t("teacher.settings.blockLocationDescription").replace(
            "{blockName}",
            blockName
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {renderLocationPermissionMessage()}

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button
              onClick={handleGetCurrentLocation}
              disabled={updating || locationPermission === "denied"}
              className="flex items-center gap-2"
            >
              {updating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <MapPin className="h-4 w-4" />
              )}
              {location
                ? t("teacher.settings.updateCurrentLocation")
                : t("teacher.settings.setCurrentLocation")}
            </Button>
          </div>

          {showLocationFields && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>{t("teacher.settings.latitude")}</Label>
                  <Input
                    type="number"
                    step="any"
                    value={localLatitude ?? (location?.latitude || "")}
                    onChange={(e) => {
                      setLocalLatitude(Number(e.target.value));
                      setHasUnsavedLocation(true);
                    }}
                    placeholder="Enter latitude"
                  />
                </div>
                <div className="space-y-2">
                  <Label>{t("teacher.settings.longitude")}</Label>
                  <Input
                    type="number"
                    step="any"
                    value={localLongitude ?? (location?.longitude || "")}
                    onChange={(e) => {
                      setLocalLongitude(Number(e.target.value));
                      setHasUnsavedLocation(true);
                    }}
                    placeholder="Enter longitude"
                  />
                </div>
              </div>

              {(hasLocationChanged || hasUnsavedLocation) && (
                <div className="flex gap-2">
                  <Button
                    onClick={handleSaveLocation}
                    disabled={updating || localLatitude === null || localLongitude === null}
                    className="flex items-center gap-2"
                  >
                    {updating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {t("teacher.settings.saveLocation")}
                  </Button>
                  {hasUnsavedLocation && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        setLocalLatitude(location?.latitude || null);
                        setLocalLongitude(location?.longitude || null);
                        setHasUnsavedLocation(false);
                      }}
                    >
                      {t("common.cancel")}
                    </Button>
                  )}
                </div>
              )}
              <div className="space-y-2">
                <Label>{t("teacher.settings.attendanceRadius")}</Label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    value={localRadius ?? (location?.radius_meters || 50)}
                    onChange={(e) => setLocalRadius(Number(e.target.value))}
                    min={10}
                    max={1000}
                    className="flex-1"
                  />
                  {hasRadiusChanged && (
                    <Button
                      onClick={handleSaveRadius}
                      disabled={savingRadius}
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      {savingRadius ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4" />
                      )}
                      {t("common.save")}
                    </Button>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("teacher.settings.studentsWithinRadiusBlock", {
                    blockName,
                  })}
                </p>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
