import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import SecondaryNavigation from "./navigation/SecondaryNavigation";
import {
  Users,
  Search,
  Plus,
  RefreshCw,
  Settings,
  Shield,
  UserCheck,
  UserX,
  FileText,
  MoreHorizontal,
  Ban,
  CheckCircle,
  Trash2,
  Wrench as WrenchIcon,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import UserSettings from "./UserSettings";
import AddUserDialog from "./AddUserDialog";
import DeleteUserDialog from "./DeleteUserDialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { format } from "date-fns";

interface User {
  id: string;
  user_id: string;
  name: string;
  email: string;
  role: "admin" | "teacher" | "student";
  access_level?: number;
  school_id?: string;
  school_name?: string;
  created_at: string;
  is_verified?: boolean; // For backward compatibility
  profile_completed?: boolean;
  department?: string | null;
  updated_at?: string;
  maintenance_mode?: boolean;
  is_deleted?: boolean;
  deleted_at?: string;
}

export default function UserAdministration() {
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState("all-users");

  // State for user settings dialog
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserSettings, setShowUserSettings] = useState(false);

  // State for add user dialog
  const [showAddUser, setShowAddUser] = useState(false);
  const [addUserRole, setAddUserRole] = useState<
    "admin" | "teacher" | "student"
  >("student");

  // State for delete user dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  useEffect(() => {
    fetchUsers();

    // Set up real-time subscription to profile changes
    const subscription = supabase
      .channel("profile-changes")
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "profiles",
        },
        (payload) => {
          console.log("Profile updated:", payload);

          // Instead of refreshing the entire list, update just the changed user
          const updatedUser = payload.new as any;
          if (updatedUser && updatedUser.id) {
            console.log("Updating user in local state:", updatedUser);

            // Update the user in the local state
            setUsers((prevUsers) =>
              prevUsers.map((user) =>
                user.id === updatedUser.id
                  ? {
                      ...user,
                      profile_completed: updatedUser.profile_completed,
                      is_verified: updatedUser.profile_completed, // For backward compatibility
                      maintenance_mode: updatedUser.maintenance_mode === true,
                      department: updatedUser.department,
                      updated_at: updatedUser.updated_at,
                    }
                  : user
              )
            );
          } else {
            // If we can't update just the changed user, refresh the entire list
            fetchUsers();
          }
        }
      )
      .subscribe();

    // Clean up subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {


      // Fetch users with their school information
      const { data, error } = await supabase
        .from("profiles")
        .select(
          `
          id,
          user_id,
          name,
          email,
          role,
          access_level,
          school_id,
          created_at,
          profile_completed,
          department,
          updated_at,
          maintenance_mode,
          is_deleted,
          deleted_at,
          schools:school_id (
            name
          )
        `
        )
        .order("created_at", { ascending: false });

      if (error) throw error;



      // Transform data to include school name and ensure all fields are present
      const transformedUsers = (data || []).map((user) => {


        // Create a new object with default values for missing fields
        return {
          ...user,
          school_name: user.schools?.name || "No School",
          // Use the actual profile_completed value from the database
          profile_completed: user.profile_completed,
          // For backward compatibility
          is_verified: user.profile_completed,
          // Ensure maintenance_mode is a boolean
          maintenance_mode: user.maintenance_mode === true,
          // Ensure is_deleted is a boolean
          is_deleted: user.is_deleted === true,
        };
      });



      // Update the state with the new data
      setUsers(transformedUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchUsers();
    setRefreshing(false);
  };

  // Function to update user profiles with admin privileges
  const updateUserProfile = async (userId: string, updates: any) => {
    try {
      // First, check if the user exists
      const { data: userData, error: userError } = await supabase
        .from("profiles")
        .select("id, name, profile_completed, maintenance_mode")
        .eq("id", userId)
        .single();

      if (userError) {
        throw new Error(`User not found: ${userError.message}`);
      }

      // Use the database function to update the profile
      const { data, error } = await supabase.rpc("execute_admin_update", {
        p_user_id: userId,
        p_maintenance_mode:
          updates.maintenance_mode !== undefined
            ? updates.maintenance_mode
            : userData.maintenance_mode,
        p_profile_completed:
          updates.profile_completed !== undefined
            ? updates.profile_completed
            : userData.profile_completed,
        p_updated_at: updates.updated_at || new Date().toISOString(),
      });

      if (error) {
        throw new Error(`Failed to update user: ${error.message}`);
      }

      return [{ ...userData, ...updates }];
    } catch (error) {
      console.error("Error updating user profile:", error);
      throw error;
    }
  };

  const handleOpenUserSettings = (user: User) => {
    setSelectedUser(user);
    setShowUserSettings(true);
  };

  const handleOpenDeleteDialog = (user: User) => {
    setSelectedUser(user);
    setShowDeleteDialog(true);
  };

  // Function to toggle user active status
  const toggleUserActiveStatus = async (user: User) => {
    try {
      // Get the current user's ID to check permissions
      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();

      if (!currentUser) {
        toast({
          title: "Error",
          description: "You must be logged in to perform this action",
          variant: "destructive",
        });
        return;
      }

      // Check if the current user is a system admin
      const { data: adminData, error: adminError } = await supabase
        .from("profiles")
        .select("role, access_level")
        .eq("id", currentUser.id)
        .single();

      if (adminError) {
        throw adminError;
      }

      if (
        !adminData ||
        adminData.role !== "admin" ||
        adminData.access_level < 2
      ) {
        toast({
          title: "Permission Denied",
          description: "Only system admins can perform this action",
          variant: "destructive",
        });
        return;
      }

      // First, get the current profile_completed status directly from the database
      const { data: currentProfileData, error: profileError } = await supabase
        .from("profiles")
        .select("profile_completed")
        .eq("id", user.id)
        .single();

      if (profileError) {
        throw profileError;
      }

      // Use the value from the database, not from the local state
      const currentStatus = currentProfileData.profile_completed === true;
      const newStatus = !currentStatus;

      console.log(
        `Toggling user status for ${user.name} (${user.id}): ${currentStatus} -> ${newStatus}`
      );

      // Get the current timestamp
      const timestamp = new Date().toISOString();

      // Update the user's profile
      try {
        await updateUserProfile(user.id, {
          profile_completed: newStatus,
          updated_at: timestamp,
        });
      } catch (updateError) {
        console.error("Error updating user status:", updateError);
        toast({
          title: "Error",
          description: "Failed to update user status. Please try again.",
          variant: "destructive",
        });
        throw updateError;
      }

      // Create a notification for the user
      try {
        // Determine if the user is a student or teacher
        const userRole = user.role;
        const notificationData: any = {
          title: newStatus ? "Account Activated" : "Account Blocked",
          message: newStatus
            ? "Your account has been activated by a system administrator. You now have full access to the system."
            : "Your account has been blocked by a system administrator. Please contact support for assistance.",
          type: "system_alert",
          read: false,
          created_at: timestamp,
        };

        // Set the appropriate ID field based on user role
        if (userRole === "student") {
          notificationData.student_id = user.id;
        } else if (userRole === "teacher") {
          notificationData.teacher_id = user.id;
        } else {
          // For admin users, we'll skip creating a notification since the table
          // doesn't have an admin_id field
          console.log("Skipping notification for admin user");
          return; // Exit the try block early
        }

        await supabase.from("notifications").insert(notificationData);
      } catch (notificationError) {
        console.error("Error creating notification:", notificationError);
        // Continue even if notification creation fails
      }

      // Update the local state with only existing columns
      setUsers(
        users.map((u) =>
          u.id === user.id
            ? {
                ...u,
                profile_completed: newStatus,
                is_verified: newStatus,
                updated_at: timestamp,
              }
            : u
        )
      );

      toast({
        title: newStatus
          ? `${
              user.role.charAt(0).toUpperCase() + user.role.slice(1)
            } Activated`
          : `${user.role.charAt(0).toUpperCase() + user.role.slice(1)} Blocked`,
        description: `${user.name} has been ${
          newStatus ? "activated" : "blocked"
        } successfully. The user will ${
          newStatus ? "now have" : "no longer have"
        } access to the system.`,
        duration: 5000,
      });

      // Don't force a refresh automatically - it might be causing issues
      // We'll let the real-time subscription handle updates
    } catch (error: any) {
      console.error("Error toggling user status:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to update user status",
        variant: "destructive",
      });
    }
  };

  // Function to toggle maintenance mode
  const toggleMaintenanceMode = async (user: User) => {
    try {
      // Get the current user's ID to check permissions
      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();

      if (!currentUser) {
        toast({
          title: "Error",
          description: "You must be logged in to perform this action",
          variant: "destructive",
        });
        return;
      }

      // Check if the current user is a system admin
      const { data: adminData, error: adminError } = await supabase
        .from("profiles")
        .select("role, access_level")
        .eq("id", currentUser.id)
        .single();

      if (adminError) {
        throw adminError;
      }

      if (
        !adminData ||
        adminData.role !== "admin" ||
        adminData.access_level < 2
      ) {
        toast({
          title: "Permission Denied",
          description: "Only system admins can perform this action",
          variant: "destructive",
        });
        return;
      }

      // Get the current user's maintenance mode status directly from the database
      const { data: userData, error: userError } = await supabase
        .from("profiles")
        .select("maintenance_mode, department")
        .eq("id", user.id)
        .single();

      if (userError) {
        throw userError;
      }

      // Check if the user is already in maintenance mode - ensure it's a boolean
      const isInMaintenanceMode = userData.maintenance_mode === true;

      // Log the actual database value for debugging
      console.log(
        `Current maintenance_mode value in database: ${userData.maintenance_mode}`
      );

      console.log(
        `Toggling maintenance mode for ${user.name} (${user.id}): ${
          isInMaintenanceMode ? "ON" : "OFF"
        } -> ${isInMaintenanceMode ? "OFF" : "ON"}`
      );

      // Toggle maintenance mode - flip the current value
      const newMaintenanceMode = !isInMaintenanceMode;

      // Get the current timestamp
      const timestamp = new Date().toISOString();

      // Update the profile with the new maintenance mode status
      try {
        await updateUserProfile(user.id, {
          maintenance_mode: newMaintenanceMode,
          updated_at: timestamp,
        });

        // Force a refresh to ensure we have the latest data
        setTimeout(() => {
          fetchUsers();
        }, 1000);
      } catch (updateError) {
        console.error("Error updating maintenance mode:", updateError);
        toast({
          title: "Error",
          description: "Failed to update maintenance mode. Please try again.",
          variant: "destructive",
        });
        throw updateError;
      }

      // Create a notification for the user
      try {
        // Determine if the user is a student or teacher
        const userRole = user.role;
        const notificationData: any = {
          title: isInMaintenanceMode
            ? "Maintenance Mode Disabled"
            : "Maintenance Mode Enabled",
          message: isInMaintenanceMode
            ? "Maintenance mode has been disabled for your account. You now have full access to the system."
            : "Your account has been placed in maintenance mode. Some features may be temporarily unavailable.",
          type: "system_alert",
          read: false,
          created_at: timestamp,
        };

        // Set the appropriate ID field based on user role
        if (userRole === "student") {
          notificationData.student_id = user.id;
        } else if (userRole === "teacher") {
          notificationData.teacher_id = user.id;
        } else {
          // For admin users, we'll skip creating a notification since the table
          // doesn't have an admin_id field
          console.log("Skipping notification for admin user");
          return; // Exit the try block early
        }

        await supabase.from("notifications").insert(notificationData);
      } catch (notificationError) {
        console.error("Error creating notification:", notificationError);
        // Continue even if notification creation fails
      }

      // Update the local state with the new maintenance mode status
      setUsers(
        users.map((u) =>
          u.id === user.id
            ? {
                ...u,
                maintenance_mode: newMaintenanceMode,
                updated_at: timestamp,
              }
            : u
        )
      );

      // Add a visual indicator to show the current state with the new status
      toast({
        title: newMaintenanceMode
          ? "Maintenance Mode Enabled"
          : "Maintenance Mode Disabled",
        description: newMaintenanceMode
          ? `Maintenance mode has been enabled for ${user.name}. The user will see a maintenance page when they log in.`
          : `Maintenance mode has been disabled for ${user.name}. The user will now have full access to the system.`,
        variant: newMaintenanceMode ? "warning" : "default",
        duration: 5000,
      });

      // Don't force a refresh automatically - it might be causing issues
      // We'll let the real-time subscription handle updates
    } catch (error: any) {
      console.error("Error toggling maintenance mode:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to toggle maintenance mode",
        variant: "destructive",
      });
    }
  };

  const handleOpenAddUser = (
    role: "admin" | "teacher" | "student" = "student"
  ) => {
    setAddUserRole(role);
    setShowAddUser(true);
  };

  // Filter users based on search query and active tab
  const filteredUsers = users.filter((user) => {
    // Filter out deleted users
    if (user.is_deleted === true) {
      console.log(`Filtering out deleted user: ${user.name} (${user.id})`);
      return false;
    }

    const matchesSearch =
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (user.school_name &&
        user.school_name.toLowerCase().includes(searchQuery.toLowerCase()));

    if (activeTab === "all-users") return matchesSearch;
    if (activeTab === "admins") return matchesSearch && user.role === "admin";
    if (activeTab === "teachers")
      return matchesSearch && user.role === "teacher";
    if (activeTab === "students")
      return matchesSearch && user.role === "student";
    if (activeTab === "verification")
      return matchesSearch && !user.profile_completed;

    return matchesSearch;
  });

  // Secondary navigation items
  const navItems = [
    {
      id: "all-users",
      label: "All Users",
      icon: <Users className="h-4 w-4" />,
    },
    {
      id: "admins",
      label: "Administrators",
      icon: <Shield className="h-4 w-4" />,
    },
    {
      id: "teachers",
      label: "Teachers",
      icon: <Users className="h-4 w-4" />,
    },
    {
      id: "students",
      label: "Students",
      icon: <Users className="h-4 w-4" />,
    },
    {
      id: "verification",
      label: "Verification",
      icon: <UserCheck className="h-4 w-4" />,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">User Administration</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      <SecondaryNavigation
        items={navItems}
        activeItem={activeTab}
        onItemChange={setActiveTab}
        className="mt-4"
      >
        {{
          "all-users": (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="relative w-full max-w-sm">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search users..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Button onClick={() => handleOpenAddUser()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Users</CardTitle>
                  <CardDescription>
                    Manage all users across the system
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="space-y-2">
                      {[...Array(5)].map((_, i) => (
                        <Skeleton key={i} className="h-12 w-full" />
                      ))}
                    </div>
                  ) : filteredUsers.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>School</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created At</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredUsers.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">
                              {user.name}
                            </TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  user.role === "admin"
                                    ? "default"
                                    : user.role === "teacher"
                                    ? "secondary"
                                    : "outline"
                                }
                              >
                                {user.role}
                                {user.role === "admin" &&
                                user.access_level === 3
                                  ? " (System)"
                                  : ""}
                              </Badge>
                            </TableCell>
                            <TableCell>{user.school_name}</TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                {user.profile_completed ? (
                                  <Badge variant="success">Active</Badge>
                                ) : (
                                  <Badge variant="destructive">Blocked</Badge>
                                )}
                                {user.maintenance_mode && (
                                  <Badge
                                    variant="warning"
                                    className="bg-amber-500"
                                  >
                                    Maintenance
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {format(new Date(user.created_at), "MMM d, yyyy")}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <Settings className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>
                                      User Actions
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() =>
                                        toggleUserActiveStatus(user)
                                      }
                                    >
                                      {user.profile_completed ? (
                                        <>
                                          <Ban className="mr-2 h-4 w-4 text-red-500" />
                                          <span>Block User</span>
                                        </>
                                      ) : (
                                        <>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Activate User</span>
                                        </>
                                      )}
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        toggleMaintenanceMode(user)
                                      }
                                    >
                                      <WrenchIcon className="mr-2 h-4 w-4 text-amber-500" />
                                      <span>Maintenance Mode</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleOpenDeleteDialog(user)
                                      }
                                      className="text-red-600"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      <span>Delete User</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      No users found
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredUsers.length} of {users.length} users
                  </div>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </CardFooter>
              </Card>
            </div>
          ),
          admins: (
            <Card>
              <CardHeader>
                <CardTitle>Administrators</CardTitle>
                <CardDescription>
                  Manage system and school administrators
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="relative w-full max-w-sm">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search administrators..."
                      className="pl-8"
                    />
                  </div>
                  <Button onClick={() => handleOpenAddUser("admin")}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Administrator
                  </Button>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Access Level</TableHead>
                        <TableHead>School</TableHead>
                        <TableHead>Created At</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users
                        .filter(
                          (user) =>
                            user.role === "admin" && user.is_deleted !== true
                        )
                        .map((admin) => (
                          <TableRow key={admin.id}>
                            <TableCell className="font-medium">
                              {admin.name}
                            </TableCell>
                            <TableCell>{admin.email}</TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  admin.access_level === 3
                                    ? "default"
                                    : "outline"
                                }
                              >
                                {admin.access_level === 3
                                  ? "System Admin"
                                  : "School Admin"}
                              </Badge>
                            </TableCell>
                            <TableCell>{admin.school_name}</TableCell>
                            <TableCell>
                              {format(
                                new Date(admin.created_at),
                                "MMM d, yyyy"
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <Settings className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>
                                      Admin Actions
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() =>
                                        toggleUserActiveStatus(admin)
                                      }
                                    >
                                      {admin.profile_completed ? (
                                        <>
                                          <Ban className="mr-2 h-4 w-4 text-red-500" />
                                          <span>Block Admin</span>
                                        </>
                                      ) : (
                                        <>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Activate Admin</span>
                                        </>
                                      )}
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        toggleMaintenanceMode(admin)
                                      }
                                    >
                                      <WrenchIcon className="mr-2 h-4 w-4 text-amber-500" />
                                      <span>Maintenance Mode</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleOpenDeleteDialog(admin)
                                      }
                                      className="text-red-600"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      <span>Delete Admin</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">
                    Access Level Management
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure access levels and permissions for administrators
                  </p>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-2 border-b">
                      <div>
                        <p className="font-medium">System Administrator</p>
                        <p className="text-xs text-muted-foreground">
                          Full access to all system features and schools
                        </p>
                      </div>
                      <Badge>Level 3</Badge>
                    </div>

                    <div className="flex items-center justify-between p-2 border-b">
                      <div>
                        <p className="font-medium">School Administrator</p>
                        <p className="text-xs text-muted-foreground">
                          Access to manage a specific school
                        </p>
                      </div>
                      <Badge variant="outline">Level 1-2</Badge>
                    </div>

                    <Button variant="outline" size="sm">
                      <Shield className="h-4 w-4 mr-2" />
                      Manage Permission Templates
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ),
          teachers: (
            <Card>
              <CardHeader>
                <CardTitle>Teachers</CardTitle>
                <CardDescription>
                  Manage teachers across all schools
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="relative w-full max-w-sm">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search teachers..."
                      className="pl-8"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline">
                      <FileText className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button onClick={() => handleOpenAddUser("teacher")}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Teacher
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Teacher ID</TableHead>
                        <TableHead>School</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users
                        .filter(
                          (user) =>
                            user.role === "teacher" && user.is_deleted !== true
                        )
                        .slice(0, 5)
                        .map((teacher) => (
                          <TableRow key={teacher.id}>
                            <TableCell className="font-medium">
                              {teacher.name}
                            </TableCell>
                            <TableCell>{teacher.email}</TableCell>
                            <TableCell>
                              {teacher.teacher_id ||
                                "T-" + teacher.id.substring(0, 6)}
                            </TableCell>
                            <TableCell>{teacher.school_name}</TableCell>
                            <TableCell>
                              {teacher.department || "General"}
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                <Badge
                                  variant={
                                    teacher.profile_completed
                                      ? "success"
                                      : "destructive"
                                  }
                                >
                                  {teacher.profile_completed
                                    ? "Active"
                                    : "Blocked"}
                                </Badge>
                                {teacher.maintenance_mode && (
                                  <Badge
                                    variant="warning"
                                    className="bg-amber-500"
                                  >
                                    Maintenance
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <Settings className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>
                                      Teacher Actions
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() =>
                                        toggleUserActiveStatus(teacher)
                                      }
                                    >
                                      {teacher.profile_completed ? (
                                        <>
                                          <Ban className="mr-2 h-4 w-4 text-red-500" />
                                          <span>Block Teacher</span>
                                        </>
                                      ) : (
                                        <>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Activate Teacher</span>
                                        </>
                                      )}
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        toggleMaintenanceMode(teacher)
                                      }
                                    >
                                      <WrenchIcon className="mr-2 h-4 w-4 text-amber-500" />
                                      <span>Maintenance Mode</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleOpenDeleteDialog(teacher)
                                      }
                                      className="text-red-600"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      <span>Delete Teacher</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-between">
                  <p className="text-sm text-muted-foreground">
                    Showing{" "}
                    {
                      users
                        .filter(
                          (user) =>
                            user.role === "teacher" && user.is_deleted !== true
                        )
                        .slice(0, 5).length
                    }{" "}
                    of{" "}
                    {
                      users.filter(
                        (user) =>
                          user.role === "teacher" && user.is_deleted !== true
                      ).length
                    }{" "}
                    teachers
                  </p>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">Bulk Operations</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button variant="outline">Import Teachers from CSV</Button>
                    <Button variant="outline">Bulk Update Status</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ),
          students: (
            <Card>
              <CardHeader>
                <CardTitle>Students</CardTitle>
                <CardDescription>
                  Manage students across all schools
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="relative w-full max-w-sm">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search students..."
                      className="pl-8"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline">
                      <FileText className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <Button onClick={() => handleOpenAddUser("student")}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Student
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Student ID</TableHead>
                        <TableHead>School</TableHead>
                        <TableHead>Grade/Year</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users
                        .filter(
                          (user) =>
                            user.role === "student" && user.is_deleted !== true
                        )
                        .slice(0, 5)
                        .map((student) => (
                          <TableRow key={student.id}>
                            <TableCell className="font-medium">
                              {student.name}
                            </TableCell>
                            <TableCell>{student.email}</TableCell>
                            <TableCell>
                              {student.student_id ||
                                "S-" + student.id.substring(0, 6)}
                            </TableCell>
                            <TableCell>{student.school_name}</TableCell>
                            <TableCell>{student.grade || "N/A"}</TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                <Badge
                                  variant={
                                    student.profile_completed
                                      ? "success"
                                      : "destructive"
                                  }
                                >
                                  {student.profile_completed
                                    ? "Active"
                                    : "Blocked"}
                                </Badge>
                                {student.maintenance_mode && (
                                  <Badge
                                    variant="warning"
                                    className="bg-amber-500"
                                  >
                                    Maintenance
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <Settings className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>
                                      Student Actions
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() =>
                                        toggleUserActiveStatus(student)
                                      }
                                    >
                                      {student.profile_completed ? (
                                        <>
                                          <Ban className="mr-2 h-4 w-4 text-red-500" />
                                          <span>Block Student</span>
                                        </>
                                      ) : (
                                        <>
                                          <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                          <span>Activate Student</span>
                                        </>
                                      )}
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() =>
                                        toggleMaintenanceMode(student)
                                      }
                                    >
                                      <WrenchIcon className="mr-2 h-4 w-4 text-amber-500" />
                                      <span>Maintenance Mode</span>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleOpenDeleteDialog(student)
                                      }
                                      className="text-red-600"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      <span>Delete Student</span>
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-between">
                  <p className="text-sm text-muted-foreground">
                    Showing{" "}
                    {
                      users
                        .filter(
                          (user) =>
                            user.role === "student" && user.is_deleted !== true
                        )
                        .slice(0, 5).length
                    }{" "}
                    of{" "}
                    {
                      users.filter(
                        (user) =>
                          user.role === "student" && user.is_deleted !== true
                      ).length
                    }{" "}
                    students
                  </p>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">Bulk Operations</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button variant="outline">Import Students from CSV</Button>
                    <Button variant="outline">Bulk Update Status</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ),
          verification: (
            <Card>
              <CardHeader>
                <CardTitle>User Verification</CardTitle>
                <CardDescription>
                  Manage user verification status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="relative w-full max-w-sm">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search users..."
                      className="pl-8"
                    />
                  </div>
                  <select className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                    <option value="all">All Users</option>
                    <option value="unverified">Unverified Only</option>
                    <option value="verified">Verified Only</option>
                  </select>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>School</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Registered</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users
                        .filter(
                          (user) =>
                            !user.profile_completed && user.is_deleted !== true
                        )
                        .slice(0, 5)
                        .map((user) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">
                              {user.name}
                            </TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {user.role.charAt(0).toUpperCase() +
                                  user.role.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>{user.school_name}</TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                <Badge variant="destructive">Blocked</Badge>
                                {user.maintenance_mode && (
                                  <Badge
                                    variant="warning"
                                    className="bg-amber-500"
                                  >
                                    Maintenance
                                  </Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {format(new Date(user.created_at), "MMM d, yyyy")}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8"
                                >
                                  <UserCheck className="h-4 w-4 mr-1" />
                                  Verify
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8"
                                >
                                  <UserX className="h-4 w-4 mr-1" />
                                  Reject
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-between">
                  <p className="text-sm text-muted-foreground">
                    Showing{" "}
                    {
                      users
                        .filter(
                          (user) =>
                            !user.profile_completed && user.is_deleted !== true
                        )
                        .slice(0, 5).length
                    }{" "}
                    of{" "}
                    {
                      users.filter(
                        (user) =>
                          !user.profile_completed && user.is_deleted !== true
                      ).length
                    }{" "}
                    blocked users
                  </p>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </div>

                <div className="border p-4 rounded-md">
                  <h3 className="text-lg font-medium mb-4">
                    Verification Settings
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-verify">
                          Auto-Verify New Users
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Automatically verify new users upon registration
                        </p>
                      </div>
                      <Switch id="auto-verify" />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="email-verification">
                          Require Email Verification
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Users must verify their email address
                        </p>
                      </div>
                      <Switch id="email-verification" defaultChecked />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="admin-approval">
                          Require Admin Approval
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Administrators must approve new user accounts
                        </p>
                      </div>
                      <Switch id="admin-approval" defaultChecked />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ),
        }}
      </SecondaryNavigation>

      {/* User Settings Dialog */}
      <UserSettings
        user={selectedUser}
        open={showUserSettings}
        onClose={() => setShowUserSettings(false)}
        onUpdate={fetchUsers}
      />

      {/* Add User Dialog */}
      <AddUserDialog
        open={showAddUser}
        onClose={() => setShowAddUser(false)}
        onUserAdded={fetchUsers}
        defaultRole={addUserRole}
      />

      {/* Delete User Dialog */}
      <DeleteUserDialog
        user={selectedUser}
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onUserDeleted={() => {
          // First update the local state to remove the user
          if (selectedUser) {
            setUsers(users.filter((u) => u.id !== selectedUser.id));
          }
          // Then fetch the latest data from the server
          fetchUsers();
        }}
      />
    </div>
  );
}
