import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

/**
 * This component forces a re-render of the entire application when the language changes
 */
export const TranslationUpdater: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { i18n } = useTranslation();
  const [key, setKey] = useState(0);

  useEffect(() => {
    // Force re-render when language changes
    const handleLanguageChange = (lng: string) => {
      // Update the key to force a re-render
      setKey((prevKey) => prevKey + 1);
    };

    // Add event listener
    i18n.on("languageChanged", handleLanguageChange);

    // Clean up
    return () => {
      i18n.off("languageChanged", handleLanguageChange);
    };
  }, [i18n]);

  // Re-render the entire application with a new key when language changes
  return <React.Fragment key={key}>{children}</React.Fragment>;
};
