# Database Permissions Fix

To fix the database permissions for the system_settings table, run the following SQL in the Supabase SQL Editor:

```sql
-- Create system_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS system_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_name TEXT NOT NULL UNIQUE,
  setting_value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Drop existing policies
DROP POLICY IF EXISTS "Ad<PERSON> can view system settings" ON system_settings;
DROP POLICY IF EXISTS "Ad<PERSON> can insert system settings" ON system_settings;
DROP POLICY IF EXISTS "Ad<PERSON> can update system settings" ON system_settings;
DROP POLICY IF EXISTS "Ad<PERSON> can delete system settings" ON system_settings;

-- Enable RLS
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Create more permissive policies
CREATE POLICY "Anyone can view system settings" ON system_settings
  FOR SELECT USING (true);

CREATE POLICY "Anyone can insert system settings" ON system_settings
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can update system settings" ON system_settings
  FOR UPDATE USING (true);

CREATE POLICY "Anyone can delete system settings" ON system_settings
  FOR DELETE USING (true);
```

## CORS Issues with SendGrid and Twilio

If you encounter CORS issues when trying to send emails or SMS messages, you'll need to set up a server-side proxy or use Supabase Edge Functions to handle these API calls. The current implementation attempts to call these APIs directly from the browser, which may be blocked by CORS policies.

For a production environment, consider:

1. Creating a Supabase Edge Function to handle these API calls
2. Setting up a server-side proxy
3. Using a CORS proxy service (not recommended for production)

## Testing Email and SMS

To test the email and SMS functionality:

1. Enter valid API credentials in the Email & SMS Services tab
2. Save the configuration
3. Enter a test email address or phone number
4. Click the Test button

If you encounter CORS errors, you may need to use a browser extension that disables CORS checks for testing purposes, but this is not recommended for production use.
