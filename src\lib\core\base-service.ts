/**
 * Base Service Class
 * Provides common functionality for all service classes
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../database.types';
import { APIResponse, QueryParams } from '../types/api';

export abstract class BaseService {
  protected supabase: SupabaseClient<Database>;
  protected tableName: string;

  constructor(supabase: SupabaseClient<Database>, tableName: string) {
    this.supabase = supabase;
    this.tableName = tableName;
  }

  /**
   * Get current user's school ID
   */
  protected async getCurrentSchoolId(): Promise<string | null> {
    const { data: { user } } = await this.supabase.auth.getUser();
    if (!user) return null;

    const { data: profile } = await this.supabase
      .from('profiles')
      .select('school_id')
      .eq('user_id', user.id)
      .single();

    return profile?.school_id || null;
  }

  /**
   * Apply school-level filtering to queries
   */
  protected applySchoolFilter(query: any, schoolId?: string) {
    if (schoolId) {
      return query.eq('school_id', schoolId);
    }
    return query;
  }

  /**
   * Apply pagination to queries
   */
  protected applyPagination(query: any, params: QueryParams) {
    const { page = 1, pageSize = 20 } = params;
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    return query.range(from, to);
  }

  /**
   * Apply sorting to queries
   */
  protected applySorting(query: any, params: QueryParams) {
    const { sort, order = 'asc' } = params;
    if (sort) {
      return query.order(sort, { ascending: order === 'asc' });
    }
    return query;
  }

  /**
   * Apply search filtering to queries
   */
  protected applySearch(query: any, params: QueryParams, searchColumns: string[]) {
    const { search } = params;
    if (search && searchColumns.length > 0) {
      const searchConditions = searchColumns
        .map(column => `${column}.ilike.%${search}%`)
        .join(',');
      return query.or(searchConditions);
    }
    return query;
  }

  /**
   * Handle database errors consistently
   */
  protected handleError(error: any): APIResponse {
    console.error(`${this.constructor.name} Error:`, error);
    
    return {
      success: false,
      status: error.status || 500,
      error: error.message || 'An unexpected error occurred',
      data: null,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create success response
   */
  protected createSuccessResponse<T>(data: T, message?: string): APIResponse<T> {
    return {
      success: true,
      status: 200,
      data,
      message,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Validate required fields
   */
  protected validateRequired(data: Record<string, any>, requiredFields: string[]): string[] {
    const errors: string[] = [];
    
    for (const field of requiredFields) {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        errors.push(`${field} is required`);
      }
    }
    
    return errors;
  }

  /**
   * Sanitize data for database insertion
   */
  protected sanitizeData(data: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (value !== undefined && value !== null) {
        if (typeof value === 'string') {
          sanitized[key] = value.trim();
        } else {
          sanitized[key] = value;
        }
      }
    }
    
    return sanitized;
  }

  /**
   * Check if user has permission for school-level operations
   */
  protected async checkSchoolPermission(schoolId: string): Promise<boolean> {
    const currentSchoolId = await this.getCurrentSchoolId();
    return currentSchoolId === schoolId;
  }

  /**
   * Get user role
   */
  protected async getUserRole(): Promise<string | null> {
    const { data: { user } } = await this.supabase.auth.getUser();
    if (!user) return null;

    const { data: profile } = await this.supabase
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    return profile?.role || null;
  }

  /**
   * Check if user has specific role
   */
  protected async hasRole(role: string): Promise<boolean> {
    const userRole = await this.getUserRole();
    return userRole === role;
  }

  /**
   * Check if user is system admin
   */
  protected async isSystemAdmin(): Promise<boolean> {
    return await this.hasRole('system_admin');
  }

  /**
   * Log audit trail for important operations
   */
  protected async logAudit(action: string, resourceType: string, resourceId: string, oldValues?: any, newValues?: any) {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user) return;

      await this.supabase.from('audit_logs').insert({
        user_id: user.id,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        old_values: oldValues,
        new_values: newValues,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to log audit trail:', error);
    }
  }
}
