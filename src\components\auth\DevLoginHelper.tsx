import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

/**
 * Development-only component to help with login issues
 * This component is only rendered in development mode
 */
export function DevLoginHelper() {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Only show in development mode
  if (!import.meta.env.DEV) {
    return null;
  }

  const handleDevLogin = async () => {
    setIsLoading(true);
    try {
      // Try to sign in with the test account
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'password123',
      });

      if (error) {
        console.error('Dev login error:', error);
        toast({
          title: 'Dev Login Failed',
          description: error.message,
          variant: 'destructive',
        });
        return;
      }

      if (data.user) {
        toast({
          title: 'Dev Login Successful',
          description: `Logged in as ${data.user.email}`,
        });

        // Get the user's profile
        const { data: profileData } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', data.user.id)
          .single();

        if (profileData) {
          // Redirect based on role
          const role = profileData.role;
          navigate(`/${role}`);
        } else {
          navigate('/');
        }
      }
    } catch (error: any) {
      console.error('Dev login error:', error);
      toast({
        title: 'Dev Login Failed',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
      <h3 className="text-sm font-medium text-yellow-800 mb-2">Development Tools</h3>
      <p className="text-xs text-yellow-700 mb-3">
        Having trouble logging in? Use this button to bypass normal authentication in development mode.
      </p>
      <Button
        variant="outline"
        size="sm"
        onClick={handleDevLogin}
        disabled={isLoading}
        className="bg-yellow-100 border-yellow-300 text-yellow-800 hover:bg-yellow-200"
      >
        {isLoading ? 'Logging in...' : 'Dev Login (<EMAIL>)'}
      </Button>
    </div>
  );
}
