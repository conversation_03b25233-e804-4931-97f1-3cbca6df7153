import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import Navbar from "@/components/shared/Navbar";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { useTheme } from "@/components/providers/ThemeProvider";
import { useToast } from "@/hooks/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import {
  LayoutDashboard,
  Users,
  Settings,
  Building2,
  Shield,
  BarChart4,
  FileText,
  User,
  AlertTriangle,
  MessageSquare,
  FootprintsIcon,
} from "lucide-react";
import SystemOverview from "@/components/system-admin/SystemOverview";
import SchoolManagement from "@/components/system-admin/SchoolManagement";
import UserAdministration from "@/components/system-admin/UserAdministration";
import SecurityCenter from "@/components/system-admin/SecurityCenter";
import SystemConfiguration from "@/components/system-admin/SystemConfiguration";
import ReportsAnalytics from "@/components/system-admin/ReportsAnalytics";
import SystemAdminProfile from "@/components/system-admin/SystemAdminProfile";
import FooterSettings from "@/components/system-admin/FooterSettings";
import FeedbackManagement from "@/components/system-admin/FeedbackManagement";
import BreadcrumbNav from "@/components/system-admin/navigation/BreadcrumbNav";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { fetchSystemStats } from "@/lib/api/system-admin";

export default function SystemAdmin() {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const { currentSchool } = useSchool();
  const { theme } = useTheme();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState(() => {
    // Try to get the tab from localStorage
    const storedTab = localStorage.getItem("systemAdminActiveTab");
    return storedTab || "overview";
  });
  const [loading, setLoading] = useState(true);
  const [systemStats, setSystemStats] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Check if user is a system admin
  useEffect(() => {
    if (!user) {
      navigate("/login");
      return;
    }

    if (!profile) {
      return;
    }

    // Redirect if not a system admin (access_level 3)
    if (profile.role !== "admin" || profile.accessLevel !== 3) {
      toast({
        title: "Access Denied",
        description:
          "You don't have permission to access the System Admin dashboard",
        variant: "destructive",
      });
      navigate("/admin");
    }
  }, [user, profile, navigate, toast]);

  // Fetch system stats
  useEffect(() => {
    const getSystemStats = async () => {
      try {
        setLoading(true);
        const stats = await fetchSystemStats();
        setSystemStats(stats);
        setError(null);
      } catch (err) {
        console.error("Error fetching system stats:", err);
        setError("Failed to load system statistics");
        toast({
          title: "Error",
          description: "Failed to load system statistics",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (profile?.accessLevel === 3) {
      getSystemStats();
    }
  }, [profile, toast]);

  // Generate breadcrumb items based on active tab
  const getBreadcrumbItems = () => {
    const items = [{ label: "System Admin", href: "/system-admin" }];

    switch (activeTab) {
      case "overview":
        items.push({ label: "Overview", href: "/system-admin?tab=overview" });
        break;
      case "schools":
        items.push({
          label: "School Management",
          href: "/system-admin?tab=schools",
        });
        break;
      case "users":
        items.push({
          label: "User Administration",
          href: "/system-admin?tab=users",
        });
        break;
      case "security":
        items.push({
          label: "Security Center",
          href: "/system-admin?tab=security",
        });
        break;
      case "configuration":
        items.push({
          label: "System Configuration",
          href: "/system-admin?tab=configuration",
        });
        break;
      case "reports":
        items.push({
          label: "Reports & Analytics",
          href: "/system-admin?tab=reports",
        });
        break;
      case "profile":
        items.push({ label: "Profile", href: "/system-admin?tab=profile" });
        break;
      case "footer":
        items.push({
          label: "Footer Settings",
          href: "/system-admin?tab=footer",
        });
        break;
      case "feedback":
        items.push({
          label: "Feedback Management",
          href: "/system-admin?tab=feedback",
        });
        break;
    }

    return items;
  };

  // Loading state
  if (!user || !profile || loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-muted-foreground">
            Loading System Administration...
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container mx-auto px-4 py-6 flex-1 flex items-center justify-center">
          <div className="text-center">
            <AlertTriangle className="h-16 w-16 text-destructive mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">Error Loading Dashboard</h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="container mx-auto px-4 py-6 flex-1"
      >
        <div className="mb-6">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-3xl font-bold mb-2"
          >
            System Administration
          </motion.h1>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <BreadcrumbNav items={getBreadcrumbItems()} />
          </motion.div>
        </div>

        <Tabs
          value={activeTab}
          onValueChange={(value) => {
            setActiveTab(value);
            localStorage.setItem("systemAdminActiveTab", value);
          }}
          className="w-full"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="mb-6 p-2 overflow-x-auto">
              <TabsList className="flex flex-nowrap min-w-max">
                {/* Primary tabs - always visible */}
                <TabsTrigger
                  value="overview"
                  className="flex items-center gap-2"
                >
                  <LayoutDashboard className="h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="schools"
                  className="flex items-center gap-2"
                >
                  <Building2 className="h-4 w-4" />
                  School Management
                </TabsTrigger>
                <TabsTrigger value="users" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  User Administration
                </TabsTrigger>
                <TabsTrigger
                  value="security"
                  className="flex items-center gap-2"
                >
                  <Shield className="h-4 w-4" />
                  Security Center
                </TabsTrigger>
                <TabsTrigger
                  value="configuration"
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  System Configuration
                </TabsTrigger>
                <TabsTrigger
                  value="reports"
                  className="flex items-center gap-2"
                >
                  <BarChart4 className="h-4 w-4" />
                  Reports & Analytics
                </TabsTrigger>
                <TabsTrigger
                  value="profile"
                  className="flex items-center gap-2"
                >
                  <User className="h-4 w-4" />
                  Profile
                </TabsTrigger>
              </TabsList>
            </Card>
          </motion.div>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              <TabsContent value="overview" className="mt-0">
                <SystemOverview stats={systemStats} />
              </TabsContent>

              <TabsContent value="schools" className="mt-0">
                <SchoolManagement />
              </TabsContent>

              <TabsContent value="users" className="mt-0">
                <UserAdministration />
              </TabsContent>

              <TabsContent value="security" className="mt-0">
                <SecurityCenter />
              </TabsContent>

              <TabsContent value="configuration" className="mt-0">
                <SystemConfiguration />
              </TabsContent>

              <TabsContent value="reports" className="mt-0">
                <ReportsAnalytics />
              </TabsContent>

              <TabsContent value="profile" className="mt-0">
                <SystemAdminProfile />
              </TabsContent>
            </motion.div>
          </AnimatePresence>
        </Tabs>
      </motion.div>
    </div>
  );
}
