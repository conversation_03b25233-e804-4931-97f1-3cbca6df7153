#!/usr/bin/env node

import { execSync, spawn } from "child_process";
import fs from "fs";
import path from "path";
import os from "os";

console.log("🔧 Setting up HTTPS for local development...\n");

// Function to run command and handle errors
function runCommand(command, description) {
  try {
    console.log(`📋 ${description}...`);
    const result = execSync(command, { stdio: "pipe", encoding: "utf8" });
    console.log(`✅ ${description} completed successfully`);
    return result;
  } catch (error) {
    console.log(`⚠️  ${description} failed: ${error.message}`);
    return null;
  }
}

// Check if mkcert is installed
function checkMkcert() {
  console.log("🔍 Checking if mkcert is installed...");

  try {
    const version = execSync("mkcert -version", {
      stdio: "pipe",
      encoding: "utf8",
    });
    console.log(`✅ mkcert is installed: ${version.trim()}`);
    return true;
  } catch (error) {
    console.log("❌ mkcert is not installed globally");
    return false;
  }
}

// Install mkcert based on platform
function installMkcert() {
  const platform = os.platform();
  console.log(`🔧 Installing mkcert for ${platform}...`);

  try {
    if (platform === "win32") {
      // Try chocolatey first, then scoop
      try {
        execSync("choco install mkcert", { stdio: "inherit" });
        console.log("✅ mkcert installed via Chocolatey");
      } catch (chocoError) {
        try {
          execSync("scoop install mkcert", { stdio: "inherit" });
          console.log("✅ mkcert installed via Scoop");
        } catch (scoopError) {
          console.log("❌ Could not install mkcert automatically");
          console.log("📋 Please install mkcert manually:");
          console.log("   - Via Chocolatey: choco install mkcert");
          console.log("   - Via Scoop: scoop install mkcert");
          console.log(
            "   - Or download from: https://github.com/FiloSottile/mkcert/releases"
          );
          return false;
        }
      }
    } else if (platform === "darwin") {
      execSync("brew install mkcert", { stdio: "inherit" });
      console.log("✅ mkcert installed via Homebrew");
    } else {
      // Linux
      console.log("📋 Please install mkcert manually for Linux:");
      console.log(
        "   - Ubuntu/Debian: apt install libnss3-tools && wget -O mkcert https://github.com/FiloSottile/mkcert/releases/latest/download/mkcert-v*-linux-amd64 && chmod +x mkcert && sudo mv mkcert /usr/local/bin/"
      );
      console.log(
        "   - Or follow instructions at: https://github.com/FiloSottile/mkcert#installation"
      );
      return false;
    }
    return true;
  } catch (error) {
    console.log(`❌ Failed to install mkcert: ${error.message}`);
    return false;
  }
}

// Setup mkcert CA
function setupMkcertCA() {
  console.log("🔧 Setting up mkcert Certificate Authority...");

  try {
    execSync("mkcert -install", { stdio: "inherit" });
    console.log("✅ mkcert CA installed successfully");
    return true;
  } catch (error) {
    console.log(`❌ Failed to setup mkcert CA: ${error.message}`);
    return false;
  }
}

// Generate certificates
function generateCertificates() {
  console.log("🔐 Generating SSL certificates...");

  // Create certs directory if it doesn't exist
  const certsDir = path.join(__dirname, "certs");
  if (!fs.existsSync(certsDir)) {
    fs.mkdirSync(certsDir);
    console.log("📁 Created certs directory");
  }

  try {
    // Generate certificates for localhost and your local IP
    const command =
      "mkcert -key-file certs/localhost-key.pem -cert-file certs/localhost.pem localhost 127.0.0.1 ::1";
    execSync(command, { stdio: "inherit" });
    console.log("✅ SSL certificates generated successfully");

    // Also generate for common local network IPs
    try {
      const networkCommand =
        "mkcert -key-file certs/network-key.pem -cert-file certs/network.pem localhost 127.0.0.1 ::1 *.local 192.168.*.* 10.0.*.* 172.16.*.*";
      execSync(networkCommand, { stdio: "inherit" });
      console.log("✅ Network SSL certificates generated successfully");
    } catch (networkError) {
      console.log(
        "⚠️  Network certificates generation failed, but localhost certificates are ready"
      );
    }

    return true;
  } catch (error) {
    console.log(`❌ Failed to generate certificates: ${error.message}`);
    return false;
  }
}

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === "IPv4" && !iface.internal) {
        return iface.address;
      }
    }
  }
  return "localhost";
}

// Main setup function
async function main() {
  console.log("🚀 Starting HTTPS setup for biometric authentication...\n");

  // Check if mkcert is installed
  if (!checkMkcert()) {
    console.log("\n🔧 Installing mkcert...");
    if (!installMkcert()) {
      console.log(
        "\n❌ Setup failed. Please install mkcert manually and run this script again."
      );
      process.exit(1);
    }
  }

  // Setup CA
  if (!setupMkcertCA()) {
    console.log("\n❌ Failed to setup Certificate Authority");
    process.exit(1);
  }

  // Generate certificates
  if (!generateCertificates()) {
    console.log("\n❌ Failed to generate certificates");
    process.exit(1);
  }

  const localIP = getLocalIP();

  console.log("\n🎉 HTTPS setup completed successfully!");
  console.log("\n📋 Next steps:");
  console.log("1. Run the development server with: npm run dev:https");
  console.log(`2. Access your app on your phone at: https://${localIP}:5173`);
  console.log(
    "3. Accept the security warning on your phone (the certificate is now trusted)"
  );
  console.log("4. Test biometric authentication!");
  console.log(
    "\n🔐 Your app now supports WebAuthn/biometric authentication on mobile devices!"
  );
}

// Run the setup
main().catch(console.error);
