import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@/lib/utils";

// This is a custom TabsContent component that doesn't use aria-hidden
// It uses the 'inert' attribute instead, which is more accessible
const AccessibleTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    // Override Radix UI's default behavior to use inert instead of aria-hidden
    tabIndex={0}
    {...props}
  />
));
AccessibleTabsContent.displayName = "AccessibleTabsContent";

export { AccessibleTabsContent };
