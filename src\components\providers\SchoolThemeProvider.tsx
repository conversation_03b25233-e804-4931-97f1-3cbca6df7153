import React, { createContext, useContext, useEffect, useState } from "react";
import { useSchool } from "@/context/SchoolContext";

interface SchoolThemeContextType {
  primaryColor: string;
  secondaryColor: string;
  logoUrl: string | null;
  applyTheme: (primaryColor: string, secondaryColor: string) => Promise<void>;
  setLogoUrl: (url: string | null) => void;
}

const SchoolThemeContext = createContext<SchoolThemeContextType>({
  primaryColor: "#4f46e5", // Default indigo
  secondaryColor: "#f97316", // Default orange
  logoUrl: null,
  applyTheme: () => Promise.resolve(),
  setLogoUrl: () => {},
});

export const useSchoolTheme = () => useContext(SchoolThemeContext);

interface SchoolThemeProviderProps {
  children: React.ReactNode;
}

export function SchoolThemeProvider({ children }: SchoolThemeProviderProps) {
  const { currentSchool } = useSchool();
  const [primaryColor, setPrimaryColor] = useState("#4f46e5");
  const [secondaryColor, setSecondaryColor] = useState("#f97316");
  const [logoUrl, setLogoUrl] = useState<string | null>(null);

  // Apply theme when school changes
  useEffect(() => {
    if (currentSchool) {
      // Load branding from database immediately
      loadBrandingFromDatabase(currentSchool.id);

      // Set up real-time subscription
      setupBrandingSubscription(currentSchool.id);

      // Cleanup function
      return () => {
        cleanupBrandingSubscription();
      };
    }
  }, [currentSchool]);

  // Load branding data from the dedicated school_branding table
  const loadBrandingFromDatabase = async (schoolId: string) => {
    try {

      
      // Import supabase client
      const { supabase } = await import("@/lib/supabase");

      // Try to get branding data from the dedicated table
      const { data: brandingData, error: brandingError } = await supabase
        .from("school_branding")
        .select("*")
        .eq("school_id", schoolId)
        .maybeSingle();



      if (brandingData && !brandingError) {
        // Update state with the branding data
        const primary = brandingData.primary_color;
        const secondary = brandingData.secondary_color;



        if (primary) {
          setPrimaryColor(primary);
        }
        if (secondary) {
          setSecondaryColor(secondary);
        }

        if (brandingData.logo_url) {
          setLogoUrl(brandingData.logo_url);
        }

        // ALWAYS apply the theme - this is the key fix
        if (primary && secondary) {
          applyThemeToDocument(primary, secondary);
          
          // Also inject CSS directly to ensure immediate application
          injectBrandingCSS(primary, secondary);
        }
      } else {
        // No branding found or error occurred
        console.log("SchoolThemeProvider: No branding found, using defaults");
        
        // PGRST116 is "not found" error, which is expected if no record exists yet
        if (brandingError && brandingError.code !== "PGRST116") {
          console.error(
            "SchoolThemeProvider: Error loading branding data:",
            brandingError
          );
        }
      }
    } catch (error) {
      console.error(
        "SchoolThemeProvider: Error in loadBrandingFromDatabase:",
        error
      );
    }
  };

  // Function to inject branding CSS directly into the document head for immediate application
  const injectBrandingCSS = (primary: string, secondary: string) => {
    try {
      // Remove any existing branding style
      const existingStyle = document.getElementById('school-branding-css');
      if (existingStyle) {
        existingStyle.remove();
      }

      // Convert hex to HSL
      const primaryHsl = hexToHSL(primary);
      const secondaryHsl = hexToHSL(secondary);

      // Create CSS that overrides all default colors
      const css = `
        :root {
          --primary: ${primaryHsl} !important;
          --primary-color: ${primary} !important;
          --secondary: ${secondaryHsl} !important;
          --secondary-color: ${secondary} !important;
          --ring: ${primaryHsl} !important;
          --sidebar-primary: ${primaryHsl} !important;
          --sidebar-ring: ${primaryHsl} !important;
        }
        
        .dark {
          --primary: ${primaryHsl} !important;
          --primary-color: ${primary} !important;
          --navbar-bg: ${primary} !important;
          --sidebar-primary: ${primaryHsl} !important;
          --sidebar-ring: ${primaryHsl} !important;
        }
      `;

      // Create and inject the style element
      const style = document.createElement('style');
      style.id = 'school-branding-css';
      style.textContent = css;
      document.head.appendChild(style);


    } catch (error) {
      console.error("SchoolThemeProvider: Error injecting branding CSS:", error);
    }
  };

  // Function to apply theme colors to CSS variables
  const applyThemeToDocument = (primary: string, secondary: string) => {
    try {
      
      // Convert hex to HSL for CSS variables
      const primaryHsl = hexToHSL(primary);
      const secondaryHsl = hexToHSL(secondary);

      // Apply to document root - CSS variables for both light and dark mode
      document.documentElement.style.setProperty("--primary", primaryHsl);
      document.documentElement.style.setProperty("--secondary", secondaryHsl);
      document.documentElement.style.setProperty("--primary-color", primary);
      document.documentElement.style.setProperty("--secondary-color", secondary);
      document.documentElement.style.setProperty("--ring", primaryHsl);
      document.documentElement.style.setProperty("--sidebar-primary", primaryHsl);
      document.documentElement.style.setProperty("--sidebar-ring", primaryHsl);

      // Update theme-color meta tag for mobile browsers
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute("content", primary);
      }



      // Force a CSS refresh
      document.documentElement.classList.add("theme-refreshing");
      setTimeout(() => {
        document.documentElement.classList.remove("theme-refreshing");
      }, 100);

    } catch (error) {
      console.error("SchoolThemeProvider: Error applying theme to document:", error);
      
      // Fallback to dynamic import if the local function fails
      import("@/lib/utils/color-utils")
        .then(({ hexToHSL }) => {
          // Convert hex to HSL for CSS variables
          const primaryHsl = hexToHSL(primary);
          const secondaryHsl = hexToHSL(secondary);

          // Apply to document root
          document.documentElement.style.setProperty("--primary", primaryHsl);
          document.documentElement.style.setProperty("--secondary", secondaryHsl);
          document.documentElement.style.setProperty("--primary-color", primary);
          document.documentElement.style.setProperty("--secondary-color", secondary);


        })
        .catch((importError) => {
          console.error("Error importing color utilities:", importError);
        });
    }
  };

  // Helper function to convert hex color to HSL string
  const hexToHSL = (hex: string): string => {
    // Remove the # if present
    hex = hex.replace(/^#/, "");

    // Parse the hex values
    let r = 0, g = 0, b = 0;
    if (hex.length === 3) {
      r = parseInt(hex[0] + hex[0], 16);
      g = parseInt(hex[1] + hex[1], 16);
      b = parseInt(hex[2] + hex[2], 16);
    } else if (hex.length === 6) {
      r = parseInt(hex.substring(0, 2), 16);
      g = parseInt(hex.substring(2, 4), 16);
      b = parseInt(hex.substring(4, 6), 16);
    } else {
      // Default to black if invalid hex
      return "0 0% 0%";
    }

    // Convert RGB to HSL
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0, l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r:
          h = (g - b) / d + (g < b ? 6 : 0);
          break;
        case g:
          h = (b - r) / d + 2;
          break;
        case b:
          h = (r - g) / d + 4;
          break;
      }

      h /= 6;
    }

    // Convert to degrees, percentage, percentage
    h = Math.round(h * 360);
    s = Math.round(s * 100);
    l = Math.round(l * 100);

    return `${h} ${s}% ${l}%`;
  };

  // Set up real-time subscription to the school_branding table
  const setupBrandingSubscription = async (schoolId: string) => {
    try {
      const { supabase } = await import("@/lib/supabase");

      // Create a subscription to the school_branding table
      const channelName = `school-branding-${schoolId}-${Date.now()}`;
      const brandingSubscription = supabase
        .channel(channelName)
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "school_branding",
            filter: `school_id=eq.${schoolId}`,
          },
          (payload) => {
            // Update the theme with the new branding data
            if (payload.new) {
              const newBranding = payload.new as any;

              // Update state with the new branding data
              if (newBranding.primary_color) {
                setPrimaryColor(newBranding.primary_color);
              }

              if (newBranding.secondary_color) {
                setSecondaryColor(newBranding.secondary_color);
              }

              if (newBranding.logo_url !== undefined) {
                setLogoUrl(newBranding.logo_url);
              }

              // Apply the theme
              if (newBranding.primary_color && newBranding.secondary_color) {
                applyThemeToDocument(
                  newBranding.primary_color,
                  newBranding.secondary_color
                );

                // Also inject CSS directly
                injectBrandingCSS(
                  newBranding.primary_color,
                  newBranding.secondary_color
                );
              }
            }
          }
        )
        .subscribe();

      // Store subscription for cleanup
      (window as any).__brandingSubscription = brandingSubscription;
    } catch (error) {
      console.error("Error setting up branding subscription:", error);
    }
  };

  // Cleanup subscription
  const cleanupBrandingSubscription = async () => {
    try {
      if ((window as any).__brandingSubscription) {
        await (window as any).__brandingSubscription.unsubscribe();
        delete (window as any).__brandingSubscription;
      }
    } catch (error) {
      console.error("Error cleaning up branding subscription:", error);
    }
  };

  // Function to manually apply theme (for settings page)
  const applyTheme = (primary: string, secondary: string) => {
    setPrimaryColor(primary);
    setSecondaryColor(secondary);
    // Apply theme immediately
    applyThemeToDocument(primary, secondary);

    // Also update the database if we have a current school
    if (currentSchool?.id) {
      // Use an async IIFE to handle the database update
      (async () => {
        try {
          // Import supabase client
          const { supabase } = await import("@/lib/supabase");

          // Update the school_branding table
          const { error: brandingError } = await supabase.from("school_branding").upsert(
            {
              school_id: currentSchool.id,
              primary_color: primary,
              secondary_color: secondary,
              logo_url: logoUrl || currentSchool.logoUrl,
              updated_at: new Date().toISOString(),
            },
            {
              onConflict: "school_id",
            }
          );

          if (brandingError) {
            console.error("Error updating branding in database:", brandingError);
          } else {
            console.log("Branding colors updated successfully in database");
          }
        } catch (error) {
          console.error("Error in database update:", error);
        }
      })();
    }

    // Return a promise that resolves when the theme has been applied
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        resolve();
      }, 200); // Wait for the theme to be applied
    });
  };

  return (
    <SchoolThemeContext.Provider
      value={{
        primaryColor,
        secondaryColor,
        logoUrl,
        applyTheme,
        setLogoUrl,
      }}
    >
      {children}
    </SchoolThemeContext.Provider>
  );
}
