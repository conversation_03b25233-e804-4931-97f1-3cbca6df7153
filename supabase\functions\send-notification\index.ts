// Enhanced Notification Service Edge Function
// Handles both email (SendGrid) and SMS (<PERSON>wi<PERSON>) notifications with improved error handling

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Enhanced CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400', // 24 hours
};

// Error codes for better error handling
const ERROR_CODES = {
  INVALID_REQUEST: 'INVALID_REQUEST',
  MISSING_CONFIG: 'MISSING_CONFIG',
  INVALID_CONFIG: 'INVALID_CONFIG',
  SERVICE_DISABLED: 'SERVICE_DISABLED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  EXTERNAL_API_ERROR: 'EXTERNAL_API_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
} as const;

// Define request types
interface EmailRequest {
  to: string;
  subject: string;
  message: string;
}

interface SMSRequest {
  to: string;
  message: string;
}

interface NotificationRequest {
  type: 'email' | 'sms';
  data: EmailRequest | SMSRequest;
}

interface ErrorResponse {
  error: string;
  code: string;
  details?: any;
  timestamp: string;
}

// Phone number validation regex (international format)
const PHONE_REGEX = /^\+[1-9]\d{1,14}$/;

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Enhanced error response helper
function createErrorResponse(code: string, message: string, details?: any): ErrorResponse {
  return {
    error: message,
    code,
    details,
    timestamp: new Date().toISOString(),
  };
}

// Validation functions
function validateEmail(email: string): boolean {
  return EMAIL_REGEX.test(email);
}

function validatePhoneNumber(phone: string): boolean {
  return PHONE_REGEX.test(phone);
}

// Test API key validity for SendGrid
async function testSendGridConfig(apiKey: string, fromEmail: string): Promise<{ valid: boolean; error?: string }> {
  try {
    const response = await fetch('https://api.sendgrid.com/v3/user/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      return { valid: true };
    } else {
      const errorData = await response.json();
      return { valid: false, error: `Invalid API key: ${errorData.errors?.[0]?.message || 'Unknown error'}` };
    }
  } catch (error) {
    return { valid: false, error: `API key validation failed: ${error.message}` };
  }
}

// Enhanced email sending function
async function sendEmail(req: EmailRequest, supabaseClient: any): Promise<{ success: boolean; message: string; code?: string; details?: any }> {
  try {
    // Validate email format
    if (!validateEmail(req.to)) {
      return {
        success: false,
        message: 'Invalid email address format',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: { email: req.to }
      };
    }

    // Get SendGrid configuration from system_settings
    const { data: configData, error: configError } = await supabaseClient
      .from('system_settings')
      .select('setting_value')
      .eq('setting_name', 'email_service_config')
      .single();

    if (configError || !configData) {
      console.error('Error fetching email config:', configError);
      return {
        success: false,
        message: 'Email service configuration not found',
        code: ERROR_CODES.MISSING_CONFIG,
        details: configError
      };
    }

    let config;
    try {
      config = JSON.parse(configData.setting_value);
    } catch (parseError) {
      return {
        success: false,
        message: 'Invalid email service configuration format',
        code: ERROR_CODES.INVALID_CONFIG,
        details: parseError
      };
    }

    // Validate required configuration fields
    if (!config.apiKey || !config.fromEmail) {
      return {
        success: false,
        message: 'Incomplete email service configuration (missing apiKey or fromEmail)',
        code: ERROR_CODES.INVALID_CONFIG,
        details: { hasApiKey: !!config.apiKey, hasFromEmail: !!config.fromEmail }
      };
    }

    // Check if service is enabled
    if (config.enabled === false) {
      return {
        success: false,
        message: 'Email service is disabled',
        code: ERROR_CODES.SERVICE_DISABLED
      };
    }

    // Validate from email format
    if (!validateEmail(config.fromEmail)) {
      return {
        success: false,
        message: 'Invalid sender email address format in configuration',
        code: ERROR_CODES.INVALID_CONFIG,
        details: { fromEmail: config.fromEmail }
      };
    }

    // Prepare SendGrid API request
    const sendGridUrl = 'https://api.sendgrid.com/v3/mail/send';
    const response = await fetch(sendGridUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        personalizations: [{ to: [{ email: req.to }] }],
        from: { email: config.fromEmail },
        subject: req.subject,
        content: [
          {
            type: 'text/plain',
            value: req.message,
          },
          {
            type: 'text/html',
            value: req.message.replace(/\n/g, '<br>'),
          },
        ],
      }),
    });

    if (response.ok) {
      return { success: true, message: 'Email sent successfully' };
    } else {
      const errorData = await response.json();
      console.error('SendGrid API error:', errorData);
      return {
        success: false,
        message: 'Failed to send email via SendGrid',
        code: ERROR_CODES.EXTERNAL_API_ERROR,
        details: errorData
      };
    }
  } catch (error) {
    console.error('Error sending email:', error);
    return {
      success: false,
      message: `Unexpected error while sending email: ${error.message}`,
      code: ERROR_CODES.INTERNAL_ERROR,
      details: { error: error.message, stack: error.stack }
    };
  }
}

// Test Twilio configuration
async function testTwilioConfig(accountSid: string, authToken: string): Promise<{ valid: boolean; error?: string }> {
  try {
    const auth = btoa(`${accountSid}:${authToken}`);
    const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${accountSid}.json`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${auth}`,
      },
    });

    if (response.ok) {
      return { valid: true };
    } else {
      const errorData = await response.json();
      return { valid: false, error: `Invalid Twilio credentials: ${errorData.message || 'Unknown error'}` };
    }
  } catch (error) {
    return { valid: false, error: `Twilio credentials validation failed: ${error.message}` };
  }
}

// Enhanced SMS sending function
async function sendSMS(req: SMSRequest, supabaseClient: any): Promise<{ success: boolean; message: string; code?: string; details?: any; sid?: string }> {
  try {
    // Validate phone number format
    if (!validatePhoneNumber(req.to)) {
      return {
        success: false,
        message: 'Invalid phone number format. Use international format (+**********)',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: { phoneNumber: req.to, expectedFormat: '+**********' }
      };
    }

    // Validate message length (SMS limit is typically 160 characters for GSM, 70 for Unicode)
    if (req.message.length > 1600) { // Allow up to 10 SMS segments
      return {
        success: false,
        message: 'SMS message too long (maximum 1600 characters)',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: { messageLength: req.message.length, maxLength: 1600 }
      };
    }

    // Get Twilio configuration from system_settings
    const { data: configData, error: configError } = await supabaseClient
      .from('system_settings')
      .select('setting_value')
      .eq('setting_name', 'sms_service_config')
      .single();

    if (configError || !configData) {
      console.error('Error fetching SMS config:', configError);
      return {
        success: false,
        message: 'SMS service configuration not found',
        code: ERROR_CODES.MISSING_CONFIG,
        details: configError
      };
    }

    let config;
    try {
      config = JSON.parse(configData.setting_value);
    } catch (parseError) {
      return {
        success: false,
        message: 'Invalid SMS service configuration format',
        code: ERROR_CODES.INVALID_CONFIG,
        details: parseError
      };
    }

    // Validate required configuration fields
    if (!config.accountSid || !config.authToken || !config.phoneNumber) {
      return {
        success: false,
        message: 'Incomplete SMS service configuration',
        code: ERROR_CODES.INVALID_CONFIG,
        details: {
          hasAccountSid: !!config.accountSid,
          hasAuthToken: !!config.authToken,
          hasPhoneNumber: !!config.phoneNumber
        }
      };
    }

    // Check if service is enabled
    if (config.enabled === false) {
      return {
        success: false,
        message: 'SMS service is disabled',
        code: ERROR_CODES.SERVICE_DISABLED
      };
    }

    // Validate Twilio phone number format
    if (!validatePhoneNumber(config.phoneNumber)) {
      return {
        success: false,
        message: 'Invalid Twilio phone number format in configuration',
        code: ERROR_CODES.INVALID_CONFIG,
        details: { twilioPhoneNumber: config.phoneNumber }
      };
    }

    // Prepare Twilio API request
    const twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${config.accountSid}/Messages.json`;
    const auth = btoa(`${config.accountSid}:${config.authToken}`);

    const formData = new URLSearchParams();
    formData.append('To', req.to);
    formData.append('From', config.phoneNumber);
    formData.append('Body', req.message);

    const response = await fetch(twilioUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${auth}`,
      },
      body: formData,
    });

    const data = await response.json();

    if (response.ok) {
      return {
        success: true,
        message: 'SMS sent successfully',
        sid: data.sid
      };
    } else {
      console.error('Twilio API error:', data);
      return {
        success: false,
        message: 'Failed to send SMS via Twilio',
        code: ERROR_CODES.EXTERNAL_API_ERROR,
        details: data
      };
    }
  } catch (error) {
    console.error('Error sending SMS:', error);
    return {
      success: false,
      message: `Unexpected error while sending SMS: ${error.message}`,
      code: ERROR_CODES.INTERNAL_ERROR,
      details: { error: error.message, stack: error.stack }
    };
  }
}

// Configuration validation endpoint
async function validateConfiguration(type: 'email' | 'sms', supabaseClient: any): Promise<{ valid: boolean; error?: string }> {
  try {
    const configName = type === 'email' ? 'email_service_config' : 'sms_service_config';

    const { data: configData, error: configError } = await supabaseClient
      .from('system_settings')
      .select('setting_value')
      .eq('setting_name', configName)
      .single();

    if (configError || !configData) {
      return { valid: false, error: `${type} configuration not found` };
    }

    const config = JSON.parse(configData.setting_value);

    if (type === 'email') {
      if (!config.apiKey || !config.fromEmail) {
        return { valid: false, error: 'Missing required email configuration fields' };
      }
      return await testSendGridConfig(config.apiKey, config.fromEmail);
    } else {
      if (!config.accountSid || !config.authToken || !config.phoneNumber) {
        return { valid: false, error: 'Missing required SMS configuration fields' };
      }
      return await testTwilioConfig(config.accountSid, config.authToken);
    }
  } catch (error) {
    return { valid: false, error: `Configuration validation failed: ${error.message}` };
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Parse request body
    let requestBody;
    try {
      requestBody = await req.json();
    } catch (parseError) {
      const errorResponse = createErrorResponse(
        ERROR_CODES.INVALID_REQUEST,
        'Invalid JSON in request body',
        { parseError: parseError.message }
      );
      return new Response(
        JSON.stringify(errorResponse),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Validate request structure
    if (!requestBody.type || !requestBody.data) {
      const errorResponse = createErrorResponse(
        ERROR_CODES.INVALID_REQUEST,
        'Missing required fields: type and data',
        { received: Object.keys(requestBody) }
      );
      return new Response(
        JSON.stringify(errorResponse),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { type, data } = requestBody as NotificationRequest;

    // Validate notification type
    if (type !== 'email' && type !== 'sms') {
      const errorResponse = createErrorResponse(
        ERROR_CODES.INVALID_REQUEST,
        'Invalid notification type. Must be "email" or "sms"',
        { receivedType: type }
      );
      return new Response(
        JSON.stringify(errorResponse),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );

    // Get the user from the request
    const {
      data: { user },
    } = await supabaseClient.auth.getUser();

    // Only allow authenticated users
    if (!user) {
      const errorResponse = createErrorResponse(
        ERROR_CODES.UNAUTHORIZED,
        'Authentication required'
      );
      return new Response(
        JSON.stringify(errorResponse),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Handle configuration validation requests
    if (requestBody.action === 'validate') {
      const validationResult = await validateConfiguration(type, supabaseClient);
      return new Response(
        JSON.stringify(validationResult),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Validate required fields for each type
    if (type === 'email') {
      const emailData = data as EmailRequest;
      if (!emailData.to || !emailData.subject || !emailData.message) {
        const errorResponse = createErrorResponse(
          ERROR_CODES.VALIDATION_ERROR,
          'Missing required email fields: to, subject, message',
          { received: Object.keys(emailData) }
        );
        return new Response(
          JSON.stringify(errorResponse),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    } else if (type === 'sms') {
      const smsData = data as SMSRequest;
      if (!smsData.to || !smsData.message) {
        const errorResponse = createErrorResponse(
          ERROR_CODES.VALIDATION_ERROR,
          'Missing required SMS fields: to, message',
          { received: Object.keys(smsData) }
        );
        return new Response(
          JSON.stringify(errorResponse),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

    // Send notification
    let result;
    if (type === 'email') {
      result = await sendEmail(data as EmailRequest, supabaseClient);
    } else {
      result = await sendSMS(data as SMSRequest, supabaseClient);
    }

    // Determine HTTP status code based on result
    const statusCode = result.success ? 200 : 400;

    return new Response(
      JSON.stringify(result),
      { status: statusCode, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Unexpected error in notification service:', error);
    const errorResponse = createErrorResponse(
      ERROR_CODES.INTERNAL_ERROR,
      'Internal server error',
      { error: error.message, stack: error.stack }
    );
    return new Response(
      JSON.stringify(errorResponse),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
