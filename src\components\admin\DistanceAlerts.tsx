import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { format } from 'date-fns';
import { MapPin, ExternalLink, Trash2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface DistanceAlert {
  id: string;
  title: string;
  message: string;
  created_at: string;
  teacher_id: string;
  teacher_read_at: string | null;
  admin_read_at: string | null;
  metadata: {
    student_name: string;
    latitude: number;
    longitude: number;
    room_number: string;
    max_allowed_distance: number;
  };
}

export default function DistanceAlerts() {
  const [alerts, setAlerts] = useState<DistanceAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    fetchAlerts();
    const unsubscribe = subscribeToAlerts();
    return () => {
      unsubscribe();
    };
  }, []);

  const fetchAlerts = async () => {
    try {
      // Get current user's school_id for proper school isolation
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('school_id')
        .eq('user_id', user?.id)
        .single();

      if (profileError || !userProfile?.school_id) {
        console.error('Error getting user school_id:', profileError);
        return;
      }

      // Get all student IDs from the same school
      const { data: schoolStudents, error: studentsError } = await supabase
        .from('profiles')
        .select('user_id')
        .eq('role', 'student')
        .eq('school_id', userProfile.school_id);

      if (studentsError) {
        console.error('Error getting school students:', studentsError);
        return;
      }

      const studentIds = schoolStudents.map(s => s.user_id);

      // Fetch notifications for students in the same school
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('type', 'distance_alert')
        .in('student_id', studentIds)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setAlerts(data || []);
    } catch (error) {
      console.error('Error fetching alerts:', error);
      toast({
        title: t('common.error'),
        description: t('admin.distanceAlerts.fetchFailed'),
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const subscribeToAlerts = () => {
    // Note: Supabase real-time subscriptions don't support complex filters like school_id
    // So we'll filter in the callback instead
    const subscription = supabase
      .channel('distance_alerts')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: 'type=eq.distance_alert'
        },
        async (payload) => {
          // Check if this notification belongs to the current user's school
          if (payload.eventType === 'INSERT' && payload.new && payload.new.student_id) {
            const { data: userProfile } = await supabase
              .from('profiles')
              .select('school_id')
              .eq('user_id', user?.id)
              .single();

            const { data: studentProfile } = await supabase
              .from('profiles')
              .select('school_id')
              .eq('user_id', payload.new.student_id)
              .single();

            // Only process if the student is from the same school
            if (userProfile?.school_id === studentProfile?.school_id) {
              setAlerts(prev => [payload.new as DistanceAlert, ...prev]);
              toast({
                title: t('admin.distanceAlerts.newAlert'),
                description: payload.new.message,
              });
            }
          } else if (payload.eventType === 'DELETE') {
            setAlerts(prev => prev.filter(alert => alert.id !== payload.old.id));
          } else if (payload.eventType === 'UPDATE') {
            setAlerts(prev => prev.map(alert =>
              alert.id === payload.new.id ? payload.new as DistanceAlert : alert
            ));
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  };

  const markAsRead = async (alertId: string) => {
    try {
      const updateData = user?.role === 'admin' 
        ? { admin_read_at: new Date().toISOString() }
        : { teacher_read_at: new Date().toISOString() };

      const { error } = await supabase
        .from('notifications')
        .update(updateData)
        .eq('id', alertId);

      if (error) throw error;

      setAlerts(prev =>
        prev.map(alert =>
          alert.id === alertId
            ? { ...alert, ...updateData }
            : alert
        )
      );
    } catch (error) {
      console.error('Error marking alert as read:', error);
      toast({
        title: t('common.error'),
        description: t('admin.distanceAlerts.markReadFailed'),
        variant: 'destructive',
      });
    }
  };

  const deleteAlert = async (alertId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', alertId);

      if (error) throw error;

      setAlerts(prev => prev.filter(alert => alert.id !== alertId));
      toast({
        title: t('common.success'),
        description: t('admin.distanceAlerts.deleteSuccess'),
      });
    } catch (error) {
      console.error('Error deleting alert:', error);
      toast({
        title: t('common.error'),
        description: t('admin.distanceAlerts.deleteFailed'),
        variant: 'destructive',
      });
    }
  };

  const openInMaps = (lat: number, lng: number) => {
    window.open(`https://www.google.com/maps?q=${lat},${lng}`, '_blank');
  };

  const isAlertRead = (alert: DistanceAlert) => {
    return user?.role === 'admin' 
      ? alert.admin_read_at !== null
      : alert.teacher_read_at !== null;
  };

  if (loading) {
    return <div>{t('admin.distanceAlerts.loading')}</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t('admin.distanceAlerts.title')}</span>
          <Badge variant="secondary">
            {alerts.filter(a => !isAlertRead(a)).length} {t('admin.distanceAlerts.unread')}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <p className="text-gray-500">{t('admin.distanceAlerts.noAlerts')}</p>
          ) : (
            alerts.map(alert => (
              <Card key={alert.id} className={isAlertRead(alert) ? 'opacity-70' : ''}>
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold">{alert.metadata.student_name}</h3>
                      <p className="text-sm text-gray-500">
                        {t('admin.distanceAlerts.room')} {alert.metadata.room_number}
                      </p>
                      <p className="text-sm mt-2">{alert.message}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {format(new Date(alert.created_at), 'PPp')}
                      </p>
                    </div>
                    <div className="flex flex-col gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openInMaps(alert.metadata.latitude, alert.metadata.longitude)}
                      >
                        <MapPin className="h-4 w-4 mr-1" />
                        {t('admin.distanceAlerts.viewLocation')}
                      </Button>
                      {!isAlertRead(alert) && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => markAsRead(alert.id)}
                        >
                          {t('admin.distanceAlerts.markAsRead')}
                        </Button>
                      )}
                      {(user?.role === 'admin' || (user?.role === 'teacher' && user.id === alert.teacher_id)) && (
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => deleteAlert(alert.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          {t('common.delete')}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
} 