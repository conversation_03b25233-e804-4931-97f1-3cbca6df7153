import { Globe, Check } from "lucide-react";
import { useTranslation } from "react-i18next";
import LanguageToggle from "@/components/shared/LanguageToggle";

export default function LanguageSettings() {
  const { t, i18n } = useTranslation();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">{t("settings.languageSettings")}</h2>
        <p className="text-gray-600">
          {t("settings.languageDescription")}
        </p>
      </div>

      {/* Supported Languages */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-lg font-medium mb-3">{t("settings.supportedLanguages")}</h3>
        <div className="grid grid-cols-2 gap-3">
          <div className="flex items-center gap-2 p-2 rounded border bg-white">
            <div className="w-6 h-4 bg-red-600 rounded-sm flex items-center justify-center">
              <span className="text-white text-xs font-bold">TR</span>
            </div>
            <span className="text-sm">Türkçe</span>
            {i18n.language === 'tr' && <Check className="w-4 h-4 text-green-600 ml-auto" />}
          </div>
          <div className="flex items-center gap-2 p-2 rounded border bg-white">
            <div className="w-6 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
              <span className="text-white text-xs font-bold">EN</span>
            </div>
            <span className="text-sm">English</span>
            {i18n.language === 'en' && <Check className="w-4 h-4 text-green-600 ml-auto" />}
          </div>
        </div>
      </div>

      {/* Language Toggle */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">{t("settings.selectLanguage")}</h3>
        <div className="flex justify-center">
          <LanguageToggle variant="default" showLabel={true} />
        </div>
      </div>

      {/* Language Information */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="flex items-start gap-3">
          <Globe className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-blue-800 mb-1">
              {t("settings.languageInfo")}
            </p>
            <ul className="text-blue-700 space-y-1">
              <li>• {t("settings.interfaceLanguage")}</li>
              <li>• {t("settings.exportLanguage")}</li>
              <li>• {t("settings.notificationLanguage")}</li>
              <li>• {t("settings.instantChange")}</li>
            </ul>
          </div>
        </div>
      </div>


    </div>
  );
}
