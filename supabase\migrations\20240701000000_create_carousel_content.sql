-- Create carousel_content table
CREATE TABLE IF NOT EXISTS public.carousel_content (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  image_url VARCHAR NOT NULL,
  active BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ,
  target_audience VARCHAR[] DEFAULT ARRAY['student', 'teacher']::VARCHAR[]
);

-- Create index for faster queries
CREATE INDEX carousel_content_school_id_idx ON public.carousel_content(school_id);
CREATE INDEX carousel_content_active_idx ON public.carousel_content(active);
CREATE INDEX carousel_content_display_order_idx ON public.carousel_content(display_order);

-- Add RLS policies
ALTER TABLE public.carousel_content ENABLE ROW LEVEL SECURITY;

-- Allow users to view carousel content for their school
CREATE POLICY "Users can view carousel content for their school"
  ON public.carousel_content
  FOR SELECT
  USING (
    auth.uid() IN (
      SELECT user_id FROM profiles WHERE school_id = carousel_content.school_id
    )
  );

-- Allow school admins to manage carousel content
CREATE POLICY "School admins can manage carousel content"
  ON public.carousel_content
  FOR ALL
  USING (
    auth.uid() IN (
      SELECT user_id FROM profiles
      WHERE school_id = carousel_content.school_id
      AND role = 'admin'
    )
  );
