import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/lib/utils/toast";
import Navbar from "@/components/shared/Navbar";
import { User, School } from "@/lib/types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Building2, Globe, ArrowLeft } from "lucide-react";
import { getAllSchools } from "@/lib/utils/school-context";
import { useTranslation } from "react-i18next";
import { getBranding } from "@/config/branding";
import LanguageToggle from "@/components/shared/LanguageToggle";
import { toast as sonnerToast } from "sonner";

export default function SignUp() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [role, setRole] = useState<"student" | "teacher" | "admin" | "">("");
  const [schoolId, setSchoolId] = useState<string>("");
  const [invitationCode, setInvitationCode] = useState("");
  const [preferredLanguage, setPreferredLanguage] = useState<"en" | "tr">("en");
  const [schools, setSchools] = useState<School[]>([]);
  const [loading, setLoading] = useState(false);
  const { signUp } = useAuth();
  const { t, i18n } = useTranslation();
  const branding = getBranding(i18n.language);

  // Fetch schools on component mount
  useEffect(() => {
    const fetchSchools = async () => {
      try {
        const schoolsData = await getAllSchools();
        setSchools(schoolsData.filter((school) => school.isActive !== false));
      } catch (error) {
        console.error("Error fetching schools:", error);
        toast.translateError(
          t,
          "common.error",
          "signup.errorFetchingSchools"
        );
      }
    };

    fetchSchools();
  }, []);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !email || !password || !role) {
      sonnerToast.error(t("auth.validationError"), {
        description: t("auth.allFieldsRequired"),
        duration: 4000,
      });
      return;
    }

    // Validate school selection
    if (schools.length > 0 && !schoolId) {
      sonnerToast.error(t("auth.validationError"), {
        description: t("auth.pleaseSelectSchool"),
        duration: 4000,
      });
      return;
    }

    // Validate invitation code if school is selected
    if (schoolId && !invitationCode) {
      sonnerToast.error(t("auth.validationError"), {
        description: t("auth.invitationCodeRequired"),
        duration: 4000,
      });
      return;
    }

    setLoading(true);

    try {
      // Normalize invitation code by trimming whitespace
      const normalizedInvitationCode = invitationCode
        ? invitationCode.trim()
        : undefined;

      const userData: Partial<User> = {
        name,
        role,
        email,
        school_id: schoolId || undefined,
        invitationCode: normalizedInvitationCode,
        preferred_language: preferredLanguage,
      };

      // Set the school name for backward compatibility
      if (schoolId) {
        const selectedSchool = schools.find((school) => school.id === schoolId);
        if (selectedSchool) {
          userData.school = selectedSchool.name;
        }
      }

      // Add role-specific IDs with a simple placeholder
      if (role === "student") {
        userData.studentId = `S-${Date.now()}`;
      } else if (role === "teacher") {
        userData.teacherId = `T-${Date.now()}`;
      } else if (role === "admin") {
        userData.adminId = `A-${Date.now()}`;
        // Set access level to 1 (school admin) by default
        userData.accessLevel = 1;
      }

      await signUp(email, password, userData);
      // Navigation is handled by AuthContext
    } catch (error: any) {
      console.error("Sign up error:", error);

      // Show friendly toast notifications for specific errors
      let errorTitle = t("auth.signupFailed");
      let errorDescription = error.message || "Failed to create account. Please try again.";

      if (error.message.includes("Invalid invitation code")) {
        errorTitle = t("auth.invalidInvitationCode");
        errorDescription = t("auth.checkInvitationCodeMessage");
      } else if (error.message.includes("No active invitation code")) {
        errorTitle = t("auth.noInvitationCode");
        errorDescription = t("auth.contactAdminForCode");
      } else if (error.message.includes("Invalid school")) {
        errorTitle = t("auth.invalidSchool");
        errorDescription = t("auth.pleaseSelectValidSchool");
      } else if (error.message.includes("already registered")) {
        errorTitle = t("auth.emailAlreadyRegistered");
        errorDescription = t("auth.trySigningInInstead");
      } else if (error.message.includes("Invalid email")) {
        errorTitle = t("auth.invalidEmail");
        errorDescription = t("auth.pleaseEnterValidEmail");
      }

      sonnerToast.error(errorTitle, {
        description: errorDescription,
        duration: 6000,
      });

      // Clear sensitive fields on error
      setPassword("");
      if (error.message.includes("Invalid invitation code")) {
        setInvitationCode("");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Back to Landing Page Button */}
          <div className="mb-6">
            <Button
              variant="ghost"
              size="sm"
              asChild
              className="text-muted-foreground hover:text-primary"
            >
              <Link to="/" className="flex items-center gap-2">
                <ArrowLeft size={16} />
                {t('common.backToHome')}
              </Link>
            </Button>
          </div>

          <Card className="w-full">
          <CardHeader>
            <CardTitle className="text-2xl">
              {t("auth.createAccount")}
            </CardTitle>
            <CardDescription>
              {t("auth.signUp")} - {branding.APP_NAME}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSignUp} className="space-y-4">
              <div className="space-y-4 mb-4">
                <LanguageToggle variant="default" showLabel={true} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">{t("common.name")}</Label>
                <Input
                  id="name"
                  placeholder={t("auth.placeholders.yourName")}
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">{t("common.email")}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder={t("auth.placeholders.yourEmail")}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">{t("common.password")}</Label>
                <PasswordInput
                  id="password"
                  placeholder={t("auth.placeholders.password")}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">{t("common.role")}</Label>
                <Select
                  value={role}
                  onValueChange={(value: any) => setRole(value)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("common.selectRole")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="student">
                      {t("common.student")}
                    </SelectItem>
                    <SelectItem value="teacher">
                      {t("common.teacher")}
                    </SelectItem>
                    <SelectItem value="admin">{t("common.admin")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {schools.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="school">{t("common.school")}</Label>
                  <Select
                    value={schoolId}
                    onValueChange={(value: string) => setSchoolId(value)}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t("common.selectSchool")} />
                    </SelectTrigger>
                    <SelectContent>
                      {schools.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {schoolId && (
                <div className="space-y-2">
                  <Label htmlFor="invitationCode">
                    {t("common.invitationCode")}
                  </Label>
                  <Input
                    id="invitationCode"
                    placeholder={t("auth.placeholders.invitationCode")}
                    value={invitationCode}
                    onChange={(e) => setInvitationCode(e.target.value)}
                    disabled={loading}
                    required
                    className="font-mono"
                  />
                  <div className="flex flex-col gap-1">
                    <p className="text-xs text-muted-foreground">
                      {t("auth.contactAdminForCode")}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {t("auth.enterCodeExactly")}
                    </p>
                  </div>
                </div>
              )}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? t("common.loading") : t("auth.signUp")}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <div className="text-sm text-muted-foreground">
              {t("auth.alreadyHaveAccount")}{" "}
              <Link to="/login" className="text-primary font-medium">
                {t("auth.signIn")}
              </Link>
            </div>
          </CardFooter>
        </Card>
        </div>
      </div>
    </div>
  );
}
