import { supabase } from "@/lib/supabase";

interface ExcuseCleanupSettings {
  enabled: boolean;
  check_interval_minutes: number;
  notify_students: boolean;
}

interface CleanupResult {
  success: boolean;
  processed_count: number;
  error?: string;
  timestamp: string;
}

/**
 * Service to handle automatic cleanup of expired excuses
 */
export class ExcuseCleanupService {
  private static instance: ExcuseCleanupService;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;

  private constructor() {}

  static getInstance(): ExcuseCleanupService {
    if (!ExcuseCleanupService.instance) {
      ExcuseCleanupService.instance = new ExcuseCleanupService();
    }
    return ExcuseCleanupService.instance;
  }

  /**
   * Start automatic excuse cleanup based on settings
   */
  async startAutomaticCleanup(): Promise<void> {
    try {
      const settings = await this.getCleanupSettings();
      
      if (!settings.enabled) {
        console.log("📋 Excuse cleanup is disabled in settings");
        return;
      }

      if (this.isRunning) {
        console.log("📋 Excuse cleanup is already running");
        return;
      }

      // Starting automatic excuse cleanup
      
      // Run initial cleanup
      await this.runCleanup();
      
      // Schedule recurring cleanup
      this.intervalId = setInterval(async () => {
        await this.runCleanup();
      }, settings.check_interval_minutes * 60 * 1000);
      
      this.isRunning = true;
      // Automatic excuse cleanup started successfully
      
    } catch (error) {
      console.error("❌ Error starting automatic excuse cleanup:", error);
    }
  }

  /**
   * Stop automatic excuse cleanup
   */
  stopAutomaticCleanup(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log("🛑 Automatic excuse cleanup stopped");
  }

  /**
   * Manually run excuse cleanup
   */
  async runCleanup(): Promise<CleanupResult> {
    try {
      // Running excuse expiry cleanup

      // Call the database function to perform cleanup
      const { data, error } = await supabase.rpc('schedule_excuse_cleanup');

      if (error) {
        // Check if the function doesn't exist
        if (error.code === 'PGRST202') {
          throw new Error("Excuse cleanup function not found. Please contact your system administrator to apply the latest database migrations.");
        }
        throw error;
      }

      const result: CleanupResult = {
        success: true,
        processed_count: data || 0,
        timestamp: new Date().toISOString()
      };

      // Excuse cleanup completed

      return result;

    } catch (error: any) {
      console.error("❌ Error during excuse cleanup:", error);

      const result: CleanupResult = {
        success: false,
        processed_count: 0,
        error: error.message,
        timestamp: new Date().toISOString()
      };

      return result;
    }
  }

  /**
   * Get cleanup settings from database
   */
  private async getCleanupSettings(): Promise<ExcuseCleanupSettings> {
    try {
      const { data, error } = await supabase
        .from("system_settings")
        .select("setting_value")
        .eq("setting_name", "excuse_auto_cleanup")
        .single();

      if (error || !data) {
        // Return default settings if not found
        return {
          enabled: true,
          check_interval_minutes: 60,
          notify_students: true
        };
      }

      // setting_value is already a JSON object (jsonb type), no need to parse
      const settings = data.setting_value as ExcuseCleanupSettings;
      return settings;

    } catch (error) {
      console.error("Error fetching cleanup settings:", error);
      // Return default settings on error
      return {
        enabled: true,
        check_interval_minutes: 60,
        notify_students: true
      };
    }
  }

  /**
   * Update cleanup settings
   */
  async updateCleanupSettings(settings: ExcuseCleanupSettings): Promise<boolean> {
    try {
      // Since we know the record exists, just update it directly
      const { error } = await supabase
        .from("system_settings")
        .update({
          setting_value: settings, // Pass the object directly, not JSON.stringify
          updated_at: new Date().toISOString()
        })
        .eq("setting_name", "excuse_auto_cleanup");

      if (error) {
        throw error;
      }

      console.log("✅ Excuse cleanup settings updated");

      // Restart cleanup with new settings
      this.stopAutomaticCleanup();
      if (settings.enabled) {
        await this.startAutomaticCleanup();
      }

      return true;

    } catch (error) {
      console.error("❌ Error updating cleanup settings:", error);
      return false;
    }
  }

  /**
   * Get cleanup status and statistics
   */
  async getCleanupStatus(): Promise<{
    isRunning: boolean;
    settings: ExcuseCleanupSettings;
    lastCleanup?: string;
    totalProcessed?: number;
  }> {
    try {
      const settings = await this.getCleanupSettings();
      
      // Get last cleanup info from audit logs
      const { data: auditData } = await supabase
        .from("audit_logs")
        .select("details, created_at")
        .eq("action", "EXCUSE_CLEANUP")
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      let lastCleanup: string | undefined;
      let totalProcessed: number | undefined;

      if (auditData) {
        lastCleanup = auditData.created_at;
        const details = typeof auditData.details === 'string' 
          ? JSON.parse(auditData.details) 
          : auditData.details;
        totalProcessed = details?.expired_excuses_processed;
      }

      return {
        isRunning: this.isRunning,
        settings,
        lastCleanup,
        totalProcessed
      };
      
    } catch (error) {
      console.error("Error getting cleanup status:", error);
      return {
        isRunning: this.isRunning,
        settings: await this.getCleanupSettings()
      };
    }
  }

  /**
   * Check for expired excuses without cleaning them up (for testing/preview)
   */
  async checkExpiredExcuses(): Promise<{
    count: number;
    excuses: any[];
  }> {
    try {
      const currentDate = new Date().toISOString().split('T')[0];
      const currentTime = new Date().toTimeString().split(' ')[0];

      const { data, error } = await supabase
        .from("excuses")
        .select(`
          id,
          start_date,
          end_date,
          start_time,
          end_time,
          reason,
          status,
          student_id,
          room_id,
          profiles!excuses_student_id_fkey(name),
          rooms!excuses_room_id_fkey(name)
        `)
        .in("status", ["approved", "rejected"])
        .or(`end_date.lt.${currentDate},and(end_date.eq.${currentDate},end_time.lt.${currentTime})`);

      if (error) {
        throw error;
      }

      return {
        count: data?.length || 0,
        excuses: data || []
      };
      
    } catch (error) {
      console.error("Error checking expired excuses:", error);
      return {
        count: 0,
        excuses: []
      };
    }
  }
}

// Export singleton instance
export const excuseCleanupService = ExcuseCleanupService.getInstance();
