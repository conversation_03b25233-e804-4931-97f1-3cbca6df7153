import React from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/context/SimpleLanguageContext";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Globe } from "lucide-react";

interface LanguageToggleProps {
  variant?: "default" | "compact";
  showLabel?: boolean;
  className?: string;
}

export default function LanguageToggle({
  variant = "default",
  showLabel = true,
  className = "",
}: LanguageToggleProps) {
  const { t } = useTranslation();
  const { currentLanguage, changeLanguage, isLoading } = useLanguage();

  const handleLanguageChange = (value: string) => {
    if (value === "en" || value === "tr") {
      changeLanguage(value);
    }
  };

  if (variant === "compact") {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {showLabel && (
          <div className="flex items-center">
            <Globe className="h-4 w-4 mr-1" />
            <span className="text-sm font-medium">{t("common.language")}:</span>
          </div>
        )}
        <select
          value={currentLanguage}
          onChange={(e) => handleLanguageChange(e.target.value)}
          disabled={isLoading}
          className="h-8 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="en">{t("common.english")}</option>
          <option value="tr">{t("common.turkish")}</option>
        </select>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {showLabel && (
        <div className="flex items-center space-x-2">
          <Globe className="h-4 w-4" />
          <Label className="text-base font-medium">
            {t("common.language")}
          </Label>
        </div>
      )}
      <RadioGroup
        value={currentLanguage}
        onValueChange={handleLanguageChange}
        className="flex space-x-4"
        disabled={isLoading}
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="en" id="language-en" />
          <Label htmlFor="language-en" className="cursor-pointer">
            {t("common.english")}
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="tr" id="language-tr" />
          <Label htmlFor="language-tr" className="cursor-pointer">
            {t("common.turkish")}
          </Label>
        </div>
      </RadioGroup>
    </div>
  );
}
