/* Teacher Dashboard Card Enhancements */

/* Statistics Cards Enhancements */
.stats-card {
  display: flex !important;
  flex-direction: column !important;
  padding: 1.25rem !important;
  border-radius: 12px !important;
  border-left: 3px solid transparent !important;
  transition: all 0.3s ease !important;
}

.stats-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

/* Status-specific styling */
.stats-card-present {
  border-left-color: #10b981 !important;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05)) !important;
}

.stats-card-absent {
  border-left-color: #ef4444 !important;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05)) !important;
}

.stats-card-late {
  border-left-color: #f59e0b !important;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05)) !important;
}

.stats-card-excused {
  border-left-color: #3b82f6 !important;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05)) !important;
}

/* Dark mode adjustments */
.dark .stats-card-present {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(16, 185, 129, 0.05)) !important;
}

.dark .stats-card-absent {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05)) !important;
}

.dark .stats-card-late {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.05)) !important;
}

.dark .stats-card-excused {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.05)) !important;
}
