import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Calendar,
  MapPin,
  Smartphone,
  Shield,
  ChevronDown,
  ChevronUp,
  Filter,
} from "lucide-react";
import { format, parseISO, isToday, isThisWeek, isThisMonth } from "date-fns";
import { tr, enUS } from "date-fns/locale";
import { useAttendanceHistory } from "@/hooks/useAttendanceHistory";
import { useTranslation } from "react-i18next";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function EnhancedAttendanceHistory() {
  const { attendanceRecords, loading, error } = useAttendanceHistory();
  const { t, i18n } = useTranslation();

  // Get the appropriate locale for date formatting
  const getDateLocale = () => {
    return i18n.language === 'tr' ? tr : enUS;
  };
  const [filterPeriod, setFilterPeriod] = useState<string>("all");
  const [expandedRecords, setExpandedRecords] = useState<
    Record<string, boolean>
  >({});

  // Toggle expanded state for a record
  const toggleExpand = (recordId: string) => {
    setExpandedRecords((prev) => ({
      ...prev,
      [recordId]: !prev[recordId],
    }));
  };

  // Filter records based on selected period
  const filteredRecords = React.useMemo(() => {
    if (filterPeriod === "all") return attendanceRecords;

    return attendanceRecords.filter((record) => {
      const date = parseISO(record.timestamp);
      switch (filterPeriod) {
        case "today":
          return isToday(date);
        case "week":
          return isThisWeek(date);
        case "month":
          return isThisMonth(date);
        default:
          return true;
      }
    });
  }, [attendanceRecords, filterPeriod]);

  // Group records by date
  const groupedRecords = React.useMemo(() => {
    const groups: Record<string, typeof attendanceRecords> = {};

    filteredRecords.forEach((record) => {
      const dateKey = format(new Date(record.timestamp), "yyyy-MM-dd");
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(record);
    });

    return Object.entries(groups)
      .sort((a, b) => new Date(b[0]).getTime() - new Date(a[0]).getTime())
      .map(([date, records]) => ({
        date,
        formattedDate: format(new Date(date), "EEEE, MMMM d, yyyy", { locale: getDateLocale() }),
        records: records.sort(
          (a, b) =>
            new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        ),
      }));
  }, [filteredRecords]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "late":
        return <Clock className="w-5 h-5 text-amber-500" />;
      case "absent":
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case "excused":
        return <Calendar className="w-5 h-5 text-blue-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "present":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
          >
            {t("attendance.present")}
          </Badge>
        );
      case "late":
        return (
          <Badge
            variant="outline"
            className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800"
          >
            {t("attendance.late")}
          </Badge>
        );
      case "absent":
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
          >
            {t("attendance.absent")}
          </Badge>
        );
      case "excused":
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800"
          >
            {t("attendance.excused")}
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {t(status.charAt(0).toUpperCase() + status.slice(1))}
          </Badge>
        );
    }
  };

  if (loading) {
    return (
      <Card className="w-full max-w-3xl mx-auto enhanced-card">
        <CardHeader>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-full max-w-md" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <Skeleton className="h-8 w-20 rounded-full" />
                </div>
                <div className="grid grid-cols-3 gap-3 mt-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-3xl mx-auto enhanced-card">
        <CardHeader>
          <CardTitle>{t("attendance.attendanceHistory")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 border border-red-200 bg-red-50 rounded-md text-red-700 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800">
            <p>{t("attendance.errorLoadingData")}</p>
            <p className="text-sm mt-1">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-3xl mx-auto enhanced-card">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{t("attendance.attendanceHistory")}</CardTitle>
            <CardDescription>
              {t("attendance.yourRecordsForAllClasses")}
            </CardDescription>
          </div>
          <div className="flex items-center">
            <Filter size={16} className="mr-2 text-muted-foreground" />
            <Select value={filterPeriod} onValueChange={setFilterPeriod}>
              <SelectTrigger className="w-[130px] h-8 text-xs">
                <SelectValue placeholder={t("attendance.filterBy")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("attendance.allTime")}</SelectItem>
                <SelectItem value="today">{t("attendance.today")}</SelectItem>
                <SelectItem value="week">{t("attendance.thisWeek")}</SelectItem>
                <SelectItem value="month">{t("attendance.thisMonth")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {filteredRecords.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="w-12 h-12 mx-auto mb-2 opacity-30" />
            <p>{t("attendance.noAttendanceData")}</p>
            <p className="text-sm">
              {t("attendance.yourAttendanceHistoryWillAppearHere")}
            </p>
          </div>
        ) : (
          <div className="space-y-8">
            {groupedRecords.map((group, groupIndex) => (
              <div key={group.date} className="space-y-2">
                <div className="sticky top-0 z-10 bg-background py-1">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    {group.formattedDate}
                  </h3>
                  <div className="h-px bg-border mt-2"></div>
                </div>

                <div className="space-y-3 pl-4 border-l-2 border-muted">
                  {group.records.map((record, recordIndex) => (
                    <motion.div
                      key={record.id}
                      className="timeline-item hover-lift"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: recordIndex * 0.05 }}
                      style={{ "--index": recordIndex } as React.CSSProperties}
                    >
                      <div className="relative">
                        {/* Timeline dot */}
                        <div className="absolute -left-[29px] top-1.5 w-4 h-4 rounded-full bg-background border-2 border-muted flex items-center justify-center">
                          <div
                            className={`w-2 h-2 rounded-full ${
                              record.status === "present"
                                ? "bg-green-500"
                                : record.status === "late"
                                ? "bg-amber-500"
                                : record.status === "absent"
                                ? "bg-red-500"
                                : record.status === "excused"
                                ? "bg-blue-500"
                                : "bg-muted-foreground"
                            }`}
                          ></div>
                        </div>

                        <div className="border rounded-lg p-4 hover:border-primary/50 transition-colors">
                          <div className="flex items-start justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center">
                                <span className="font-medium">
                                  {record.room?.name || t("attendance.unknownRoom")}
                                </span>
                                <span className="mx-2 text-muted-foreground">
                                  •
                                </span>
                                <span className="text-sm text-muted-foreground">
                                  {format(new Date(record.timestamp), "h:mm a", { locale: getDateLocale() })}
                                </span>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {record.course?.name || t("attendance.unknownCourse")}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              {getStatusBadge(record.status)}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => toggleExpand(record.id)}
                              >
                                {expandedRecords[record.id] ? (
                                  <ChevronUp size={16} />
                                ) : (
                                  <ChevronDown size={16} />
                                )}
                              </Button>
                            </div>
                          </div>

                          <AnimatePresence>
                            {expandedRecords[record.id] && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: "auto", opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.2 }}
                                className="overflow-hidden"
                              >
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-4 pt-3 border-t">
                                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                                    <Shield className="w-3.5 h-3.5" />
                                    <span className="capitalize">
                                      {record.verificationMethod === "pin"
                                        ? t("attendance.pinVerification")
                                        : record.verificationMethod ===
                                          "biometric"
                                        ? t("attendance.biometricVerification")
                                        : t("attendance.manualVerification")}
                                    </span>
                                  </div>
                                  {record.location && (
                                    <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                                      <MapPin className="w-3.5 h-3.5" />
                                      <span>{t("attendance.locationVerified")}</span>
                                    </div>
                                  )}
                                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                                    <Smartphone className="w-3.5 h-3.5" />
                                    <span>{t("attendance.deviceWeb")}</span>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
