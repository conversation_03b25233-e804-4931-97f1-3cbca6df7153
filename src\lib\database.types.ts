export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      carousel_content: {
        Row: {
          id: string;
          school_id: string;
          title: string;
          description: string | null;
          image_url: string;
          active: boolean;
          display_order: number;
          created_by: string | null;
          created_at: string;
          updated_at: string;
          start_date: string | null;
          end_date: string | null;
          target_audience: string[];
        };
        Insert: {
          id?: string;
          school_id: string;
          title: string;
          description?: string | null;
          image_url: string;
          active?: boolean;
          display_order?: number;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
          start_date?: string | null;
          end_date?: string | null;
          target_audience?: string[];
        };
        Update: {
          id?: string;
          school_id?: string;
          title?: string;
          description?: string | null;
          image_url?: string;
          active?: boolean;
          display_order?: number;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
          start_date?: string | null;
          end_date?: string | null;
          target_audience?: string[];
        };
        Relationships: [
          {
            foreignKeyName: "carousel_content_school_id_fkey";
            columns: ["school_id"];
            referencedRelation: "schools";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "carousel_content_created_by_fkey";
            columns: ["created_by"];
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      schools: {
        Row: {
          id: string;
          name: string;
          address: string | null;
          city: string | null;
          state: string | null;
          zip: string | null;
          country: string | null;
          phone: string | null;
          email: string | null;
          website: string | null;
          logo_url: string | null;
          primary_color: string | null;
          secondary_color: string | null;
          invitation_code: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          address?: string | null;
          city?: string | null;
          state?: string | null;
          zip?: string | null;
          country?: string | null;
          phone?: string | null;
          email?: string | null;
          website?: string | null;
          logo_url?: string | null;
          primary_color?: string | null;
          secondary_color?: string | null;
          invitation_code?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          address?: string | null;
          city?: string | null;
          state?: string | null;
          zip?: string | null;
          country?: string | null;
          phone?: string | null;
          email?: string | null;
          website?: string | null;
          logo_url?: string | null;
          primary_color?: string | null;
          secondary_color?: string | null;
          invitation_code?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      attendance_records: {
        Row: {
          id: string;
          student_id: string;
          room_id: string;
          timestamp: string;
          device_info: string;
          verification_method: string;
          status: string;
          location: Json;
          created_at: string;
          school_id: string | null;
        };
        Insert: {
          id?: string;
          student_id: string;
          room_id: string;
          timestamp?: string;
          device_info: string;
          verification_method: string;
          status: string;
          location: Json;
          created_at?: string;
          school_id?: string | null;
        };
        Update: {
          id?: string;
          student_id?: string;
          room_id?: string;
          timestamp?: string;
          device_info?: string;
          verification_method?: string;
          status?: string;
          location?: Json;
          created_at?: string;
          school_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "attendance_records_student_id_fkey";
            columns: ["student_id"];
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "attendance_records_school_id_fkey";
            columns: ["school_id"];
            referencedRelation: "schools";
            referencedColumns: ["id"];
          }
        ];
      };
      fraud_cases: {
        Row: {
          id: string;
          student_id: string;
          attendance_id: string;
          timestamp: string;
          evidence_type: string;
          status: string;
          notes: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          student_id: string;
          attendance_id: string;
          timestamp?: string;
          evidence_type: string;
          status?: string;
          notes?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          student_id?: string;
          attendance_id?: string;
          timestamp?: string;
          evidence_type?: string;
          status?: string;
          notes?: string | null;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "fraud_cases_student_id_fkey";
            columns: ["student_id"];
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      profiles: {
        Row: {
          id: string;
          user_id: string;
          role: string;
          name: string;
          email: string;
          photo_url: string | null;
          updated_at: string;
          created_at: string;
          student_id: string | null;
          teacher_id: string | null;
          admin_id: string | null;
          department: string | null;
          position: string | null;
          subject: string | null;
          course: string | null;
          biometric_registered: boolean | null;
          block_name: string | null;
          room_number: string | null;
          pin: string | null;
          school_id: string | null;
          access_level: number | null;
          school: string | null;
          preferred_language: "en" | "tr" | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          role: string;
          name: string;
          email: string;
          photo_url?: string | null;
          updated_at?: string;
          created_at?: string;
          student_id?: string | null;
          teacher_id?: string | null;
          admin_id?: string | null;
          department?: string | null;
          position?: string | null;
          subject?: string | null;
          course?: string | null;
          biometric_registered?: boolean | null;
          block_name?: string | null;
          room_number?: string | null;
          pin?: string | null;
          school_id?: string | null;
          access_level?: number | null;
          school?: string | null;
          preferred_language?: "en" | "tr" | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          role?: string;
          name?: string;
          email?: string;
          photo_url?: string | null;
          updated_at?: string;
          created_at?: string;
          student_id?: string | null;
          teacher_id?: string | null;
          admin_id?: string | null;
          department?: string | null;
          position?: string | null;
          subject?: string | null;
          course?: string | null;
          biometric_registered?: boolean | null;
          block_name?: string | null;
          room_number?: string | null;
          pin?: string | null;
          school_id?: string | null;
          access_level?: number | null;
          school?: string | null;
          preferred_language?: "en" | "tr" | null;
        };
        Relationships: [
          {
            foreignKeyName: "profiles_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "profiles_school_id_fkey";
            columns: ["school_id"];
            referencedRelation: "schools";
            referencedColumns: ["id"];
          }
        ];
      };
      rooms: {
        Row: {
          id: string;
          name: string;
          building: string;
          floor: number;
          capacity: number;
          current_qr_code: string | null;
          qr_expiry: string | null;
          created_at: string;
          school_id: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          building: string;
          floor: number;
          capacity: number;
          current_qr_code?: string | null;
          qr_expiry?: string | null;
          created_at?: string;
          school_id?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          building?: string;
          floor?: number;
          capacity?: number;
          current_qr_code?: string | null;
          qr_expiry?: string | null;
          created_at?: string;
          school_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "rooms_school_id_fkey";
            columns: ["school_id"];
            referencedRelation: "schools";
            referencedColumns: ["id"];
          }
        ];
      };
      courses: {
        Row: {
          id: string;
          name: string;
          teacher_id: string;
          room_id: string;
          schedule: Json;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          teacher_id: string;
          room_id: string;
          schedule: Json;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          teacher_id?: string;
          room_id?: string;
          schedule?: Json;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "courses_teacher_id_fkey";
            columns: ["teacher_id"];
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "courses_room_id_fkey";
            columns: ["room_id"];
            referencedRelation: "rooms";
            referencedColumns: ["id"];
          }
        ];
      };
      notifications: {
        Row: {
          id: string;
          student_id: string;
          title: string;
          message: string;
          type: string;
          read: boolean;
          timestamp: string;
          metadata: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          student_id: string;
          title: string;
          message: string;
          type: string;
          read?: boolean;
          timestamp?: string;
          metadata?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          student_id?: string;
          title?: string;
          message?: string;
          type?: string;
          read?: boolean;
          timestamp?: string;
          metadata?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "notifications_student_id_fkey";
            columns: ["student_id"];
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
