import { TFunction } from 'i18next';

/**
 * Helper function to safely get a string from a translation
 * @param t The i18next translation function
 * @param key The translation key
 * @param fallback Fallback string if translation fails
 * @returns A string that is safe to use in toast
 */
export const safeTranslate = (t: TFunction, key: string, fallback: string): string => {
  try {
    const translation = t(key);
    return typeof translation === 'string' ? translation : fallback;
  } catch (error) {
    console.error(`Translation error for key "${key}":`, error);
    return fallback;
  }
};

/**
 * Helper function to create a toast with safe translations
 * @param toast The toast function
 * @param t The i18next translation function
 * @param titleKey The translation key for the title
 * @param descriptionKey The translation key for the description
 * @param titleFallback Fallback string for the title
 * @param descriptionFallback Fallback string for the description
 * @param options Additional toast options
 */
export const createSafeToast = (
  toast: any,
  t: TFunction,
  titleKey: string,
  descriptionKey: string,
  titleFallback: string,
  descriptionFallback: string,
  options: any = {}
) => {
  const title = safeTranslate(t, titleKey, titleFallback);
  const description = safeTranslate(t, descriptionKey, descriptionFallback);
  
  toast({
    title,
    description,
    ...options
  });
};
