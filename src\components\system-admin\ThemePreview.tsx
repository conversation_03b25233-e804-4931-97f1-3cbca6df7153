import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ThemeColors } from "@/lib/types";
import { Bell, Check, X, AlertTriangle, Info } from "lucide-react";

interface ThemePreviewProps {
  colors: ThemeColors;
  theme: "light" | "dark";
}

export default function ThemePreview({ colors, theme }: ThemePreviewProps) {
  return (
    <div className={`theme-preview ${theme}`}>
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            <span>Theme Preview</span>
            <Badge variant="outline">{theme === "light" ? "Light Mode" : "Dark Mode"}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Typography */}
          <div className="space-y-2">
            <h1 className="text-2xl font-bold">Heading 1</h1>
            <h2 className="text-xl font-semibold">Heading 2</h2>
            <h3 className="text-lg font-medium">Heading 3</h3>
            <p className="text-base">Regular paragraph text</p>
            <p className="text-sm text-muted-foreground">Muted text</p>
          </div>

          {/* Buttons */}
          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              <Button variant="default">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
            </div>
          </div>

          {/* Form Elements */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input id="name" placeholder="Enter your name" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" placeholder="Enter your email" />
            </div>
          </div>

          {/* Switch */}
          <div className="flex items-center space-x-2">
            <Switch id="airplane-mode" />
            <Label htmlFor="airplane-mode">Airplane Mode</Label>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="account" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="account">Account</TabsTrigger>
              <TabsTrigger value="password">Password</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="account" className="p-4 border rounded-md mt-2">
              Account settings content
            </TabsContent>
            <TabsContent value="password" className="p-4 border rounded-md mt-2">
              Password settings content
            </TabsContent>
            <TabsContent value="settings" className="p-4 border rounded-md mt-2">
              General settings content
            </TabsContent>
          </Tabs>

          {/* Status Indicators */}
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2 p-2 bg-green-100 text-green-800 rounded-md dark:bg-green-900 dark:text-green-100">
              <Check size={16} />
              <span>Success</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-red-100 text-red-800 rounded-md dark:bg-red-900 dark:text-red-100">
              <X size={16} />
              <span>Error</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-yellow-100 text-yellow-800 rounded-md dark:bg-yellow-900 dark:text-yellow-100">
              <AlertTriangle size={16} />
              <span>Warning</span>
            </div>
            <div className="flex items-center gap-2 p-2 bg-blue-100 text-blue-800 rounded-md dark:bg-blue-900 dark:text-blue-100">
              <Info size={16} />
              <span>Information</span>
            </div>
          </div>

          {/* Notification */}
          <div className="p-4 border rounded-md flex items-start gap-3">
            <Bell className="h-5 w-5 text-primary" />
            <div>
              <h4 className="font-medium">Notification Title</h4>
              <p className="text-sm text-muted-foreground">
                This is a sample notification message that would appear in the application.
              </p>
            </div>
          </div>

          {/* Color Swatches */}
          <div className="grid grid-cols-4 gap-2">
            <ColorSwatch color={colors.primary} name="Primary" />
            <ColorSwatch color={colors.secondary} name="Secondary" />
            <ColorSwatch color={colors.accent} name="Accent" />
            <ColorSwatch color={colors.background} name="Background" />
            <ColorSwatch color={colors.foreground} name="Foreground" />
            <ColorSwatch color={colors.muted} name="Muted" />
            <ColorSwatch color={colors.card} name="Card" />
            <ColorSwatch color={colors.border} name="Border" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface ColorSwatchProps {
  color: string;
  name: string;
}

function ColorSwatch({ color, name }: ColorSwatchProps) {
  return (
    <div className="flex flex-col items-center">
      <div
        className="w-12 h-12 rounded-md border"
        style={{ backgroundColor: color }}
      ></div>
      <span className="text-xs mt-1">{name}</span>
    </div>
  );
}
