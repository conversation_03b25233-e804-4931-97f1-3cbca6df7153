import { supabase } from "@/lib/supabase";
import {
  format,
  subDays,
  startOfMonth,
  endOfMonth,
  eachMonthOfInterval,
} from "date-fns";
import { ensureExcusesTableExists } from "@/lib/migrations/excuses-table-migration";

/**
 * Fetch system statistics for the dashboard
 * @returns Object containing system statistics
 */
export const fetchSystemStats = async () => {
  try {
    // Fetch school count
    const { data: schoolData, error: schoolError } = await supabase
      .from("schools")
      .select("id", { count: "exact" });

    if (schoolError) throw schoolError;

    // Fetch user count
    const { data: userData, error: userError } = await supabase
      .from("profiles")
      .select("id", { count: "exact" });

    if (userError) throw userError;

    // Fetch active user count (for now, we'll use all users since last_login_at doesn't exist)
    // TODO: Add last_login_at column to profiles table or use auth.users table for login tracking
    const activeUserData = userData; // Use all users as active users for now

    // Fetch alert count
    const { data: alertData, error: alertError } = await supabase
      .from("notifications")
      .select("id", { count: "exact" })
      .eq("type", "system")
      .eq("read", false);

    if (alertError) throw alertError;

    // Return system stats
    return {
      schoolCount: schoolData.length,
      userCount: userData.length,
      activeUserCount: activeUserData.length,
      alertCount: alertData.length,
      systemHealth: "healthy", // Default to healthy
      lastUpdated: new Date(),
    };
  } catch (error) {
    console.error("Error fetching system stats:", error);
    throw error;
  }
};

/**
 * Fetch recent audit logs
 * @param limit Number of logs to fetch
 * @returns Array of audit logs
 */
export const fetchRecentAuditLogs = async (limit = 5) => {
  try {
    // First, fetch the basic audit logs
    const { data: auditLogs, error: auditError } = await supabase
      .from("audit_logs")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(limit);

    if (auditError) throw auditError;

    // If we have audit logs, fetch the related user and school information separately
    if (auditLogs && auditLogs.length > 0) {
      // Get unique user IDs and school IDs from the audit logs
      const userIds = [
        ...new Set(
          auditLogs.filter((log) => log.user_id).map((log) => log.user_id)
        ),
      ];
      const schoolIds = [
        ...new Set(
          auditLogs.filter((log) => log.school_id).map((log) => log.school_id)
        ),
      ];

      // Fetch user information if we have user IDs
      let userMap = {};
      if (userIds.length > 0) {
        const { data: users, error: userError } = await supabase
          .from("profiles")
          .select("id, email")
          .in("id", userIds);

        if (userError) console.error("Error fetching users:", userError);
        else if (users) {
          userMap = users.reduce((acc, user) => {
            acc[user.id] = user;
            return acc;
          }, {});
        }
      }

      // Fetch school information if we have school IDs
      let schoolMap = {};
      if (schoolIds.length > 0) {
        const { data: schools, error: schoolError } = await supabase
          .from("schools")
          .select("id, name")
          .in("id", schoolIds);

        if (schoolError) console.error("Error fetching schools:", schoolError);
        else if (schools) {
          schoolMap = schools.reduce((acc, school) => {
            acc[school.id] = school;
            return acc;
          }, {});
        }
      }

      // Combine the data
      const enrichedLogs = auditLogs.map((log) => ({
        ...log,
        user: log.user_id ? userMap[log.user_id] || null : null,
        school: log.school_id ? schoolMap[log.school_id] || null : null,
      }));

      return enrichedLogs;
    }

    return auditLogs || [];
  } catch (error) {
    console.error("Error fetching recent logs:", error);
    throw error;
  }
};

/**
 * Fetch all schools with user counts
 * @returns Array of schools with user counts
 */
export const fetchSchoolsWithUserCounts = async () => {
  try {
    // Fetch schools
    const { data: schools, error: schoolsError } = await supabase
      .from("schools")
      .select("*")
      .order("name");

    if (schoolsError) throw schoolsError;

    // Fetch user counts for each school
    const schoolsWithUserCounts = await Promise.all(
      (schools || []).map(async (school) => {
        const { count, error: countError } = await supabase
          .from("profiles")
          .select("*", { count: "exact", head: true })
          .eq("school_id", school.id);

        if (countError) {
          console.error("Error fetching user count:", countError);
          return { ...school, user_count: 0 };
        }

        return { ...school, user_count: count || 0 };
      })
    );

    return schoolsWithUserCounts;
  } catch (error) {
    console.error("Error fetching schools with user counts:", error);
    throw error;
  }
};

/**
 * Fetch all users with their school information
 * @returns Array of users with school information
 */
export const fetchUsersWithSchoolInfo = async () => {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select(
        `
        *,
        schools:school_id (
          name
        )
      `
      )
      .order("created_at", { ascending: false });

    if (error) throw error;

    // Transform data to include school name
    const transformedUsers = (data || []).map((user) => ({
      ...user,
      school_name: user.schools?.name || "No School",
    }));

    return transformedUsers;
  } catch (error) {
    console.error("Error fetching users with school info:", error);
    throw error;
  }
};

/**
 * Fetch audit logs with pagination
 * @param limit Number of logs to fetch
 * @param offset Offset for pagination
 * @returns Array of audit logs
 */
export const fetchAuditLogs = async (limit = 50, offset = 0) => {
  try {
    // First, fetch the basic audit logs
    const { data: auditLogs, error: auditError } = await supabase
      .from("audit_logs")
      .select("*")
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (auditError) throw auditError;

    // If we have audit logs, fetch the related user and school information separately
    if (auditLogs && auditLogs.length > 0) {
      // Get unique user IDs and school IDs from the audit logs
      const userIds = [
        ...new Set(
          auditLogs.filter((log) => log.user_id).map((log) => log.user_id)
        ),
      ];
      const schoolIds = [
        ...new Set(
          auditLogs.filter((log) => log.school_id).map((log) => log.school_id)
        ),
      ];

      // Fetch user information if we have user IDs
      let userMap = {};
      if (userIds.length > 0) {
        const { data: users, error: userError } = await supabase
          .from("profiles")
          .select("id, email")
          .in("id", userIds);

        if (userError) console.error("Error fetching users:", userError);
        else if (users) {
          userMap = users.reduce((acc, user) => {
            acc[user.id] = user;
            return acc;
          }, {});
        }
      }

      // Fetch school information if we have school IDs
      let schoolMap = {};
      if (schoolIds.length > 0) {
        const { data: schools, error: schoolError } = await supabase
          .from("schools")
          .select("id, name")
          .in("id", schoolIds);

        if (schoolError) console.error("Error fetching schools:", schoolError);
        else if (schools) {
          schoolMap = schools.reduce((acc, school) => {
            acc[school.id] = school;
            return acc;
          }, {});
        }
      }

      // Combine the data
      const enrichedLogs = auditLogs.map((log) => ({
        ...log,
        user: log.user_id ? userMap[log.user_id] || null : null,
        school: log.school_id ? schoolMap[log.school_id] || null : null,
      }));

      return enrichedLogs;
    }

    return auditLogs || [];
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    throw error;
  }
};

/**
 * Fetch system settings
 * @returns Object containing system settings
 */
export const fetchSystemSettings = async () => {
  try {
    const { data, error } = await supabase.from("system_settings").select("*");

    if (error) throw error;

    // Transform array of settings to an object
    const settings = (data || []).reduce((acc, setting) => {
      try {
        acc[setting.setting_name] =
          typeof setting.setting_value === "string"
            ? JSON.parse(setting.setting_value)
            : setting.setting_value;
      } catch (e) {
        acc[setting.setting_name] = setting.setting_value;
      }
      return acc;
    }, {} as Record<string, any>);

    return settings;
  } catch (error) {
    console.error("Error fetching system settings:", error);
    throw error;
  }
};

/**
 * Update a system setting
 * @param settingName Name of the setting to update
 * @param settingValue New value for the setting
 * @returns Boolean indicating success
 */
export const updateSystemSetting = async (
  settingName: string,
  settingValue: any
) => {
  try {
    // Check if setting exists
    const { data: existingData, error: existingError } = await supabase
      .from("system_settings")
      .select("id")
      .eq("setting_name", settingName)
      .single();

    if (existingError && existingError.code !== "PGRST116") {
      throw existingError;
    }

    // Prepare the value as JSONB
    const jsonValue =
      typeof settingValue === "string"
        ? settingValue
        : JSON.stringify(settingValue);

    if (existingData?.id) {
      // Update existing setting
      const { error } = await supabase
        .from("system_settings")
        .update({
          setting_value: jsonValue,
          updated_at: new Date().toISOString(),
        })
        .eq("setting_name", settingName);

      if (error) throw error;
    } else {
      // Insert new setting
      const { error } = await supabase.from("system_settings").insert({
        setting_name: settingName,
        setting_value: jsonValue,
        updated_at: new Date().toISOString(),
      });

      if (error) throw error;
    }

    return true;
  } catch (error) {
    console.error(`Error updating system setting ${settingName}:`, error);
    throw error;
  }
};

/**
 * Fetch usage statistics for the system
 * @param days Number of days to fetch data for
 * @returns Object containing usage statistics
 */
export const fetchUsageStatistics = async (days = 30) => {
  try {
    // Ensure the excuses table exists
    await ensureExcusesTableExists();

    const startDate = subDays(new Date(), days).toISOString();

    // Fetch login count (using all users for now since last_login_at doesn't exist)
    // TODO: Add last_login_at column to profiles table or use auth.users table for login tracking
    const { count: loginCount, error: loginError } = await supabase
      .from("profiles")
      .select("*", { count: "exact", head: true });

    if (loginError) throw loginError;

    // Fetch attendance records count
    const { count: attendanceCount, error: attendanceError } = await supabase
      .from("attendance_records")
      .select("*", { count: "exact", head: true })
      .gt("timestamp", startDate);

    if (attendanceError) throw attendanceError;

    // Fetch QR scan count (attendance records with verification_method = 'qr')
    const { count: qrScanCount, error: qrScanError } = await supabase
      .from("attendance_records")
      .select("*", { count: "exact", head: true })
      .eq("verification_method", "qr")
      .gt("timestamp", startDate);

    if (qrScanError) throw qrScanError;

    // Fetch excuse submissions count
    let excuseCount = 0;
    try {
      const { count, error: excuseError } = await supabase
        .from("excuses")
        .select("*", { count: "exact", head: true })
        .gt("created_at", startDate);

      if (!excuseError) {
        excuseCount = count || 0;
      }
    } catch (error) {
      console.error("Error fetching excuses count:", error);
      // Continue execution even if excuses table doesn't exist
    }

    return {
      loginCount: loginCount || 0,
      attendanceCount: attendanceCount || 0,
      qrScanCount: qrScanCount || 0,
      excuseCount: excuseCount || 0,
      period: days,
    };
  } catch (error) {
    console.error("Error fetching usage statistics:", error);
    throw error;
  }
};

/**
 * Fetch all invitation codes for all schools
 * @returns Array of schools with their invitation codes
 */
export const fetchAllInvitationCodes = async () => {
  try {
    // First check if invitation_expiry column exists in the schools table
    const { data: columnsData, error: columnsError } = await supabase
      .from("schools")
      .select("id")
      .limit(1);

    if (columnsError) {
      console.error("Error checking schools table:", columnsError);
      // If there's an error, try a simpler query without invitation_expiry
      const { data, error } = await supabase
        .from("schools")
        .select("id, name, invitation_code, updated_at")
        .order("name");

      if (error) throw error;

      return data || [];
    }

    // If the first query worked, try the full query
    try {
      const { data, error } = await supabase
        .from("schools")
        .select("id, name, invitation_code, invitation_expiry, updated_at")
        .order("name");

      if (error) throw error;

      return data || [];
    } catch (selectError) {
      // If the full query fails, fall back to a simpler query
      console.error("Error with full query, trying fallback:", selectError);
      const { data, error } = await supabase
        .from("schools")
        .select("id, name, invitation_code, updated_at")
        .order("name");

      if (error) throw error;

      return data || [];
    }
  } catch (error) {
    console.error("Error fetching invitation codes:", error);
    throw error;
  }
};

/**
 * Generate a new invitation code for a school
 * @param schoolId The ID of the school
 * @param expiryDate Optional expiry date for the invitation code
 * @returns The new invitation code
 */
export const generateInvitationCode = async (
  schoolId: string,
  expiryDate?: Date | null
) => {
  try {
    // Generate a new code
    const newCode =
      "INV-" + Math.random().toString(36).substring(2, 10).toUpperCase();

    // Update the school record
    const { data, error } = await supabase
      .from("schools")
      .update({
        invitation_code: newCode,
        invitation_expiry: expiryDate ? expiryDate.toISOString() : null,
        updated_at: new Date().toISOString(),
      })
      .eq("id", schoolId)
      .select("invitation_code")
      .single();

    if (error) throw error;

    // Update school_settings if it exists
    const { data: settingsData, error: settingsError } = await supabase
      .from("school_settings")
      .select("id")
      .eq("school_id", schoolId)
      .single();

    // If settings exist, update the invitation_expiry
    if (!settingsError && settingsData) {
      await supabase
        .from("school_settings")
        .update({
          invitation_expiry: expiryDate ? expiryDate.toISOString() : null,
          updated_at: new Date().toISOString(),
        })
        .eq("school_id", schoolId);
    } else if (settingsError && settingsError.code === "PGRST116") {
      // If settings don't exist, create them
      await supabase.from("school_settings").insert({
        school_id: schoolId,
        invitation_expiry: expiryDate ? expiryDate.toISOString() : null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    }

    // Log the action in audit logs
    await supabase.from("audit_logs").insert({
      school_id: schoolId,
      action_type: "generate",
      entity_type: "invitation_code",
      details: { code: newCode },
      created_at: new Date().toISOString(),
    });

    return data?.invitation_code || newCode;
  } catch (error) {
    console.error("Error generating invitation code:", error);
    throw error;
  }
};

/**
 * Revoke an invitation code for a school
 * @param schoolId The ID of the school
 * @returns Boolean indicating success
 */
export const revokeInvitationCode = async (schoolId: string) => {
  try {
    const { error } = await supabase
      .from("schools")
      .update({
        invitation_code: null,
        invitation_expiry: null,
        updated_at: new Date().toISOString(),
      })
      .eq("id", schoolId);

    if (error) throw error;

    // Log the action in audit logs
    await supabase.from("audit_logs").insert({
      school_id: schoolId,
      action_type: "revoke",
      entity_type: "invitation_code",
      details: { message: "Invitation code revoked" },
      created_at: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error("Error revoking invitation code:", error);
    throw error;
  }
};

/**
 * Fetch school settings for a specific school
 * @param schoolId The ID of the school
 * @returns School settings object
 */
export const fetchSchoolSettings = async (schoolId: string) => {
  try {
    // First try to get from school_settings table
    const { data: settingsData, error: settingsError } = await supabase
      .from("school_settings")
      .select("*")
      .eq("school_id", schoolId)
      .single();

    // If no settings found, get basic school info
    if (settingsError && settingsError.code === "PGRST116") {
      const { data: schoolData, error: schoolError } = await supabase
        .from("schools")
        .select("*")
        .eq("id", schoolId)
        .single();

      if (schoolError) throw schoolError;

      // Return default settings with school data
      return {
        id: null,
        school_id: schoolId,
        email_notifications_enabled: true,
        sms_notifications_enabled: false,
        invitation_expiry: null,
        theme_css: null,
        custom_login_message: null,
        require_location_verification: true,
        require_biometric_verification: false,
        allow_pin_verification: true,
        allow_wifi_verification: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        school: schoolData,
      };
    }

    if (settingsError) throw settingsError;

    // Get school data to include with settings
    const { data: schoolData, error: schoolError } = await supabase
      .from("schools")
      .select("*")
      .eq("id", schoolId)
      .single();

    if (schoolError) throw schoolError;

    // Combine settings with school data
    return {
      ...settingsData,
      school: schoolData,
    };
  } catch (error) {
    console.error("Error fetching school settings:", error);
    throw error;
  }
};

/**
 * Update school settings
 * @param schoolId The ID of the school
 * @param settings The settings to update
 * @returns Boolean indicating success
 */
export const updateSchoolSettings = async (
  schoolId: string,
  settings: Record<string, any>
) => {
  try {
    // Check if settings exist for this school
    const { data: existingSettings, error: checkError } = await supabase
      .from("school_settings")
      .select("id")
      .eq("school_id", schoolId)
      .single();

    // Extract school-specific settings that should go in the schools table
    const schoolSettings = {
      name: settings.name,
      logo_url: settings.logo_url,
      primary_color: settings.primary_color,
      secondary_color: settings.secondary_color,
      custom_login_message: settings.custom_login_message,
      updated_at: new Date().toISOString(),
    };

    // Update the school record
    const { error: schoolError } = await supabase
      .from("schools")
      .update(schoolSettings)
      .eq("id", schoolId);

    if (schoolError) throw schoolError;

    // Remove school-specific settings from the settings object
    const settingsToUpdate = { ...settings };
    delete settingsToUpdate.name;
    delete settingsToUpdate.logo_url;
    delete settingsToUpdate.primary_color;
    delete settingsToUpdate.secondary_color;

    // Add updated_at timestamp
    settingsToUpdate.updated_at = new Date().toISOString();

    if (checkError && checkError.code === "PGRST116") {
      // Settings don't exist, create them
      const { error: insertError } = await supabase
        .from("school_settings")
        .insert({
          school_id: schoolId,
          ...settingsToUpdate,
          created_at: new Date().toISOString(),
        });

      if (insertError) throw insertError;
    } else {
      // Settings exist, update them
      const { error: updateError } = await supabase
        .from("school_settings")
        .update(settingsToUpdate)
        .eq("school_id", schoolId);

      if (updateError) throw updateError;
    }

    // Log the action in audit logs
    await supabase.from("audit_logs").insert({
      school_id: schoolId,
      action_type: "update",
      entity_type: "school_settings",
      details: { message: "School settings updated" },
      created_at: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error("Error updating school settings:", error);
    throw error;
  }
};

/**
 * Fetch system school settings overrides
 * @param schoolId Optional school ID to filter by
 * @returns Array of system school settings overrides
 */
export const fetchSystemSchoolSettingsOverrides = async (schoolId?: string) => {
  try {
    let query = supabase.from("system_school_settings_overrides").select("*");

    if (schoolId) {
      query = query.or(`school_id.eq.${schoolId},applies_to_all.eq.true`);
    }

    const { data, error } = await query;

    if (error) throw error;

    return data || [];
  } catch (error) {
    console.error("Error fetching system school settings overrides:", error);
    throw error;
  }
};

/**
 * Set a system school settings override
 * @param settingName The name of the setting to override
 * @param settingValue The value to set
 * @param schoolId The ID of the school to apply the override to, or null for all schools
 * @param overrideEnabled Whether the override is enabled
 * @param appliesToAll Whether the override applies to all schools
 * @returns Boolean indicating success
 */
export const setSystemSchoolSettingsOverride = async (
  settingName: string,
  settingValue: any,
  schoolId: string | null,
  overrideEnabled: boolean = true,
  appliesToAll: boolean = false
) => {
  try {
    // Prepare the value as JSONB
    const jsonValue =
      typeof settingValue === "object" ? settingValue : { value: settingValue };

    // Check if an override already exists
    const { data: existingOverride, error: checkError } = await supabase
      .from("system_school_settings_overrides")
      .select("id")
      .eq("setting_name", settingName)
      .eq(schoolId ? "school_id" : "applies_to_all", schoolId ? schoolId : true)
      .maybeSingle();

    if (checkError && checkError.code !== "PGRST116") {
      throw checkError;
    }

    if (existingOverride) {
      // Update existing override
      const { error } = await supabase
        .from("system_school_settings_overrides")
        .update({
          setting_value: jsonValue,
          override_enabled: overrideEnabled,
          applies_to_all: appliesToAll,
          updated_at: new Date().toISOString(),
        })
        .eq("id", existingOverride.id);

      if (error) throw error;
    } else {
      // Create new override
      const { error } = await supabase
        .from("system_school_settings_overrides")
        .insert({
          setting_name: settingName,
          setting_value: jsonValue,
          school_id: schoolId,
          override_enabled: overrideEnabled,
          applies_to_all: appliesToAll,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    }

    // Log the action in audit logs
    await supabase.from("audit_logs").insert({
      school_id: schoolId,
      action_type: "update",
      entity_type: "system_school_settings_override",
      details: {
        message: "System school settings override updated",
        setting_name: settingName,
        applies_to_all: appliesToAll,
      },
      created_at: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error("Error setting system school settings override:", error);
    throw error;
  }
};

/**
 * Remove a system school settings override
 * @param settingName The name of the setting to remove the override for
 * @param schoolId The ID of the school to remove the override for, or null for all schools
 * @returns Boolean indicating success
 */
export const removeSystemSchoolSettingsOverride = async (
  settingName: string,
  schoolId: string | null
) => {
  try {
    let query = supabase
      .from("system_school_settings_overrides")
      .delete()
      .eq("setting_name", settingName);

    if (schoolId) {
      query = query.eq("school_id", schoolId);
    } else {
      query = query.eq("applies_to_all", true);
    }

    const { error } = await query;

    if (error) throw error;

    // Log the action in audit logs
    await supabase.from("audit_logs").insert({
      school_id: schoolId,
      action_type: "delete",
      entity_type: "system_school_settings_override",
      details: {
        message: "System school settings override removed",
        setting_name: settingName,
      },
      created_at: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error("Error removing system school settings override:", error);
    throw error;
  }
};

/**
 * Apply system school settings overrides to all schools
 * @param settingName The name of the setting to apply
 * @param settingValue The value to set
 * @param overrideEnabled Whether the override is enabled
 * @returns Boolean indicating success
 */
export const applySystemSettingToAllSchools = async (
  settingName: string,
  settingValue: any,
  overrideEnabled: boolean = true
) => {
  try {
    // Set the global override
    await setSystemSchoolSettingsOverride(
      settingName,
      settingValue,
      null,
      overrideEnabled,
      true
    );

    // Log the action in audit logs
    await supabase.from("audit_logs").insert({
      action_type: "update",
      entity_type: "system_school_settings_override",
      details: {
        message: "System setting applied to all schools",
        setting_name: settingName,
      },
      created_at: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error("Error applying system setting to all schools:", error);
    throw error;
  }
};

/**
 * Fetch analytics data for a specific school
 * @param schoolId The ID of the school
 * @param days Number of days to fetch data for
 * @returns School analytics data
 */
export const fetchSchoolAnalytics = async (schoolId: string, days = 30) => {
  try {
    // Ensure the excuses table exists
    await ensureExcusesTableExists();

    const startDate = subDays(new Date(), days).toISOString();

    // Fetch user count
    const { count: userCount, error: userError } = await supabase
      .from("profiles")
      .select("*", { count: "exact", head: true })
      .eq("school_id", schoolId);

    if (userError) throw userError;

    // Fetch attendance records count
    const { count: attendanceCount, error: attendanceError } = await supabase
      .from("attendance_records")
      .select("*", { count: "exact", head: true })
      .eq("school_id", schoolId)
      .gt("timestamp", startDate);

    if (attendanceError) throw attendanceError;

    // Fetch attendance status breakdown
    const { data: statusData, error: statusError } = await supabase
      .from("attendance_records")
      .select("status")
      .eq("school_id", schoolId)
      .gt("timestamp", startDate);

    if (statusError) throw statusError;

    // Count attendance by status
    const statusCounts = (statusData || []).reduce((acc, record) => {
      const status = record.status || "unknown";
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Fetch excuse submissions count
    let excuseCount = 0;
    try {
      // Check if the excuses table exists by doing a simple count
      const { count: totalCount, error: totalError } = await supabase
        .from("excuses")
        .select("*", { count: "exact", head: true });

      if (!totalError) {
        // If the table exists, try to get school-specific count
        // First check if the school_id column exists
        try {
          // Try to get excuses for this school by joining with profiles
          const { count, error: excuseError } = await supabase
            .from("excuses")
            .select("*, profiles!inner(school_id)", {
              count: "exact",
              head: true,
            })
            .eq("profiles.school_id", schoolId);

          if (!excuseError) {
            // If the join works, try with the date filter
            try {
              const { count: filteredCount, error: filteredError } =
                await supabase
                  .from("excuses")
                  .select("*, profiles!inner(school_id)", {
                    count: "exact",
                    head: true,
                  })
                  .eq("profiles.school_id", schoolId)
                  .gt("created_at", startDate);

              if (!filteredError) {
                excuseCount = filteredCount || 0;
              } else {
                excuseCount = count || 0;
              }
            } catch (dateError) {
              console.error("Error with date filter on excuses:", dateError);
              excuseCount = count || 0;
            }
          } else {
            // If the join fails, just use the total count as a fallback
            excuseCount = totalCount || 0;
          }
        } catch (error) {
          console.error("Error with school filter on excuses:", error);
          excuseCount = totalCount || 0;
        }
      }
    } catch (error) {
      console.error("Error fetching excuses count:", error);
      // Continue execution even if excuses table doesn't exist
    }

    // Get user growth over time (by month)
    const sixMonthsAgo = subDays(new Date(), 180).toISOString();
    const { data: userGrowthData, error: growthError } = await supabase
      .from("profiles")
      .select("created_at")
      .eq("school_id", schoolId)
      .gt("created_at", sixMonthsAgo)
      .order("created_at");

    if (growthError) throw growthError;

    // Group users by month
    const monthlyGrowth = userGrowthData?.reduce((acc, user) => {
      const month = format(new Date(user.created_at), "yyyy-MM");
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Create a complete series of months
    const today = new Date();
    const months = eachMonthOfInterval({
      start: subDays(today, 180),
      end: today,
    }).map((date) => format(date, "yyyy-MM"));

    // Fill in missing months with zeros
    const userGrowthSeries = months.map((month) => ({
      month,
      count: monthlyGrowth?.[month] || 0,
    }));

    return {
      userCount: userCount || 0,
      attendanceCount: attendanceCount || 0,
      excuseCount: excuseCount || 0,
      statusCounts,
      userGrowthSeries,
      period: days,
    };
  } catch (error) {
    console.error("Error fetching school analytics:", error);
    throw error;
  }
};

/**
 * Fetch analytics data for all schools
 * @param days Number of days to fetch data for
 * @returns Analytics data for all schools
 */
/**
 * Set a school to maintenance mode
 * @param schoolId The ID of the school
 * @param inMaintenance Whether to enable or disable maintenance mode
 * @param maintenanceMessage Optional message to display during maintenance
 * @param estimatedTime Optional estimated time for maintenance completion
 * @returns Boolean indicating success
 */
export const setSchoolMaintenanceMode = async (
  schoolId: string,
  inMaintenance: boolean,
  maintenanceMessage?: string,
  estimatedTime?: string
) => {
  try {
    // Update the school record
    const { error } = await supabase
      .from("schools")
      .update({
        maintenance_mode: inMaintenance,
        maintenance_message: maintenanceMessage || null,
        maintenance_estimated_time: estimatedTime || null,
        updated_at: new Date().toISOString(),
      })
      .eq("id", schoolId);

    if (error) throw error;

    // Log the action in audit logs
    await supabase.from("audit_logs").insert({
      school_id: schoolId,
      action_type: inMaintenance ? "enable" : "disable",
      entity_type: "maintenance_mode",
      details: {
        message: inMaintenance
          ? "School set to maintenance mode"
          : "School maintenance mode disabled",
        maintenance_message: maintenanceMessage,
        estimated_time: estimatedTime,
      },
      created_at: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error("Error setting school maintenance mode:", error);
    throw error;
  }
};

/**
 * Block or activate a school
 * @param schoolId The ID of the school
 * @param active Whether to activate (true) or block (false) the school
 * @returns Boolean indicating success
 */
export const setSchoolActiveStatus = async (
  schoolId: string,
  active: boolean
) => {
  try {
    // Update the school record
    const { error } = await supabase
      .from("schools")
      .update({
        status: active ? "active" : "inactive",
        updated_at: new Date().toISOString(),
      })
      .eq("id", schoolId);

    if (error) throw error;

    // Log the action in audit logs
    await supabase.from("audit_logs").insert({
      school_id: schoolId,
      action_type: active ? "activate" : "block",
      entity_type: "school",
      details: { message: active ? "School activated" : "School blocked" },
      created_at: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error("Error setting school active status:", error);
    throw error;
  }
};

/**
 * Delete a school and all associated data
 * @param schoolId The ID of the school to delete
 * @returns Boolean indicating success
 */
export const deleteSchool = async (schoolId: string) => {
  try {
    // First, get the school name for audit logs
    const { data: schoolData, error: schoolError } = await supabase
      .from("schools")
      .select("name")
      .eq("id", schoolId)
      .single();

    if (schoolError) throw schoolError;

    // Log the action in audit logs before deletion
    await supabase.from("audit_logs").insert({
      action_type: "delete",
      entity_type: "school",
      details: {
        message: "School deleted",
        school_name: schoolData.name,
        school_id: schoolId,
      },
      created_at: new Date().toISOString(),
    });

    // Delete the school - this will cascade delete all related data
    // due to foreign key constraints
    const { error } = await supabase
      .from("schools")
      .delete()
      .eq("id", schoolId);

    if (error) throw error;

    return true;
  } catch (error) {
    console.error("Error deleting school:", error);
    throw error;
  }
};

export const fetchAllSchoolsAnalytics = async (days = 30) => {
  try {
    // Fetch all schools
    const { data: schools, error: schoolsError } = await supabase
      .from("schools")
      .select("id, name")
      .order("name");

    if (schoolsError) throw schoolsError;

    // Fetch analytics for each school
    const schoolsAnalytics = await Promise.all(
      (schools || []).map(async (school) => {
        try {
          const analytics = await fetchSchoolAnalytics(school.id, days);
          return {
            ...school,
            analytics,
          };
        } catch (error) {
          console.error(
            `Error fetching analytics for school ${school.id}:`,
            error
          );
          return {
            ...school,
            analytics: {
              userCount: 0,
              attendanceCount: 0,
              excuseCount: 0,
              statusCounts: {},
              userGrowthSeries: [],
              period: days,
            },
          };
        }
      })
    );

    return schoolsAnalytics;
  } catch (error) {
    console.error("Error fetching all schools analytics:", error);
    throw error;
  }
};
