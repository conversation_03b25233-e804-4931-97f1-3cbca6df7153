-- Enable RLS on notifications table
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions
GRANT ALL ON public.notifications TO authenticated;

-- Policy for students to view their own notifications
CREATE POLICY "Students can view their own notifications"
ON public.notifications
FOR SELECT
TO authenticated
USING (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE id::text = student_id::text
  )
);

-- Policy for students to update their own notifications (e.g., mark as read)
CREATE POLICY "Students can update their own notifications"
ON public.notifications
FOR UPDATE
TO authenticated
USING (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE id::text = student_id::text
  )
)
WITH CHECK (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE id::text = student_id::text
  )
);

-- Policy for teachers to create notifications for their students
CREATE POLICY "Teachers can create notifications"
ON public.notifications
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role IN ('teacher', 'admin')
  )
);

-- Policy for teachers to view notifications they created
CREATE POLICY "Teachers can view notifications for their students"
ON public.notifications
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role IN ('teacher', 'admin')
  )
);

-- Policy for system to create notifications
CREATE POLICY "System can create notifications"
ON public.notifications
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text
  )
);

-- Policy for admins to manage all notifications
CREATE POLICY "Admins can manage all notifications"
ON public.notifications
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role = 'admin'
  )
); 