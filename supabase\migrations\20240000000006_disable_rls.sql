-- Drop all existing policies first
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can create profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update any profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can delete profiles" ON profiles;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON profiles;

-- Disable RLS on profiles table since it doesn't contain sensitive data
-- Passwords are stored in auth.users which has its own security
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON profiles TO authenticated;

-- Ensure the admin user exists
INSERT INTO profiles (id, user_id, name, email, role)
VALUES (
  '6cfc05df-41d8-4a37-8115-7376d88f2462',
  '6cfc05df-41d8-4a37-8115-7376d88f2462',
  '<PERSON>',
  '<EMAIL>',
  'admin'
)
ON CONFLICT (id) DO UPDATE 
SET role = 'admin'
WHERE profiles.id = '6cfc05df-41d8-4a37-8115-7376d88f2462'; 