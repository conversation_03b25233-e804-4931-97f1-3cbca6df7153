-- Create tablet_devices table for device authentication
CREATE TABLE IF NOT EXISTS public.tablet_devices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  device_id VARCHAR(64) UNIQUE NOT NULL,
  school_id UUID NOT NULL REFERENCES public.schools(id) ON DELETE CASCADE,
  room_id UUID NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
  block_id UUID REFERENCES public.blocks(id) ON DELETE SET NULL,
  device_name VARCHAR(255) NOT NULL,
  certificate VARCHAR(64) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  last_seen TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tablet_devices_device_id ON public.tablet_devices(device_id);
CREATE INDEX IF NOT EXISTS idx_tablet_devices_school_id ON public.tablet_devices(school_id);
CREATE INDEX IF NOT EXISTS idx_tablet_devices_room_id ON public.tablet_devices(room_id);
CREATE INDEX IF NOT EXISTS idx_tablet_devices_active ON public.tablet_devices(is_active);
CREATE INDEX IF NOT EXISTS idx_tablet_devices_last_seen ON public.tablet_devices(last_seen);

-- Enable RLS
ALTER TABLE public.tablet_devices ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view tablet devices in their school"
ON public.tablet_devices FOR SELECT
TO authenticated
USING (
  school_id IN (
    SELECT school_id FROM public.profiles 
    WHERE user_id = auth.uid()
  )
);

CREATE POLICY "Admins can manage tablet devices in their school"
ON public.tablet_devices FOR ALL
TO authenticated
USING (
  school_id IN (
    SELECT school_id FROM public.profiles 
    WHERE user_id = auth.uid() 
    AND role IN ('admin', 'teacher')
  )
);

-- Allow public access for tablet registration (will be secured by device fingerprinting)
CREATE POLICY "Allow tablet device registration"
ON public.tablet_devices FOR INSERT
TO anon
WITH CHECK (true);

CREATE POLICY "Allow tablet device authentication"
ON public.tablet_devices FOR SELECT
TO anon
USING (is_active = true);

CREATE POLICY "Allow tablet device updates"
ON public.tablet_devices FOR UPDATE
TO anon
USING (is_active = true);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_tablet_devices_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER trigger_update_tablet_devices_updated_at
  BEFORE UPDATE ON public.tablet_devices
  FOR EACH ROW
  EXECUTE FUNCTION update_tablet_devices_updated_at();

-- Function to clean up inactive devices (optional)
CREATE OR REPLACE FUNCTION cleanup_inactive_tablet_devices()
RETURNS void AS $$
BEGIN
  -- Mark devices as inactive if not seen for 7 days
  UPDATE public.tablet_devices 
  SET is_active = false 
  WHERE last_seen < now() - interval '7 days' 
  AND is_active = true;
  
  -- Delete inactive devices older than 30 days
  DELETE FROM public.tablet_devices 
  WHERE is_active = false 
  AND updated_at < now() - interval '30 days';
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.tablet_devices TO anon;
GRANT ALL ON public.tablet_devices TO authenticated;
