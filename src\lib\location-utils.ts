interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface LocationVerificationResult {
  isWithinRadius: boolean;
  message: string;
  alertType: 'warning' | 'error' | 'info';
  alertTitle: string;
}

/**
 * Calculates the distance between two points using the Haversine formula
 */
export function calculateDistance(point1: Coordinates, point2: Coordinates): number {
  const R = 6371000; // Earth's radius in meters
  const lat1 = point1.latitude * Math.PI / 180;
  const lat2 = point2.latitude * Math.PI / 180;
  const deltaLat = (point2.latitude - point1.latitude) * Math.PI / 180;
  const deltaLon = (point2.longitude - point1.longitude) * Math.PI / 180;

  const a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
    Math.cos(lat1) * Math.cos(lat2) *
    Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in meters
}

/**
 * Verifies if a student's location is within acceptable range of the room
 */
export function verifyLocationForAttendance(
  studentLocation: Coordinates,
  roomLocation: Coordinates,
  allowedRadius: number,
  maxSchoolDistance: number = 1000 // Default 1km for school premises check
): LocationVerificationResult {
  const distance = calculateDistance(studentLocation, roomLocation);

  // Convert to kilometers for VPN check
  const distanceInKm = distance / 1000;

  // VPN check - if distance is more than 200km
  if (distanceInKm > 200) {
    return {
      isWithinRadius: false,
      message: "It seems you might be using a VPN which is affecting your location. Please disable any VPN services and try again to ensure accurate attendance tracking.",
      alertType: 'error',
      alertTitle: 'VPN Detected'
    };
  }

  // Not in school check - if distance is more than configured school distance
  if (distance > maxSchoolDistance) {
    return {
      isWithinRadius: false,
      message: `You appear to be too far from the school premises (${Math.round(distance)}m away). Attendance can only be marked when you're physically present at school.`,
      alertType: 'error',
      alertTitle: 'Not at School'
    };
  }

  // Within allowed radius
  if (distance <= allowedRadius) {
    return {
      isWithinRadius: true,
      message: "You're in the correct location. Attendance can be marked.",
      alertType: 'info',
      alertTitle: 'Location Verified'
    };
  }

  // Outside allowed radius but within school premises
  const excessDistance = Math.round(distance - allowedRadius);
  const distanceDisplay = distance >= 1000
    ? `${(distance / 1000).toFixed(1)}km`
    : `${Math.round(distance)}m`;

  return {
    isWithinRadius: false,
    message: `You're ${distanceDisplay} away from your assigned room, which is ${excessDistance}m outside the ${allowedRadius}m allowed radius. Please move closer to your assigned room.`,
    alertType: 'warning',
    alertTitle: 'Outside Allowed Radius'
  };
} 