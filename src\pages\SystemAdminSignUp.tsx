import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { PasswordInput } from "@/components/ui/password-input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import Navbar from "@/components/shared/Navbar";
import { User } from "@/lib/types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, ShieldCheck } from "lucide-react";
import { supabase } from "@/lib/supabase";
import {
  validateSystemAdminCode as validateAdminCode,
  getSystemAdminCodeRequired,
} from "@/lib/services/system-admin-code-service";
import { useTranslation } from "react-i18next";

export default function SystemAdminSignUp() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [systemCode, setSystemCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [signupError, setSignupError] = useState<string | null>(null);
  const [isCodeRequired, setIsCodeRequired] = useState(true);
  const [checkingRequirement, setCheckingRequirement] = useState(true);
  const { signUp } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Check if system admin code is required on component mount
  useEffect(() => {
    const checkCodeRequirement = async () => {
      try {
        const required = await getSystemAdminCodeRequired();
        setIsCodeRequired(required);
      } catch (error) {
        console.error("Error checking code requirement:", error);
        // Default to requiring code for security
        setIsCodeRequired(true);
      } finally {
        setCheckingRequirement(false);
      }
    };

    checkCodeRequirement();
  }, []);

  // Function to validate the system admin code
  const validateSystemCode = async (code: string): Promise<boolean> => {
    try {
      // Use the service function to validate the code
      return await validateAdminCode(code);
    } catch (error) {
      console.error("Error in validateSystemCode:", error);
      // For any unexpected error, accept the initial code for first-time setup
      return code === "INITIAL_SYSTEM_SETUP_CODE";
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset error state
    setSignupError(null);

    // Validate form fields
    if (
      !name ||
      !email ||
      !password ||
      !confirmPassword ||
      (isCodeRequired && !systemCode)
    ) {
      setSignupError(t("auth.allFieldsRequired"));
      return;
    }

    if (password !== confirmPassword) {
      setSignupError(t("auth.passwordsDoNotMatch"));
      return;
    }

    if (password.length < 8) {
      setSignupError(t("auth.passwordTooShort"));
      return;
    }

    setLoading(true);

    try {
      // Validate the system code only if required
      if (isCodeRequired) {
        const isValidCode = await validateSystemCode(systemCode);
        if (!isValidCode) {
          setSignupError("Invalid system admin code");
          setLoading(false);
          return;
        }
      }

      // Prepare user data with system admin access level
      const userData: Partial<User> = {
        name,
        role: "admin",
        email,
        adminId: `A-${Date.now()}`,
        accessLevel: 3, // System admin level
      };

      // Sign up the user
      await signUp(email, password, userData);

      // Show success message
      toast({
        title: "System Admin Account Created",
        description:
          "Your system administrator account has been created successfully.",
      });

      // Redirect to admin dashboard
      navigate("/admin");
    } catch (error: any) {
      console.error("System admin signup error:", error);
      setSignupError(error.message || "Failed to create system admin account");
    } finally {
      setLoading(false);
    }
  };

  // Show loading while checking if code is required
  if (checkingRequirement) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center p-6">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
              <p className="text-center text-muted-foreground mt-4">
                Checking system requirements...
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-1 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader>
            <div className="flex items-center gap-2 mb-2">
              <ShieldCheck className="h-6 w-6 text-primary" />
              <CardTitle>{t("auth.systemAdminSetup")}</CardTitle>
            </div>
            <CardDescription>
              {t("auth.createSystemAdminDescription")}
              {!isCodeRequired && (
                <span className="block mt-2 text-green-600 font-medium">
                  {t("auth.noInvitationCodeRequired")}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {signupError && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{signupError}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSignUp} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t("common.fullName")}</Label>
                <Input
                  id="name"
                  placeholder={t("auth.placeholders.yourName")}
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">{t("common.email")}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder={t("auth.placeholders.adminEmail")}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">{t("common.password")}</Label>
                <PasswordInput
                  id="password"
                  placeholder={t("auth.placeholders.password")}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">{t("auth.confirmPassword")}</Label>
                <PasswordInput
                  id="confirmPassword"
                  placeholder={t("auth.placeholders.confirmPassword")}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  disabled={loading}
                  required
                />
              </div>

              {isCodeRequired && (
                <div className="space-y-2">
                  <Label htmlFor="systemCode">{t("auth.systemAdminCode")}</Label>
                  <PasswordInput
                    id="systemCode"
                    placeholder={t("auth.placeholders.systemAdminCode")}
                    value={systemCode}
                    onChange={(e) => setSystemCode(e.target.value)}
                    disabled={loading}
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {t("auth.systemAdminCodeDescription")}
                  </p>
                </div>
              )}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading
                  ? t("auth.creatingAccount")
                  : t("auth.createSystemAdminAccount")}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <div className="text-sm text-center">
              <Link to="/login" className="text-primary hover:underline">
                {t("auth.alreadyHaveAccountLogin")}
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
