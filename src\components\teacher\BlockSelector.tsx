import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, ChevronDown } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { Block, Room } from "@/lib/types";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import { useTranslation } from "react-i18next";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface BlockSelectorProps {
  selectedRoom: string;
  onRoomSelect: (roomId: string) => void;
  teacherId: string;
  onBlockSelect: (blockId: string | null) => void;
  selectedBlock: string | null;
}

export function BlockSelector({
  selectedRoom,
  onRoomSelect,
  teacherId,
  onBlockSelect,
  selectedBlock,
}: BlockSelectorProps) {
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [newBlockName, setNewBlockName] = useState("");
  const [newRoomName, setNewRoomName] = useState("");
  const [isAddingBlock, setIsAddingBlock] = useState(false);
  const [isAddingRoom, setIsAddingRoom] = useState(false);
  const { toast } = useToast();
  const { profile } = useAuth();
  const { currentSchool } = useSchool();
  const { t } = useTranslation();

  // Fetch blocks and rooms
  useEffect(() => {
    fetchBlocks();
    fetchRooms();
  }, []);

  const fetchBlocks = async () => {
    try {
      // Only fetch blocks for the current school
      let query = supabase.from("blocks").select("*").order("name");

      // Filter by school_id if available
      if (profile?.school_id) {
        query = query.eq("school_id", profile.school_id);
      }

      const { data, error } = await query;

      if (error) throw error;
      setBlocks(data || []);
    } catch (error) {
      toast({
        title: t("common.error"),
        description: t("students.directory.fetchBlocksRoomsError"),
        variant: "destructive",
      });
    }
  };

  const fetchRooms = async () => {
    try {
      // Only fetch rooms for the current school (not just the teacher's rooms)
      let query = supabase.from("rooms").select("*").order("name");

      // Filter by school_id if available
      if (profile?.school_id) {
        query = query.eq("school_id", profile.school_id);
      }

      const { data, error } = await query;

      if (error) throw error;
      setRooms(data || []);
    } catch (error) {
      toast({
        title: t("common.error"),
        description: t("students.directory.fetchBlocksRoomsError"),
        variant: "destructive",
      });
    }
  };

  const handleAddBlock = async () => {
    try {
      if (!newBlockName.trim()) {
        toast({
          title: "Error",
          description: "Block name cannot be empty",
          variant: "destructive",
        });
        return;
      }

      if (!profile?.school_id) {
        toast({
          title: "Error",
          description:
            "School information is missing. Please complete your profile first.",
          variant: "destructive",
        });
        return;
      }

      const { data, error } = await supabase
        .from("blocks")
        .insert([
          {
            name: newBlockName.trim(),
            school_id: profile.school_id,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      setBlocks([...blocks, data]);
      setNewBlockName("");
      setIsAddingBlock(false);

      toast({
        title: "Success",
        description: "Block added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add block",
        variant: "destructive",
      });
    }
  };

  const handleAddRoom = async () => {
    try {
      if (!selectedBlock || !newRoomName.trim()) {
        toast({
          title: "Error",
          description: "Please select a block and enter a room name",
          variant: "destructive",
        });
        return;
      }

      if (!profile?.school_id) {
        toast({
          title: "Error",
          description:
            "School information is missing. Please complete your profile first.",
          variant: "destructive",
        });
        return;
      }

      const { data, error } = await supabase
        .from("rooms")
        .insert([
          {
            name: newRoomName.trim(),
            block_id: selectedBlock,
            teacher_id: teacherId,
            floor: 1,
            capacity: 30,
            school_id: profile.school_id,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      setRooms([...rooms, data]);
      setNewRoomName("");
      setIsAddingRoom(false);

      toast({
        title: "Success",
        description: "Room added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add room",
        variant: "destructive",
      });
    }
  };

  const getCurrentSelection = () => {
    if (!selectedBlock) return t("students.directory.allBlocks");
    const block = blocks.find((b) => b.id === selectedBlock);
    if (!selectedRoom)
      return `${t("common.block")} ${block?.name || ""}`;
    const room = rooms.find((r) => r.id === selectedRoom);
    return `${t("common.block")} ${block?.name || ""} - ${
      room?.name || ""
    }`;
  };

  return (
    <div className="flex flex-col gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full md:w-[250px] justify-between text-xs md:text-sm h-9 px-2 md:px-4"
          >
            <span className="truncate">{getCurrentSelection()}</span>
            <ChevronDown className="h-3 w-3 md:h-4 md:w-4 ml-1 md:ml-2 flex-shrink-0" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-[calc(100vw-2rem)] md:w-[250px] p-1 md:p-2">
          <DropdownMenuItem
            onClick={() => {
              onBlockSelect(null);
              onRoomSelect("");
            }}
            className="text-xs md:text-sm py-1.5 md:py-2"
          >
            {t("students.directory.allBlocks")}
          </DropdownMenuItem>
          {blocks.map((block) => (
            <DropdownMenuSub key={block.id}>
              <DropdownMenuSubTrigger
                onClick={() => {
                  onBlockSelect(block.id);
                  onRoomSelect("");
                }}
                className="text-xs md:text-sm py-1.5 md:py-2"
              >
                <span className="truncate">
                  {t("common.block")} {block.name}
                </span>
              </DropdownMenuSubTrigger>
              <DropdownMenuSubContent className="w-[calc(100vw-4rem)] md:w-[200px] p-1 md:p-2">
                <DropdownMenuItem
                  onClick={() => {
                    onBlockSelect(block.id);
                    onRoomSelect("");
                  }}
                  className="text-xs md:text-sm py-1.5 md:py-2"
                >
                  {t("students.directory.allRooms")}
                </DropdownMenuItem>
                {rooms
                  .filter((room) => room.block_id === block.id)
                  .map((room) => (
                    <DropdownMenuItem
                      key={room.id}
                      onClick={() => {
                        onBlockSelect(block.id);
                        onRoomSelect(room.id);
                      }}
                      className="text-xs md:text-sm py-1.5 md:py-2"
                    >
                      <span className="truncate">{room.name}</span>
                    </DropdownMenuItem>
                  ))}
                <DropdownMenuItem
                  onClick={() => {
                    onBlockSelect(block.id);
                    setIsAddingRoom(true);
                  }}
                  className="text-primary text-xs md:text-sm py-1.5 md:py-2"
                >
                  <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2 flex-shrink-0" />{" "}
                  <span className="truncate">
                    {t("students.directory.addNewRoom")}
                  </span>
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuSub>
          ))}
          <DropdownMenuItem
            onClick={() => setIsAddingBlock(true)}
            className="text-primary text-xs md:text-sm py-1.5 md:py-2"
          >
            <Plus className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2 flex-shrink-0" />{" "}
            <span className="truncate">
              {t("students.directory.addNewBlock")}
            </span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={isAddingBlock} onOpenChange={setIsAddingBlock}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("students.directory.addNewBlock")}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="blockName">
                {t("students.directory.blockName")}
              </Label>
              <Input
                id="blockName"
                value={newBlockName}
                onChange={(e) => setNewBlockName(e.target.value)}
                placeholder={t("students.directory.blockName")}
              />
            </div>
            <Button onClick={handleAddBlock}>
              {t("students.directory.addNewBlock")}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={isAddingRoom} onOpenChange={setIsAddingRoom}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("students.directory.addNewRoom")}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="roomName">{t("common.room")}</Label>
              <Input
                id="roomName"
                value={newRoomName}
                onChange={(e) => setNewRoomName(e.target.value)}
                placeholder={t("common.room")}
              />
            </div>
            <Button onClick={handleAddRoom}>
              {t("students.directory.addNewRoom")}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
