import { createClient } from '@supabase/supabase-js';

const supabaseUrl = "https://wclwxrilybnzkhvqzbmy.supabase.co";
const supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndjbHd4cmlseWJuemtodnF6Ym15Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU2NzA5NTksImV4cCI6MjA2MTI0Njk1OX0.MIARsz34RX0EftvwUkWIrEYQqE8VstxaCI31mjLhSHw";

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkRooms() {
  console.log('Checking all rooms in the database...');
  
  // Get all rooms
  const { data: rooms, error: roomsError } = await supabase
    .from('rooms')
    .select('*');

  if (roomsError) {
    console.error('Error querying rooms:', roomsError);
    return;
  }

  console.log('Found rooms:', rooms);

  // Check room_locations table
  const { data: locations, error: locationsError } = await supabase
    .from('room_locations')
    .select('*');

  if (locationsError) {
    console.error('Error querying room_locations:', locationsError);
    return;
  }

  console.log('Found room locations:', locations);
}

checkRooms().catch(console.error); 