-- Add school_id to rooms table if it doesn't exist
ALTER TABLE rooms ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id);

-- Update existing rooms to use the school_id from their teacher's profile
UPDATE rooms r
SET school_id = (
  SELECT school_id
  FROM profiles p
  WHERE p.id = r.teacher_id
)
WHERE r.school_id IS NULL;

-- Enable RLS on rooms table
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Teachers can view rooms in their school" ON rooms;
DROP POLICY IF EXISTS "Teachers can manage their own rooms" ON rooms;
DROP POLICY IF EXISTS "School admins can manage rooms in their school" ON rooms;
DROP POLICY IF EXISTS "System admins can manage all rooms" ON rooms;
DROP POLICY IF EXISTS "Students can view rooms in their school" ON rooms;

-- Teachers can view all rooms in their school
CREATE POLICY "Teachers can view rooms in their school"
ON rooms
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Teachers can manage their own rooms
CREATE POLICY "Teachers can manage their own rooms"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
    AND teacher.id = teacher_id
  )
)
WITH CHECK (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
    AND teacher.id = teacher_id
  )
  AND
  -- Ensure school_id matches the teacher's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage all rooms in their school
CREATE POLICY "School admins can manage rooms in their school"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Ensure school_id matches the admin's school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all rooms
CREATE POLICY "System admins can manage all rooms"
ON rooms
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Students can view rooms in their school
CREATE POLICY "Students can view rooms in their school"
ON rooms
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
  )
  AND
  -- Check if the room belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);