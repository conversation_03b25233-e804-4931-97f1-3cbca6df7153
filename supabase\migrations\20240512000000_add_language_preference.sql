-- Create language enum type if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'language_code') THEN
    CREATE TYPE language_code AS ENUM ('en', 'tr');
  END IF;
END $$;

-- Add preferred_language column to profiles table
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS preferred_language language_code DEFAULT 'en';

-- Update RLS policies to allow users to update their language preference
CREATE POLICY update_own_language_policy ON profiles
  FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (
    auth.uid() = user_id AND
    (
      -- Allow updating only the preferred_language column
      (
        (OLD.name = NEW.name) AND
        (OLD.email = NEW.email) AND
        (OLD.role = NEW.role) AND
        (OLD.photo_url IS NOT DISTINCT FROM NEW.photo_url) AND
        (OLD.student_id IS NOT DISTINCT FROM NEW.student_id) AND
        (OLD.teacher_id IS NOT DISTINCT FROM NEW.teacher_id) AND
        (OLD.admin_id IS NOT DISTINCT FROM NEW.admin_id) AND
        (OLD.department IS NOT DISTINCT FROM NEW.department) AND
        (OLD.position IS NOT DISTINCT FROM NEW.position) AND
        (OLD.subject IS NOT DISTINCT FROM NEW.subject) AND
        (OLD.course IS NOT DISTINCT FROM NEW.course) AND
        (OLD.biometric_registered IS NOT DISTINCT FROM NEW.biometric_registered) AND
        (OLD.block_name IS NOT DISTINCT FROM NEW.block_name) AND
        (OLD.room_number IS NOT DISTINCT FROM NEW.room_number) AND
        (OLD.pin IS NOT DISTINCT FROM NEW.pin) AND
        (OLD.school_id IS NOT DISTINCT FROM NEW.school_id) AND
        (OLD.school IS NOT DISTINCT FROM NEW.school) AND
        (OLD.access_level IS NOT DISTINCT FROM NEW.access_level)
      )
      OR
      -- Or allow full profile updates (for existing policies)
      (OLD.* IS DISTINCT FROM NEW.*)
    )
  );
