-- Add school_id to blocks table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'blocks' AND column_name = 'school_id'
  ) THEN
    ALTER TABLE blocks ADD COLUMN school_id UUID REFERENCES schools(id);
  END IF;
END $$;

-- Update existing blocks to use the default school
DO $$
DECLARE
  default_school_id UUID;
BEGIN
  -- Get the default school ID
  SELECT id INTO default_school_id FROM public.schools LIMIT 1;
  
  -- Update blocks table
  UPDATE public.blocks
  SET school_id = default_school_id
  WHERE school_id IS NULL;
END $$;

-- Add RLS policies for blocks table
ALTER TABLE blocks ENABLE ROW LEVEL SECURITY;

-- Teachers can view blocks in their school
CREATE POLICY "Teachers can view blocks in their school"
ON blocks
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a teacher
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the block belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- School admins can manage blocks in their school
CREATE POLICY "School admins can manage blocks in their school"
ON blocks
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the block belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the block belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- System admins can manage all blocks
CREATE POLICY "System admins can manage all blocks"
ON blocks
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- Students can view blocks in their school
CREATE POLICY "Students can view blocks in their school"
ON blocks
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a student
  EXISTS (
    SELECT 1 FROM profiles student
    WHERE student.user_id = auth.uid()
    AND student.role = 'student'
  )
  AND
  -- Check if the block belongs to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);
