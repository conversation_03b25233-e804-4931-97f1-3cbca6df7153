import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import {
  getDatabaseCleanupSettings,
  getCleanupStatistics,
  triggerDatabaseCleanup,
} from '@/lib/services/database-cleanup-service';
import {
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Database,
  BarChart3,
} from 'lucide-react';

export default function DatabaseCleanupTest() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<any>(null);
  const [settings, setSettings] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching cleanup data...');
      
      // Test statistics
      const stats = await getCleanupStatistics();
      console.log('Statistics result:', stats);
      setStatistics(stats);
      
      // Test settings
      const settingsData = await getDatabaseCleanupSettings();
      console.log('Settings result:', settingsData);
      setSettings(settingsData);
      
      toast({
        title: 'Data Loaded',
        description: 'Successfully loaded cleanup data',
      });
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      toast({
        title: 'Error',
        description: 'Failed to load cleanup data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const testCleanup = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Testing cleanup...');
      const result = await triggerDatabaseCleanup(true); // Force cleanup
      console.log('Cleanup result:', result);
      
      if (result.success) {
        toast({
          title: 'Cleanup Test Successful',
          description: `Total records processed: ${result.total_deleted || 0}`,
        });
      } else {
        toast({
          title: 'Cleanup Test Failed',
          description: 'Cleanup was not successful',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error testing cleanup:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      toast({
        title: 'Cleanup Test Error',
        description: 'Failed to test cleanup functionality',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Cleanup System Test
          </CardTitle>
          <CardDescription>
            Test the improved database cleanup functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex gap-2">
            <Button
              onClick={fetchData}
              disabled={loading}
              variant="outline"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            <Button
              onClick={testCleanup}
              disabled={loading}
              variant="destructive"
            >
              <Database className="h-4 w-4 mr-2" />
              Test Cleanup
            </Button>
          </div>

          {/* Statistics Display */}
          {statistics && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Current Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="p-3 border rounded text-center">
                    <div className="text-lg font-bold">{statistics.notifications || 0}</div>
                    <div className="text-sm text-muted-foreground">Notifications</div>
                  </div>
                  <div className="p-3 border rounded text-center">
                    <div className="text-lg font-bold">{statistics.attendance_records || 0}</div>
                    <div className="text-sm text-muted-foreground">Attendance</div>
                  </div>
                  <div className="p-3 border rounded text-center">
                    <div className="text-lg font-bold">{statistics.location_alerts || 0}</div>
                    <div className="text-sm text-muted-foreground">Location Alerts</div>
                  </div>
                  <div className="p-3 border rounded text-center">
                    <div className="text-lg font-bold">{statistics.system_logs || 0}</div>
                    <div className="text-sm text-muted-foreground">System Logs</div>
                  </div>
                  <div className="p-3 border rounded text-center">
                    <div className="text-lg font-bold">{statistics.qr_sessions || 0}</div>
                    <div className="text-sm text-muted-foreground">QR Sessions</div>
                  </div>
                  <div className="p-3 border rounded text-center">
                    <div className="text-lg font-bold">{statistics.user_activity_logs || 0}</div>
                    <div className="text-sm text-muted-foreground">Activity Logs</div>
                  </div>
                </div>
                
                {statistics.last_cleanup_at && (
                  <div className="mt-4 p-3 bg-muted rounded">
                    <div className="text-sm">
                      <strong>Last Cleanup:</strong> {new Date(statistics.last_cleanup_at).toLocaleString()}
                    </div>
                  </div>
                )}
                
                {statistics.cleanup_enabled !== undefined && (
                  <div className="mt-2 flex items-center gap-2">
                    {statistics.cleanup_enabled ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    )}
                    <span className="text-sm">
                      Cleanup is {statistics.cleanup_enabled ? 'enabled' : 'disabled'}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Settings Display */}
          {settings && (
            <Card>
              <CardHeader>
                <CardTitle>Current Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div>Enabled: {settings.enabled ? 'Yes' : 'No'}</div>
                  <div>Frequency: {settings.cleanup_frequency}</div>
                  <div>Notifications retention: {settings.notifications_retention_days} days</div>
                  <div>Attendance retention: {settings.attendance_records_retention_days} days</div>
                  {settings.location_alerts_retention_days && (
                    <div>Location alerts retention: {settings.location_alerts_retention_days} days</div>
                  )}
                  {settings.system_logs_retention_days && (
                    <div>System logs retention: {settings.system_logs_retention_days} days</div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Debug Information */}
          <Card>
            <CardHeader>
              <CardTitle>Debug Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-xs">
                <div><strong>Statistics type:</strong> {typeof statistics}</div>
                <div><strong>Settings type:</strong> {typeof settings}</div>
                <div><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</div>
                <div><strong>Error:</strong> {error || 'None'}</div>
              </div>
              
              {statistics && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium">Raw Statistics Data</summary>
                  <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                    {JSON.stringify(statistics, null, 2)}
                  </pre>
                </details>
              )}
              
              {settings && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium">Raw Settings Data</summary>
                  <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                    {JSON.stringify(settings, null, 2)}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
}
