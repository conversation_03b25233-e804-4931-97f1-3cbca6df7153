# 🔐 Security Remediation Guide

## ⚠️ **CRITICAL SECURITY ISSUES**

### 1. **API Keys Exposed in Client Code** 🚨

**Current Issue**: All API keys are exposed via `VITE_` environment variables:
```javascript
// ❌ EXPOSED TO USERS
VITE_SENDGRID_API_KEY=your_sendgrid_api_key
VITE_TWILIO_AUTH_TOKEN=your_twilio_auth_token
VITE_TWILIO_ACCOUNT_SID=your_twilio_account_sid
```

**Risk Level**: **CRITICAL** - Anyone can view source and steal your API keys

**Solution**: Move to Supabase Edge Functions

#### Step 1: Create Secure Edge Functions

```bash
# Create notification edge function
supabase functions new send-secure-notification
```

#### Step 2: Update Edge Function Code

```typescript
// supabase/functions/send-secure-notification/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { type, to, subject, message } = await req.json()
    
    // Get secrets from Supabase environment (not exposed to client)
    const SENDGRID_API_KEY = Deno.env.get('SENDGRID_API_KEY')
    const TWILIO_AUTH_TOKEN = Deno.env.get('TWILIO_AUTH_TOKEN')
    const TWILIO_ACCOUNT_SID = Deno.env.get('TWILIO_ACCOUNT_SID')
    
    if (type === 'email') {
      // Send email using SendGrid
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SENDGRID_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          personalizations: [{ to: [{ email: to }] }],
          from: { email: '<EMAIL>' },
          subject,
          content: [{ type: 'text/plain', value: message }],
        }),
      })
      
      return new Response(JSON.stringify({ success: true }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }
    
    if (type === 'sms') {
      // Send SMS using Twilio
      const response = await fetch(
        `https://api.twilio.com/2010-04-01/Accounts/${TWILIO_ACCOUNT_SID}/Messages.json`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${btoa(`${TWILIO_ACCOUNT_SID}:${TWILIO_AUTH_TOKEN}`)}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            To: to,
            From: '+**********', // Your Twilio number
            Body: message,
          }),
        }
      )
      
      return new Response(JSON.stringify({ success: true }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }
    
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
```

#### Step 3: Deploy Edge Function

```bash
# Set environment variables in Supabase dashboard
supabase secrets set SENDGRID_API_KEY=your_actual_key
supabase secrets set TWILIO_AUTH_TOKEN=your_actual_token
supabase secrets set TWILIO_ACCOUNT_SID=your_actual_sid

# Deploy function
supabase functions deploy send-secure-notification --project-ref your_project_ref
```

#### Step 4: Update Client Code

```typescript
// Replace direct API calls with edge function calls
const sendNotification = async (type: 'email' | 'sms', to: string, subject: string, message: string) => {
  const { data, error } = await supabase.functions.invoke('send-secure-notification', {
    body: { type, to, subject, message }
  })
  
  if (error) throw error
  return data
}
```

### 2. **Environment Variables Security** 🔒

**Current Issue**: Development configurations mixed with production

**Solution**: Create environment-specific configurations

#### Production Environment Variables

```bash
# .env.production
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key

# Remove these - now handled by edge functions
# VITE_SENDGRID_API_KEY=
# VITE_TWILIO_AUTH_TOKEN=
# VITE_TWILIO_ACCOUNT_SID=

# QR Security (generate new production keys)
NEXT_PUBLIC_QR_EXPIRY_SECONDS=300
NEXT_PUBLIC_QR_CHALLENGE_ROTATION_SECONDS=30
NEXT_PUBLIC_QR_CHALLENGE_GRACE_SLOTS=1

# App Branding
VITE_APP_NAME="Your Production App Name"
VITE_APP_SHORT_NAME="YPA"
VITE_COMPANY_NAME="Your Company"
VITE_CONTACT_EMAIL="<EMAIL>"
```

### 3. **Database Security Policies** 🛡️

**Current Issue**: Need to verify all RLS policies are properly configured

#### Audit Database Security

```sql
-- Check which tables have RLS enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Check existing policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';
```

#### Essential RLS Policies

```sql
-- Ensure profiles table is secure
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = user_id);

-- Ensure attendance records are secure
ALTER TABLE attendance_records ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own school's attendance" ON attendance_records
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.user_id = auth.uid() 
      AND profiles.school_id = attendance_records.school_id
    )
  );

-- Secure sensitive tables
ALTER TABLE schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE excuses ENABLE ROW LEVEL SECURITY;
```

### 4. **Input Validation & Sanitization** 🧹

**Current Issue**: Need comprehensive input validation

#### Client-Side Validation

```typescript
// Add to lib/validation.ts
import { z } from 'zod'

export const attendanceSchema = z.object({
  student_id: z.string().uuid(),
  location_id: z.string().uuid(),
  qr_code: z.string().min(1).max(1000),
  timestamp: z.date(),
})

export const excuseSchema = z.object({
  student_id: z.string().uuid(),
  reason: z.string().min(1).max(500),
  start_date: z.date(),
  end_date: z.date(),
  supporting_document: z.string().url().optional(),
})

// Use in components
const validateAttendance = (data: unknown) => {
  return attendanceSchema.parse(data)
}
```

#### Server-Side Validation (Edge Functions)

```typescript
// In edge functions, validate all inputs
const validateInput = (data: any, schema: any) => {
  try {
    return schema.parse(data)
  } catch (error) {
    throw new Error(`Invalid input: ${error.message}`)
  }
}
```

### 5. **Rate Limiting** ⏱️

**Current Issue**: No rate limiting implemented

#### Implement Rate Limiting

```typescript
// lib/rate-limiter.ts
const rateLimiter = new Map<string, { count: number; resetTime: number }>()

export const checkRateLimit = (userId: string, maxRequests = 10, windowMs = 60000) => {
  const now = Date.now()
  const userLimit = rateLimiter.get(userId)
  
  if (!userLimit || now > userLimit.resetTime) {
    rateLimiter.set(userId, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (userLimit.count >= maxRequests) {
    return false
  }
  
  userLimit.count++
  return true
}
```

### 6. **Content Security Policy** 📋

**Current Issue**: No CSP headers configured

#### Add CSP Headers

```html
<!-- Add to index.html -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self' https://*.supabase.co wss://*.supabase.co;
  frame-src 'none';
  object-src 'none';
  base-uri 'self';
  form-action 'self';
">
```

### 7. **Secure Headers** 🔒

**Current Issue**: Missing security headers

#### Configure Security Headers

```javascript
// For Vercel - vercel.json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        },
        {
          "key": "Permissions-Policy",
          "value": "camera=(), microphone=(), geolocation=()"
        }
      ]
    }
  ]
}
```

## 🚀 **Implementation Priority**

### Week 1: Critical Security Fixes
1. Move API keys to edge functions
2. Set up production environment variables
3. Audit and fix RLS policies
4. Implement basic rate limiting

### Week 2: Enhanced Security
1. Add comprehensive input validation
2. Configure security headers
3. Set up CSP policies
4. Implement audit logging

### Week 3: Monitoring & Testing
1. Set up security monitoring
2. Implement error tracking
3. Conduct security testing
4. Document security procedures

## 🔍 **Security Testing Checklist**

- [ ] Test with API keys removed from client
- [ ] Verify RLS policies block unauthorized access
- [ ] Test rate limiting functionality
- [ ] Validate input sanitization
- [ ] Check security headers are present
- [ ] Test authentication flows
- [ ] Verify session management
- [ ] Test CORS policies

## 📞 **Emergency Response**

If you discover a security breach:

1. **Immediately revoke** all exposed API keys
2. **Rotate** all secrets and tokens
3. **Check logs** for unauthorized access
4. **Notify users** if data was compromised
5. **Document** the incident for future prevention

**Remember**: Security is not a one-time task but an ongoing process!
