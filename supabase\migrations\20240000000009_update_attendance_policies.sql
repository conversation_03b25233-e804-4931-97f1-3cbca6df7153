-- Enable RLS on attendance_records table
ALTER TABLE public.attendance_records ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions
GRANT ALL ON public.attendance_records TO authenticated;

-- Policy for students to insert their own attendance records
CREATE POLICY "Students can insert their own attendance records"
ON public.attendance_records
FOR INSERT
TO authenticated
WITH CHECK (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE id::text = student_id::text
  )
);

-- Policy for students to update their own attendance records
CREATE POLICY "Students can update their own attendance records"
ON public.attendance_records
FOR UPDATE
TO authenticated
USING (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE id::text = student_id::text
  )
)
WITH CHECK (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE id::text = student_id::text
  )
);

-- Policy for students to view their own attendance records
CREATE POLICY "Students can view their own attendance records"
ON public.attendance_records
FOR SELECT
TO authenticated
USING (
  auth.uid()::text = (
    SELECT user_id::text 
    FROM profiles 
    WHERE id::text = student_id::text
  )
  OR
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role IN ('teacher', 'admin')
  )
);

-- Policy for teachers to view attendance records for their rooms
CREATE POLICY "Teachers can view attendance records for their rooms"
ON public.attendance_records
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM rooms 
    WHERE rooms.id::text = attendance_records.room_id::text
    AND rooms.teacher_id::text = (
      SELECT id::text 
      FROM profiles 
      WHERE user_id::text = auth.uid()::text
    )
  )
);

-- Policy for teachers to update attendance records for their rooms
CREATE POLICY "Teachers can update attendance records for their rooms"
ON public.attendance_records
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM rooms 
    WHERE rooms.id::text = attendance_records.room_id::text
    AND rooms.teacher_id::text = (
      SELECT id::text 
      FROM profiles 
      WHERE user_id::text = auth.uid()::text
      AND role = 'teacher'
    )
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM rooms 
    WHERE rooms.id::text = attendance_records.room_id::text
    AND rooms.teacher_id::text = (
      SELECT id::text 
      FROM profiles 
      WHERE user_id::text = auth.uid()::text
      AND role = 'teacher'
    )
  )
);

-- Policy for admins to manage all attendance records
CREATE POLICY "Admins can manage all attendance records"
ON public.attendance_records
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role = 'admin'
  )
); 