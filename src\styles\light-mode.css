/* Light Mode Specific Styles */

/* Light mode overrides for better visibility */
.light {
  /* Light mode primary - Logo navy blue for better contrast */
  --primary: 225 80% 16%;
  --primary-foreground: 0 0% 100%;

  /* Light mode specific adjustments */
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;

  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
}

/* Light mode button styles */
.light .bg-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Light mode navbar adjustments */
.light nav {
  background-color: hsl(var(--primary));
}

/* Light mode card styles */
.light .card {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

/* Light mode text contrast */
.light .text-foreground {
  color: hsl(var(--foreground));
}

/* Light mode form elements */
.light input,
.light textarea,
.light select {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

/* Light mode focus states */
.light input:focus,
.light textarea:focus,
.light select:focus {
  ring-color: hsl(var(--ring));
}
