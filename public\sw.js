// Enhanced Service Worker for PWA
const CACHE_NAME = 'attendance-app-v2.0';
const STATIC_CACHE = 'static-v2.0';
const DYNAMIC_CACHE = 'dynamic-v2.0';
const IMAGE_CACHE = 'images-v2.0';

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/android-chrome-192x192.svg',
  '/android-chrome-512x512.svg',
  '/logo.svg',
  '/logo-horizontal.svg',
  '/favicon.svg'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\/api\/auth\/user/,
  /\/api\/profiles/,
  /\/api\/notifications/
];

// Maximum cache sizes
const MAX_DYNAMIC_CACHE_SIZE = 50;
const MAX_IMAGE_CACHE_SIZE = 30;

// Helper function to limit cache size
async function limitCacheSize(cacheName, maxSize) {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  if (keys.length > maxSize) {
    await cache.delete(keys[0]);
    limitCacheSize(cacheName, maxSize);
  }
}

// Install event - cache static resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.resolve()
      .then(() => {
        return caches.open(STATIC_CACHE);
      })
      .then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        // Only skip waiting if we're not already controlling clients
        try {
          return self.skipWaiting();
        } catch (error) {
          console.warn('Skip waiting failed:', error);
          return Promise.resolve();
        }
      })
      .catch((error) => {
        console.error('Failed to cache static assets:', error);
        // Don't fail the installation completely
        return Promise.resolve();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    Promise.resolve()
      .then(() => {
        // Clean up old caches
        return caches.keys();
      })
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE && cacheName !== IMAGE_CACHE) {
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        // Only claim clients if we're in the right state
        try {
          if (self.registration && self.registration.active === self) {
            return self.clients.claim();
          } else {
            return Promise.resolve();
          }
        } catch (error) {
          console.warn('Failed to claim clients:', error);
          return Promise.resolve();
        }
      })
      .catch((error) => {
        console.error('Service Worker activation failed:', error);
      })
  );
});

// Enhanced fetch event with different caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;

  try {
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
      return;
    }

    // Handle different types of requests with proper error handling
    if (request.destination === 'image') {
      // Cache-first strategy for images
      event.respondWith(
        cacheFirstStrategy(request, IMAGE_CACHE).catch((error) => {
          console.warn('Image caching failed:', error);
          return new Response('Image unavailable', { status: 503 });
        })
      );
    } else if (API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname))) {
      // Network-first strategy for API calls
      event.respondWith(
        networkFirstStrategy(request, DYNAMIC_CACHE).catch((error) => {
          console.warn('API request failed:', error);
          return new Response('API unavailable offline', { status: 503 });
        })
      );
    } else if (url.origin === location.origin) {
      // Stale-while-revalidate for app resources
      event.respondWith(
        staleWhileRevalidateStrategy(request, STATIC_CACHE).catch((error) => {
          console.warn('App resource failed:', error);
          return new Response('Resource unavailable', { status: 503 });
        })
      );
    } else {
      // Network-only for external resources with error handling
      event.respondWith(
        fetch(request).catch((error) => {
          console.warn('External resource failed:', error);
          return new Response('External resource unavailable offline', {
            status: 503,
            statusText: 'Service Unavailable'
          });
        })
      );
    }
  } catch (error) {
    console.error('Fetch event error:', error);
    event.respondWith(
      new Response('Service Worker error', { status: 500 })
    );
  }
});

// Caching strategies
async function cacheFirstStrategy(request, cacheName) {
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }

  try {
    const networkResponse = await fetch(request);
    if (networkResponse && networkResponse.ok) {
      // Clone the response before using it
      const responseToCache = networkResponse.clone();
      const cache = await caches.open(cacheName);
      await cache.put(request, responseToCache);
      limitCacheSize(cacheName, cacheName === IMAGE_CACHE ? MAX_IMAGE_CACHE_SIZE : MAX_DYNAMIC_CACHE_SIZE);
    }
    return networkResponse;
  } catch (error) {
    console.error('Cache-first strategy failed:', error);
    return new Response('Offline', { status: 503 });
  }
}

async function networkFirstStrategy(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse && networkResponse.ok) {
      // Clone the response before using it
      const responseToCache = networkResponse.clone();
      const cache = await caches.open(cacheName);
      await cache.put(request, responseToCache);
      limitCacheSize(cacheName, MAX_DYNAMIC_CACHE_SIZE);
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    return cachedResponse || new Response('Offline', { status: 503 });
  }
}

async function staleWhileRevalidateStrategy(request, cacheName) {
  const cachedResponse = await caches.match(request);

  // Start the network request in the background
  const fetchPromise = fetch(request).then(async (networkResponse) => {
    if (networkResponse && networkResponse.ok) {
      try {
        // Clone the response before using it
        const responseToCache = networkResponse.clone();
        const cache = await caches.open(cacheName);
        await cache.put(request, responseToCache);
      } catch (error) {
        console.warn('Failed to cache response:', error);
      }
    }
    return networkResponse;
  }).catch((error) => {
    console.warn('Network request failed:', error);
    return cachedResponse;
  });

  // Return cached response immediately if available, otherwise wait for network
  return cachedResponse || fetchPromise;
}

// Push event - handle incoming push notifications
self.addEventListener('push', (event) => {
  
  let notificationData = {
    title: 'Attendance Reminder',
    body: 'You have a new attendance notification',
    icon: '/android-chrome-192x192.svg',
    badge: '/android-chrome-192x192.svg',
    image: '/logo-horizontal.svg',
    tag: 'attendance-notification',
    requireInteraction: true,
    vibrate: [200, 100, 200, 100, 200],
    actions: [
      {
        action: 'check-in',
        title: '✅ Check In Now'
      },
      {
        action: 'view',
        title: '👁️ View Details'
      }
    ]
  };

  // Parse notification data if provided
  if (event.data) {
    try {
      const data = event.data.json();
      notificationData = {
        ...notificationData,
        ...data,
        // Ensure critical properties for attendance reminders
        requireInteraction: data.type === 'attendance_reminder' ? true : false,
        vibrate: data.type === 'attendance_reminder' ? [300, 100, 300, 100, 300] : [200, 100, 200],
        silent: false, // Always play sound
        renotify: data.type === 'attendance_reminder' ? true : false
      };
    } catch (error) {
      console.error('Error parsing push notification data:', error);
    }
  }

  // Show the notification
  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  
  event.notification.close();

  // Handle action clicks
  if (event.action === 'check-in') {
    // Open app to QR scanner
    event.waitUntil(
      clients.openWindow('/?tab=scan&action=checkin')
    );
  } else if (event.action === 'view') {
    // Open app to notifications
    event.waitUntil(
      clients.openWindow('/?tab=notifications')
    );
  } else {
    // Default click - open app
    event.waitUntil(
      clients.matchAll({ type: 'window' }).then((clientList) => {
        // If app is already open, focus it
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            return client.focus();
          }
        }
        // Otherwise open new window
        if (clients.openWindow) {
          return clients.openWindow('/');
        }
      })
    );
  }
});

// Background sync for offline notifications
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync-notifications') {
    event.waitUntil(
      // Sync any pending notifications when back online
      syncPendingNotifications()
    );
  }
});

async function syncPendingNotifications() {
  try {
    // This would sync with your backend when connection is restored
  } catch (error) {
    console.error('Error syncing notifications:', error);
  }
}

// Handle messages from the main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    try {
      self.skipWaiting().then(() => {
        // Skip waiting completed
      }).catch((error) => {
        console.warn('Skip waiting failed:', error);
      });
    } catch (error) {
      console.warn('Failed to process SKIP_WAITING message:', error);
    }
  }
});

// Handle periodic background sync
self.addEventListener('periodicsync', (event) => {
  if (event.tag === 'background-attendance-sync') {
    event.waitUntil(syncPendingNotifications());
  }
});

// Global error handlers
self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker unhandled promise rejection:', event.reason);
  event.preventDefault(); // Prevent the default browser behavior
});
