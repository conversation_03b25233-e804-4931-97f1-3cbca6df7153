import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  WifiOff, 
  Wifi, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';

interface OfflineIndicatorProps {
  showFullPage?: boolean;
}

export default function OfflineIndicator({ showFullPage = false }: OfflineIndicatorProps) {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);
  const [lastOnlineTime, setLastOnlineTime] = useState<Date | null>(null);
  const { t } = useTranslation();

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
      setLastOnlineTime(new Date());
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial last online time if online
    if (navigator.onLine) {
      setLastOnlineTime(new Date());
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetry = () => {
    window.location.reload();
  };

  if (showFullPage && !isOnline) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-full max-w-md"
        >
          <Card className="border-2 border-orange-200 bg-gradient-to-r from-orange-50 to-amber-50">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                <WifiOff className="w-8 h-8 text-orange-600" />
              </div>
              <CardTitle className="text-xl text-orange-800">
                {t('pwa.offline')}
              </CardTitle>
              <CardDescription className="text-orange-600">
                You're currently offline. Some features may be limited.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-orange-100 p-4 rounded-lg">
                <h3 className="font-medium text-orange-800 mb-2">Available Offline:</h3>
                <ul className="space-y-1 text-sm text-orange-700">
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    View cached attendance data
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Access profile information
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    View notifications
                  </li>
                </ul>
              </div>
              
              <div className="bg-red-100 p-4 rounded-lg">
                <h3 className="font-medium text-red-800 mb-2">Requires Internet:</h3>
                <ul className="space-y-1 text-sm text-red-700">
                  <li className="flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    QR code scanning
                  </li>
                  <li className="flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    Real-time attendance updates
                  </li>
                  <li className="flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    Biometric authentication
                  </li>
                </ul>
              </div>

              {lastOnlineTime && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="w-4 h-4" />
                  Last online: {lastOnlineTime.toLocaleTimeString()}
                </div>
              )}

              <Button 
                onClick={handleRetry}
                className="w-full bg-orange-600 hover:bg-orange-700"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  // Mini indicator for when online/offline
  return (
    <AnimatePresence>
      {showOfflineMessage && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="fixed top-4 right-4 z-50"
        >
          <Badge variant="destructive" className="flex items-center gap-2 px-3 py-2">
            <WifiOff className="w-4 h-4" />
            {t('pwa.offline')}
          </Badge>
        </motion.div>
      )}
      
      {isOnline && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="fixed top-4 right-4 z-50"
        >
          <Badge variant="default" className="flex items-center gap-2 px-3 py-2 bg-green-600">
            <Wifi className="w-4 h-4" />
            {t('pwa.online')}
          </Badge>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
