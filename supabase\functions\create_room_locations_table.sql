CREATE OR REPLACE FUNCTION create_room_locations_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the table already exists
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'room_locations') THEN
    -- Create the room_locations table
    CREATE TABLE public.room_locations (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      room_id UUID NOT NULL REFERENCES public.rooms(id) ON DELETE CASCADE,
      latitude DECIMAL(10, 8) NOT NULL,
      longitude DECIMAL(11, 8) NOT NULL,
      radius_meters INTEGER NOT NULL DEFAULT 50,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
      updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
      UNIQUE(room_id)
    );

    -- Add RLS policies
    ALTER TABLE public.room_locations ENABLE ROW LEVEL SECURITY;

    -- Allow teachers to read any room location
    CREATE POLICY "Teachers can read any room location"
    ON public.room_locations FOR SELECT
    TO authenticated
    USING (
      EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.user_id = auth.uid()
        AND profiles.role = 'teacher'
      )
    );

    -- Allow teachers to update their own room locations
    CREATE POLICY "Teachers can update their own room locations"
    ON public.room_locations FOR ALL
    TO authenticated
    USING (
      EXISTS (
        SELECT 1 FROM public.rooms
        WHERE rooms.id = room_locations.room_id
        AND rooms.teacher_id = (
          SELECT id FROM public.profiles
          WHERE profiles.user_id = auth.uid()
          AND profiles.role = 'teacher'
        )
      )
    );

    -- Allow students to read room locations for rooms they can access
    CREATE POLICY "Students can read room locations"
    ON public.room_locations FOR SELECT
    TO authenticated
    USING (
      EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.user_id = auth.uid()
        AND profiles.role = 'student'
        AND EXISTS (
          SELECT 1 FROM public.rooms
          WHERE rooms.id = room_locations.room_id
        )
      )
    );

    -- Grant permissions
    GRANT ALL ON public.room_locations TO authenticated;
    GRANT USAGE ON SCHEMA public TO authenticated;
  END IF;
END;
$$; 