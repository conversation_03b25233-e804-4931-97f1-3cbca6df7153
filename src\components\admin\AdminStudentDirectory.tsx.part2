  return (
    <div className="space-y-6">
      {/* Main Student Directory Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
            <div>
              <CardTitle>Student Directory</CardTitle>
              <CardDescription>
                View and manage all students in the system
              </CardDescription>
            </div>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name, ID, or email..."
                    className="pl-8 pr-8 w-full md:w-[280px]"
                    value={searchQuery}
                    onChange={(e) => {
                      // Update in real-time
                      setSearchQuery(e.target.value);
                    }}
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery("")}
                      className="absolute right-2.5 top-2.5 h-4 w-4 text-muted-foreground hover:text-foreground"
                      aria-label="Clear search"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
                {searchQuery && (
                  <Badge
                    variant="outline"
                    className="flex items-center gap-1 h-9 px-3"
                  >
                    <Search className="h-3 w-3" />
                    <span>
                      {searchQuery.length > 15
                        ? `${searchQuery.substring(0, 15)}...`
                        : searchQuery}
                    </span>
                  </Badge>
                )}
              </div>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <Select
                    value={selectedBlock}
                    onValueChange={handleBlockChange}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select Block" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Blocks</SelectItem>
                      {blocks.map((block) => (
                        <SelectItem key={block.id} value={block.id}>
                          Block {block.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  <DoorOpen className="h-4 w-4 text-muted-foreground" />
                  <Select value={selectedRoom} onValueChange={handleRoomChange}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select Room" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">All Rooms</SelectItem>
                      {availableRooms.map((room) => (
                        <SelectItem key={room.id} value={room.id}>
                          Room {room.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead className="w-[80px]">Profile</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Student ID</TableHead>
                    <TableHead>Course</TableHead>
                    <TableHead>Block</TableHead>
                    <TableHead>Room</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assignedStudents.length > 0 ? (
                    assignedStudents.map((student) => {
                      const status = getAttendanceStatus(student.id);
                      return (
                        <TableRow key={student.id}>
                          <TableCell>
                            {student.photoUrl ? (
                              <img
                                src={student.photoUrl}
                                alt={student.name}
                                className="w-10 h-10 rounded-full object-cover ring-2 ring-primary/10"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center ring-2 ring-primary/10">
                                <UserCircle className="w-6 h-6 text-muted-foreground" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="font-medium">
                            {student.name}
                          </TableCell>
                          <TableCell>
                            {student.studentId || "Not set"}
                          </TableCell>
                          <TableCell>{student.course || "Not set"}</TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {student.blocks
                                ? `Block ${student.blocks.name}`
                                : "Not Assigned"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {student.rooms
                                ? `Room ${student.rooms.name}`
                                : "Not Assigned"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getStatusIcon(status)}
                              <span className="capitalize">{status}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedStudent(student)}
                              >
                                View
                              </Button>
                              <Button
                                variant={
                                  status === "present"
                                    ? "destructive"
                                    : "default"
                                }
                                size="sm"
                                onClick={() => toggleAttendanceStatus(student)}
                              >
                                {status === "present" ? (
                                  <>
                                    <X className="w-4 h-4 mr-1" />
                                    Absent
                                  </>
                                ) : (
                                  <>
                                    <Check className="w-4 h-4 mr-1" />
                                    Present
                                  </>
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          {searchQuery ? (
                            <>
                              <Search className="w-10 h-10 opacity-20 mb-2" />
                              <p>No students match your search criteria</p>
                              <p className="text-sm mt-1">
                                Try searching by name, ID, or email
                              </p>
                            </>
                          ) : selectedBlock !== "all" ||
                            selectedRoom !== "none" ? (
                            <>
                              <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                              <p>
                                No students assigned to this{" "}
                                {selectedRoom !== "none" ? "room" : "block"}
                              </p>
                            </>
                          ) : (
                            <>
                              <UserCircle className="w-10 h-10 opacity-20 mb-2" />
                              <p>No students found in the system</p>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Student Profile Dialog */}
      {selectedStudent && (
        <Dialog
          open={!!selectedStudent}
          onOpenChange={(open) => !open && setSelectedStudent(null)}
        >
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Student Profile</DialogTitle>
              <DialogDescription>
                Detailed information about {selectedStudent.name}
              </DialogDescription>
            </DialogHeader>

            <div className="flex flex-col items-center space-y-4 pt-4">
              {/* Student Photo */}
              <div className="relative">
                {selectedStudent.photoUrl ? (
                  <img
                    src={selectedStudent.photoUrl}
                    alt={selectedStudent.name}
                    className="w-24 h-24 rounded-full object-cover border-2 border-primary"
                  />
                ) : (
                  <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center border-2 border-primary">
                    <User size={32} />
                  </div>
                )}
              </div>

              {/* Student Name */}
              <h2 className="text-xl font-bold">{selectedStudent.name}</h2>

              {/* Student Details */}
              <div className="w-full space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <div className="font-medium">Student ID:</div>
                  <div>{selectedStudent.studentId || "Not set"}</div>

                  <div className="font-medium">Email:</div>
                  <div className="break-all">{selectedStudent.email}</div>

                  <div className="font-medium">Course:</div>
                  <div>{selectedStudent.course || "Not set"}</div>

                  <div className="font-medium">Block:</div>
                  <div>
                    {selectedStudent.blocks
                      ? `Block ${selectedStudent.blocks.name}`
                      : "Not Assigned"}
                  </div>

                  <div className="font-medium">Room:</div>
                  <div>
                    {selectedStudent.rooms
                      ? `Room ${selectedStudent.rooms.name}`
                      : "Not Assigned"}
                  </div>

                  <div className="font-medium">Biometric:</div>
                  <div>
                    {selectedStudent.biometricRegistered ? (
                      <Badge className="bg-green-100 text-green-800">
                        Registered
                      </Badge>
                    ) : (
                      <Badge
                        variant="outline"
                        className="text-muted-foreground"
                      >
                        Not Registered
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex w-full space-x-2 pt-4">
                <Button
                  onClick={() => toggleAttendanceStatus(selectedStudent)}
                  className="flex-1"
                  variant={
                    getAttendanceStatus(selectedStudent.id) === "present"
                      ? "destructive"
                      : "default"
                  }
                >
                  {getAttendanceStatus(selectedStudent.id) === "present" ? (
                    <>
                      <X className="mr-2 h-4 w-4" />
                      Mark as Absent
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Mark as Present
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
