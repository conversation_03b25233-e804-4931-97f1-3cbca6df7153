import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import {
  Edit2,
  Trash2,
  Plus,
  Building2,
  DoorOpen,
  MoreVertical,
} from "lucide-react";
import { Block, Room } from "@/lib/types";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useSchool } from "@/context/SchoolContext";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface BlockManagementProps {
  teacherId: string;
}

export function BlockManagement({ teacherId }: BlockManagementProps) {
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [isAddingBlock, setIsAddingBlock] = useState(false);
  const [isAddingRoom, setIsAddingRoom] = useState(false);
  const [isEditingBlock, setIsEditingBlock] = useState<string | null>(null);
  const [isEditingRoom, setIsEditingRoom] = useState<string | null>(null);
  const [isDeletingBlock, setIsDeletingBlock] = useState<string | null>(null);
  const [isDeletingRoom, setIsDeletingRoom] = useState<string | null>(null);
  const [selectedBlock, setSelectedBlock] = useState<string | null>(null);
  const [newBlockName, setNewBlockName] = useState("");
  const [newRoomName, setNewRoomName] = useState("");
  const { toast } = useToast();
  const { profile } = useAuth();
  const { currentSchool } = useSchool();

  // Fetch blocks and rooms
  useEffect(() => {
    fetchBlocks();
    fetchRooms();
  }, []);

  const fetchBlocks = async () => {
    try {
      // Only fetch blocks for the current school
      let query = supabase.from("blocks").select("*").order("name");

      // Filter by school_id if available
      if (profile?.school_id) {
        query = query.eq("school_id", profile.school_id);
      }

      const { data, error } = await query;

      if (error) throw error;
      setBlocks(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch blocks",
        variant: "destructive",
      });
    }
  };

  const fetchRooms = async () => {
    try {
      // Only fetch rooms for the current school and teacher
      let query = supabase
        .from("rooms")
        .select("*")
        .eq("teacher_id", teacherId)
        .order("name");

      // Filter by school_id if available
      if (profile?.school_id) {
        query = query.eq("school_id", profile.school_id);
      }

      const { data, error } = await query;

      if (error) throw error;
      setRooms(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch rooms",
        variant: "destructive",
      });
    }
  };

  const handleAddBlock = async () => {
    try {
      if (!newBlockName.trim()) {
        toast({
          title: "Error",
          description: "Block name cannot be empty",
          variant: "destructive",
        });
        return;
      }

      if (!profile?.school_id) {
        toast({
          title: "Error",
          description:
            "School information is missing. Please complete your profile first.",
          variant: "destructive",
        });
        return;
      }

      const { data, error } = await supabase
        .from("blocks")
        .insert([
          {
            name: newBlockName.trim(),
            school_id: profile.school_id,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      setBlocks([...blocks, data]);
      setNewBlockName("");
      setIsAddingBlock(false);

      toast({
        title: "Success",
        description: "Block added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add block",
        variant: "destructive",
      });
    }
  };

  const handleEditBlock = async (blockId: string) => {
    try {
      if (!newBlockName.trim()) {
        toast({
          title: "Error",
          description: "Block name cannot be empty",
          variant: "destructive",
        });
        return;
      }

      const { error } = await supabase
        .from("blocks")
        .update({ name: newBlockName.trim() })
        .eq("id", blockId);

      if (error) throw error;

      setBlocks(
        blocks.map((block) =>
          block.id === blockId ? { ...block, name: newBlockName.trim() } : block
        )
      );
      setNewBlockName("");
      setIsEditingBlock(null);

      toast({
        title: "Success",
        description: "Block updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update block",
        variant: "destructive",
      });
    }
  };

  const handleDeleteBlock = async (blockId: string) => {
    try {
      // Check if there are any rooms in this block
      const blockRooms = rooms.filter((room) => room.block_id === blockId);
      if (blockRooms.length > 0) {
        toast({
          title: "Error",
          description:
            "Cannot delete block with existing rooms. Please delete the rooms first.",
          variant: "destructive",
        });
        return;
      }

      const { error } = await supabase
        .from("blocks")
        .delete()
        .eq("id", blockId);

      if (error) throw error;

      setBlocks(blocks.filter((block) => block.id !== blockId));
      setIsDeletingBlock(null);

      toast({
        title: "Success",
        description: "Block deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete block",
        variant: "destructive",
      });
    }
  };

  const handleAddRoom = async () => {
    try {
      if (!selectedBlock || !newRoomName.trim()) {
        toast({
          title: "Error",
          description: "Please select a block and enter a room name",
          variant: "destructive",
        });
        return;
      }

      if (!profile?.school_id) {
        toast({
          title: "Error",
          description:
            "School information is missing. Please complete your profile first.",
          variant: "destructive",
        });
        return;
      }

      const { data, error } = await supabase
        .from("rooms")
        .insert([
          {
            name: newRoomName.trim(),
            block_id: selectedBlock,
            teacher_id: teacherId,
            floor: 1,
            capacity: 30,
            school_id: profile.school_id,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      setRooms([...rooms, data]);
      setNewRoomName("");
      setIsAddingRoom(false);

      toast({
        title: "Success",
        description: "Room added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add room",
        variant: "destructive",
      });
    }
  };

  const handleEditRoom = async (roomId: string) => {
    try {
      if (!newRoomName.trim()) {
        toast({
          title: "Error",
          description: "Room name cannot be empty",
          variant: "destructive",
        });
        return;
      }

      const { error } = await supabase
        .from("rooms")
        .update({ name: newRoomName.trim() })
        .eq("id", roomId);

      if (error) throw error;

      setRooms(
        rooms.map((room) =>
          room.id === roomId ? { ...room, name: newRoomName.trim() } : room
        )
      );
      setNewRoomName("");
      setIsEditingRoom(null);

      toast({
        title: "Success",
        description: "Room updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update room",
        variant: "destructive",
      });
    }
  };

  const handleDeleteRoom = async (roomId: string) => {
    try {
      const { error } = await supabase.from("rooms").delete().eq("id", roomId);

      if (error) throw error;

      setRooms(rooms.filter((room) => room.id !== roomId));
      setIsDeletingRoom(null);

      toast({
        title: "Success",
        description: "Room deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete room",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Block & Room Management</CardTitle>
        <CardDescription>Manage your blocks and rooms</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex justify-end">
            <Button onClick={() => setIsAddingBlock(true)} className="gap-2">
              <Plus size={16} /> Add New Block
            </Button>
          </div>

          <Accordion type="single" collapsible className="w-full space-y-4">
            {blocks.map((block) => (
              <AccordionItem
                key={block.id}
                value={block.id}
                className="border rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1">
                    <Building2 size={20} className="text-muted-foreground" />
                    <span className="font-semibold">Block {block.name}</span>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 p-0 ml-2"
                        >
                          <MoreVertical
                            size={16}
                            className="text-muted-foreground hover:text-foreground"
                          />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[160px]">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsEditingBlock(block.id);
                            setNewBlockName(block.name);
                          }}
                        >
                          <Edit2 className="mr-2 h-4 w-4" />
                          Edit Block
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsDeletingBlock(block.id);
                          }}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Block
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <AccordionTrigger className="flex-none hover:no-underline" />
                </div>
                <AccordionContent>
                  <div className="pt-4 space-y-4">
                    <div className="flex justify-end">
                      <Button
                        onClick={() => {
                          setSelectedBlock(block.id);
                          setIsAddingRoom(true);
                        }}
                        variant="outline"
                        className="gap-2"
                      >
                        <Plus size={16} /> Add Room
                      </Button>
                    </div>
                    <div className="grid gap-4">
                      {rooms
                        .filter((room) => room.block_id === block.id)
                        .map((room) => (
                          <div
                            key={room.id}
                            className="flex items-center justify-between p-3 border rounded-md"
                          >
                            <div className="flex items-center gap-2">
                              <DoorOpen
                                size={16}
                                className="text-muted-foreground"
                              />
                              <span>{room.name}</span>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 p-0 ml-2"
                                  >
                                    <MoreVertical
                                      size={16}
                                      className="text-muted-foreground hover:text-foreground"
                                    />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent
                                  align="end"
                                  className="w-[160px]"
                                >
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setIsEditingRoom(room.id);
                                      setNewRoomName(room.name);
                                    }}
                                  >
                                    <Edit2 className="mr-2 h-4 w-4" />
                                    Edit Room
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => setIsDeletingRoom(room.id)}
                                    className="text-destructive"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete Room
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>

        {/* Add Block Dialog */}
        <Dialog open={isAddingBlock} onOpenChange={setIsAddingBlock}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Block</DialogTitle>
              <DialogDescription>
                Enter a name for the new block.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="blockName">Block Name</Label>
                <Input
                  id="blockName"
                  placeholder="Enter block name"
                  value={newBlockName}
                  onChange={(e) => setNewBlockName(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddingBlock(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddBlock}>Add Block</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Block Dialog */}
        <Dialog
          open={!!isEditingBlock}
          onOpenChange={() => setIsEditingBlock(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Block</DialogTitle>
              <DialogDescription>Update the block name.</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="editBlockName">Block Name</Label>
                <Input
                  id="editBlockName"
                  placeholder="Enter block name"
                  value={newBlockName}
                  onChange={(e) => setNewBlockName(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditingBlock(null)}>
                Cancel
              </Button>
              <Button
                onClick={() =>
                  isEditingBlock && handleEditBlock(isEditingBlock)
                }
              >
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Block Confirmation */}
        <AlertDialog
          open={!!isDeletingBlock}
          onOpenChange={() => setIsDeletingBlock(null)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                block.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setIsDeletingBlock(null)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() =>
                  isDeletingBlock && handleDeleteBlock(isDeletingBlock)
                }
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Add Room Dialog */}
        <Dialog open={isAddingRoom} onOpenChange={setIsAddingRoom}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Room</DialogTitle>
              <DialogDescription>
                Enter a name for the new room.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="roomName">Room Name</Label>
                <Input
                  id="roomName"
                  placeholder="Enter room name"
                  value={newRoomName}
                  onChange={(e) => setNewRoomName(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddingRoom(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddRoom}>Add Room</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Room Dialog */}
        <Dialog
          open={!!isEditingRoom}
          onOpenChange={() => setIsEditingRoom(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Room</DialogTitle>
              <DialogDescription>Update the room name.</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="editRoomName">Room Name</Label>
                <Input
                  id="editRoomName"
                  placeholder="Enter room name"
                  value={newRoomName}
                  onChange={(e) => setNewRoomName(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditingRoom(null)}>
                Cancel
              </Button>
              <Button
                onClick={() => isEditingRoom && handleEditRoom(isEditingRoom)}
              >
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Room Confirmation */}
        <AlertDialog
          open={!!isDeletingRoom}
          onOpenChange={() => setIsDeletingRoom(null)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                room.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setIsDeletingRoom(null)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() =>
                  isDeletingRoom && handleDeleteRoom(isDeletingRoom)
                }
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
}
