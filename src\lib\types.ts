/**
 * Main Types Export
 * Re-exports all type definitions from organized modules
 * @deprecated Use specific type modules instead: auth.ts, school.ts, attendance.ts, ui.ts, api.ts
 */

// Re-export all types from organized modules
export * from './types/auth';
export * from './types/school';
export * from './types/attendance';
export * from './types/ui';
export * from './types/api';
export * from './types/parent-contact';

// Legacy exports for backward compatibility
// These will be removed in a future version
export type {
  User,
  Student,
  Teacher,
  Admin,
  School,
  Block,
  Room,
  AttendanceRecord,
  Course,
  Excuse,
  ThemeColors,
  ThemeSettings,
  FraudCase
} from './types/auth';

export type {
  School as LegacySchool,
  Block as LegacyBlock,
  Room as LegacyRoom
} from './types/school';

export type {
  AttendanceRecord as LegacyAttendanceRecord,
  Course as LegacyCourse,
  Excuse as LegacyExcuse,
  FraudCase as LegacyFraudCase
} from './types/attendance';

export type {
  ThemeColors as LegacyThemeColors,
  ThemeSettings as LegacyThemeSettings
} from './types/ui';

// Note: All type definitions have been moved to organized modules
// This file now serves as a central export point for backward compatibility
