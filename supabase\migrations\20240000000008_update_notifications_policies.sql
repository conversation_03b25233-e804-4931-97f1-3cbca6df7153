-- Add policy for students to update their own notifications
CREATE POLICY "Students can update their own notifications"
  ON public.notifications
  FOR UPDATE
  TO authenticated
  USING (student_id::text = (
    SELECT id::text FROM profiles WHERE user_id::text = auth.uid()::text
  ))
  WITH CHECK (student_id::text = (
    SELECT id::text FROM profiles WHERE user_id::text = auth.uid()::text
  ));

-- Add policy for teachers and admins to update any notifications
CREATE POLICY "Teachers and admins can update any notifications"
  ON public.notifications
  FOR UPDATE
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM profiles 
    WHERE user_id::text = auth.uid()::text 
    AND role IN ('teacher', 'admin')
  ));

-- Create function to automatically archive read notifications
CREATE OR REPLACE FUNCTION handle_read_notifications()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.read = true THEN
    -- Instead of deleting, we'll update the notification to be read
    NEW.updated_at = now();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for handling read notifications
CREATE TRIGGER read_notifications_trigger
  BEFORE UPDATE OF read
  ON notifications
  FOR EACH ROW
  EXECUTE FUNCTION handle_read_notifications();

-- Grant necessary permissions
GRANT UPDATE ON public.notifications TO authenticated; 