import { useState, useEffect } from 'react';

interface GeoLocationState {
  country: string | null;
  loading: boolean;
  error: string | null;
}

export function useGeoLocation() {
  const [state, setState] = useState<GeoLocationState>({
    country: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    const fetchCountry = async () => {
      try {
        // Try to get country from browser's language setting first
        const browserLanguage = navigator.language || (navigator as any).userLanguage;
        let countryFromBrowser = null;
        
        if (browserLanguage && browserLanguage.includes('-')) {
          countryFromBrowser = browserLanguage.split('-')[1];
        }
        
        if (countryFromBrowser && countryFromBrowser.length === 2) {
          setState({
            country: countryFromBrowser,
            loading: false,
            error: null,
          });
          return;
        }
        
        // If browser language doesn't provide country, try IP-based geolocation
        const response = await fetch('https://ipapi.co/json/');
        if (!response.ok) {
          throw new Error('Failed to fetch location data');
        }
        
        const data = await response.json();
        setState({
          country: data.country_code,
          loading: false,
          error: null,
        });
      } catch (error) {
        console.error('Error fetching geolocation:', error);
        // Fallback to US if geolocation fails
        setState({
          country: 'US',
          loading: false,
          error: 'Failed to detect location, using default',
        });
      }
    };

    fetchCountry();
  }, []);

  return state;
}
