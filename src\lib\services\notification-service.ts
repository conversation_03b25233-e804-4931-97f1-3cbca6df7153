import { supabase } from "@/lib/supabase";
import { ParentContact } from "@/lib/types/parent-contact";
import {
  sendEmailWithSendGrid,
  sendSMSWithTwilio,
  getEmailServiceConfig,
  getSMSServiceConfig,
} from "./external-notification-services";

interface NotificationData {
  subject: string;
  message: string;
  studentName: string;
  excuseId: string;
  startDate: string;
  endDate: string;
  reason: string;
  schoolName?: string;
  contactEmail?: string;
  schoolPolicy?: string;
  templateType?: 'new' | 'approved' | 'rejected'; // Add template type
}

// Send email using real email service
const sendEmail = async (
  to: string,
  subject: string,
  message: string
): Promise<boolean> => {
  console.log(`[EMAIL SERVICE] Sending email to ${to}`);

  // Check if email service is configured
  const emailConfig = await getEmailServiceConfig();
  if (!emailConfig) {
    console.warn("Email service not configured. Email will not be sent.");
    return false;
  }

  // Send email using SendGrid
  return await sendEmailWithSendGrid(to, subject, message);
};

// Send SMS using real SMS service
const sendSMS = async (to: string, message: string): Promise<boolean> => {
  console.log(`[SMS SERVICE] Sending SMS to ${to}`);

  // Check if SMS service is configured
  const smsConfig = await getSMSServiceConfig();
  if (!smsConfig) {
    console.warn("SMS service not configured. SMS will not be sent.");
    return false;
  }

  // Send SMS using Twilio
  return await sendSMSWithTwilio(to, message);
};

// Get school information for enhanced templates
const getSchoolInfo = async (): Promise<{ schoolName?: string; contactEmail?: string; schoolPolicy?: string }> => {
  try {
    // Get notification settings which now include template variables
    const { data: notificationData, error: notificationError } = await supabase
      .from("system_settings")
      .select("setting_value")
      .eq("setting_name", "parent_notification_settings")
      .single();

    if (!notificationError && notificationData) {
      const settings = JSON.parse(notificationData.setting_value);
      return {
        schoolName: settings.school_name || "Your School Name",
        contactEmail: settings.contact_email || "<EMAIL>",
        schoolPolicy: settings.school_policy || "Please contact the school office for attendance policy information."
      };
    }

    // Fallback values
    return {
      schoolName: "Campus Guardian School",
      contactEmail: "<EMAIL>",
      schoolPolicy: "Please contact the school office for attendance policy information."
    };
  } catch (error) {
    console.error("Error fetching school info:", error);
    return {
      schoolName: "Campus Guardian School",
      contactEmail: "<EMAIL>",
      schoolPolicy: "Please contact the school office for attendance policy information."
    };
  }
};

// Get admin's language preference
const getAdminLanguage = async (): Promise<string> => {
  try {
    // Get the current user's profile to determine language
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return 'en';

    const { data: profile, error } = await supabase
      .from("profiles")
      .select("preferred_language")
      .eq("user_id", user.id)
      .single();

    if (!error && profile?.preferred_language) {
      return profile.preferred_language;
    }

    return 'en'; // Default to English
  } catch (error) {
    console.error("Error fetching admin language:", error);
    return 'en';
  }
};

// Get localized notification templates
const getNotificationTemplates = async (templateType: 'new' | 'approved' | 'rejected', language?: string) => {
  try {
    // Get admin's language if not provided
    const adminLanguage = language || await getAdminLanguage();

    // Try to get custom templates from system settings first
    const { data: settingsData, error } = await supabase
      .from("system_settings")
      .select("setting_value")
      .eq("setting_name", "parent_notification_settings")
      .single();

    if (!error && settingsData) {
      const settings = JSON.parse(settingsData.setting_value);
      // Check if language-specific templates exist
      const langKey = adminLanguage === 'tr' ? '_tr' : '';
      const emailKey = `email_template_${templateType}${langKey}`;
      const smsKey = `sms_template_${templateType}${langKey}`;

      if (settings[emailKey] && settings[smsKey]) {
        return {
          emailTemplate: settings[emailKey],
          smsTemplate: settings[smsKey]
        };
      }
    }

    // Fallback to localized default templates
    const fallbackTemplates = adminLanguage === 'tr' ? {
      new: {
        email: "Sayın Veli/Vasi,\n\nÇocuğunuz {{studentName}}'in {{schoolName}} okulundan devamsızlık talebi gönderdiğini bildirmek isteriz.\n\nTalep Detayları:\n- Başlangıç Tarihi: {{startDate}}\n- Bitiş Tarihi: {{endDate}}\n- Sebep: {{reason}}\n\nBu talep şu anda okul yönetiminin onayını beklemektedir. Talep incelendikten sonra bilgilendirileceksiniz.\n\nSorularınız için lütfen {{contactEmail}} adresinden bizimle iletişime geçin.\n\n{{schoolPolicy}}\n\nTeşekkürler,\n{{schoolName}} Devam Takip Sistemi",
        sms: "BİLDİRİM: {{studentName}} {{startDate}} - {{endDate}} tarihleri için devamsızlık talebinde bulundu. Sebep: {{reason}}. Sorular için {{contactEmail}}"
      },
      approved: {
        email: "Sayın Veli/Vasi,\n\nÇocuğunuzun devamsızlık talebi ONAYLANDI.\n\nÖğrenci: {{studentName}}\nBaşlangıç Tarihi: {{startDate}}\nBitiş Tarihi: {{endDate}}\nSebep: {{reason}}\n\nSorularınız için {{contactEmail}} adresinden iletişime geçin.\n\nTeşekkürler,\n{{schoolName}}",
        sms: "ONAYLANDI: {{studentName}}'in {{startDate}} - {{endDate}} devamsızlık talebi {{schoolName}} tarafından onaylandı."
      },
      rejected: {
        email: "Sayın Veli/Vasi,\n\nÇocuğunuzun devamsızlık talebi REDDEDİLDİ.\n\nÖğrenci: {{studentName}}\nBaşlangıç Tarihi: {{startDate}}\nBitiş Tarihi: {{endDate}}\nSebep: {{reason}}\n\nSorularınız için {{contactEmail}} adresinden iletişime geçin.\n\nTeşekkürler,\n{{schoolName}}",
        sms: "REDDEDİLDİ: {{studentName}}'in {{startDate}} - {{endDate}} devamsızlık talebi reddedildi. İletişim: {{contactEmail}}"
      }
    } : {
      new: {
        email: "Dear Parent/Guardian,\n\nThis is to inform you that your child, {{studentName}}, has submitted a request for absence from {{schoolName}}.\n\nRequest Details:\n- Start Date: {{startDate}}\n- End Date: {{endDate}}\n- Reason: {{reason}}\n\nThis request is currently pending approval from school administration. You will be notified once the request has been reviewed.\n\nFor questions, please contact us at {{contactEmail}}.\n\n{{schoolPolicy}}\n\nThank you,\n{{schoolName}} Attendance System",
        sms: "NOTICE: {{studentName}} has requested absence from {{startDate}} to {{endDate}}. Reason: {{reason}}. Contact {{contactEmail}} for questions."
      },
      approved: {
        email: "Dear Parent/Guardian,\n\nYour child's absence request has been APPROVED.\n\nStudent: {{studentName}}\nStart Date: {{startDate}}\nEnd Date: {{endDate}}\nReason: {{reason}}\n\nFor questions, contact {{contactEmail}}.\n\nThank you,\n{{schoolName}}",
        sms: "APPROVED: {{studentName}}'s absence request for {{startDate}} to {{endDate}} has been approved."
      },
      rejected: {
        email: "Dear Parent/Guardian,\n\nYour child's absence request has been REJECTED.\n\nStudent: {{studentName}}\nStart Date: {{startDate}}\nEnd Date: {{endDate}}\nReason: {{reason}}\n\nFor questions, contact {{contactEmail}}.\n\nThank you,\n{{schoolName}}",
        sms: "REJECTED: {{studentName}}'s absence request for {{startDate}} to {{endDate}} has been rejected. Contact school."
      }
    };

    return {
      emailTemplate: fallbackTemplates[templateType].email,
      smsTemplate: fallbackTemplates[templateType].sms
    };
  } catch (error) {
    console.error("Error fetching notification templates:", error);
    // Return basic fallback in English
    return {
      emailTemplate: "Dear Parent/Guardian,\n\nYour child {{studentName}} has an attendance update.\n\nThank you,\n{{schoolName}}",
      smsTemplate: "{{studentName}} attendance update. Contact school for details."
    };
  }
};

// Enhanced message formatting with template variables and localization
const formatMessage = async (data: NotificationData, isEmail: boolean, templateType: 'new' | 'approved' | 'rejected' = 'new'): Promise<string> => {
  // Get admin's language for localization
  const adminLanguage = await getAdminLanguage();

  const dateOptions: Intl.DateTimeFormatOptions = {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  };

  // Format dates according to admin's language
  const locale = adminLanguage === 'tr' ? 'tr-TR' : 'en-US';
  const startDate = new Date(data.startDate).toLocaleDateString(locale, dateOptions);
  const endDate = new Date(data.endDate).toLocaleDateString(locale, dateOptions);

  // Get school info and localized templates
  const schoolInfo = await getSchoolInfo();
  const templates = await getNotificationTemplates(templateType, adminLanguage);

  const template = isEmail ? templates.emailTemplate : templates.smsTemplate;

  return template
    .replace(/{{studentName}}/g, data.studentName)
    .replace(/{{startDate}}/g, startDate)
    .replace(/{{endDate}}/g, endDate)
    .replace(/{{reason}}/g, data.reason)
    .replace(/{{schoolName}}/g, data.schoolName || schoolInfo.schoolName || (adminLanguage === 'tr' ? "Okul Adı" : "Your School Name"))
    .replace(/{{contactEmail}}/g, data.contactEmail || schoolInfo.contactEmail || "<EMAIL>")
    .replace(/{{schoolPolicy}}/g, data.schoolPolicy || schoolInfo.schoolPolicy || (adminLanguage === 'tr' ? "Devam politikası bilgileri için lütfen okul idaresi ile iletişime geçin." : "Please contact the school office for attendance policy information."));
};

// Send notifications to parents based on their preferences
export const notifyParents = async (
  studentId: string,
  notificationData: NotificationData
): Promise<{ success: boolean; message: string }> => {
  try {
    // Fetch parent contacts for the student
    const { data: parentContacts, error } = await supabase
      .from("parent_contacts")
      .select("*")
      .eq("student_id", studentId)
      .eq("notifications_enabled", true);

    if (error) throw error;

    if (!parentContacts || parentContacts.length === 0) {
      console.log(
        `No parent contacts found for student ${studentId} or notifications disabled`
      );
      return {
        success: false,
        message: "No parent contacts found or notifications disabled",
      };
    }

    const results = await Promise.all(
      parentContacts.map(async (contact: ParentContact) => {
        const { notification_method, email, phone, parent_name } = contact;

        // Skip if notification method is none
        if (notification_method === "none") {
          return {
            success: true,
            message: "Notifications disabled for this contact",
          };
        }

        // Prepare personalized message with correct template type
        const templateType = notificationData.templateType || 'new';
        const emailMessage = await formatMessage(notificationData, true, templateType);
        const smsMessage = await formatMessage(notificationData, false, templateType);

        // Send notifications based on preference
        if (notification_method === "email" || notification_method === "both") {
          if (email) {
            try {
              const success = await sendEmail(
                email,
                notificationData.subject,
                emailMessage
              );

              // Log the notification
              await logNotification(
                studentId,
                notificationData.excuseId,
                "email",
                email,
                success
              );
            } catch (error: any) {
              console.error("Error sending email notification:", error);
              await logNotification(
                studentId,
                notificationData.excuseId,
                "email",
                email,
                false,
                error.message
              );
            }
          }
        }

        if (notification_method === "sms" || notification_method === "both") {
          if (phone) {
            try {
              const success = await sendSMS(phone, smsMessage);

              // Log the notification
              await logNotification(
                studentId,
                notificationData.excuseId,
                "sms",
                phone,
                success
              );
            } catch (error: any) {
              console.error("Error sending SMS notification:", error);
              await logNotification(
                studentId,
                notificationData.excuseId,
                "sms",
                phone,
                false,
                error.message
              );
            }
          }
        }

        return {
          success: true,
          message: `Notifications sent to ${parent_name}`,
        };
      })
    );

    // Check if all notifications were sent successfully
    const allSuccessful = results.every((result) => result.success);

    return {
      success: allSuccessful,
      message: allSuccessful
        ? "All notifications sent successfully"
        : "Some notifications failed to send",
    };
  } catch (error: any) {
    console.error("Error sending parent notifications:", error);
    return { success: false, message: `Error: ${error.message}` };
  }
};

// Log notification events for auditing
export const logNotification = async (
  studentId: string,
  excuseId: string,
  notificationType: "email" | "sms",
  recipient: string,
  success: boolean,
  errorMessage?: string
): Promise<void> => {
  try {
    await supabase.from("notification_logs").insert({
      student_id: studentId,
      excuse_id: excuseId,
      notification_type: notificationType,
      recipient,
      success,
      error_message: errorMessage,
      created_at: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error logging notification:", error);
  }
};
