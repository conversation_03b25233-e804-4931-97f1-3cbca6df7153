/**
 * User Service
 * Enhanced user management with proper error handling and validation
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../database.types';
import { BaseService } from '../core/base-service';
import { User, UserRole } from '../types/auth';
import { APIResponse, QueryParams } from '../types/api';
import { VALIDATION_RULES } from '../constants/validation';

export class UserService extends BaseService {
  constructor(supabase: SupabaseClient<Database>) {
    super(supabase, 'profiles');
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<APIResponse<User>> {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      
      if (!user) {
        return {
          success: false,
          status: 401,
          error: 'User not authenticated',
          data: null,
        };
      }

      const { data: profile, error } = await this.supabase
        .from('profiles')
        .select(`
          *,
          schools:school_id (
            id,
            name,
            logo_url,
            primary_color,
            secondary_color
          )
        `)
        .eq('user_id', user.id)
        .single();

      if (error) {
        return this.handleError(error);
      }

      return this.createSuccessResponse(profile as User);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, data: Partial<User>): Promise<APIResponse<User>> {
    try {
      // Validate required fields
      const errors = this.validateProfileData(data);
      if (errors.length > 0) {
        return {
          success: false,
          status: 400,
          error: errors.join(', '),
          data: null,
        };
      }

      // Check permission
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user || user.id !== userId) {
        const isSystemAdmin = await this.isSystemAdmin();
        if (!isSystemAdmin) {
          return {
            success: false,
            status: 403,
            error: 'Insufficient permissions',
            data: null,
          };
        }
      }

      // Get old values for audit
      const { data: oldProfile } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      // Sanitize and update data
      const sanitizedData = this.sanitizeData(data);
      sanitizedData.updated_at = new Date().toISOString();

      const { data: updatedProfile, error } = await this.supabase
        .from('profiles')
        .update(sanitizedData)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        return this.handleError(error);
      }

      // Log audit trail
      await this.logAudit('update', 'profile', userId, oldProfile, updatedProfile);

      return this.createSuccessResponse(updatedProfile as User, 'Profile updated successfully');
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Get users by school (admin only)
   */
  async getUsersBySchool(params: QueryParams): Promise<APIResponse<User[]>> {
    try {
      // Check if user is admin or system admin
      const userRole = await this.getUserRole();
      if (!userRole || !['admin', 'system_admin'].includes(userRole)) {
        return {
          success: false,
          status: 403,
          error: 'Insufficient permissions',
          data: null,
        };
      }

      const schoolId = await this.getCurrentSchoolId();
      if (!schoolId && userRole !== 'system_admin') {
        return {
          success: false,
          status: 400,
          error: 'School ID required',
          data: null,
        };
      }

      let query = this.supabase
        .from('profiles')
        .select(`
          *,
          schools:school_id (
            id,
            name
          )
        `);

      // Apply school filter for non-system admins
      if (userRole !== 'system_admin' && schoolId) {
        query = this.applySchoolFilter(query, schoolId);
      }

      // Apply search, sorting, and pagination
      query = this.applySearch(query, params, ['name', 'email', 'student_id', 'teacher_id']);
      query = this.applySorting(query, params);
      query = this.applyPagination(query, params);

      const { data: users, error, count } = await query;

      if (error) {
        return this.handleError(error);
      }

      return {
        success: true,
        status: 200,
        data: users as User[],
        pagination: {
          page: params.page || 1,
          pageSize: params.pageSize || 20,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / (params.pageSize || 20)),
          hasNext: ((params.page || 1) * (params.pageSize || 20)) < (count || 0),
          hasPrev: (params.page || 1) > 1,
        },
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Create new user (admin only)
   */
  async createUser(userData: Partial<User>): Promise<APIResponse<User>> {
    try {
      // Check permissions
      const userRole = await this.getUserRole();
      if (!userRole || !['admin', 'system_admin'].includes(userRole)) {
        return {
          success: false,
          status: 403,
          error: 'Insufficient permissions',
          data: null,
        };
      }

      // Validate required fields
      const requiredFields = ['name', 'email', 'role'];
      const errors = this.validateRequired(userData, requiredFields);
      
      // Additional validation
      const validationErrors = this.validateProfileData(userData);
      errors.push(...validationErrors);

      if (errors.length > 0) {
        return {
          success: false,
          status: 400,
          error: errors.join(', '),
          data: null,
        };
      }

      // Set school_id for non-system admins
      if (userRole !== 'system_admin') {
        const schoolId = await this.getCurrentSchoolId();
        userData.school_id = schoolId;
      }

      // Create auth user first
      const { data: authUser, error: authError } = await this.supabase.auth.admin.createUser({
        email: userData.email!,
        password: this.generateTemporaryPassword(),
        email_confirm: true,
      });

      if (authError) {
        return this.handleError(authError);
      }

      // Create profile
      const profileData = {
        ...this.sanitizeData(userData),
        user_id: authUser.user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data: profile, error: profileError } = await this.supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single();

      if (profileError) {
        // Cleanup auth user if profile creation fails
        await this.supabase.auth.admin.deleteUser(authUser.user.id);
        return this.handleError(profileError);
      }

      // Log audit trail
      await this.logAudit('create', 'user', authUser.user.id, null, profile);

      return this.createSuccessResponse(profile as User, 'User created successfully');
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Delete user (admin only)
   */
  async deleteUser(userId: string): Promise<APIResponse<void>> {
    try {
      // Check permissions
      const userRole = await this.getUserRole();
      if (!userRole || !['admin', 'system_admin'].includes(userRole)) {
        return {
          success: false,
          status: 403,
          error: 'Insufficient permissions',
          data: null,
        };
      }

      // Get user data for audit
      const { data: userData } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      // Check school permission for non-system admins
      if (userRole !== 'system_admin' && userData?.school_id) {
        const hasPermission = await this.checkSchoolPermission(userData.school_id);
        if (!hasPermission) {
          return {
            success: false,
            status: 403,
            error: 'Cannot delete user from different school',
            data: null,
          };
        }
      }

      // Soft delete profile
      const { error: profileError } = await this.supabase
        .from('profiles')
        .update({
          is_deleted: true,
          deleted_at: new Date().toISOString(),
        })
        .eq('user_id', userId);

      if (profileError) {
        return this.handleError(profileError);
      }

      // Delete auth user
      const { error: authError } = await this.supabase.auth.admin.deleteUser(userId);
      if (authError) {
        console.error('Failed to delete auth user:', authError);
      }

      // Log audit trail
      await this.logAudit('delete', 'user', userId, userData, null);

      return this.createSuccessResponse(undefined, 'User deleted successfully');
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Validate profile data
   */
  private validateProfileData(data: Partial<User>): string[] {
    const errors: string[] = [];

    if (data.email && !VALIDATION_RULES.EMAIL.PATTERN.test(data.email)) {
      errors.push('Invalid email format');
    }

    if (data.name && data.name.length < VALIDATION_RULES.NAME.MIN_LENGTH) {
      errors.push(`Name must be at least ${VALIDATION_RULES.NAME.MIN_LENGTH} characters`);
    }

    if (data.phone && !VALIDATION_RULES.PHONE.PATTERN.test(data.phone)) {
      errors.push('Invalid phone number format');
    }

    if (data.studentId && !VALIDATION_RULES.STUDENT_ID.PATTERN.test(data.studentId)) {
      errors.push('Invalid student ID format');
    }

    return errors;
  }

  /**
   * Generate temporary password for new users
   */
  private generateTemporaryPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@#$%';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }
}
