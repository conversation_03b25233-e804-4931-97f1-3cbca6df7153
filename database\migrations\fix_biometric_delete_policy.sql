-- Fix for biometric credentials DELETE policy
-- Run this in your Supabase SQL Editor

-- Add missing DELETE policy for biometric_credentials table
-- This allows users to delete their own biometric credentials
CREATE POLICY "Users can delete their own credentials"
    ON public.biometric_credentials
    FOR DELETE
    USING (auth.uid() = user_id);

-- Verify the policy was created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'biometric_credentials';
