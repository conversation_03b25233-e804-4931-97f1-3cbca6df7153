"use client";

import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Trash2,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  User,
  MessageSquare,
  MapPin,
  Calendar,
  FileText,
} from "lucide-react";

interface BiometricDeletionRequest {
  id: string;
  student_id: string;
  student_name: string;
  student_email: string;
  school_id: string;
  block_name?: string;
  room_name?: string;
  reason?: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  approved_at?: string;
  rejected_at?: string;
  approved_by?: string;
  rejected_by?: string;
  admin_notes?: string;
}

export default function BiometricDeletionRequests() {
  const [requests, setRequests] = useState<BiometricDeletionRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<BiometricDeletionRequest | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [processing, setProcessing] = useState(false);
  const [clearing, setClearing] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation("translation", { keyPrefix: "admin.biometricDeletionRequests" });

  const fetchRequests = async () => {
    try {
      setLoading(true);

      // Get the current user's profile to get their school_id
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("school_id")
        .eq("user_id", user?.id)
        .single();

      if (profileError) throw profileError;

      // Fetch deletion requests for the admin's school only
      const { data, error } = await supabase
        .from("biometric_deletion_requests")
        .select("*")
        .eq("school_id", profile.school_id)
        .order("created_at", { ascending: false });

      if (error) throw error;

      setRequests(data || []);
    } catch (error) {
      console.error("Error fetching biometric deletion requests:", error);
      toast({
        title: t("errorLoading"),
        description: t("errorLoadingMessage"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchRequests();
    }
  }, [user]);

  const pendingRequests = requests.filter((req) => req.status === "pending");
  const processedRequests = requests.filter((req) => req.status !== "pending");

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="text-yellow-600 border-yellow-300">
            {t("pending")}
          </Badge>
        );
      case "approved":
        return (
          <Badge variant="outline" className="text-green-600 border-green-300">
            {t("approved")}
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="text-red-600 border-red-300">
            {t("rejected")}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleApproveRequest = async () => {
    if (!selectedRequest) return;

    try {
      setProcessing(true);

      // Get current user ID for approved_by field
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      // Update the request status directly - this will trigger our notification function
      const { error } = await supabase
        .from("biometric_deletion_requests")
        .update({
          status: "approved",
          admin_notes: adminNotes || null,
          approved_by: user.id,
          approved_at: new Date().toISOString(),
        })
        .eq("id", selectedRequest.id);

      if (error) throw error;

      toast({
        title: t("requestApproved"),
        description: t("credentialsDeleted"),
      });

      // Update local state
      setRequests(prev =>
        prev.map(req =>
          req.id === selectedRequest.id
            ? { ...req, status: 'approved', admin_notes: adminNotes, approved_by: user.id, approved_at: new Date().toISOString() }
            : req
        )
      );

      setShowApproveDialog(false);
      setSelectedRequest(null);
      setAdminNotes("");
    } catch (error) {
      console.error("Error approving request:", error);
      toast({
        title: t("errorApproving"),
        description: t("errorApprovingMessage"),
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleRejectRequest = async () => {
    if (!selectedRequest) return;

    try {
      setProcessing(true);

      // Get current user ID for rejected_by field
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error("User not authenticated");

      // Update the request status directly - this will trigger our notification function
      const { error } = await supabase
        .from("biometric_deletion_requests")
        .update({
          status: "rejected",
          admin_notes: adminNotes || null,
          rejected_by: user.id,
          rejected_at: new Date().toISOString(),
        })
        .eq("id", selectedRequest.id);

      if (error) throw error;

      toast({
        title: t("requestRejected"),
        description: t("studentNotified"),
      });

      // Update local state
      setRequests(prev =>
        prev.map(req =>
          req.id === selectedRequest.id
            ? { ...req, status: 'rejected', admin_notes: adminNotes, rejected_by: user.id, rejected_at: new Date().toISOString() }
            : req
        )
      );

      setShowRejectDialog(false);
      setSelectedRequest(null);
      setAdminNotes("");
    } catch (error) {
      console.error("Error rejecting request:", error);
      toast({
        title: t("errorRejecting"),
        description: t("errorRejectingMessage"),
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleClearProcessedRequests = async () => {
    try {
      setClearing(true);

      const { data, error } = await supabase.rpc("clear_processed_biometric_deletion_requests");

      if (error) throw error;

      toast({
        title: t("processedRequestsCleared"),
        description: t("processedRequestsClearedMessage", { count: data || 0 }),
      });

      // Refresh the requests list
      fetchRequests();
      setShowClearDialog(false);
    } catch (error) {
      console.error("Error clearing processed requests:", error);
      toast({
        title: t("errorClearing"),
        description: t("errorClearingMessage"),
        variant: "destructive",
      });
    } finally {
      setClearing(false);
    }
  };

  const handleClearSingleRequest = async (requestId: string) => {
    try {
      setProcessing(true);

      const { error } = await supabase.rpc("clear_biometric_deletion_request", {
        request_id: requestId,
      });

      if (error) throw error;

      toast({
        title: t("requestCleared"),
        description: t("requestClearedMessage"),
      });

      // Remove the request from local state
      setRequests(prev => prev.filter(req => req.id !== requestId));
    } catch (error) {
      console.error("Error clearing request:", error);
      toast({
        title: t("errorClearing"),
        description: t("errorClearingMessage"),
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t("loading")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Trash2 className="h-5 w-5 text-red-600" />
          <div>
            <h2 className="text-lg font-semibold">{t("title")}</h2>
            <p className="text-sm text-muted-foreground">
              {t("description")}
            </p>
          </div>
        </div>

        {processedRequests.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowClearDialog(true)}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {t("clearProcessed")} ({processedRequests.length})
          </Button>
        )}
      </div>

      {/* Pending Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-yellow-600" />
            {t("pendingRequests")} ({pendingRequests.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {pendingRequests.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">
                {t("noPendingRequests")}
              </h3>
              <p className="text-muted-foreground">
                {t("noPendingRequestsMessage")}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("student")}</TableHead>
                    <TableHead>{t("location")}</TableHead>
                    <TableHead>{t("requestDate")}</TableHead>
                    <TableHead>{t("actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.student_name}</div>
                          <div className="text-sm text-muted-foreground">{request.student_email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {request.block_name && request.room_name
                            ? `${request.block_name} - ${request.room_name}`
                            : request.block_name || request.room_name || "-"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{formatDate(request.created_at)}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedRequest(request);
                              setShowDetailsDialog(true);
                            }}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            {t("view")}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-600 border-green-300 hover:bg-green-50"
                            onClick={() => {
                              setSelectedRequest(request);
                              setAdminNotes("");
                              setShowApproveDialog(true);
                            }}
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            {t("approve")}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-300 hover:bg-red-50"
                            onClick={() => {
                              setSelectedRequest(request);
                              setAdminNotes("");
                              setShowRejectDialog(true);
                            }}
                          >
                            <XCircle className="h-3 w-3 mr-1" />
                            {t("reject")}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Processed Requests */}
      {processedRequests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              {t("processedRequests")} ({processedRequests.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("student")}</TableHead>
                    <TableHead>{t("status")}</TableHead>
                    <TableHead>{t("processedDate")}</TableHead>
                    <TableHead>{t("actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {processedRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.student_name}</div>
                          <div className="text-sm text-muted-foreground">{request.student_email}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(request.status)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatDate(request.approved_at || request.rejected_at || request.updated_at)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedRequest(request);
                              setShowDetailsDialog(true);
                            }}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            {t("view")}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleClearSingleRequest(request.id)}
                            disabled={processing}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            {processing ? t("clearing") : t("clear")}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Request Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              {t("requestDetails")}
            </DialogTitle>
            <DialogDescription>
              {t("requestDetailsDescription")}
            </DialogDescription>
          </DialogHeader>

          {selectedRequest && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("studentName")}</span>
                  </div>
                  <p className="text-sm pl-6">{selectedRequest.student_name}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("studentEmail")}</span>
                  </div>
                  <p className="text-sm pl-6">{selectedRequest.student_email}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("location")}</span>
                  </div>
                  <p className="text-sm pl-6">
                    {selectedRequest.block_name && selectedRequest.room_name
                      ? `${selectedRequest.block_name} - ${selectedRequest.room_name}`
                      : selectedRequest.block_name || selectedRequest.room_name || "-"}
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("requestDate")}</span>
                  </div>
                  <p className="text-sm pl-6">{formatDate(selectedRequest.created_at)}</p>
                </div>
              </div>

              {selectedRequest.reason && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("requestReason")}</span>
                  </div>
                  <p className="text-sm pl-6 bg-muted p-3 rounded-md">{selectedRequest.reason}</p>
                </div>
              )}

              <div className="space-y-2">
                <span className="text-sm font-medium">{t("status")}</span>
                <div className="pl-6">{getStatusBadge(selectedRequest.status)}</div>
              </div>

              {selectedRequest.admin_notes && (
                <div className="space-y-2">
                  <span className="text-sm font-medium">{t("adminNotes")}</span>
                  <p className="text-sm pl-6 bg-muted p-3 rounded-md">{selectedRequest.admin_notes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Approve Request Dialog */}
      <AlertDialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              {t("approveRequest")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("approveRequestDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4">
            <Textarea
              placeholder={t("adminNotesPlaceholder")}
              value={adminNotes}
              onChange={(e) => setAdminNotes(e.target.value)}
              rows={3}
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleApproveRequest}
              disabled={processing}
              className="bg-green-600 hover:bg-green-700"
            >
              {processing ? t("approving") : t("approve")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reject Request Dialog */}
      <AlertDialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              {t("rejectRequest")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("rejectRequestDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4">
            <Textarea
              placeholder={t("adminNotesPlaceholder")}
              value={adminNotes}
              onChange={(e) => setAdminNotes(e.target.value)}
              rows={3}
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRejectRequest}
              disabled={processing}
              className="bg-red-600 hover:bg-red-700"
            >
              {processing ? t("rejecting") : t("reject")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Clear Processed Requests Dialog */}
      <AlertDialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              {t("clearProcessedRequests")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("clearProcessedRequestsDescription", { count: processedRequests.length })}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleClearProcessedRequests}
              disabled={clearing}
              className="bg-red-600 hover:bg-red-700"
            >
              {clearing ? t("clearing") : t("clearAll")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
