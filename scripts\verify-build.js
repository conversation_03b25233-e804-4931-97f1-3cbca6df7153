#!/usr/bin/env node

/**
 * Build Verification Script
 * Checks if the build is ready for deployment
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🔍 Verifying build for deployment...\n');

// Check if dist folder exists
const distPath = path.join(projectRoot, 'dist');
if (!fs.existsSync(distPath)) {
  console.error('❌ dist folder not found. Run "npm run build" first.');
  process.exit(1);
}

// Check if index.html exists
const indexPath = path.join(distPath, 'index.html');
if (!fs.existsSync(indexPath)) {
  console.error('❌ index.html not found in dist folder.');
  process.exit(1);
}

// Check if vercel.json exists
const vercelConfigPath = path.join(projectRoot, 'vercel.json');
if (!fs.existsSync(vercelConfigPath)) {
  console.error('❌ vercel.json not found. This is required for SPA routing.');
  process.exit(1);
}

// Check if _redirects exists
const redirectsPath = path.join(projectRoot, 'public', '_redirects');
if (!fs.existsSync(redirectsPath)) {
  console.warn('⚠️  public/_redirects not found. This is a backup for SPA routing.');
}

// Check environment variables
const envExample = path.join(projectRoot, '.env.example');
if (fs.existsSync(envExample)) {
  const envContent = fs.readFileSync(envExample, 'utf8');
  const requiredVars = envContent.match(/VITE_\w+/g) || [];
  
  console.log('📋 Required environment variables for Vercel:');
  requiredVars.forEach(varName => {
    console.log(`   - ${varName}`);
  });
  console.log('');
}

// Check build size
const stats = fs.statSync(distPath);
const buildFiles = fs.readdirSync(distPath, { recursive: true });
const jsFiles = buildFiles.filter(file => file.endsWith('.js'));
const cssFiles = buildFiles.filter(file => file.endsWith('.css'));

console.log('📊 Build Statistics:');
console.log(`   - JavaScript files: ${jsFiles.length}`);
console.log(`   - CSS files: ${cssFiles.length}`);
console.log(`   - Total files: ${buildFiles.length}`);
console.log('');

// Verify vercel.json content
try {
  const vercelConfig = JSON.parse(fs.readFileSync(vercelConfigPath, 'utf8'));
  if (vercelConfig.rewrites && vercelConfig.rewrites.length > 0) {
    const hasIndexRewrite = vercelConfig.rewrites.some(
      rewrite => rewrite.destination === '/index.html'
    );
    if (hasIndexRewrite) {
      console.log('✅ vercel.json configured correctly for SPA routing');
    } else {
      console.warn('⚠️  vercel.json missing SPA routing configuration');
    }
  }
} catch (error) {
  console.error('❌ Invalid vercel.json format');
  process.exit(1);
}

console.log('\n🎉 Build verification complete!');
console.log('\n📋 Deployment Checklist:');
console.log('   ✅ Build files generated');
console.log('   ✅ vercel.json configured');
console.log('   ✅ SPA routing setup');
console.log('\n🚀 Ready to deploy to Vercel!');
console.log('\nNext steps:');
console.log('   1. Set environment variables in Vercel dashboard');
console.log('   2. Deploy using "vercel --prod" or Vercel dashboard');
console.log('   3. Test all routes after deployment');
