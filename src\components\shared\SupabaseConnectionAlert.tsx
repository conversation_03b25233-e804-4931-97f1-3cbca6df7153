import { useState, useEffect, useRef } from "react";
import { checkSupabaseConnection } from "@/lib/supabase";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { RefreshCw } from "lucide-react";

export default function SupabaseConnectionAlert() {
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean;
    checking: boolean;
  }>({
    connected: true,
    checking: false,
  });
  const { t, i18n } = useTranslation();
  const toastIdRef = useRef<string | number | null>(null);
  const hasShownErrorRef = useRef(false);

  // Check connection on component mount
  useEffect(() => {
    checkConnection();
  }, []);

  // Function to check Supabase connection
  const checkConnection = async () => {
    setConnectionStatus((prev) => ({ ...prev, checking: true }));

    try {
      const result = await checkSupabaseConnection();

      if (result.connected) {
        // Connection restored
        if (hasShownErrorRef.current) {
          // Dismiss any existing error toast
          if (toastIdRef.current) {
            toast.dismiss(toastIdRef.current);
          }

          // Show success toast
          const currentLang = i18n.language;
          if (currentLang === 'tr') {
            toast.success("Bağlandı", {
              duration: 2000,
            });
          } else {
            toast.success("Connected", {
              duration: 2000,
            });
          }
          hasShownErrorRef.current = false;
        }

        setConnectionStatus({
          connected: true,
          checking: false,
        });
      } else {
        // Connection failed
        if (!hasShownErrorRef.current) {
          const currentLang = i18n.language;

          if (currentLang === 'tr') {
            toastIdRef.current = toast.error("Bağlantı kesildi", {
              duration: Infinity,
              action: {
                label: "Tekrar dene",
                onClick: () => checkConnection(),
              },
            });
          } else {
            toastIdRef.current = toast.error("Connection lost", {
              duration: Infinity,
              action: {
                label: "Retry",
                onClick: () => checkConnection(),
              },
            });
          }
          hasShownErrorRef.current = true;
        }

        setConnectionStatus({
          connected: false,
          checking: false,
        });
      }
    } catch (error) {
      // Handle connection check error
      if (!hasShownErrorRef.current) {
        const currentLang = i18n.language;

        if (currentLang === 'tr') {
          toastIdRef.current = toast.error("Bağlantı kesildi", {
            duration: Infinity,
            action: {
              label: "Tekrar dene",
              onClick: () => checkConnection(),
            },
          });
        } else {
          toastIdRef.current = toast.error("Connection lost", {
            duration: Infinity,
            action: {
              label: "Retry",
              onClick: () => checkConnection(),
            },
          });
        }
        hasShownErrorRef.current = true;
      }

      setConnectionStatus({
        connected: false,
        checking: false,
      });
    }
  };

  // This component doesn't render anything visible - it only manages toasts
  return null;
}
