-- Create system_school_settings_overrides table to store system admin overrides for school settings
CREATE TABLE IF NOT EXISTS public.system_school_settings_overrides (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  school_id UUID REFERENCES schools(id) ON DELETE CASCADE,
  setting_name <PERSON><PERSON>HAR NOT NULL,
  setting_value JSONB NOT NULL,
  override_enabled BOOLEAN DEFAULT true,
  applies_to_all BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(school_id, setting_name)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_system_overrides_school_id ON public.system_school_settings_overrides(school_id);
CREATE INDEX IF NOT EXISTS idx_system_overrides_setting_name ON public.system_school_settings_overrides(setting_name);
CREATE INDEX IF NOT EXISTS idx_system_overrides_applies_to_all ON public.system_school_settings_overrides(applies_to_all);

-- Enable RLS on system_school_settings_overrides table
ALTER TABLE public.system_school_settings_overrides ENABLE ROW LEVEL SECURITY;

-- Only system admins can manage system_school_settings_overrides
CREATE POLICY "System admins can manage system_school_settings_overrides"
ON public.system_school_settings_overrides
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
)
WITH CHECK (
  -- Check if the current user is a system admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 3
  )
);

-- School admins can view system_school_settings_overrides for their school
CREATE POLICY "School admins can view system_school_settings_overrides for their school"
ON public.system_school_settings_overrides
FOR SELECT
TO authenticated
USING (
  -- Check if the current user is a school admin
  EXISTS (
    SELECT 1 FROM profiles admin
    WHERE admin.user_id = auth.uid()
    AND admin.role = 'admin'
    AND admin.access_level = 1
  )
  AND
  -- Check if the settings belong to the same school or apply to all schools
  (
    school_id = (
      SELECT school_id FROM profiles
      WHERE user_id = auth.uid()
    )
    OR
    applies_to_all = true
  )
);

-- Grant necessary permissions
GRANT ALL ON public.system_school_settings_overrides TO authenticated;

-- Add function to check if a setting is overridden by system admin
CREATE OR REPLACE FUNCTION is_setting_overridden(
  p_school_id UUID,
  p_setting_name VARCHAR
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM system_school_settings_overrides
    WHERE (school_id = p_school_id OR applies_to_all = true)
    AND setting_name = p_setting_name
    AND override_enabled = true
  );
END;
$$;

-- Add function to get the overridden value of a setting
CREATE OR REPLACE FUNCTION get_overridden_setting_value(
  p_school_id UUID,
  p_setting_name VARCHAR
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_value JSONB;
BEGIN
  -- First check for school-specific override
  SELECT setting_value INTO v_value
  FROM system_school_settings_overrides
  WHERE school_id = p_school_id
  AND setting_name = p_setting_name
  AND override_enabled = true;
  
  -- If no school-specific override, check for global override
  IF v_value IS NULL THEN
    SELECT setting_value INTO v_value
    FROM system_school_settings_overrides
    WHERE applies_to_all = true
    AND setting_name = p_setting_name
    AND override_enabled = true;
  END IF;
  
  RETURN v_value;
END;
$$;
