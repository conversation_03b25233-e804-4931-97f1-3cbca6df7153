import { useState, useEffect } from "react";
import { useParentContacts } from "@/hooks/useParentContacts";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useTranslation } from "react-i18next";
import {
  ParentContact,
  ParentContactFormValues,
} from "@/lib/types/parent-contact";

import NotificationHistory from "@/components/admin/NotificationHistory";
import { CustomPhoneInput } from "@/components/ui/phone-input";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import {
  UserPlus,
  Mail,
  Phone,
  Bell,
  BellOff,
  Trash2,
  Edit,
  Users,
  Search,
  CheckCircle,
  XCircle,
  History,
  Clock,
} from "lucide-react";

export default function ParentContactsManager() {
  const {
    parentContacts,
    loading,
    createParentContact,
    updateParentContact,
    deleteParentContact,
  } = useParentContacts();
  const [students, setStudents] = useState<any[]>([]);
  const [loadingStudents, setLoadingStudents] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStudentId, setSelectedStudentId] = useState<string | null>(
    null
  );
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingContact, setEditingContact] = useState<ParentContact | null>(
    null
  );
  const [bulkNotificationEnabled, setBulkNotificationEnabled] = useState<
    Record<string, boolean>
  >({});
  const [activeTab, setActiveTab] = useState("students");
  const { toast } = useToast();
  const { t } = useTranslation();
  const { profile } = useAuth();

  // Form state
  const [formData, setFormData] = useState<ParentContactFormValues>({
    parent_name: "",
    email: "",
    phone: "",
    notification_method: "email",
    notifications_enabled: true,
  });

  // Fetch all students from the current school only
  useEffect(() => {
    const fetchStudents = async () => {
      if (!profile?.school_id) return;

      try {
        setLoadingStudents(true);
        let query = supabase
          .from("profiles")
          .select("id, name, email")
          .eq("role", "student")
          .order("name");

        // Filter by school_id for school admins (system admins see all)
        if (profile.accessLevel !== 3) {
          query = query.eq("school_id", profile.school_id);
        }

        const { data, error } = await query;

        if (error) throw error;
        setStudents(data || []);
      } catch (err) {
        console.error("Error fetching students:", err);
        toast({
          title: t("admin.parentNotifications.error"),
          description: t("admin.parentNotifications.failedToLoadStudents"),
          variant: "destructive",
        });
      } finally {
        setLoadingStudents(false);
      }
    };

    fetchStudents();
  }, [profile?.school_id, profile?.accessLevel, toast]);

  // Initialize bulk notification state
  useEffect(() => {
    const initialState: Record<string, boolean> = {};
    students.forEach((student) => {
      initialState[student.id] = true;
    });
    setBulkNotificationEnabled(initialState);
  }, [students]);

  // Filter students based on search query
  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get contacts for the selected student
  const studentContacts = parentContacts.filter(
    (contact) => contact.student_id === selectedStudentId
  );

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Handle bulk notification toggle
  const handleBulkNotificationToggle = async (
    studentId: string,
    enabled: boolean
  ) => {
    try {
      // Update all contacts for this student
      const { data, error } = await supabase
        .from("parent_contacts")
        .update({ notifications_enabled: enabled })
        .eq("student_id", studentId);

      if (error) throw error;

      // Update local state
      setBulkNotificationEnabled((prev) => ({
        ...prev,
        [studentId]: enabled,
      }));

      toast({
        title: enabled
          ? t("admin.parentNotifications.notificationsEnabled")
          : t("admin.parentNotifications.notificationsDisabled"),
        description: enabled
          ? t("admin.parentNotifications.notificationsEnabledMessage")
          : t("admin.parentNotifications.notificationsDisabledMessage"),
      });

      // Refresh contacts
      if (selectedStudentId === studentId) {
        // Refresh the parent contacts
        parentContacts.forEach((contact) => {
          if (contact.student_id === studentId) {
            updateParentContact(contact.id, { notifications_enabled: enabled });
          }
        });
      }
    } catch (err: any) {
      console.error("Error updating notification settings:", err);
      toast({
        title: t("admin.parentNotifications.error"),
        description: err.message,
        variant: "destructive",
      });
    }
  };

  // Handle form submission for adding a new contact
  const handleAddContact = async () => {
    if (!selectedStudentId) {
      toast({
        title: t("admin.parentNotifications.error"),
        description: t("admin.parentNotifications.selectStudentError"),
        variant: "destructive",
      });
      return;
    }

    try {
      await createParentContact({
        ...formData,
        student_id: selectedStudentId,
      });

      // Reset form and close dialog
      setFormData({
        parent_name: "",
        email: "",
        phone: "",
        notification_method: "email",
        notifications_enabled: true,
      });
      setIsAddDialogOpen(false);
    } catch (err) {
      console.error("Error adding parent contact:", err);
    }
  };

  // Handle form submission for editing a contact
  const handleEditContact = async () => {
    if (!editingContact) return;

    try {
      await updateParentContact(editingContact.id, formData);
      setIsEditDialogOpen(false);
      setEditingContact(null);
    } catch (err) {
      console.error("Error updating parent contact:", err);
    }
  };

  // Open edit dialog with contact data
  const openEditDialog = (contact: ParentContact) => {
    setEditingContact(contact);
    setFormData({
      parent_name: contact.parent_name,
      email: contact.email || "",
      phone: contact.phone || "",
      notification_method: contact.notification_method,
      notifications_enabled: contact.notifications_enabled,
    });
    setIsEditDialogOpen(true);
  };

  // Handle contact deletion
  const handleDeleteContact = async (contactId: string) => {
    if (
      window.confirm("Are you sure you want to delete this parent contact?")
    ) {
      try {
        await deleteParentContact(contactId);
      } catch (err) {
        console.error("Error deleting parent contact:", err);
      }
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{t("admin.parentNotifications.title")}</CardTitle>
        <CardDescription>
          {t("admin.parentNotifications.description")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 gap-1 sm:gap-2">
            <TabsTrigger
              value="students"
              className="flex items-center justify-center text-xs sm:text-sm py-2 px-1 sm:px-3"
            >
              <Users className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="truncate max-w-[80px] sm:max-w-none">
                {t("admin.parentNotifications.students")}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="contacts"
              className="flex items-center justify-center text-xs sm:text-sm py-2 px-1 sm:px-3"
            >
              <Phone className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="truncate max-w-[80px] sm:max-w-none">
                {t("admin.parentNotifications.parentContacts")}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="history"
              className="flex items-center justify-center text-xs sm:text-sm py-2 px-1 sm:px-3"
            >
              <History className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
              <span className="truncate max-w-[80px] sm:max-w-none">
                {t("admin.parentNotifications.notificationHistory")}
              </span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="students" className="space-y-4">
            <div className="flex items-center space-x-2 my-4">
              <Search className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
              <Input
                placeholder={`${t(
                  "admin.parentNotifications.searchPlaceholder"
                )}...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 h-8 sm:h-9 text-xs sm:text-sm"
              />
            </div>

            {loadingStudents ? (
              <div className="space-y-2">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="flex items-center space-x-4 p-2">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                        {t("admin.parentNotifications.studentName")}
                      </TableHead>
                      <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                        {t("admin.parentNotifications.email")}
                      </TableHead>
                      <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                        {t("admin.parentNotifications.notifications")}
                      </TableHead>
                      <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                        {t("admin.parentNotifications.actions")}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStudents.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-4">
                          {t("admin.parentNotifications.noStudentsFound")}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredStudents.map((student) => (
                        <TableRow key={student.id}>
                          <TableCell className="font-medium text-xs sm:text-sm py-2 sm:py-3">
                            {student.name}
                          </TableCell>
                          <TableCell className="text-xs sm:text-sm py-2 sm:py-3">
                            <span className="text-muted-foreground">
                              {student.email}
                            </span>
                          </TableCell>
                          <TableCell className="py-2 sm:py-3">
                            <Switch
                              checked={
                                bulkNotificationEnabled[student.id] || false
                              }
                              onCheckedChange={(checked) =>
                                handleBulkNotificationToggle(
                                  student.id,
                                  checked
                                )
                              }
                              className="scale-90 sm:scale-100"
                            />
                          </TableCell>
                          <TableCell className="py-2 sm:py-3">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedStudentId(student.id);
                                setActiveTab("contacts");
                              }}
                              className="h-7 sm:h-8 text-xs sm:text-sm px-2 sm:px-3"
                            >
                              {t("admin.parentNotifications.manageContacts")}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>

          <TabsContent value="contacts" className="space-y-4">
            {!selectedStudentId ? (
              <div className="text-center py-6 sm:py-8 border rounded-md bg-muted/20">
                <Users className="mx-auto h-10 w-10 sm:h-12 sm:w-12 text-muted-foreground mb-2" />
                <h3 className="text-base sm:text-lg font-medium">
                  {t("admin.parentNotifications.noStudentSelected")}
                </h3>
                <p className="text-xs sm:text-sm text-muted-foreground max-w-md mx-auto">
                  {t("admin.parentNotifications.selectStudentMessage")}
                </p>
              </div>
            ) : (
              <>
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 w-full">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab("students")}
                      className="h-7 sm:h-8 text-xs sm:text-sm"
                    >
                      <Users className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      {t("admin.parentNotifications.backToStudents")}
                    </Button>
                    <h3 className="text-sm sm:text-lg font-medium">
                      {t("admin.parentNotifications.parentContactsFor")}{" "}
                      <span className="text-primary">
                        {students.find((s) => s.id === selectedStudentId)?.name}
                      </span>
                    </h3>

                  </div>
                  <Dialog
                    open={isAddDialogOpen}
                    onOpenChange={setIsAddDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button className="mt-3 sm:mt-0 w-full sm:w-auto text-xs sm:text-sm h-8 sm:h-9">
                        <UserPlus className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                        {t("admin.parentNotifications.addParentContact")}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle className="text-base sm:text-lg">
                          {t("admin.parentNotifications.addParentContact")}
                        </DialogTitle>
                        <DialogDescription className="text-sm">
                          {t("admin.parentNotifications.addContactInfo")}
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-3 sm:space-y-4 py-3 sm:py-4">
                        <div className="space-y-2">
                          <Label htmlFor="parent_name" className="text-sm font-medium">
                            {t("admin.parentNotifications.parentGuardianName")}
                          </Label>
                          <Input
                            id="parent_name"
                            name="parent_name"
                            value={formData.parent_name}
                            onChange={handleInputChange}
                            placeholder={t(
                              "admin.parentNotifications.fullName"
                            )}
                            className="text-sm"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-sm font-medium">
                            {t("admin.parentNotifications.emailAddress")}
                          </Label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            placeholder={t(
                              "admin.parentNotifications.emailPlaceholder"
                            )}
                            className="text-sm"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="phone" className="text-sm font-medium">
                            {t("admin.parentNotifications.phoneNumber")}
                          </Label>
                          <CustomPhoneInput
                            id="phone"
                            name="phone"
                            value={formData.phone}
                            onChange={(value) =>
                              setFormData((prev) => ({ ...prev, phone: value }))
                            }
                            placeholder={t(
                              "admin.parentNotifications.enterPhoneNumber"
                            )}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="notification_method" className="text-sm font-medium">
                            {t("admin.parentNotifications.notificationMethod")}
                          </Label>
                          <Select
                            value={formData.notification_method}
                            onValueChange={(value) =>
                              handleSelectChange("notification_method", value)
                            }
                          >
                            <SelectTrigger className="text-sm">
                              <SelectValue
                                placeholder={t(
                                  "admin.parentNotifications.selectNotificationMethod"
                                )}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="email">
                                {t("admin.parentNotifications.emailOnly")}
                              </SelectItem>
                              <SelectItem value="sms">
                                {t("admin.parentNotifications.smsOnly")}
                              </SelectItem>
                              <SelectItem value="both">
                                {t("admin.parentNotifications.bothEmailSms")}
                              </SelectItem>
                              <SelectItem value="none">
                                {t("admin.parentNotifications.noNotifications")}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="notifications_enabled"
                            checked={formData.notifications_enabled}
                            onCheckedChange={(checked) =>
                              handleSwitchChange(
                                "notifications_enabled",
                                checked as boolean
                              )
                            }
                          />
                          <Label htmlFor="notifications_enabled" className="text-sm">
                            {t("admin.parentNotifications.enableNotifications")}
                          </Label>
                        </div>
                      </div>
                      <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
                        <Button
                          variant="outline"
                          onClick={() => setIsAddDialogOpen(false)}
                          className="w-full sm:w-auto text-sm"
                        >
                          {t("admin.parentNotifications.cancel")}
                        </Button>
                        <Button onClick={handleAddContact} className="w-full sm:w-auto text-sm">
                          {t("admin.parentNotifications.addContact")}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>

                {/* Edit Contact Dialog */}
                <Dialog
                  open={isEditDialogOpen}
                  onOpenChange={setIsEditDialogOpen}
                >
                  <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle className="text-base sm:text-lg">
                        {t("admin.parentNotifications.editParentContact")}
                      </DialogTitle>
                      <DialogDescription className="text-sm">
                        {t("admin.parentNotifications.updateContactInfo")}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-3 sm:space-y-4 py-3 sm:py-4">
                      <div className="space-y-2">
                        <Label htmlFor="edit_parent_name" className="text-sm font-medium">
                          {t("admin.parentNotifications.parentGuardianName")}
                        </Label>
                        <Input
                          id="edit_parent_name"
                          name="parent_name"
                          value={formData.parent_name}
                          onChange={handleInputChange}
                          placeholder={t("admin.parentNotifications.fullName")}
                          className="text-sm"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit_email" className="text-sm font-medium">
                          {t("admin.parentNotifications.emailAddress")}
                        </Label>
                        <Input
                          id="edit_email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder={t(
                            "admin.parentNotifications.emailPlaceholder"
                          )}
                          className="text-sm"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit_phone" className="text-sm font-medium">
                          {t("admin.parentNotifications.phoneNumber")}
                        </Label>
                        <CustomPhoneInput
                          id="edit_phone"
                          name="phone"
                          value={formData.phone}
                          onChange={(value) =>
                            setFormData((prev) => ({ ...prev, phone: value }))
                          }
                          placeholder={t(
                            "admin.parentNotifications.enterPhoneNumber"
                          )}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="edit_notification_method" className="text-sm font-medium">
                          {t("admin.parentNotifications.notificationMethod")}
                        </Label>
                        <Select
                          value={formData.notification_method}
                          onValueChange={(value) =>
                            handleSelectChange("notification_method", value)
                          }
                        >
                          <SelectTrigger id="edit_notification_method" className="text-sm">
                            <SelectValue
                              placeholder={t(
                                "admin.parentNotifications.selectNotificationMethod"
                              )}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="email">
                              {t("admin.parentNotifications.emailOnly")}
                            </SelectItem>
                            <SelectItem value="sms">
                              {t("admin.parentNotifications.smsOnly")}
                            </SelectItem>
                            <SelectItem value="both">
                              {t("admin.parentNotifications.bothEmailSms")}
                            </SelectItem>
                            <SelectItem value="none">
                              {t("admin.parentNotifications.noNotifications")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="edit_notifications_enabled"
                          checked={formData.notifications_enabled}
                          onCheckedChange={(checked) =>
                            handleSwitchChange(
                              "notifications_enabled",
                              checked as boolean
                            )
                          }
                        />
                        <Label htmlFor="edit_notifications_enabled" className="text-sm">
                          {t("admin.parentNotifications.enableNotifications")}
                        </Label>
                      </div>
                    </div>
                    <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
                      <Button
                        variant="outline"
                        onClick={() => setIsEditDialogOpen(false)}
                        className="w-full sm:w-auto text-sm"
                      >
                        {t("admin.parentNotifications.cancel")}
                      </Button>
                      <Button onClick={handleEditContact} className="w-full sm:w-auto text-sm">
                        {t("admin.parentNotifications.saveChanges")}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <Skeleton key={i} className="h-16 w-full" />
                    ))}
                  </div>
                ) : studentContacts.length === 0 ? (
                  <div className="text-center py-6 sm:py-8 border rounded-md bg-muted/20">
                    <UserPlus className="mx-auto h-10 w-10 sm:h-12 sm:w-12 text-muted-foreground mb-2" />
                    <h3 className="text-base sm:text-lg font-medium">
                      {t("admin.parentNotifications.noParentContacts")}
                    </h3>
                    <p className="text-xs sm:text-sm text-muted-foreground mb-4 max-w-md mx-auto">
                      {t("admin.parentNotifications.noContactsAdded")}
                    </p>
                    <Button
                      onClick={() => setIsAddDialogOpen(true)}
                      className="text-xs sm:text-sm h-8 sm:h-9"
                    >
                      {t("admin.parentNotifications.addFirstContact")}
                    </Button>
                  </div>
                ) : (
                  <div className="border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                            {t("admin.parentNotifications.parentName")}
                          </TableHead>
                          <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                            {t("admin.parentNotifications.contactInfo")}
                          </TableHead>
                          <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                            {t("admin.parentNotifications.notificationMethod")}
                          </TableHead>
                          <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                            {t("admin.parentNotifications.status")}
                          </TableHead>
                          <TableHead className="text-xs sm:text-sm py-2 sm:py-3">
                            {t("admin.parentNotifications.actions")}
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {studentContacts.map((contact) => (
                          <TableRow key={contact.id}>
                            <TableCell className="font-medium text-xs sm:text-sm py-2 sm:py-3">
                              {contact.parent_name}
                            </TableCell>
                            <TableCell className="py-2 sm:py-3">
                              <div className="space-y-1">
                                {contact.email && (
                                  <div className="flex items-center text-xs sm:text-sm">
                                    <Mail className="mr-1 sm:mr-2 h-3 w-3" />
                                    {contact.email}
                                  </div>
                                )}
                                {contact.phone && (
                                  <div className="flex items-center text-xs sm:text-sm">
                                    <Phone className="mr-1 sm:mr-2 h-3 w-3" />
                                    {contact.phone}
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="text-xs sm:text-sm py-2 sm:py-3">
                              {contact.notification_method === "email" &&
                                t("admin.parentNotifications.emailOnly")}
                              {contact.notification_method === "sms" &&
                                t("admin.parentNotifications.smsOnly")}
                              {contact.notification_method === "both" &&
                                t("admin.parentNotifications.bothEmailSms")}
                              {contact.notification_method === "none" &&
                                t("admin.parentNotifications.noNotifications")}
                            </TableCell>
                            <TableCell className="py-2 sm:py-3">
                              {contact.notifications_enabled ? (
                                <div className="flex items-center text-green-600 text-xs sm:text-sm">
                                  <CheckCircle className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                                  {t("admin.parentNotifications.enabled")}
                                </div>
                              ) : (
                                <div className="flex items-center text-red-600 text-xs sm:text-sm">
                                  <XCircle className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                                  {t("admin.parentNotifications.disabled")}
                                </div>
                              )}
                            </TableCell>
                            <TableCell className="py-2 sm:py-3">
                              <div className="flex space-x-1 sm:space-x-2">
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => openEditDialog(contact)}
                                  className="h-7 w-7 sm:h-8 sm:w-8"
                                >
                                  <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() =>
                                    handleDeleteContact(contact.id)
                                  }
                                  className="h-7 w-7 sm:h-8 sm:w-8"
                                >
                                  <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <NotificationHistory />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
