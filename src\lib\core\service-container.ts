/**
 * Service Container
 * Dependency injection container for managing service instances
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../database.types';

// Service interfaces
export interface IUserService {
  getCurrentUser(): Promise<any>;
  updateProfile(data: any): Promise<any>;
  deleteUser(id: string): Promise<any>;
}

export interface IAttendanceService {
  recordAttendance(data: any): Promise<any>;
  getAttendanceHistory(params: any): Promise<any>;
  getAttendanceStats(params: any): Promise<any>;
}

export interface ISchoolService {
  getSchools(): Promise<any>;
  createSchool(data: any): Promise<any>;
  updateSchool(id: string, data: any): Promise<any>;
}

export interface INotificationService {
  sendNotification(data: any): Promise<any>;
  getNotifications(userId: string): Promise<any>;
  markAsRead(id: string): Promise<any>;
}

// Service container class
export class ServiceContainer {
  private services: Map<string, any> = new Map();
  private supabase: SupabaseClient<Database>;

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  /**
   * Register a service instance
   */
  register<T>(name: string, service: T): void {
    this.services.set(name, service);
  }

  /**
   * Register a service factory
   */
  registerFactory<T>(name: string, factory: () => T): void {
    this.services.set(name, factory);
  }

  /**
   * Get a service instance
   */
  get<T>(name: string): T {
    const service = this.services.get(name);
    
    if (!service) {
      throw new Error(`Service '${name}' not found`);
    }

    // If it's a factory function, call it to get the instance
    if (typeof service === 'function') {
      return service();
    }

    return service;
  }

  /**
   * Check if a service is registered
   */
  has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Remove a service
   */
  remove(name: string): void {
    this.services.delete(name);
  }

  /**
   * Clear all services
   */
  clear(): void {
    this.services.clear();
  }

  /**
   * Get the Supabase client
   */
  getSupabase(): SupabaseClient<Database> {
    return this.supabase;
  }

  /**
   * Initialize default services
   */
  async initializeServices(): Promise<void> {
    // Lazy load services to avoid circular dependencies
    const { UserService } = await import('../services/user-service');
    const { AttendanceService } = await import('../services/attendance-service');
    const { SchoolService } = await import('../services/school-service');
    const { NotificationService } = await import('../services/notification-service');

    // Register service factories
    this.registerFactory('userService', () => new UserService(this.supabase));
    this.registerFactory('attendanceService', () => new AttendanceService(this.supabase));
    this.registerFactory('schoolService', () => new SchoolService(this.supabase));
    this.registerFactory('notificationService', () => new NotificationService(this.supabase));
  }
}

// Global service container instance
let serviceContainer: ServiceContainer | null = null;

/**
 * Initialize the global service container
 */
export function initializeServiceContainer(supabase: SupabaseClient<Database>): ServiceContainer {
  if (!serviceContainer) {
    serviceContainer = new ServiceContainer(supabase);
  }
  return serviceContainer;
}

/**
 * Get the global service container
 */
export function getServiceContainer(): ServiceContainer {
  if (!serviceContainer) {
    throw new Error('Service container not initialized. Call initializeServiceContainer first.');
  }
  return serviceContainer;
}

/**
 * Convenience function to get a service
 */
export function getService<T>(name: string): T {
  return getServiceContainer().get<T>(name);
}

// Service name constants
export const SERVICE_NAMES = {
  USER: 'userService',
  ATTENDANCE: 'attendanceService',
  SCHOOL: 'schoolService',
  NOTIFICATION: 'notificationService',
} as const;
