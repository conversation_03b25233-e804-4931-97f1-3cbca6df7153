import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "./use-toast";
import {
  ParentContact,
  ParentContactFormValues,
} from "@/lib/types/parent-contact";
import { useTranslation } from "react-i18next";

interface UseParentContactsProps {
  studentId?: string;
}

export function useParentContacts({ studentId }: UseParentContactsProps = {}) {
  const [parentContacts, setParentContacts] = useState<ParentContact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { profile } = useAuth();
  const { toast } = useToast();
  const { t } = useTranslation();

  const fetchParentContacts = async (targetStudentId?: string) => {
    try {
      setLoading(true);
      setError(null);

      // Determine which student ID to use
      const effectiveStudentId =
        targetStudentId ||
        studentId ||
        (profile?.role === "student" ? profile?.id : null);

      if (
        !effectiveStudentId &&
        profile?.role !== "admin" &&
        profile?.role !== "teacher"
      ) {
        throw new Error(
          "No student ID provided and user is not an admin or teacher"
        );
      }

      let query = supabase.from("parent_contacts").select(`
        *,
        student:profiles!parent_contacts_student_id_fkey(id, name, school_id)
      `);

      // If we have a specific student ID, filter by it
      if (effectiveStudentId) {
        query = query.eq("student_id", effectiveStudentId);
      }

      // Filter by school_id for non-system admins
      if (profile?.school_id && profile?.accessLevel !== 3) {
        // Join with profiles table to filter by school_id
        query = query.eq("student.school_id", profile.school_id);
      }

      const { data, error: fetchError } = await query.order("created_at", {
        ascending: false,
      });

      if (fetchError) throw fetchError;

      setParentContacts(data || []);
    } catch (err: any) {
      console.error("Error fetching parent contacts:", err);
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  const createParentContact = async (
    contactData: ParentContactFormValues & { student_id: string }
  ) => {
    try {
      if (!profile) {
        throw new Error("User not authenticated");
      }

      // Validate that at least one contact method is provided
      if (!contactData.email && !contactData.phone) {
        throw new Error(
          "At least one contact method (email or phone) must be provided"
        );
      }

      const { data, error } = await supabase
        .from("parent_contacts")
        .insert({
          ...contactData,
          updated_at: new Date().toISOString(),
        })
        .select();

      if (error) throw error;

      toast({
        title: t("admin.parentNotifications.contactAdded"),
        description: t("admin.parentNotifications.contactAddedSuccess"),
      });

      // Update the local state
      setParentContacts((prev) => [data[0], ...prev]);

      return data[0];
    } catch (err: any) {
      console.error("Error creating parent contact:", err);
      toast({
        title: t("admin.parentNotifications.error"),
        description: err.message,
        variant: "destructive",
      });
      throw err;
    }
  };

  const updateParentContact = async (
    id: string,
    contactData: Partial<ParentContactFormValues>
  ) => {
    try {
      if (!profile) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("parent_contacts")
        .update({
          ...contactData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select();

      if (error) throw error;

      toast({
        title: t("admin.parentNotifications.contactUpdated"),
        description: t("admin.parentNotifications.contactUpdatedSuccess"),
      });

      // Update the local state
      setParentContacts((prev) =>
        prev.map((contact) => (contact.id === id ? data[0] : contact))
      );

      return data[0];
    } catch (err: any) {
      console.error("Error updating parent contact:", err);
      toast({
        title: t("admin.parentNotifications.error"),
        description: err.message,
        variant: "destructive",
      });
      throw err;
    }
  };

  const deleteParentContact = async (id: string) => {
    try {
      if (!profile) {
        throw new Error("User not authenticated");
      }

      const { error } = await supabase
        .from("parent_contacts")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: t("admin.parentNotifications.contactDeleted"),
        description: t("admin.parentNotifications.contactDeletedSuccess"),
      });

      // Update the local state
      setParentContacts((prev) => prev.filter((contact) => contact.id !== id));
    } catch (err: any) {
      console.error("Error deleting parent contact:", err);
      toast({
        title: t("admin.parentNotifications.error"),
        description: t("admin.parentNotifications.failedToDeleteContact", {
          message: err.message,
        }),
        variant: "destructive",
      });
      throw err;
    }
  };

  // Fetch parent contacts on component mount
  useEffect(() => {
    fetchParentContacts();
  }, [studentId, profile]);

  return {
    parentContacts,
    loading,
    error,
    fetchParentContacts,
    createParentContact,
    updateParentContact,
    deleteParentContact,
  };
}
