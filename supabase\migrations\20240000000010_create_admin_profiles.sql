-- Create admin_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_profiles (
  id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  admin_id VARCHAR NOT NULL,
  position VARCHAR NOT NULL,
  school VARCHAR NOT NULL,
  access_level INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', now())
);

-- Create function to update admin profiles
CREATE OR REPLACE FUNCTION update_admin_profile()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.role = 'admin' THEN
    -- Check if admin profile exists
    IF EXISTS (SELECT 1 FROM admin_profiles WHERE id = NEW.id) THEN
      -- Update existing admin profile
      UPDATE admin_profiles
      SET 
        position = COALESCE(NEW.position, admin_profiles.position),
        school = COALESCE(NEW.school, admin_profiles.school),
        updated_at = NOW()
      WHERE id = NEW.id;
    ELSE
      -- Insert new admin profile if position and school are provided
      IF NEW.position IS NOT NULL AND NEW.school IS NOT NULL THEN
        INSERT INTO admin_profiles (id, user_id, admin_id, position, school)
        VALUES (
          NEW.id,
          NEW.user_id,
          COALESCE(NEW.admin_id, 'A-' || extract(epoch from now())::bigint),
          NEW.position,
          NEW.school
        );
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update admin_profiles table
DROP TRIGGER IF EXISTS update_admin_profile_trigger ON profiles;
CREATE TRIGGER update_admin_profile_trigger
AFTER INSERT OR UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_admin_profile();

-- Add school column to profiles table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'school'
  ) THEN
    ALTER TABLE profiles ADD COLUMN school VARCHAR;
  END IF;
END $$;

-- Add profile_completed column to profiles table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'profile_completed'
  ) THEN
    ALTER TABLE profiles ADD COLUMN profile_completed BOOLEAN DEFAULT false;
  END IF;
END $$;
