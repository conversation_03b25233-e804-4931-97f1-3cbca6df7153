import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Download, FileText, Users, Clock, Trash2, CalendarIcon, Globe } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { format, subDays, startOfDay, endOfDay, formatDate } from "date-fns";
import { tr, enUS } from "date-fns/locale";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { toast as sonnerToast } from "sonner";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { downloadCSV } from "@/lib/reportUtils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BRANDING } from "@/config/branding";

interface DayReport {
  date: string;
  displayDate: string;
  totalStudents: number;
  presentStudents: number;
  absentStudents: number;
  lateStudents: number;
  excusedStudents: number;
  attendanceRate: number;
  canExport: boolean;
}

interface EnhancedAttendanceRecord {
  id: string;
  studentId: string;
  roomId: string;
  timestamp: string;
  deviceInfo: string;
  location: any;
  verificationMethod: string;
  status: string;
  studentName: string;
  roomName: string;
  createdAt?: string;
}

// Define status type for filtering
type StatusFilter = "all" | "present" | "absent" | "late" | "excused";

export default function AttendanceReports() {
  const { t, i18n } = useTranslation();

  // Get the appropriate locale for date formatting
  const getDateLocale = () => {
    return i18n.language === 'tr' ? tr : enUS;
  };
  const { toast } = useToast();
  const { profile } = useAuth();
  const [reports, setReports] = useState<DayReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState<string | null>(null);
  const [cleaning, setCleaning] = useState(false);

  // Status display information with translations
  const statusInfo = {
    all: {
      label: t("attendance.status.allStatuses"),
      color: "default",
      icon: "🔍",
    },
    present: {
      label: t("attendance.status.presentOnly"),
      color: "green",
      icon: "✅",
    },
    absent: {
      label: t("attendance.status.absentOnly"),
      color: "destructive",
      icon: "❌",
    },
    late: {
      label: t("attendance.status.lateOnly"),
      color: "yellow",
      icon: "⏰",
    },
    excused: {
      label: t("attendance.status.excusedOnly"),
      color: "blue",
      icon: "📝",
    },
  };

  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });

  const [statusFilter, setStatusFilter] = useState<StatusFilter>("all");

  // Generate last 7 days
  const generateLast7Days = (): DayReport[] => {
    const days: DayReport[] = [];
    for (let i = 0; i < 7; i++) {
      const date = subDays(new Date(), i);
      const dateStr = format(date, 'yyyy-MM-dd');
      const displayDate = format(date, 'MMM dd, yyyy', { locale: getDateLocale() });
      
      days.push({
        date: dateStr,
        displayDate,
        totalStudents: 0,
        presentStudents: 0,
        absentStudents: 0,
        attendanceRate: 0,
        canExport: true,
      });
    }
    return days;
  };

  // Fetch attendance data for the last 7 days
  const fetchAttendanceData = async () => {
    if (!profile?.school_id) return;

    try {
      setLoading(true);
      const days = generateLast7Days();
      
      // Get total students for the school
      const { data: studentsData, error: studentsError } = await supabase
        .from("profiles")
        .select("id")
        .eq("role", "student")
        .eq("school_id", profile.school_id);

      if (studentsError) throw studentsError;
      
      const totalStudents = studentsData?.length || 0;

      // Get attendance records for the last 7 days
      const sevenDaysAgo = format(subDays(new Date(), 7), 'yyyy-MM-dd');
      const today = format(new Date(), 'yyyy-MM-dd');

      const { data: attendanceData, error: attendanceError } = await supabase
        .from("attendance_records")
        .select("student_id, timestamp, status")
        .eq("school_id", profile.school_id)
        .gte("timestamp", sevenDaysAgo)
        .lte("timestamp", today + "T23:59:59");

      if (attendanceError) throw attendanceError;

      // Process attendance data for each day
      const updatedDays = days.map(day => {
        const dayStart = startOfDay(new Date(day.date));
        const dayEnd = endOfDay(new Date(day.date));

        // Get attendance records for this day
        const dayRecords = attendanceData?.filter(record => {
          const recordDate = new Date(record.timestamp);
          return recordDate >= dayStart && recordDate <= dayEnd;
        }) || [];

        // Count students by status for this day
        const statusCounts = {
          present: new Set(),
          late: new Set(),
          excused: new Set(),
        };

        // Process each record and count unique students by status
        dayRecords.forEach(record => {
          const studentId = record.student_id;
          const status = record.status;

          // Add student to appropriate status set (latest status wins)
          if (status === "present") {
            statusCounts.present.add(studentId);
            // Remove from other statuses if present
            statusCounts.late.delete(studentId);
            statusCounts.excused.delete(studentId);
          } else if (status === "late") {
            statusCounts.late.add(studentId);
            // Remove from other statuses if late
            statusCounts.present.delete(studentId);
            statusCounts.excused.delete(studentId);
          } else if (status === "excused") {
            statusCounts.excused.add(studentId);
            // Remove from other statuses if excused
            statusCounts.present.delete(studentId);
            statusCounts.late.delete(studentId);
          }
        });

        // Calculate counts
        const presentStudents = statusCounts.present.size;
        const lateStudents = statusCounts.late.size;
        const excusedStudents = statusCounts.excused.size;
        const attendedStudents = presentStudents + lateStudents + excusedStudents;
        const absentStudents = Math.max(0, totalStudents - attendedStudents);

        // Calculate attendance rate (present + late + excused / total)
        const attendanceRate = totalStudents > 0 ? (attendedStudents / totalStudents) * 100 : 0;

        return {
          ...day,
          totalStudents,
          presentStudents,
          lateStudents,
          excusedStudents,
          absentStudents,
          attendanceRate,
        };
      });

      setReports(updatedDays);
    } catch (error) {
      console.error("Error fetching attendance data:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.fetchError"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Get attendance records for export including absent students
  const getAttendanceRecords = async (selectedDate: Date): Promise<EnhancedAttendanceRecord[]> => {
    if (!profile?.school_id) return [];

    try {
      const dayStart = startOfDay(selectedDate);
      const dayEnd = endOfDay(selectedDate);

      // Get all students in the school
      const { data: allStudents, error: studentsError } = await supabase
        .from("profiles")
        .select("id, name, student_id, email, room_id")
        .eq("school_id", profile.school_id)
        .eq("role", "student");

      if (studentsError) throw studentsError;

      // Get attendance records for the selected date
      const { data: attendanceData, error: attendanceError } = await supabase
        .from("attendance_records")
        .select(`
          id,
          student_id,
          room_id,
          timestamp,
          device_info,
          location,
          verification_method,
          status,
          created_at
        `)
        .eq("school_id", profile.school_id)
        .gte("timestamp", dayStart.toISOString())
        .lte("timestamp", dayEnd.toISOString())
        .order("timestamp", { ascending: true });

      if (attendanceError) throw attendanceError;

      // Get all rooms in the school
      const { data: roomsData, error: roomsError } = await supabase
        .from("rooms")
        .select("id, name")
        .eq("school_id", profile.school_id);

      if (roomsError) throw roomsError;

      // Create lookup maps
      const roomsMap = new Map(roomsData?.map(room => [room.id, room]) || []);
      const attendanceMap = new Map(attendanceData?.map(record => [record.student_id, record]) || []);

      // Create enhanced records for all students
      const enhancedRecords: EnhancedAttendanceRecord[] = (allStudents || []).map(student => {
        const attendanceRecord = attendanceMap.get(student.id);
        const room = roomsMap.get(student.room_id);

        if (attendanceRecord) {
          // Student has an attendance record
          return {
            id: attendanceRecord.id,
            studentId: student.student_id || attendanceRecord.student_id,
            studentName: student.name || "Unknown Student",
            roomId: attendanceRecord.room_id,
            roomName: roomsMap.get(attendanceRecord.room_id)?.name || room?.name || "Unknown Room",
            timestamp: attendanceRecord.timestamp,
            status: attendanceRecord.status as "present" | "absent" | "late" | "excused",
            verificationMethod: attendanceRecord.verification_method as "biometric" | "pin" | "manual",
            deviceInfo: attendanceRecord.device_info || "",
            location: attendanceRecord.location,
            createdAt: attendanceRecord.created_at,
          };
        } else {
          // Student is absent (no attendance record)
          return {
            id: `absent-${student.id}-${format(selectedDate, 'yyyy-MM-dd')}`,
            studentId: student.student_id || student.id,
            studentName: student.name || "Unknown Student",
            roomId: student.room_id || "",
            roomName: room?.name || "Unknown Room",
            timestamp: dayStart.toISOString(),
            status: "absent" as const,
            verificationMethod: "manual" as const,
            deviceInfo: "No attendance record",
            location: null,
            createdAt: dayStart.toISOString(),
          };
        }
      });

      return enhancedRecords;
    } catch (error) {
      console.error("Error fetching attendance records:", error);
      return [];
    }
  };

  // Export functionality matching teacher's implementation
  const handleExport = async (exportFormat: "csv" | "pdf" | "html", selectedDate: Date) => {
    if (!profile?.school_id) return;

    try {
      setExporting(exportFormat);

      const records = await getAttendanceRecords(selectedDate);

      if (records.length === 0) {
        toast({
          title: t("attendance.export.noStudents"),
          description: t("attendance.export.noStudentsDescription"),
          variant: "destructive",
        });
        return;
      }

      // Filter records by status if needed
      const filteredRecords = statusFilter === "all"
        ? records
        : records.filter(record => record.status === statusFilter);

      const dateRangeForExport = {
        from: selectedDate,
        to: selectedDate,
      };

      const filename = `attendance-${format(selectedDate, 'yyyy-MM-dd')}`;
      const title = t("admin.attendanceManagement.dailyReports");

      if (exportFormat === "csv") {
        exportToCSV(records, `${filename}.csv`, dateRangeForExport);
      } else if (exportFormat === "pdf") {
        // Use modern HTML-to-PDF approach like teacher's export
        const htmlContent = generateHTMLContent(true, records, dateRangeForExport);

        // Create a new window for PDF generation
        const printWindow = window.open("", "_blank");
        if (printWindow) {
          printWindow.document.write(htmlContent);
          printWindow.document.close();

          // Wait for content to load then print
          printWindow.onload = () => {
            setTimeout(() => {
              printWindow.print();
              printWindow.close();
            }, 500);
          };
        }
      } else if (exportFormat === "html") {
        downloadHTML(records, `${filename}.html`, dateRangeForExport);
      }

      sonnerToast.success(t("attendance.export.exportSuccessful"), {
        description: t("attendance.export.reportExportedAs", {
          format: exportFormat.toUpperCase(),
        }),
      });
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: t("attendance.export.exportFailed"),
        description: t("attendance.export.failedToGenerate"),
        variant: "destructive",
      });
    } finally {
      setExporting(null);
    }
  };

  // CSV export function matching teacher's implementation
  const exportToCSV = (
    records: EnhancedAttendanceRecord[],
    filename: string,
    dateRange?: { from: Date; to: Date }
  ) => {
    const filteredRecords = statusFilter === "all"
      ? records
      : records.filter(record => record.status === statusFilter);

    if (filteredRecords.length === 0) {
      toast({
        title: t("attendance.export.noRecordsFound"),
        description: t("attendance.export.noRecordsForDateRange"),
        variant: "destructive",
      });
      return;
    }

    // CSV headers
    const headers = [
      t("attendance.export.headers.dateTime"),
      t("attendance.export.headers.studentName"),
      t("attendance.export.headers.studentId"),
      t("attendance.export.headers.room"),
      t("attendance.export.headers.status"),
      t("attendance.export.headers.verification"),
    ];

    // CSV rows
    const rows = filteredRecords.map((record) => {
      // Safely handle date formatting with fallback
      let formattedDate = t("attendance.export.notAvailable");
      try {
        const dateValue = record.timestamp || record.createdAt;
        if (dateValue) {
          const date = new Date(dateValue);
          if (!isNaN(date.getTime())) {
            formattedDate = formatDate(date, "yyyy-MM-dd HH:mm", { locale: getDateLocale() });
          }
        }
      } catch (e) {
        // Keep default value
      }

      return [
        formattedDate,
        record.studentName || t("attendance.export.unknownStudent"),
        record.studentId || "N/A",
        record.roomName || t("attendance.export.unknownRoom"),
        t(`attendance.status.${record.status}`),
        record.verificationMethod === "manual"
          ? t("attendance.export.manualByTeacher")
          : record.verificationMethod
            ? record.verificationMethod.charAt(0).toUpperCase() + record.verificationMethod.slice(1)
            : t("attendance.export.notAvailable"),
      ];
    });

    const csvContent = [
      headers.join(","),
      ...rows.map((row) =>
        row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(",")
      ),
    ].join("\n");

    downloadFile(
      csvContent,
      filename,
      "text/csv"
    );
  };

  // HTML download function
  const downloadHTML = (
    records: EnhancedAttendanceRecord[],
    filename: string,
    dateRange?: { from: Date; to: Date }
  ) => {
    const htmlContent = generateHTMLContent(false, records, dateRange);
    downloadFile(
      htmlContent,
      filename,
      "text/html"
    );
  };

  // Generate HTML content for export - Matching teacher's beautiful design
  const generateHTMLContent = (
    forPrint: boolean,
    records: EnhancedAttendanceRecord[],
    dateRange?: { from: Date; to: Date }
  ): string => {
    const filteredRecords = statusFilter === "all"
      ? records
      : records.filter(record => record.status === statusFilter);

    const dateRangeStr = dateRange?.from
      ? formatDate(dateRange.from, "PPP")
      : formatDate(new Date(), "PPP");

    // Calculate statistics from ALL records, not just filtered ones
    const presentCount = records.filter(r => r.status === "present").length;
    const absentCount = records.filter(r => r.status === "absent").length;
    const lateCount = records.filter(r => r.status === "late").length;
    const excusedCount = records.filter(r => r.status === "excused").length;
    const totalRecords = records.length;

    return `<!DOCTYPE html>
<html lang="${t("common.locale")}" dir="${t("common.direction")}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${t("attendance.export.attendanceReport")}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.12),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
            color: white;
            padding: 24px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 8px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .header h2 {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 400;
            margin-bottom: 4px;
            position: relative;
            z-index: 1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 24px;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 24px;
            border-radius: 16px;
            text-align: center;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.08),
                0 0 0 1px rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.12),
                0 0 0 1px rgba(255, 255, 255, 0.5);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-card.total .stat-number { color: #3b82f6; }
        .stat-card.present .stat-number { color: #10b981; }
        .stat-card.absent .stat-number { color: #ef4444; }
        .stat-card.late .stat-number { color: #f59e0b; }
        .stat-card.excused .stat-number { color: #8b5cf6; }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }

        .content {
            padding: 40px;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .table-wrapper {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            min-width: 600px;
        }

        th {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            position: relative;
        }

        th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
        }

        td {
            padding: 16px 20px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.875rem;
            color: #374151;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e0f2fe 0%, #e1f5fe 100%);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-present {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            color: #065f46;
            border: 1px solid #34d399;
        }

        .status-absent {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            color: #991b1b;
            border: 1px solid #f87171;
        }

        .status-late {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }

        .status-excused {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1e40af;
            border: 1px solid #3b82f6;
        }

        @media print {
            body {
                background: white !important;
                padding: 0;
                margin: 0;
                font-size: 12px;
            }

            body::before {
                display: none;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
                background: white;
                backdrop-filter: none;
                max-width: none;
                margin: 0;
            }

            .header {
                background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                print-color-adjust: exact;
                padding: 20px 30px;
            }

            .stats-grid {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                print-color-adjust: exact;
                padding: 25px 30px;
            }

            .stat-card {
                background: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                print-color-adjust: exact;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            }

            .stat-card::before {
                background: linear-gradient(90deg, #667eea, #764ba2, #f093fb) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                print-color-adjust: exact;
            }

            th {
                background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                print-color-adjust: exact;
                color: white !important;
            }

            .status-badge {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                print-color-adjust: exact;
            }

            .status-present {
                background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%) !important;
                color: #065f46 !important;
                border: 1px solid #34d399 !important;
            }

            .status-absent {
                background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%) !important;
                color: #991b1b !important;
                border: 1px solid #f87171 !important;
            }

            .status-late {
                background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
                color: #92400e !important;
                border: 1px solid #f59e0b !important;
            }

            .status-excused {
                background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
                color: #1e40af !important;
                border: 1px solid #3b82f6 !important;
            }

            .stat-card:hover,
            tr:hover {
                transform: none;
            }

            .content {
                padding: 20px 30px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 15px 20px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .header h2 {
                font-size: 0.9rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                padding: 15px 20px;
                gap: 10px;
            }

            .stat-card {
                padding: 10px;
            }

            .content {
                padding: 15px 20px;
            }

            .table-container {
                border-radius: 10px;
                margin: 0 -10px;
            }

            .table-wrapper {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: thin;
                scrollbar-color: #cbd5e0 #f7fafc;
            }

            .table-wrapper::-webkit-scrollbar {
                height: 6px;
            }

            .table-wrapper::-webkit-scrollbar-track {
                background: #f7fafc;
            }

            .table-wrapper::-webkit-scrollbar-thumb {
                background: #cbd5e0;
                border-radius: 3px;
            }

            table {
                min-width: 700px;
            }

            th, td {
                padding: 8px 6px;
                font-size: 0.75rem;
                white-space: nowrap;
            }

            th {
                font-size: 0.7rem;
                padding: 12px 8px;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.7rem;
            }

            .status-badge {
                padding: 4px 8px;
                font-size: 0.65rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 5px;
            }

            .header {
                padding: 10px 15px;
            }

            .header h1 {
                font-size: 1.25rem;
            }

            .header h2 {
                font-size: 0.8rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                padding: 10px 15px;
                gap: 8px;
            }

            .stat-card {
                padding: 8px;
            }

            .content {
                padding: 10px 15px;
            }

            .table-container {
                margin: 0 -15px;
                border-radius: 8px;
            }

            table {
                min-width: 650px;
            }

            th, td {
                padding: 6px 4px;
                font-size: 0.7rem;
            }

            th {
                font-size: 0.65rem;
                padding: 10px 6px;
            }

            .stat-number {
                font-size: 1.25rem;
            }

            .stat-label {
                font-size: 0.65rem;
            }

            .status-badge {
                font-size: 0.6rem;
                padding: 1px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${t("attendance.export.attendanceReport")}</h1>
            <h2>${t("attendance.export.comprehensiveReport")}</h2>
            <p style="margin-top: 8px; opacity: 0.9; font-size: 0.9rem;">${t("attendance.export.generatedOn")}: ${dateRangeStr}</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-number">${totalRecords}</div>
                <div class="stat-label">${t("attendance.export.totalRecords")}</div>
            </div>
            <div class="stat-card present">
                <div class="stat-number">${presentCount}</div>
                <div class="stat-label">${t("attendance.status.present")}</div>
            </div>
            <div class="stat-card absent">
                <div class="stat-number">${absentCount}</div>
                <div class="stat-label">${t("attendance.status.absent")}</div>
            </div>
            <div class="stat-card late">
                <div class="stat-number">${lateCount}</div>
                <div class="stat-label">${t("attendance.status.late")}</div>
            </div>
            <div class="stat-card excused">
                <div class="stat-number">${excusedCount}</div>
                <div class="stat-label">${t("attendance.status.excused")}</div>
            </div>
        </div>

        <div class="content">
            <div class="table-container">
                <div class="table-wrapper">
                    <table>
                    <thead>
                        <tr>
                            <th>📅 ${t("attendance.export.headers.dateTime")}</th>
                            <th>👤 ${t("attendance.export.headers.studentName")}</th>
                            <th>🆔 ${t("attendance.export.headers.studentId")}</th>
                            <th>🏫 ${t("attendance.export.headers.room")}</th>
                            <th>📊 ${t("attendance.export.headers.status")}</th>
                            <th>🔍 ${t("attendance.export.headers.verification")}</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredRecords.map(record => {
                            // Safely handle date formatting with fallback
                            let formattedDate = t("attendance.export.notAvailable");
                            try {
                                // Try timestamp first, then createdAt as fallback
                                const dateValue = record.timestamp || record.createdAt;
                                if (dateValue) {
                                    const date = new Date(dateValue);
                                    if (!isNaN(date.getTime())) {
                                        formattedDate = formatDate(date, "PPp");
                                    }
                                }
                            } catch (e) {
                                // Keep default value
                            }

                            return `
                            <tr>
                                <td>
                                    <strong>${formattedDate}</strong>
                                </td>
                                <td><strong>${record.studentName || t("attendance.export.unknownStudent")}</strong></td>
                                <td>${record.studentId || "N/A"}</td>
                                <td>${record.roomName || t("attendance.export.unknownRoom")}</td>
                                <td>
                                    <span class="status-badge status-${record.status}">
                                        ${t(`attendance.status.${record.status}`)}
                                    </span>
                                </td>
                                <td>${record.verificationMethod === "manual"
                                    ? t("attendance.export.manualByTeacher")
                                    : record.verificationMethod
                                      ? record.verificationMethod.charAt(0).toUpperCase() + record.verificationMethod.slice(1)
                                      : t("attendance.export.notAvailable")}</td>
                            </tr>`;
                        }).join('')}
                    </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;
  };

  // Download file helper
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: `${mimeType};charset=utf-8;` });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Clean up old records (older than 7 days)
  const cleanupOldRecords = async () => {
    if (!profile?.school_id) return;

    try {
      setCleaning(true);
      const sevenDaysAgo = format(subDays(new Date(), 7), 'yyyy-MM-dd');

      const { error } = await supabase
        .from("attendance_records")
        .delete()
        .eq("school_id", profile.school_id)
        .lt("timestamp", sevenDaysAgo);

      if (error) throw error;

      sonnerToast.success(t("admin.attendanceManagement.cleanupSuccess"), {
        description: t("admin.attendanceManagement.cleanupSuccessDescription"),
      });

      // Refresh data
      await fetchAttendanceData();
    } catch (error) {
      console.error("Error cleaning up old records:", error);
      toast({
        title: t("common.error"),
        description: t("admin.attendanceManagement.cleanupError"),
        variant: "destructive",
      });
    } finally {
      setCleaning(false);
    }
  };

  useEffect(() => {
    fetchAttendanceData();
  }, [profile?.school_id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner message={t("admin.attendanceManagement.loadingReports")} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">
            {t("admin.attendanceManagement.last7Days")}
          </h3>
          <p className="text-sm text-muted-foreground">
            {t("admin.attendanceManagement.last7DaysDescription")}
          </p>
        </div>
        <Button
          onClick={cleanupOldRecords}
          disabled={cleaning}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <Trash2 className="h-4 w-4" />
          {cleaning ? t("common.cleaning") : t("admin.attendanceManagement.cleanup")}
        </Button>
      </div>

      {/* Export Interface - Matching Teacher's Design */}
      <div className="flex flex-col gap-4 border rounded-lg p-4 shadow-sm">
        {/* Mobile View */}
        <div className="md:hidden">
          <div className="flex flex-col justify-between items-start mb-3">
            <div className="space-y-1 mb-2">
              <h3 className="font-medium text-base">
                {t("attendance.export.exportRecords")}
              </h3>
              <p className="text-xs text-muted-foreground">
                {t("attendance.export.selectDateRangeAndFormat")}
              </p>
            </div>
          </div>

          {/* Date Selection */}
          <div className="space-y-3 mb-4">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !dateRange.from && "text-muted-foreground"
                  )}
                  size="sm"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange.from ? (
                    formatDate(dateRange.from, "MMM dd, yyyy", { locale: getDateLocale() })
                  ) : (
                    <span>{t("attendance.export.selectDate")}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="single"
                  selected={dateRange.from}
                  onSelect={(date) => setDateRange({ from: date, to: date })}
                  disabled={(date) => {
                    const today = new Date();
                    const sevenDaysAgo = subDays(today, 7);
                    return date > today || date < sevenDaysAgo;
                  }}
                  locale={getDateLocale()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            {/* Status Filter */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="w-full justify-between">
                  <span className="flex items-center gap-2">
                    <span>{statusInfo[statusFilter].icon}</span>
                    <span>{statusInfo[statusFilter].label}</span>
                  </span>
                  <span className="text-xs opacity-60">▼</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuLabel>{t("attendance.export.filterByStatus")}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  {Object.entries(statusInfo).map(([status, info]) => (
                    <DropdownMenuItem
                      key={status}
                      onClick={() => setStatusFilter(status as StatusFilter)}
                      className="flex items-center gap-2"
                    >
                      <span>{info.icon}</span>
                      <span>{info.label}</span>
                      {statusFilter === status && <span className="ml-auto">✓</span>}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Export Buttons */}
          <div className="grid grid-cols-3 gap-2">
            <Button
              onClick={() => dateRange.from && handleExport("html", dateRange.from)}
              disabled={!dateRange.from || exporting === "html"}
              variant="outline"
              size="sm"
              className="flex flex-col items-center gap-1 h-auto py-2"
            >
              <Globe className="h-4 w-4 text-blue-600" />
              <span className="text-xs">HTML</span>
            </Button>
            <Button
              onClick={() => dateRange.from && handleExport("pdf", dateRange.from)}
              disabled={!dateRange.from || exporting === "pdf"}
              variant="outline"
              size="sm"
              className="flex flex-col items-center gap-1 h-auto py-2"
            >
              <FileText className="h-4 w-4 text-red-600" />
              <span className="text-xs">PDF</span>
            </Button>
            <Button
              onClick={() => dateRange.from && handleExport("csv", dateRange.from)}
              disabled={!dateRange.from || exporting === "csv"}
              variant="outline"
              size="sm"
              className="flex flex-col items-center gap-1 h-auto py-2"
            >
              <FileText className="h-4 w-4 text-green-600" />
              <span className="text-xs">CSV</span>
            </Button>
          </div>
        </div>

        {/* Desktop View */}
        <div className="hidden md:block">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex-1 space-y-1">
              <h3 className="font-medium">
                {t("attendance.export.exportRecords")}
              </h3>
              <p className="text-sm text-muted-foreground">
                {t("attendance.export.selectDateRangeAndFormat")}
              </p>
            </div>
            <div className="flex flex-wrap gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "justify-start text-left font-normal",
                      !dateRange.from && "text-muted-foreground"
                    )}
                    size="sm"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      formatDate(dateRange.from, "MMM dd", { locale: getDateLocale() })
                    ) : (
                      <span>{t("attendance.export.selectDate")}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={dateRange.from}
                    onSelect={(date) => setDateRange({ from: date, to: date })}
                    disabled={(date) => {
                      const today = new Date();
                      const sevenDaysAgo = subDays(today, 7);
                      return date > today || date < sevenDaysAgo;
                    }}
                    locale={getDateLocale()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <span className="flex items-center gap-2">
                      <span>{statusInfo[statusFilter].icon}</span>
                      <span className="hidden sm:inline">{statusInfo[statusFilter].label}</span>
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>{t("attendance.export.filterByStatus")}</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    {Object.entries(statusInfo).map(([status, info]) => (
                      <DropdownMenuItem
                        key={status}
                        onClick={() => setStatusFilter(status as StatusFilter)}
                        className="flex items-center gap-2"
                      >
                        <span>{info.icon}</span>
                        <span>{info.label}</span>
                        {statusFilter === status && <span className="ml-auto">✓</span>}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    disabled={!dateRange.from || !!exporting}
                    size="sm"
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    {exporting ? t("common.exporting") : t("attendance.export.export")}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>{t("attendance.export.chooseFormat")}</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuItem
                      onClick={() => dateRange.from && handleExport("html", dateRange.from)}
                      disabled={!dateRange.from || exporting === "html"}
                      className="flex items-center gap-2 cursor-pointer hover:bg-blue-50 focus:bg-blue-50"
                    >
                      <Globe className="h-4 w-4 text-blue-600" />
                      <span>{t("attendance.export.exportAsHTML")}</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => dateRange.from && handleExport("pdf", dateRange.from)}
                      disabled={!dateRange.from || exporting === "pdf"}
                      className="flex items-center gap-2 cursor-pointer hover:bg-red-50 focus:bg-red-50"
                    >
                      <FileText className="h-4 w-4 text-red-600" />
                      <span>{t("attendance.export.exportAsPDF")}</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => dateRange.from && handleExport("csv", dateRange.from)}
                      disabled={!dateRange.from || exporting === "csv"}
                      className="flex items-center gap-2 cursor-pointer hover:bg-green-50 focus:bg-green-50"
                    >
                      <FileText className="h-4 w-4 text-green-600" />
                      <span>{t("attendance.export.exportAsCSV")}</span>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* Reports Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {reports.map((report) => (
          <Card key={report.date} className="relative">
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center justify-between">
                <span>{report.displayDate}</span>
                <Badge variant={report.attendanceRate >= 80 ? "default" : "secondary"}>
                  {report.attendanceRate.toFixed(1)}%
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Total Students */}
              <div className="flex items-center gap-2 text-sm font-medium">
                <Users className="h-4 w-4 text-blue-500" />
                <span>{t("common.total")}: {report.totalStudents}</span>
              </div>

              {/* Attendance Statistics Grid */}
              <div className="grid grid-cols-2 gap-3 text-sm">
                {/* Present */}
                <div className="flex items-center gap-2 p-2 bg-green-50 rounded-md">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-700">
                    {t("common.present")}: <strong>{report.presentStudents}</strong>
                  </span>
                </div>

                {/* Late */}
                <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded-md">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-yellow-700">
                    {t("common.late")}: <strong>{report.lateStudents}</strong>
                  </span>
                </div>

                {/* Absent */}
                <div className="flex items-center gap-2 p-2 bg-red-50 rounded-md">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-red-700">
                    {t("common.absent")}: <strong>{report.absentStudents}</strong>
                  </span>
                </div>

                {/* Excused */}
                <div className="flex items-center gap-2 p-2 bg-blue-50 rounded-md">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-blue-700">
                    {t("common.excused")}: <strong>{report.excusedStudents}</strong>
                  </span>
                </div>
              </div>

              <Button
                onClick={() => {
                  const reportDate = new Date(report.date);
                  setDateRange({ from: reportDate, to: reportDate });
                }}
                className="w-full"
                size="sm"
                variant="outline"
              >
                <Calendar className="h-4 w-4 mr-2" />
                {t("attendance.export.selectForExport")}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
