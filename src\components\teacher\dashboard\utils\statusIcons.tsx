import { CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";

export const getStatusIcon = (
  status: "present" | "absent" | "late" | "excused"
) => {
  switch (status) {
    case "present":
      return <CheckCircle className="text-green-600" size={20} />;
    case "late":
      return <Clock className="text-yellow-600" size={20} />;
    case "excused":
      return <AlertCircle className="text-blue-600" size={20} />;
    case "absent":
      return <XCircle className="text-red-600" size={20} />;
  }
};
