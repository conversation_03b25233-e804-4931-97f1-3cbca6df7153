import React, { createContext, useContext, ReactNode } from "react";
import { usePersistentTab } from "@/hooks/usePersistentTab";
import { useLocation } from "react-router-dom";

type TabContextType = {
  activeTab: string;
  setActiveTab: (tab: string) => void;
};

const TabContext = createContext<TabContextType | undefined>(undefined);

export const TabProvider: React.FC<{
  children: ReactNode;
  defaultTab?: string;
}> = ({ children, defaultTab = "scan" }) => {
  const location = useLocation();
  const path = location.pathname.split("/")[1] || "default";
  const storageKey = `${path}-activeTab`;

  const [activeTab, setActiveTab] = usePersistentTab(defaultTab, storageKey);

  return (
    <TabContext.Provider value={{ activeTab, setActiveTab }}>
      {children}
    </TabContext.Provider>
  );
};

export const useTab = (): TabContextType => {
  const context = useContext(TabContext);
  if (context === undefined) {
    throw new Error("useTab must be used within a TabProvider");
  }
  return context;
};
