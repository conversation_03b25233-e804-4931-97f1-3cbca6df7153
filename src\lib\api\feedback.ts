import { supabase } from "@/lib/supabase";
import { User } from "@/lib/types";

export interface FeedbackSubmission {
  id: string;
  user_id: string | null;
  user_role: string | null;
  name: string | null;
  email: string | null;
  phone: string | null;
  message: string;
  school_id: string | null;
  status: "unread" | "read" | "archived";
  created_at: string;
  updated_at: string;
}

export interface FeedbackSubmissionInput {
  name?: string;
  email?: string;
  phone?: string;
  message: string;
}

/**
 * Submit feedback from a user
 * @param feedback The feedback data to submit
 * @param user The current user (optional)
 * @returns The created feedback submission
 */
export const submitFeedback = async (
  feedback: FeedbackSubmissionInput,
  user?: User | null
): Promise<FeedbackSubmission | null> => {
  try {
    console.log("Submitting feedback:", feedback);
    console.log(
      "User info:",
      user ? { id: user.id, role: user.role, email: user.email } : "No user"
    );

    // Simplified feedback data that doesn't rely on user information
    const feedbackData = {
      name: feedback.name || "Anonymous",
      email: feedback.email || user?.email || null,
      phone: feedback.phone || null,
      message: feedback.message,
      // Only include user-related fields if we have a user
      ...(user
        ? {
            user_id: user.id,
            user_role: user.role,
            school_id: user.school_id,
          }
        : {}),
    };

    console.log("Feedback data to insert:", feedbackData);

    const { data, error } = await supabase
      .from("feedback_submissions")
      .insert(feedbackData)
      .select("*")
      .single();

    if (error) {
      console.error("Error submitting feedback:", error);
      console.error("Error details:", error.message, error.details, error.hint);
      return null;
    }

    console.log("Feedback submitted successfully:", data);
    return data;
  } catch (error) {
    console.error("Error submitting feedback:", error);
    console.error(
      "Error details:",
      error instanceof Error ? error.message : JSON.stringify(error)
    );
    return null;
  }
};

/**
 * Get all feedback submissions for system admin
 * @returns Array of feedback submissions
 */
export const getAllFeedbackSubmissions = async (): Promise<
  FeedbackSubmission[]
> => {
  try {
    const { data, error } = await supabase
      .from("feedback_submissions")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching feedback submissions:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching feedback submissions:", error);
    return [];
  }
};

/**
 * Get feedback submissions for a specific school
 * @param schoolId The school ID to filter by
 * @returns Array of feedback submissions for the school
 */
export const getSchoolFeedbackSubmissions = async (
  schoolId: string
): Promise<FeedbackSubmission[]> => {
  try {
    const { data, error } = await supabase
      .from("feedback_submissions")
      .select("*")
      .eq("school_id", schoolId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching school feedback submissions:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Error fetching school feedback submissions:", error);
    return [];
  }
};

/**
 * Update the status of a feedback submission
 * @param id The ID of the feedback submission
 * @param status The new status
 * @returns True if update was successful
 */
export const updateFeedbackStatus = async (
  id: string,
  status: "unread" | "read" | "archived"
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("feedback_submissions")
      .update({ status, updated_at: new Date().toISOString() })
      .eq("id", id);

    if (error) {
      console.error("Error updating feedback status:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error updating feedback status:", error);
    return false;
  }
};

/**
 * Delete a feedback submission
 * @param id The ID of the feedback submission to delete
 * @returns True if deletion was successful
 */
export const deleteFeedback = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("feedback_submissions")
      .delete()
      .eq("id", id);

    if (error) {
      console.error("Error deleting feedback:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error deleting feedback:", error);
    return false;
  }
};
