-- Fix attendance_records table to include missing block_id column
-- This migration adds the block_id column that was missing from the table

-- Add block_id column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'attendance_records' 
    AND column_name = 'block_id'
  ) THEN
    ALTER TABLE public.attendance_records 
    ADD COLUMN block_id UUID REFERENCES blocks(id) ON DELETE SET NULL;
    
    RAISE NOTICE 'Added block_id column to attendance_records table';
  ELSE
    RAISE NOTICE 'block_id column already exists in attendance_records table';
  END IF;
END $$;

-- Add qr_session_id column if it doesn't exist (also needed for security)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'attendance_records' 
    AND column_name = 'qr_session_id'
  ) THEN
    ALTER TABLE public.attendance_records 
    ADD COLUMN qr_session_id UUID;
    
    RAISE NOTICE 'Added qr_session_id column to attendance_records table';
  ELSE
    RAISE NOTICE 'qr_session_id column already exists in attendance_records table';
  END IF;
END $$;

-- Add location_data column if it doesn't exist (for GPS tracking)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'attendance_records' 
    AND column_name = 'location_data'
  ) THEN
    ALTER TABLE public.attendance_records 
    ADD COLUMN location_data JSONB;
    
    RAISE NOTICE 'Added location_data column to attendance_records table';
  ELSE
    RAISE NOTICE 'location_data column already exists in attendance_records table';
  END IF;
END $$;

-- Add school_id column if it doesn't exist (for multi-tenant isolation)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'attendance_records' 
    AND column_name = 'school_id'
  ) THEN
    ALTER TABLE public.attendance_records 
    ADD COLUMN school_id UUID REFERENCES schools(id) ON DELETE CASCADE;
    
    RAISE NOTICE 'Added school_id column to attendance_records table';
  ELSE
    RAISE NOTICE 'school_id column already exists in attendance_records table';
  END IF;
END $$;

-- Create indexes for the new columns if they don't exist
CREATE INDEX IF NOT EXISTS idx_attendance_records_block_id ON public.attendance_records(block_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_qr_session_id ON public.attendance_records(qr_session_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_school_id ON public.attendance_records(school_id);

-- Update RLS policies to include school_id isolation
DROP POLICY IF EXISTS "Allow authenticated users full access" ON public.attendance_records;

-- Create school-isolated RLS policy
CREATE POLICY "School isolation for attendance records" ON public.attendance_records
  FOR ALL
  TO authenticated
  USING (
    school_id IN (
      SELECT school_id FROM profiles WHERE id = auth.uid()
    )
  )
  WITH CHECK (
    school_id IN (
      SELECT school_id FROM profiles WHERE id = auth.uid()
    )
  );

-- Grant necessary permissions
GRANT ALL ON public.attendance_records TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Create function to automatically set school_id from user profile
CREATE OR REPLACE FUNCTION set_attendance_school_id()
RETURNS TRIGGER AS $$
BEGIN
  -- If school_id is not provided, get it from the user's profile
  IF NEW.school_id IS NULL THEN
    SELECT school_id INTO NEW.school_id
    FROM profiles
    WHERE id = NEW.student_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically set school_id
DROP TRIGGER IF EXISTS set_attendance_school_id_trigger ON public.attendance_records;
CREATE TRIGGER set_attendance_school_id_trigger
  BEFORE INSERT ON public.attendance_records
  FOR EACH ROW
  EXECUTE FUNCTION set_attendance_school_id();

-- Create function to check for duplicate attendance (enhanced)
CREATE OR REPLACE FUNCTION check_duplicate_attendance_enhanced()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if student already has attendance for today in this room
  IF EXISTS (
    SELECT 1 FROM attendance_records
    WHERE student_id = NEW.student_id
    AND room_id = NEW.room_id
    AND DATE(timestamp) = DATE(NEW.timestamp)
    AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
  ) THEN
    RAISE EXCEPTION 'Student already has attendance recorded for today in this room';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for duplicate check
DROP TRIGGER IF EXISTS check_duplicate_attendance_enhanced_trigger ON public.attendance_records;
CREATE TRIGGER check_duplicate_attendance_enhanced_trigger
  BEFORE INSERT OR UPDATE ON public.attendance_records
  FOR EACH ROW
  EXECUTE FUNCTION check_duplicate_attendance_enhanced();

RAISE NOTICE 'Successfully fixed attendance_records table structure';
