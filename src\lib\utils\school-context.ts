import { supabase } from "@/lib/supabase";
import { User } from "@/lib/types";

/**
 * School context utility functions for handling multi-school functionality
 */

/**
 * Get the current user's school ID
 * @param userId The user ID to get the school for
 * @returns The school ID or null if not found
 */
export const getUserSchoolId = async (
  userId: string
): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("school_id")
      .eq("user_id", userId)
      .single();

    if (error) {
      console.error("Error getting user school ID:", error);
      return null;
    }

    return data?.school_id || null;
  } catch (error) {
    console.error("Unexpected error getting user school ID:", error);
    return null;
  }
};

/**
 * Get the current user's access level
 * @param userId The user ID to get the access level for
 * @returns The access level (1: School admin, 2: District admin, 3: System admin) or 1 if not found
 */
export const getUserAccessLevel = async (userId: string): Promise<number> => {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("access_level")
      .eq("user_id", userId)
      .single();

    if (error) {
      console.error("Error getting user access level:", error);
      return 1; // Default to school admin
    }

    return data?.access_level || 1;
  } catch (error) {
    console.error("Unexpected error getting user access level:", error);
    return 1; // Default to school admin
  }
};

/**
 * Check if the user is a system admin
 * @param userId The user ID to check
 * @returns True if the user is a system admin, false otherwise
 */
export const isSystemAdmin = async (userId: string): Promise<boolean> => {
  const accessLevel = await getUserAccessLevel(userId);
  return accessLevel === 3;
};

/**
 * Add school context to a Supabase query
 * @param query The Supabase query to add school context to
 * @param user The current user
 * @returns The query with school context added
 */
export const withSchoolContext = async (query: any, user: User | null) => {
  if (!user) {
    return query;
  }

  // If the user is a system admin, don't filter by school
  if (user.accessLevel === 3) {
    return query;
  }

  // Get the user's school ID
  const schoolId = user.school_id || (await getUserSchoolId(user.id));

  if (schoolId) {
    return query.eq("school_id", schoolId);
  }

  return query;
};

/**
 * Get all schools
 * @returns Array of schools
 */
export const getAllSchools = async () => {
  try {
    const { data, error } = await supabase
      .from("schools")
      .select("*")
      .order("name");

    if (error) {
      console.error("Error getting schools:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Unexpected error getting schools:", error);
    return [];
  }
};

/**
 * Get a school by ID
 * @param schoolId The school ID to get
 * @returns The school or null if not found
 */
export const getSchoolById = async (schoolId: string) => {
  try {
    // Check if schoolId is valid
    if (!schoolId) {
      console.warn("Invalid school ID provided to getSchoolById");
      return null;
    }

    // First try with single() which expects exactly one result
    let { data, error } = await supabase
      .from("schools")
      .select("*")
      .eq("id", schoolId)
      .single();

    if (error) {
      console.error("Error getting school:", error);

      // If the error is because no rows were returned, try without single()
      if (error.code === "PGRST116") {
        const { data: multiData, error: multiError } = await supabase
          .from("schools")
          .select("*")
          .eq("id", schoolId);

        if (multiError) {
          console.error("Error getting school (fallback):", multiError);
          return null;
        }

        // If we got results, use the first one
        if (multiData && multiData.length > 0) {
          data = multiData[0];
        } else {
          return null;
        }
      } else {
        return null;
      }
    }

    if (!data) {
      return null;
    }

    // Map the database fields to the application's School type
    return {
      id: data.id,
      name: data.name,
      address: data.address,
      city: data.city,
      state: data.state,
      zip: data.zip,
      country: data.country,
      phone: data.phone,
      email: data.email,
      website: data.website,
      logoUrl: data.logo_url,
      primaryColor: data.primary_color,
      secondaryColor: data.secondary_color,
      invitationCode: data.invitation_code, // Ensure this is properly mapped
      isActive: data.is_active,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
  } catch (error) {
    console.error("Unexpected error getting school:", error);
    return null;
  }
};

/**
 * Create a new school
 * @param schoolData The school data to create
 * @returns The created school or null if failed
 */
export const createSchool = async (schoolData: any) => {
  try {
    console.log("Creating school with data:", schoolData);

    // Check if required fields are present
    if (!schoolData.name) {
      console.error("School name is required");
      return null;
    }

    // Make sure we have the correct fields for the schools table
    const sanitizedData = {
      name: schoolData.name,
      address: schoolData.address || null,
      city: schoolData.city || null,
      state: schoolData.state || null,
      zip: schoolData.zip || null,
      country: schoolData.country || null,
      phone: schoolData.phone || null,
      email: schoolData.email || null,
      website: schoolData.website || null,
      status: schoolData.status || "active",
      created_at: schoolData.created_at || new Date().toISOString(),
      updated_at: schoolData.updated_at || new Date().toISOString(),
    };

    // Add any additional fields that might be in the schools table
    if (schoolData.invitation_code)
      sanitizedData.invitation_code = schoolData.invitation_code;
    if (schoolData.primaryColor)
      sanitizedData.primary_color = schoolData.primaryColor;
    if (schoolData.secondaryColor)
      sanitizedData.secondary_color = schoolData.secondaryColor;
    if (schoolData.isActive !== undefined)
      sanitizedData.is_active = schoolData.isActive;

    console.log("Sanitized school data:", sanitizedData);

    const { data, error } = await supabase
      .from("schools")
      .insert(sanitizedData)
      .select()
      .single();

    if (error) {
      console.error("Error creating school:", error);
      return null;
    }

    console.log("School created successfully:", data);
    return data;
  } catch (error) {
    console.error("Unexpected error creating school:", error);
    return null;
  }
};

/**
 * Update a school
 * @param schoolId The school ID to update
 * @param schoolData The school data to update
 * @returns The updated school or null if failed
 */
export const updateSchool = async (schoolId: string, schoolData: any) => {
  try {
    console.log("Updating school with ID:", schoolId, "Data:", schoolData);

    // Check if required fields are present
    if (!schoolId) {
      console.error("School ID is required for updates");
      return null;
    }

    // Make sure we have the correct fields for the schools table
    const sanitizedData: any = {};

    // Only include fields that are provided
    if (schoolData.name !== undefined) sanitizedData.name = schoolData.name;
    if (schoolData.address !== undefined)
      sanitizedData.address = schoolData.address;
    if (schoolData.city !== undefined) sanitizedData.city = schoolData.city;
    if (schoolData.state !== undefined) sanitizedData.state = schoolData.state;
    if (schoolData.zip !== undefined) sanitizedData.zip = schoolData.zip;
    if (schoolData.country !== undefined)
      sanitizedData.country = schoolData.country;
    if (schoolData.phone !== undefined) sanitizedData.phone = schoolData.phone;
    if (schoolData.email !== undefined) sanitizedData.email = schoolData.email;
    if (schoolData.website !== undefined)
      sanitizedData.website = schoolData.website;
    if (schoolData.status !== undefined)
      sanitizedData.status = schoolData.status;
    if (schoolData.updated_at !== undefined)
      sanitizedData.updated_at = schoolData.updated_at;

    // Handle special fields
    if (schoolData.primaryColor !== undefined)
      sanitizedData.primary_color = schoolData.primaryColor;
    if (schoolData.secondaryColor !== undefined)
      sanitizedData.secondary_color = schoolData.secondaryColor;
    if (schoolData.isActive !== undefined)
      sanitizedData.is_active = schoolData.isActive;

    console.log("Sanitized update data:", sanitizedData);

    const { data, error } = await supabase
      .from("schools")
      .update(sanitizedData)
      .eq("id", schoolId)
      .select()
      .single();

    if (error) {
      console.error("Error updating school:", error);
      return null;
    }

    console.log("School updated successfully:", data);
    return data;
  } catch (error) {
    console.error("Unexpected error updating school:", error);
    return null;
  }
};
